<template>
    <CustCard :title="title">
        <el-row class="dataOverview" :gutter="20">
            <el-col :span="12">
                <el-row :gutter="20">
                    <el-col :class="['dataOverview_list', `per-${list.charge.length}`]" v-for="(item, index) in list.charge" :key="index" :span="24">
                        <div class="dataOverview_list_bg">
                            <img :src="item.icon" :alt="item.name">
                            <div>
                                <span class="val">{{ item.value }} <em>{{ item.unit }}</em></span>
                                <span class="name">{{ item.name }}</span>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </el-col>
            <el-col :span="12">
                <el-row :gutter="20">
                    <el-col :class="['dataOverview_list', `per-${list.discharge.length}`]" v-for="(item, index) in list.discharge" :key="index" :span="24">
                        <div class="dataOverview_list_bg">
                            <img :src="item.icon" :alt="item.name">
                            <div>
                                <span class="val">{{ item.value }} <em>{{ item.unit }}</em></span>
                                <span class="name">{{ item.name }}</span>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </el-col>
        </el-row>
    </CustCard>
</template>

<script>
import CustCard from '@/views/components/cO2View/components/common/screen/components/custCard.vue'
import {
    buildingSummaryData,
    buildingEnergyTypeSummary
} from "@/api/energy/apis";
import {
    deviceDataSummary,
} from "@/api/device/apis";
export default {
    components: {
        CustCard
    },
    props: {
        title: {
            type: String,
            default: "数据总览"
        },
        params: {
            type: Object,
            default() {
                return {
                    // "chargeDeviceTypes":"储能放电",
                    // "dischargeDeviceTypes":"储能充电"
                }
            }
        },
    },
    data() {
        return {
            curBuilding: this.gf.getCurBuilding(),
            list: {
                charge: [
                    {
                        icon: "/image/energyStatistical_sum.png",
                        name: "累计充电",
                        value: 0,
                        unit: "kWh",
                        code: "total",
                        from: this.$moment('1949-10-01').startOf("year").format("YYYY-MM-DD"),
                        to: this.$moment().endOf("year").format("YYYY-MM-DD"),
                    },
                    {
                        icon: "/image/energyStatistical_day.png",
                        name: "本日充电",
                        value: 0,
                        unit: "kwh",
                        code: "today",
                        from: this.$moment().startOf("day").format("YYYY-MM-DD"),
                        to: this.$moment().endOf("day").format("YYYY-MM-DD"),
                    },
                    {
                        icon: "/image/energyStatistical_year.png",
                        name: "本年充电",
                        value: 0,
                        unit: "kWh",
                        code: "curYear",
                        from: this.$moment().startOf("year").format("YYYY-MM-DD"),
                        to: this.$moment().endOf("year").format("YYYY-MM-DD"),
                    },
                ],
                discharge: [
                    {
                        icon: "/image/energyStatistical_sum.png",
                        name: "累计放电",
                        value: 0,
                        unit: "kWh",
                        code: "total",
                        from: this.$moment('1949-10-01').startOf("year").format("YYYY-MM-DD"),
                        to: this.$moment().endOf("year").format("YYYY-MM-DD"),
                    },
                    {
                        icon: "/image/energyStatistical_day.png",
                        name: "本日放电",
                        value: 0,
                        unit: "kwh",
                        code: "today",
                        from: this.$moment().startOf("day").format("YYYY-MM-DD"),
                        to: this.$moment().endOf("day").format("YYYY-MM-DD"),
                    },
                    {
                        icon: "/image/energyStatistical_year.png",
                        name: "本年放电",
                        value: 0,
                        unit: "kWh",
                        code: "curYear",
                        from: this.$moment().startOf("year").format("YYYY-MM-DD"),
                        to: this.$moment().endOf("year").format("YYYY-MM-DD"),
                    }
                ]
            }
        }
    },
    created() {
        if(this.params.hideList){
            this.list.charge = this.list.charge.filter(item => this.params.hideList.indexOf(item.code) < 0);
            this.list.discharge = this.list.discharge.filter(item => this.params.hideList.indexOf(item.code) < 0);
        }
        this.getSummaryData();
    },
    methods: {
        getSummaryData() {
            this.list.charge.forEach(item => {
                const { from, to } = item;
                let deviceTypes = this.params.chargeDeviceTypes;
                buildingEnergyTypeSummary({
                    buildingId: this.curBuilding.id,
                    from, to,
                    deviceTypes: deviceTypes,
                }).then(({ data }) => {
                    const dt = data[deviceTypes] ? data[deviceTypes][deviceTypes] : {};
                    this.$set(item, 'value', (dt.total || 0).toFixed(2))
                })
            });
            this.list.discharge.forEach(item => {
                const { from, to } = item;
                let deviceTypes = this.params.dischargeDeviceTypes;
                buildingEnergyTypeSummary({
                    buildingId: this.curBuilding.id,
                    from, to,
                    deviceTypes: deviceTypes,
                }).then(({ data }) => {
                    const dt = data[deviceTypes] ? data[deviceTypes][deviceTypes] : {};
                    this.$set(item, 'value', (dt.total || 0).toFixed(2))
                })
            });
        },
    }
}
</script>

<style scoped lang='scss'>
.dataOverview {
    padding: 12px;
    height: 100%;
    &>.el-col {
        height: 100%;
        &>.el-row {
            height: 100%;
        }
    }
    .dataOverview_list {

        margin-bottom: 0;
        &.per-3 {
            height: 33.333%;
        }
        &.per-2 {
            height: 44%;
        }
        &.per-1 {
            height: 74%;
        }
        .dataOverview_list_bg {
            display: flex;
            align-items: center;
            height: 100%;
            background: url('/image/energyStatistical_bg.png') no-repeat 0 96%;
            background-size: contain;

            img {
                width: 22%;
                margin-left: 28px;
            }

            &>div {
                display: flex;
                flex-direction: column;
                margin-left: 18px;

                .val {
                    font-size: 24px;
                    color: #3CCCF9;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    font-weight: 400;

                    em {
                        font-style: normal;
                        font-size: 14px;
                        color: #FFFFFF;
                    }
                }

                .name {
                    font-size: 12px;
                }
            }
        }
    }

}
</style>