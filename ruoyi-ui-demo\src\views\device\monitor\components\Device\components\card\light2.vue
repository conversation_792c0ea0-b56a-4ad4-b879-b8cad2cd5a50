<template>
  <div class="baseMult airConditionCard" :class="item.status + 'Out'">
    <div class="header">
      <span class="text">
        <el-tooltip effect="dark" :content="item.name + ' (' + item.id + ')'" placement="top">
          <span class="itemName" v-text="item.name"></span>
        </el-tooltip>
      </span>
      <el-checkbox class="chbox" v-model="checked" :label="item.id" :key="item.id"> {{ ct }} </el-checkbox>
      <div class="clearfix"></div>
    </div>

    <div class="body">
      <div style="flex-wrap: wrap;" :class="['bls', getBgClass()]"></div>
      <div class="bls_btm flex flex-sp mt10">
        <el-tooltip content="模式" placement="top-start" trigger="hover" class="dropdown">
          <el-dropdown size="mini" type="primary" trigger="click" @command="strategySelectChange" :hide-on-click="false">
            <span class="el-dropdown-link">
              模式<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in strategyListAfterFilter" :key="item.id" :command="item.id"
                :class="{ 'selected': strategySelect.indexOf(item.id) > -1 }">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-tooltip>
        <el-tooltip content="开关" placement="top-start" trigger="hover" class="switch" v-if="hasStatusTagData">
          <el-switch v-model="statusSet" active-color="#2294FE" inactive-color="#5F7C9E" :active-value="getActiveValue"
            :inactive-value="getInactiveValue" @change="statusChange">
          </el-switch>
        </el-tooltip>
        <el-tooltip content="设备详情" placement="top-start" trigger="hover">
          <div class="detail" @click="handleDeviceSummaryShow(item, deviceInd)">详情</div>
        </el-tooltip>
      </div>
      <div class="curren_mode">模式 | {{ strategySelectNameSum }}</div>
    </div>
  </div>
</template>


<script>
import DeviceBaseFunc from "../DeviceBaseFunc";
import { updateStrategyDevice, strategyDeviceList, resourceDevice } from '@/api/device/apis'
export default {
  name: "BaseMultNewCard",
  mixins: [DeviceBaseFunc], // 继承父模块方法
  props: {
    "displayKeys": {
      type: Array,
      default: () => { return [] },
      desc: "卡片上显示的三个属性值，如果没有，则按oid取前3个"
    },
    "summaryType": {
      type: String,
      default: "device",
      desc: "设备统计模块显示内容, device/sensor"
    },
    "cardDataNum": {
      type: Number,
      default: 3,
      desc: "卡片形式下数据展示个数"
    },
    "dialogType": {
      type: String,
      default: "",
      desc: "设备弹框详情样式，复杂样式:mult"
    },
    "strategyList": {
      type: Array,
      default() {
        return []
      },
    },
  },
  components: {},
  computed: {
    strategySelectNameSum() {
      const arr = this.strategyList.filter(item => this.strategySelect.indexOf(item.id) > -1);
      const nameArr = arr.map(item => { return item.name });
      return nameArr.join(',')
    },
    strategyListAfterFilter() {
      const arr = this.strategyList.filter(item => item.status == 'Y') || [];
      return arr
    },
    getActiveValue () {
      const it = this.statusMap.find(item => item.name === "on");
      return it.code
    },
    getInactiveValue () {
      const it = this.statusMap.find(item => item.name === "off");
      return it.code
    }
  },
  data() {
    return {
      checked: true, // 选中状态
      ct: "", // 占位，checkbox 不显示文字

      // 卡片上显示的数据
      // datas: [],
      statusMap: [
        {
          name: "off",
          maybe: ['停', '关'],
          code: "",
          drRealVal: 0
        },
        {
          name: "on",
          maybe: ['开', '启', '运'],
          code: "",
          drRealVal: 1
        }
      ],
      statusSet: 0,
      strategySelect: [],
      tag: "status",
      hasStatusTagData: true
    }
  },
  created() {
  },
  destroyed() { },
  watch: {
    device: {
      handler(val, oldVal) {
        if (val) {
          console.log(val, 'device')
          this.backUpData();
        }
      },
      immediate: true,
      deep: true,
    }
  },
  mounted() {
  },
  methods: {
    backUpData() {
      let item = JSON.parse(JSON.stringify(this.device));
      this.item = item;
      this.getStatusData();
      this.strategySelect = item.strategyList.map(item2 => item2.id);
    },
    getBgClass() {
      const realVal = (this.item.swichObj || {}).drRealVal || 0;
      const it = this.statusMap.find(item => item.drRealVal == realVal);
      return `zm_${it.name}`
    },
    statusChange(val) {
      const dt = this.item.deviceDataBase.find(item => item.drId && item.dmTag.indexOf(this.tag) > -1) || {};
      dt.val = val;
      this.handleSaveDevice(dt);
    },
    async getCurrentDeviceStrategyList() {
      resourceDevice({
        deviceId: this.item.id
      }).then(({ data = [] }) => {
        this.strategySelect = data.strategyList.map(item => item.id);
        this.$message({
          message: `应用策略已更新`,
          type: 'success'
        });
      })
    },
    strategySelectChange(val) {
      const id = this.item.id;
      const idx = this.strategySelect.findIndex(item => item == val);
      if (idx > -1) {
        this.strategySelect.splice(idx, 1)
      } else {
        this.strategySelect.push(val)
      }
      strategyDeviceList({ id: val }).then(({ data = [] }) => {
        const idx = data.findIndex(item => item.deviceId == id);
        let deviceIdsArr = data.map(item => item.deviceId);
        if (idx < 0) {
          deviceIdsArr.push(id);
          updateStrategyDevice({
            strategyIds: val,
            deviceIds: deviceIdsArr.join(',')
          }).then(res => {
            this.getCurrentDeviceStrategyList();
          })
        } else {
          deviceIdsArr = deviceIdsArr.filter(item => item != id);
          updateStrategyDevice({
            strategyIds: val,
            deviceIds: deviceIdsArr.join(',')
          }).then(res => {
            this.getCurrentDeviceStrategyList();
          })
        }
      })
    },
    getStatusData () {
      const dt = this.item.deviceDataBase.find(item => item.drId && item.dmTag.indexOf(this.tag) > -1) || {};
      this.statusSet = dt.dVal || "0";
      if(dt && dt.drId && dt.drOtherData ){
        for (const k in (dt.otherRDataMap || {})) {
          const it = this.statusMap.find(item => {
            const had = item.maybe.find(i => dt.otherRDataMap[k].indexOf(i) > -1);
            return had
          });
          if(it){
            it.code = k
          }
        }
      } else {
        this.hasStatusTagData = false;
      }
    }
  }
}
</script>

<style scoped lang="scss">
.header {
  border: none;

  .itemName {
    width: calc(100% - 25px);
  }

  .chbox {
    top: -10px;
    right: -10px;
  }
}

.smBtn .el-button {
  padding: 5px 8px;
}

.bl {
  padding: 4px;
}

.bls_half {
  min-height: 140px;
}

.bls_triplet {
  min-height: 70px;
}

.bls_total>.bl {
  width: 100%;
}

.bls_half>.bl {
  width: 46%;
}

.bls_triplet>.bl {
  width: 30%;
}

.bls_fifth {
  justify-content: flex-start;
}

.bls_fifth>.bl {
  width: 33%;
}

.n {
  max-width: 100% !important;
}

.unit {
  color: #fff;
  font-size: 14px;
}

.detail {
  border-radius: 2px;
  border: 1px solid rgba(60, 204, 249, 0.4);
  font-size: 12px;
  color: #3CCCF9;
  padding: 4px 10px;
  cursor: pointer;
}

.el-dropdown-menu__item {
  &.selected {
    color: #2294FE;
  }
}

.body {
  position: relative;
  padding: 18px 12px 12px;

  .bls {
    min-height: 86px;

    &.zm_on {
      background: url('/image/zm_on.png') no-repeat center;
      background-size: 84px;
    }

    &.zm_off {
      background: url('/image/zm_off.png') no-repeat center;
      background-size: 84px;
    }

    &.zm_warning {
      background: url('/image/zm_warning.png') no-repeat center;
      background-size: 84px;
    }
  }

  .bls_btm {
    padding: 0;
    position: relative;

    .switch {
      position: absolute;
      left: 50%;
      transform: translate(-50%, 0);
      top: 3px;
    }

    .dropdown {
      line-height: 26px;
    }
  }

  .curren_mode {
    position: absolute;
    top: -8px;
    left: 12px;
    font-size: 12px;
  }
}
</style>
