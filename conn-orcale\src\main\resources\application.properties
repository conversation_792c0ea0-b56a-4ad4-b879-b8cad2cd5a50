
# nohup java -Dserver.port=8080 -jar oracle-conn-1.0.jar &
# nohup java -Dspring.profiles.active=prod -jar oracle-conn-1.0.jar &

server.port=8080
#server.address=127.0.0.1
server.session-timeout=60

#spring.datasource.url=******************************************
#spring.datasource.username=vqor4s
#spring.datasource.password=vqor314400
#spring.datasource.driver-class-name=oracle.jdbc.OracleDriver


spring.datasource.url=****************************************************************************************************************************************
spring.datasource.username=tmp_test
spring.datasource.password=TmpTest121
spring.datasource.driver-class-name=com.mysql.jdbc.Driver

#spring.datasource.device2.url=****************************************************************************************************************************************
#spring.datasource.device2.username=tmp_test
#spring.datasource.device2.password=TmpTest121
#spring.datasource.device2.driver-class-name=com.mysql.jdbc.Driver


#mybatis.mapperLocations=classpath:mapper/*.xml
#
## 数据库结构一样
#source_mapper_path=/mapper/**.xml
#source_entity_package=com.energy.dao

mybatis.config-location=classpath:/mybatis/mybatis-config.xml
mybatis.mapper-locations=classpath:/mybatis/mapper/*.xml


# 导入driver
#mvn install:install-file -DgroupId=com.oracle -DartifactId=ojdbc6 -Dversion=11.2.0.3 -Dpackaging=jar -Dfile=ojdbc8.jar
