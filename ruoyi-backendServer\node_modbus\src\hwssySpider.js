/**
 仁济医院项目 对接 华为Iot 设施云 平台数据
 功能:
 运行: node src/hwssySpider.js [buildingId] [port] [dbName] [debug]

 字典 building_config_[buildingId] 中增加配置
 hw_server：   服务器地址，  https://facility-api.cn-north-4.myhuaweicloud.com
 hw_username： 用户名
 hw_password： 密码
 hw_instanceId：
 hw_collectorId： 采集器id


 // 1分钟执行一次
 syncDeviceData(); -> 同步所有数据

 // 1小时重启一次


 */

// create an empty modbus client
const schedule = require("node-schedule");
var moment = require("moment");

const Db = require("./mydb");
const renjiApi = require("./lib/hwssyApi");
const helper = require("./helper");

const sysConfig = require("./conf/sysConfig").sysConfig();
const config = {};

const buildingId = process.argv[2] > 0 ? process.argv[2] : 1;
const port = process.argv[3] && process.argv[3] != "debug" ? process.argv[3] : 8001;
const dbName = process.argv[4] && process.argv[4] != "debug" ? process.argv[4] : null;
global.isDebug = process.argv[5] == "debug" ? true : false;

// 参数替换 覆盖之前的配置
if (dbName) {
    sysConfig.mysql.database = dbName;
}
// 替换端口
if (port) {
    sysConfig.localServer.port = port;
}

var _db = new Db(sysConfig.mysql);
// 双数据库同步
if (typeof sysConfig.mysql2 != "undefined") {
    _db.createServer2(sysConfig.mysql2);
}

// i接口实例，启动加载配置后，初始化
let _pxServ = null;

var pxConfigSql = `
    select
        *
    from sys_dict_data
    where dict_type = ? and dict_label like "hw_%";
`;
var codeSql = `
    SELECT
        code
    FROM a_item
    where collector_id = ?
`;

var dbUpdateSql = `
    SELECT
        i.code,
        i.name,
        d.id as item_data_id,
        d.name as item_data_name,
        d.val as item_data_val,
        ifnull(d.max_val, '') max_val,
        ifnull(d.min_val, '') min_val,
        p.func_list,
        p.func
    FROM a_collector c
    LEFT JOIN a_item i on c.id = i.collector_id
    LEFT JOIN a_item_data d on i.id = d.item_id
    LEFT JOIN a_point_table p on d.id = p.item_data_id
    where c.id = ?
`;

// 全局变量
var gtmp = {};
var codeList = [];
var dbDataList = [];

// 读取配置，根据 buildingId 获取对应配置
async function readApiConfig() {
    if (gtmp.readApiConfig) {
        // 任务未执行完成, 等待下次轮训
        return false;
    }
    // 初始化服务
    gtmp.readApiConfig = true;
    try {
        let list = await _db.doSql(pxConfigSql, ["building_config_" + buildingId]);
        if (list.length > 0) {
            for (let i = 0; i < list.length; i++) {
                let conf = list[i];
                if (conf.dict_label == "hw_server") {
                    config.server = conf.dict_value;
                }
                if (conf.dict_label == "hw_username") {
                    config.username = conf.dict_value;
                }
                if (conf.dict_label == "hw_password") {
                    config.password = conf.dict_value;
                }
                if (conf.dict_label == "hw_instanceId") {
                    config.instanceId = conf.dict_value;
                }
                if (conf.dict_label == "hw_collectorId") {
                    config.collectorId = conf.dict_value;
                }
            }
        }
        helper.debug("read db config", config);
        // 实例化接口服务
        _pxServ = new renjiApi.RjApi(config);

        await _pxServ.getToken();
    } catch (e) {
        console.trace(e);
        helper.info("readApiConfig error", e.message);
        gtmp.readApiConfig = false;
        process.exit(1);
    }
    gtmp.readApiConfig = false;
}
async function getData() {
    codeList = await _db.doSql(codeSql, [config.collectorId]);
    dbDataList = await _db.doSql(dbUpdateSql, [config.collectorId]);
}

async function syncDeviceData() {
    if (gtmp.syncDeviceData) {
        // 任务未执行完成, 等待下次轮训
        return false;
    }
    // 初始化服务
    gtmp.syncDeviceData = true;
    try {
        if (codeList.length > 0) {
            for (let i = 0; i < codeList.length; i++) {
                try {
                    // 调用 api 获取数据
                    let apiDataRes = await _pxServ.getDeviceData(codeList[i].code);
                    let services = (apiDataRes.services && apiDataRes.services[0] && apiDataRes.services[0].properties) || {};
                    let metadata = (apiDataRes.metadata && apiDataRes.metadata[0] && apiDataRes.metadata[0].properties) || {};
                    if (dbDataList.length) {
                        for (let j = 0; j < dbDataList.length; j++) {
                            let cp = dbDataList[j];
                            if (codeList[i].code == cp.code) {
                                if (services.hasOwnProperty(cp.func)) {
                                    let val = services[cp.func];
                                    let updateTime = (metadata[cp.func] && moment(metadata[cp.func]["timestamp"]).format("YYYY-MM-DD HH:mm:ss")) || "";
                                    // 更新数据点位
                                    helper.debug("[sync to db]", codeList[i].code, cp.item_data_id, val);
                                    // 统一数据入库
                                    await helper.syncItemData2Db({
                                        id: cp.item_data_id,
                                        val: val,
                                        updatedAt: updateTime || moment().format("YYYY-MM-DD HH:mm:ss"),
                                    });
                                } else {
                                    helper.debug("no.........", codeList[i].code, cp.func, services);
                                }
                            }
                        }
                    }
                } catch (e) {
                    helper.info("error:", e.message);
                }
            }
        }
        //}
    } catch (e) {
        console.trace(e);
        helper.info("syncDeviceData error", e.message);
        gtmp.syncDeviceData = false;
    }
    gtmp.syncDeviceData = false;
}
async function start() {
    // 1分钟执行一次    30 * * * * *
    schedule.scheduleJob("1 */1 * * * *", () => {
        syncDeviceData();
        helper.log("syncDeviceData success");
    });
    // 半小时执行一次
    schedule.scheduleJob("1 */30 * * * *", () => {
        getData();
        helper.log("generaterEnergyData success");
    });
    // 1小时执行一次
    schedule.scheduleJob("1 1 * * * *", () => {
        helper.log("Auto restart server");
        process.exit(1);
    });

    // 数据服务初始化
    helper.initDataServer(sysConfig);
    helper.info("initDataServer success");

    // 日志服务连接至远程logstash
    helper.connectLogstash(sysConfig);
    helper.info("connectLogstash success");

    await readApiConfig();
    await getData();
    await syncDeviceData();
    helper.info("syncDeviceData success");
    helper.info("start success");
}

start();
