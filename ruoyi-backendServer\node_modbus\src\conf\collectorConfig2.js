
function collectorConfig() {
    return [
        {
            ind: 0,
            // lp_ry_jx nbiot 电表
            collectorId: 201,
            requestUrl: "http://**************:8003/collect",
            pointTable: {
                "type": "ammeter",
                "name": "dlt645 电表",
                "fileName": "DLT 645-2007-多功能通讯协议.pdf",
                "module": "dlt645",
                "dName": "dlt645",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "customer": {
                    "formatter": "formatter"
                },
                "commands": [
                    {
                        "options": {
                            "name": "正向读数",
                            "code": "00010000"
                        },
                        "points":[
                            {"id": "正向读数", "dotPosition": 2 }
                        ]
                    },
                    {
                        "options": {
                            "name": "反向读数",
                            "code": "00020000"
                        },
                        "points":[
                            {"id": "反向读数", "dotPosition": 2 }
                        ]
                    }
                ]
            }
        },
        {
            ind: 1,
            host: "**************",
            port: 502,
            collectorId: 2,
        },
        {
            ind: 2,
            host: "**************",
            port: 502,
            collectorId: 3,
            timeout: 5000,
        },
        {
            ind: 3,
            host: "127.0.0.1",
            port: 502,
            collectorId: 4,
            timeout: 5000,
        },
        {
            ind: 4,
            "collectorId": 60,
            "host": "mqtt://emqtt.sunallies.com",
            "username": "test",
            "password": "test",
            "prefix": "nMFouzeYceCr1KBxC",
            "from": "subscribes",
            "_from": "topic 订阅对应设备来源, 从配置的 subscribes 获取",
            "subscribes": [
                // 水泵
                {
                    "dataId": 3001,
                    "uri": "/1s/YX",
                },
                {
                    "dataId": 3002,
                    "uri": "/2s/YX",
                },
                {
                    "dataId": 3003,
                    "uri": "/3s/YX",
                },
                {
                    "dataId": 3004,
                    "uri": "/4s/YX",
                },
                {
                    "dataId": 3005,
                    "uri": "/5s/YX",
                },
                {
                    "dataId": 3006,
                    "uri": "/6s/YX",
                },
                // 风机
                {
                    "dataId": 3101,
                    "uri": "/1/YX",
                },
                {
                    "dataId": 3102,
                    "uri": "/1/GZ",
                },
                {
                    "dataId": 3103,
                    "uri": "/1/SZD",
                },

                {
                    "dataId": 3151,
                    "uri": "/2/YX",
                },
                {
                    "dataId": 3152,
                    "uri": "/2/GZ",
                },
                {
                    "dataId": 3153,
                    "uri": "/2/SZD",
                },

                {
                    "dataId": 3201,
                    "uri": "/3/YX",
                },
                {
                    "dataId": 3202,
                    "uri": "/3/GZ",
                },
                {
                    "dataId": 3203,
                    "uri": "/3/SZD",
                },

                {
                    "dataId": 3251,
                    "uri": "/4/YX",
                },
                {
                    "dataId": 3252,
                    "uri": "/4/GZ",
                },
                {
                    "dataId": 3253,
                    "uri": "/4/SZD",
                },
                {
                    "dataId": 3254,
                    "uri": "/4/awd", // A温度
                },
                {
                    "dataId": 3255,
                    "uri": "/4/bwd",
                },
                {
                    "dataId": 3256,
                    "uri": "/4/cwd",
                },
                {
                    "dataId": 3257,
                    "uri": "/4/nwd",
                },
                {
                    "dataId": 3258,
                    "uri": "/4/adl", // A电流
                },
                {
                    "dataId": 3259,
                    "uri": "/4/bdl",
                },
                {
                    "dataId": 3260,
                    "uri": "/4/cdl",
                },
                {
                    "dataId": 3261,
                    "uri": "/4/ady", // A电压
                },
                {
                    "dataId": 3262,
                    "uri": "/4/bdy",
                },
                {
                    "dataId": 3263,
                    "uri": "/4/cdy",
                },
                {
                    "dataId": 3264,
                    "uri": "/4/lddl", // 漏电电流
                },
                {
                    "dataId": 3265,
                    "uri": "/4/dldybj", // 电流电压报警
                },
                {
                    "dataId": 3266,
                    "uri": "/4/ldwdbj", // 漏电温度报警
                },
                {
                    "dataId": 3267,
                    "uri": "/4/ayg", // A相有功功率
                },
                {
                    "dataId": 3268,
                    "uri": "/4/byg",
                },
                {
                    "dataId": 3269,
                    "uri": "/4/cyg",
                },
                {
                    "dataId": 3270,
                    "uri": "/4/awg", // A相无功功率
                },
                {
                    "dataId": 3271,
                    "uri": "/4/bwg",
                },
                {
                    "dataId": 3272,
                    "uri": "/4/cwg",
                },
                {
                    "dataId": 3273,
                    "uri": "/4/agl", // A相功率因素
                },
                {
                    "dataId": 3274,
                    "uri": "/4/bgl",
                },
                {
                    "dataId": 3275,
                    "uri": "/4/cgl",
                },
                {
                    "dataId": 3276,
                    "uri": "/4/hgl", // 合相功率因数
                },
                {
                    "dataId": 3277,  // 合相电能
                    "uri": "/4/hxdn",
                },
                {
                    "dataId": 3278,  // 电流平衡
                    "uri": "/4/dlph",
                },

                {
                    "dataId": 3301,
                    "uri": "/5/YX",
                },
                {
                    "dataId": 3302,
                    "uri": "/5/GZ",
                },
                {
                    "dataId": 3303,
                    "uri": "/5/SZD",
                },

                {
                    "dataId": 3351,
                    "uri": "/6/YX",
                },
                {
                    "dataId": 3352,
                    "uri": "/6/GZ",
                },
                {
                    "dataId": 3353,
                    "uri": "/6/SZD",
                },

                {
                    "dataId": 3401,
                    "uri": "/7/YX",
                },
                {
                    "dataId": 3402,
                    "uri": "/7/GZ",
                },
                {
                    "dataId": 3403,
                    "uri": "/7/SZD",
                },

                {
                    "dataId": 3451,
                    "uri": "/8/YX",
                },
                {
                    "dataId": 3452,
                    "uri": "/8/GZ",
                },
                {
                    "dataId": 3453,
                    "uri": "/8/SZD",
                }
            ]
        },
        {
            ind: 5,
            // lp_ry_yh 兴业，农行 电表 485 tcp 透传
            host: "0.0.0.0",
            port: 5002,
            collectorId: 101,
            timeout: 5000,
        },
        {
            ind: 6,
            // lp_ry_yh 兴业/农业银行 485 4G透传 电表
            collectorId: 201,
            name: "兴业/农业银行4854G透传电表",
            requestUrl: "http://**************:8003/collect",
            pointTable: {
                "type": "ammeter",
                "name": "dlt645 电表",
                "fileName": "DLT 645-2007-多功能通讯协议.pdf",
                "module": "dlt645",
                "dName": "dlt645",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "customer": {
                    "formatter": "formatter"
                },
                "commands": [
                    {
                        "options": {
                            "name": "正向读数",
                            "code": "00010000"
                        },
                        "points":[
                            {"id": "正向读数", "dotPosition": 2 }
                        ]
                    },
                    {
                        "options": {
                            "name": "反向读数",
                            "code": "00020000"
                        },
                        "points":[
                            {"id": "反向读数", "dotPosition": 2 }
                        ]
                    }
                ]
            }
        },
        {
            ind: 7,
            "collectorId": 50,
            "host": "mqtt://emqtt.sunallies.com",
            "username": "test",
            "password": "test",
            "prefix": "UP/a13O9YxeEcu/",
            "from": "db",
            "_from": "topic 订阅对应设备来源, 读取 a_item.code 字段, , lp_ry_demo, lp_ry_jstsg",
            // "subscribes": [
            //     // 电表
            //     {
            //         "itemCode": "864424043300836",
            //         "uri": "864424043300836",
            //     },
            //     {
            //         "itemCode": "865771056541337",
            //         "uri": "865771056541337",
            //     },
            // ]
        },
        {
            ind: 8,
            "collectorId": 301,
            "host": "mqtt://emqtt.sunallies.com",
            "username": "test",
            "password": "test",
            "prefix": "llcomm/data/",
            "from": "db",
            "_from": "topic 订阅对应设备来源, 读取 a_item.code 字段",
            // "subscribes": [
            //     // 水表 fefe68010000026846259301
            //     {
            //         "itemCode": "fefe68010000026846259301",
            //         "uri": "/data/fefe68010000026846259301",
            //     },
            //     {
            //         "itemCode": "fefe68010000026846259401",
            //         "uri": "/data/fefe68010000026846259401",
            //     },
            // ]

        },
        {
            ind: 9,
            "collectorId": 51,
            "host": "mqtt://emqtt.sunallies.com",
            "username": "test",
            "password": "test",
            "prefix": "UP/a103H5IqMXM/",
            "from": "db",
            "_from": "topic 订阅对应设备来源, 读取 a_item.code 字段, lp_ry_jstsg",
        },
        // 新 mqtt 水表
        {
            ind: 10,
            "collectorId": 52,
            "host": "mqtt://emqtt.sunallies.com",
            "username": "test",
            "password": "test",
            "prefix": "UP/a105H5IqMXs/",
            "from": "db",
            "_from": "topic 订阅对应设备来源, 读取 a_item.code 字段, lp_ry_jstsg",
        },

        // 泰伦 techcon modbus 数采 线路温度
        {
            ind: 11,
            host: "**************",
            port: 502,
            timeout: 3000,
            collectorId: 1001,
        },
        // 泰伦 有人透传网关 modbus 数采 (电表)
        {
            ind: 12,
            host: "**************",
            port: 8765,
            timeout: 3000,
            collectorId: 1101,
        },
        // 泰伦 有人透传网关 modbus 数采 (空调)
        {
            ind: 13,
            //host: "**************",
            //port: 8765,
            host: "127.0.0.1",
            port: 8002,
            timeout: 3000,
            interval: 10000,
            collectorId: 1201,
        },

        // 服务端 modbus tcp 主动采集， 测试 modbus
        // 泰伦变配电
        {
            "ind": 14,
            "collectorId": 202,
            "requestUrl": "http://**************:8003/collect",
            // "requestUrl": "http://0.0.0.0:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "变配电",
                "fileName": "泰伦/变配电电表.xlsx",
                "module": "modbus",
                "dName": "modbus",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "commands": []
            }
        },
        // 服务端 modbus tcp 主动采集， 测试 dlt645
        {
            "ind": 15,
            "collectorId": 203,
            "name": "泰伦dlt645电表-203",
            "requestUrl": "http://**************:8003/collect",
            // "requestUrl": "http://0.0.0.0:8003/collect",
            "pointTable": {
                "type": "ammeter",
                "name": "电表",
                "fileName": "XXXX",
                "module": "dlt645",
                "dName": "dlt645",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "commands": []
            }
        },

        // tcp 客户端 主动同步，养老院 剑鱼文档
        {
            "ind": 16,
            "timeout": 3000,
            "httpPort": 8003,
            "syncServer": {
                // host: "************",
                // port: 6000,
                host: "0.0.0.0",
                port: 6000,
            },
            "syncServer2": {
                // host: "************",
                // port: 6000,
                host: "0.0.0.0",
                port: 6000,
            },
            "listenServer": {
                host: "***********",
                port: 32008,
            },
        },

        // modbus rtu 模式，泰伦变配电
        {
            "ind": 17,
            com: "COM3",
            baudRate: 9600,
            timeout: 3000,
            collectorId: 601,
        },

        // 泰伦 视频在离线检查
        {
            "ind": 18,
            collectorId: 101,
            timeout: 10000,
        },

        // 泰伦 充电桩 储能车 光伏车棚等
        {
            "ind": 19,
            "collectorId": 70,
            "from": "db",
            // 原点点mqtt
            // "host": "mqtt://**************",
            // username: "public",
            // password: "shz76hg%rsdq#ws",
            // topic: "/lanxinghztl",
            // 新兰星mqtt
            host: "mqtt://**************",
            username: "lanxing",
            password: "naudcno9kaqlli",
            topic: "/lanxinghztl",
        },

        // 嘉兴 ems4 数据导入 bms
        {
            "ind": 20,
            "dbFrom": {
                "ll.ems4": {
                    "name": "ll.ems4",
                    "host": "rm-uf67xbt5r3r17jddto.mysql.rds.aliyuncs.com",
                    "database": "ems4",
                    "username": "llproj",
                    "password": "asjfy3j7Y@62o@hksowi",
                }
            },
            "dbTo": {
                "ll.lp_ry_jx": {
                    "name": "ll.lp_ry_jx",
                    "host": "rm-uf67xbt5r3r17jddto.mysql.rds.aliyuncs.com",
                    "database": "lp_ry_jx",
                    "username": "llproj",
                    "password": "asjfy3j7Y@62o@hksowi",
                }
            },
        },
        {
            "ind": 21,
            "dbFrom": {
                "hy": {  // 海盐
                    "name": "hy",
                    "host": "***************",
                    "port": 3306,
                    "database": "ems4",
                    "username": "root",
                    "password": "root123"
                },
                "jx": {
                    "name": "jx",
                    "host": "***************",
                    "port": 3306,
                    "database": "ems4",
                    "username": "root",
                    "password": "root123"
                },
                "js": {  // 嘉善
                    "name": "js",
                    "host": "**************",
                    "port": 3306,
                    "database": "ems4",
                    "username": "root",
                    "password": "root123"
                },
                "rt": {  // 融通
                    "name": "rt",
                    "host": "**************",
                    "port": 3306,
                    "database": "ems4",
                    "username": "root",
                    "password": "root1234"
                },
                "xz": {  // 秀洲
                    "name": "xz",
                    "host": "**************",
                    "port": 3306,
                    "database": "ems4",
                    "username": "root",
                    "password": "root123"
                },
                // bh  滨海
                "bh": {
                    "name": "bh",
                    "host": "***************",
                    "port": 3306,
                    "database": "ems4",
                    "username": "root",
                    "password": "root123"
                },
            },
            "dbTo": {
                "ll.lp_ry_jx": {
                    "name": "ll.lp_ry_jx",
                    "host": "rm-uf67xbt5r3r17jddto.mysql.rds.aliyuncs.com",
                    "database": "lp_ry_jx",
                    "username": "llproj",
                    "password": "asjfy3j7Y@62o@hksowi",
                }
            },
        },
        // 余姚电力局 电表
        {
            "ind": 22,
            "name": "余姚电力电表yydl001",
            "collectorId": 301,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            // "requestUrl": "http://0.0.0.0:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485电表",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "commands": []
            }
        },
        // 余姚电力局 空调读
        {
            "ind": 23,
            "name": "余姚电力空调yydl002",
            "collectorId": 302,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            // "requestUrl": "http://0.0.0.0:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485空调",
                "fileName": "",
                "module": "modbus_airCondition",
                "dName": "modbus",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "commands": []
            }
        },
        // modbus rtu 模式，嘉兴空调度
        {
            "ind": 24,
            com: "COM4",
            baudRate: 9600,
            timeout: 3000,
            collectorId: 601,
        },
        // 余姚舜能 空调 yydl003
        {
            "ind": 25,
            "name": "余姚舜能空调yydl003",
            "collectorId": 301,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            // "requestUrl": "http://0.0.0.0:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485空调",
                "fileName": "",
                "module": "modbus_airCondition",
                "dName": "modbus",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "commands": []
            }
        },
        // 余姚舜能 空调 sndl001
        {
            "ind": 26,
            "name": "余姚舜能空调sndl001",
            "collectorId": 302,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            // "requestUrl": "http://0.0.0.0:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485空调",
                "fileName": "",
                "module": "modbus_airCondition",
                "dName": "modbus",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "commands": []
            }
        },
        // 杭州江晖地铁 消防 hzjh001
        {
            "ind": 27,
            "name": "杭州江晖地铁消防hzjh001",
            "collectorId": 81,
        },

        // 上海国际会议中心
        {
            "ind": 28,
            "name": "上海国际会议中心mqtt_topic_ibms_shgh",
            "collectorId": 1001,
            "host": "mqtt://**************",
            "username": "lanxing",
            "password": "naudcno9kaqlli",
            "prefix": "ibms_shgh",
            "from": "db_onetopic",
            "_from": "topic 订阅对应设备来源, 从配置的 subscribes 获取",
        },

        // 有人透传 modbus 模式，宁波能源大楼空调
        {
            "ind": 29,
            "name": "宁波能源大楼空调nbc29n",
            collectorId: 601,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485空调",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "commands": []
            }
        },
        // 有人透传 modbus 模式，宁波能源大楼电表
        {
            "ind": 30,
            "name": "宁波能源大楼空调nbc30n",
            collectorId: 602,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485电表",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "commands": []
            }
        },
        // 监听 mqtt 模式，国际会议中心照明，参考： 上海国际会议中心/照明信息-mqtt调试.md
        // 执行 src/mqttClientZM.js
        {
            "ind": 31,
            "name": "上海国际会议中心mqtt_topic_ibms_shgh_zm",
            "collectorId": 2001,
            "collectorCode": "gs08291110",
            "host": "mqtt://**************",
            "username": "lanxing",
            "password": "naudcno9kaqlli",
            "prefix": "ibms_shgh_zm",
            "from": "db",
            "_from": "topic 订阅对应设备来源, 从配置的 subscribes 获取",
        },

        // 嘉兴学院 有人透传网关 modbus 数采 (空调)
        {
            ind: 32,
            host: "***********",
            port: 5678,
            timeout: 3000,
            interval: 10000,
            collectorId: 1001,
        },
        // 嘉兴学院 有人透传网关 modbus 数采 (照明)
        {
            ind: 33,
            host: "***********",
            port: 5678,
            timeout: 3000,
            interval: 10000,
            collectorId: 1002,
        },
        // 嘉兴学院 obix网关 空调数采
        {
            ind: 34,
            "name": "嘉兴学院空调机组obix_3001",
            collectorId: 3001,
            server: "http://************:80/obix/config/Drivers/NiagaraNetwork",
            username: "obixuser",
            password: "Obix123456",
            timeout: 5000,
        },
        // 温州乐清 有人透传网关 modbus 数采 (照明)
        {
            ind: 35,
            "name": "温州乐清照明_5001",
            collectorId: 5001,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485照明",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        // 温州乐清 有人透传网关 modbus 数采 (水表)
        {
            ind: 36,
            "name": "温州乐清照明_6001",
            collectorId: 6001,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485水表",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        // 温州乐清 有人透传网关 modbus 数采 (电表)
        {
            ind: 37,
            "name": "温州乐清电表_7001",
            collectorId: 7001,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485电表",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        // 温州乐清 有人透传网关 modbus 数采 (水表)
        {
            ind: 38,
            "name": "温州乐清空调_8001",
            collectorId: 8001,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485空调",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        {
            ind: 39,
            // 金华新能源小镇 有人透传网关 modbus 数采 (空调1)
            "name": "金华新能源小镇空调_10101",
            collectorId: 10101,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485modbus",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        {
            ind: 40,
            // 金华新能源小镇 有人透传网关 modbus 数采 (空调2)
            "name": "金华新能源小镇空调_10102",
            collectorId: 10102,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485modbus",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        {
            ind: 41,
            // 金华新能源小镇 有人透传网关 modbus 数采 (照明)
            "name": "金华新能源小镇照明_10103",
            collectorId: 10103,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485modbus",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        {
            ind: 42,
            // 金华新能源小镇 有人透传网关 modbus 数采 (压力)
            "name": "金华新能源小镇压力_10104",
            collectorId: 10104,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485modbus",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        {
            ind: 43,
            // 金华新能源小镇 有人透传网关 modbus 数采 (红外)
            "name": "金华新能源小镇红外_10105",
            collectorId: 10105,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485modbus",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        {
            ind: 44,
            // 金华新能源小镇 有人透传网关 modbus 数采 (一氧化碳)
            "name": "金华新能源小镇一氧化碳_10106",
            collectorId: 10106,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485modbus",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        {
            ind: 45,
            // 金华新能源小镇 有人透传网关 modbus 数采 (水流量)
            "name": "金华新能源小镇水流量_10107",
            collectorId: 10107,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485modbus",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        {
            ind: 46,
            // 金华新能源小镇 有人透传网关 modbus 数采 (超声波流量)
            "name": "金华新能源小镇超声波流量_10108",
            collectorId: 10108,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485modbus",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        {
            ind: 47,
            // 金华新能源小镇 有人透传网关 modbus 数采 (液位开关)
            "name": "金华新能源小镇液位开关_10109",
            collectorId: 10109,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485modbus",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        {
            ind: 48,
            // 金华新能源小镇 有人透传网关 modbus 数采 (电表)
            "name": "金华新能源小镇电表_1001",
            collectorId: 1001,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485modbus",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        {
            ind: 49,
            // 温州文成 有人透传网关 modbus 数采 (空调)
            "name": "温州文成空调_11001",
            collectorId: 11001,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485modbus",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        {
            // 温州文成 空开自带网关 (智能空开)
            ind: 50,
            "collectorId": 12001,
            "host": "mqtt://emqtt.lanxing.tech",
            "username": "lanxing",
            "password": "lanxing",
            "prefix": "UP/wzwc/",
            "from": "db",
            "_from": "topic 订阅对应设备来源, 读取 a_item.code 字段, lp_ry_jstsg",
        },
        // 杭汽轮 水表 CJ/T188 协议
        {
            "ind": 51,
            "collectorId": 203,
            "name": "下发指令",
            "requestUrl": "http://**************:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "水表",
                "fileName": "bms/杭汽轮/Joymeter后付费水表CJ188协议通信规范 - v1.7.pdf",
                "module": "cjt188",
                "dName": "cjt188",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "commands": []
            }
        },
        // 金华新能源 路灯 厂家自定义 协议
        {
            "ind": 52,
            "collectorId": 61,
            "name": "金华新能源路灯_61",
            "requestUrl": "http://**************:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "lamp",
                "fileName": "bms/金华新能源小镇/路灯/485通讯智能照明协议.doc",
                "module": "jhxzLamp",
                "dName": "jhxzLamp",
                "commands": []
            }
        },
        // 杭州江辉-虚拟点，write/read 同步
        {
            "ind": 53,
            "collectorId": 100,
            "name": "杭州江辉-虚拟点_100",
        },
        // 合肥电信大楼 645电表
        {
            "ind": 54,
            "collectorId": 1111,
            "name": "合肥电信大楼_1111_645电表",
            "requestUrl": "http://106.15.0.215:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "电表",
                "fileName": "bms/金华新能源小镇/路灯/485通讯智能照明协议.doc",
                "module": "dlt645",
                "dName": "dlt645",
                "commands": []
            }
        },
        // 合肥电信大楼 645电表
        {
            "ind": 55,
            "collectorId": 2222,
            "name": "合肥电信大楼_2222_485空调",
            "requestUrl": "http://106.15.0.215:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "空调",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
             }
        },
        // 安徽怀远 门禁db同步
        {
            "ind": 56,
            "collectorId": 0,
            "name": "安徽怀远_0_门禁db同步",
            "mssql": {
                "server": "192.168.20.2",
                "port": 1433,
                "database": "menjin",
                "username": "sa",
                "password": "123456",
                "encrypt": false,
             }
        },
        // 安徽怀远 电子巡更db同步
        {
            "ind": 57,
            "collectorId": 1,
            "name": "安徽怀远_1_电子巡更db同步",
            "mssql": {
                "server": "**************",
                "port": 1433,
                "database": "BSPatrol",
                "username": "sa",
                "password": "admin123",
                "encrypt": false,
             }
        },
        // 武汉党校 用户数据db同步
        {
            "ind": 58,
            "collectorId": 1,
            "name": "武汉党校_1_用户数据db同步",
            "mysql": {
                // "host": "**********",
                // "port": 3306,
                // "database": "user_interface",
                // "username": "BA_INTER",
                // "password": "BA@%2023",
                // "timezone": "08:00",

                "host": "rm-uf625r51qfcrxlt36fo.mysql.rds.aliyuncs.com",
                "port": 3306,
                "database": "ly_wh_dx",
                "username": "llproj",
                "password": "hph4iGR7bLdpm",
                "timezone": "08:00",
             }
        },
        // 武汉党校 日志db同步
        {
            "ind": 59,
            "collectorId": 1,
            "name": "武汉党校_1_日志db同步",
            "url": "http://**********:9998/logService/api/log/add",
            "appId": 'BAXT',
            "appKey": "240326180952deFSRQn9xCPHVuuxo6m",
            "serverIp": "*************",
        },
        // 景德镇鱼山码头 道巴斯 BA、灯控通讯
        {
            "ind": 60,
            "collectorId": 35,
            "name": "景德镇鱼山码头_35_BA灯控通讯",
            "server": "http://***********/soap",
        },
        // 浙江玉环人民医院  皓骐照明Can转Tcp通讯
        {
            "ind": 61,
            "collectorId": 36,
            "name": "浙江玉环人民医院_36_皓骐照明灯控通讯",
            "requestUrl": "http://localhost:8003/collect",
            "sleepTime": 800,
            "pointTable": {
                "type": "canToTcp",
                "name": "照明",
                "module": "haoqiCanToTcp",
                "dName": "light",
                "commands": []
            }
        },
        // 临时演示 空调view001
        {
            "ind": 62,
            "name": "临时演示空调view001",
            "collectorId": 301,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485空调",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "commands": []
            }
        },
        // 武汉电建百花谷  皓骐照明 智途网关
        {
            "ind": 63,
            "collectorId": 37,
            "name": "武汉电建百花谷_37_皓骐照明智途网关",
            "requestUrl": "http://localhost:8003/collect",
            "sleepTime": 800,
            "pointTable": {
                "type": "tcp",
                "name": "照明",
                "module": "haoqiCan",
                "dName": "light",
                "commands": []
            }
        },
        // 64，华住冷源群控
        {
            ind: 64,
            "collectorId": 3001,
            "host": "mqtt://***********",
            "username": "ykong",
            "password": "ykong",
            "prefix": "unicom/mqtt/v1/devices/yukon001/data",
            "writeTopic": "unicom/mqtt/v1/devices/yukon001/command",
            "from": "db_onetopic",
            // 配置需要计算cop的点的对应关系
            "keyMap": {
                "冷冻水出水温度": "10011901-AI1",
                "冷却水回水温度": "10011901-AI3",
                "温差计算": [
                    {
                        "weight": "1002101-P",
                        "highTemp": "1002101-tw_e_E",
                        "lowTemp": "1002101-tw_e_L",
                    },
                    {
                        "weight": "1002102-P",
                        "highTemp": "1002102-tw_e_E",
                        "lowTemp": "1002102-tw_e_L",
                    },
                    {
                        "weight": "1002103-P",
                        "highTemp": "1002103-tw_e_E",
                        "lowTemp": "1002103-tw_e_L",
                    }
                ],
                "冷冻水回水水流量": "10011901-AI5",
                "冷冻水回水水流累计流量": "10011901-AI5SUM",
                "累计电能": ["1002101-Ep", "1002102-Ep", "1002103-Ep", "10010701-Ep", "10010801-Ep", "10010901-Ep"],
                "总功率": ["1002101-P", "1002102-P", "1002103-P", "10010701-P", "10010801-P", "10010901-P"],
                "COP": "10011901-COP",
                "EER": "10011901-EER",
                "冷负荷": "10011901-冷负荷",
            },
            // 收到关键信息的标记
            "actKey": {
              "key": "id",
              "val": "0119001",
            },
            // 收到消息后多久开始执行计算逻辑
            "actSleep": 5000,
        },
        // 通用网关在线检查
        {
            ind: 65,
            collectorId: 7001,
            checkStr: ["视频播放","html"],
            timeout: 5000,
        },
        //苏州国际会议酒店
        {
            ind: 66,
            "collectorId": 3001,
            "host": "mqtt://***********",
            "username": "ykong",
            "password": "ykong",
            "prefix": "unicom/mqtt/v1/devices/szgjhyjd/data",
            "writeTopic": "unicom/mqtt/v1/devices/szgjhyjd/command",
            "from": "db_onetopic",
        },
        //上海建科院
        {
            ind: 67,
            "collectorId": 6,
            "host": "mqtt://***********",
            "username": "ykong",
            "password": "ykong",
            prefix: "unicom/mqtt/v1/devices/SHJKYuan/data",
            writeTopic: "unicom/mqtt/v1/devices/SHJKYuan/command",
            "from": "db_onetopic",
        },
        //正大天晴
        {
            ind: 68,
            "collectorId": 6,
            "host": "mqtt://***********",
            "username": "ykong",
            "password": "ykong",
            "prefix": "unicom/mqtt/v1/devices/sxzyzdtq/data",
            "writeTopic": "unicom/mqtt/v1/devices/sxzyzdtq/command",
            "from": "db_onetopic",
        },
        // 从数据源同步数据到本地
        {
            ind: 69,
            "collectorId": "9001,9002,9003",
            "dbFrom": {
                "host": "rm-uf625r51qfcrxlt36fo.mysql.rds.aliyuncs.com",
                "database": "ly_jx_xc",
                "username": "ly_jx_xc",
                "encodePwd": "51d7609e7b5abc50576a9847701865e1",
            },
        },
        //正大天晴 ly_nj_zdtq
        {
            ind: 70,
            "collectorId": 3001,
            "host": "mqtt://***********",
            "username": "ykong",
            "password": "ykong",
            "prefix": "unicom/mqtt/v1/devices/sxzyzdtq/data",
            "writeTopic": "unicom/mqtt/v1/devices/sxzyzdtq/command",
            "from": "db_onetopic",
        },
        // 天津大学
        {
            "ind": 71,
            "collectorId": 0,
            "name": "天津大学_0_门禁db同步",
            "mssql": {
                "server": "rm-uf6ch83ej1m9xrfx0fo.sqlserver.rds.aliyuncs.com",
                "port": 1433,
                "database": "zkdt",
                "username": "sa",
                "password": "lanxing121!",
                "encrypt": false,
             }
        },
        // 通用 tcp 转 mqtt 协议中间件 (天津大学)
        {
            "ind": 72,
            "name": "tcp2mqtt_server",
            "deviceList": [
                {
                    ip: "************",
                    port: 5768
                },
                {
                    ip: "127.0.0.1",
                    port: 1234
                }
            ]
        },
        // 天津大学 门禁数采 tcp 脚本
        {
            "ind": 73,
            "name": "天津大学_门禁数采_11",
            "collectorId": 11,
            "requestUrl": "http://localhost:8003/collect",
            "pointTable": {
                "type": "accessControl",
                "name": "pk388门禁",
                "fileName": "Pk388&398&P2000&P4000通讯协议.doc",
                "module": "pk388",
                "dName": "pk388",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "commands": []
            }
        },
        // 奉贤实验室 传感器数采 rtu 2 tcp 脚本 温湿度+压力 8002
        {
            "ind": 74,
            "name": "奉贤实验室_modbus传感器数采_111",
            "collectorId": 111,
            "requestUrl": "http://localhost:8012/collect",
            "pointTable": {
                "type": "sensor",
                "name": "sensor",
                "fileName": "温湿度--模拟量输入系列使用手册(RS485版).pdf",
                "module": "modbus_base",
                "dName": "modbus",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "commands": []
            }
        },
        // 奉贤实验室 传感器数采 rtu 2 tcp 脚本 CO2 8005
        {
            "ind": 75,
            "name": "奉贤实验室_modbus传感器数采_112",
            "collectorId": 112,
            "requestUrl": "http://localhost:8015/collect",
            "pointTable": {
                "type": "sensor",
                "name": "sensor",
                "fileName": "温湿度--模拟量输入系列使用手册(RS485版).pdf",
                "module": "modbus_base",
                "dName": "modbus",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "commands": []
            }
        },
        // 奉贤实验室 传感器数采 tcp 2 mqtt 脚本 冰箱温湿度+开关门
        {
            "ind": 76,
            "name": "奉贤实验室_蓝牙传感器数采_tcp2mqtt",
            "collectorId": 113,
            "host": "mqtt://localhost",
            "username": "lanxing",
            "password": "lanxing",
            "topic": "ly_ibms/data/#",
            "from": "db",
        },
        // 金华浦江行政服务中心 多联机
        {
            "ind": 77,
            "name": "金华浦江行政服务中心多联机jhpj001",
            "collectorId": 101,
            "requestUrl": "http://bms.lanxing.tech:9014/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485多联机",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                //"address": "202101112033", // 非必填，对应 a_item.code
                "commands": []
            }
        },
        // 上海现代院 wifi udp 消息监听
        {
            "ind": 78,
            "name": "上海现代院ApWifiMqttListener",
            "collectorId": 4,
            "host": "mqtt://**************",
            "port": 1883,
            "username": "test",
            "password": "test",
            "prefix": "yk_ibms/udp/#",
            "from": "db_onetopic",
            "func": (data) => {
                return JSON.stringify({
                    id: data.slice(19,25).toString("hex"),
                    from: data.slice(0,8).toString("utf8"),
                    time: data.slice(10,18).toString("hex"),
                    mac: data.slice(18,24).toString("hex"),
                    status: data.slice(24,25).toString("hex"),
                })
            },
        },
        // 嘉兴学院 modbus 电表
        {
            ind: 79,
            "name": "嘉兴学院电表_44",
            collectorId: 44,
            "requestUrl": "http://bms.lanxing.tech:8003/collect",
            "pointTable": {
                "type": "modbus",
                "name": "485电表",
                "fileName": "",
                "module": "modbus_base",
                "dName": "modbus",
                "commands": []
            }
        },
        // 浦江行政中心
        {
            ind: 80,
            "collectorId": 11,
            "host": "mqtt://**************",
            "username": "lanxing",
            "password": "lanxing",
            "from": "db",
            "prefix": "/sys/JHPJ/",  // 基础过滤用
            "stringCode": "utf8",   // 数据编码
            "readTpick": (code) => {
                return "/sys/JHPJ/" + code + "/thing/event/property/post";
            } ,
            "writeTopic": (code) => {
                return "/sys/JHPJ/" + code + "/thing/service/property/set";
            },
            "func": (topic, data) => {
                let s = topic.split("/");
                data = JSON.parse(data);
                data.itemCode = s[3];
                return data;
            },  // 前置函数
        },
    ];
};

module.exports.collectorConfig = collectorConfig;
