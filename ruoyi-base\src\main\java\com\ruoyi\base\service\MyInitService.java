package com.ruoyi.base.service;

import com.ruoyi.base.mapper.DbMigrateMapper;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DictUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class MyInitService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private DbMigrateMapper dbMigrateMapper;

    @Value("${server.port}")
    private int port;

    @Value("${spring.datasource.druid.master.db}")
    private String db;

    @PostConstruct
    public void run() {
        logger.info("-------- clean redis cache --------");
        // 清除定时任务的缓存
        String key1 = "updateDeviceStatus_1";
        String key2 = "updateDeviceStatus_5";
        String key3 = "updateDeviceStatus_60";
        String key4 = "__ssoForYYDL";
        String key5 = "__ssoTokenForYYDL";
        redisCache.deleteObject(key1);
        redisCache.deleteObject(key2);
        redisCache.deleteObject(key3);
        redisCache.deleteObject(key4);
        redisCache.deleteObject(key5);
        // iot 缓存
        String key6 = "collectServer";
        String key7 = "itemData";
        String key8 = "itemWarning";
        redisCache.deleteObject(key6);
        redisCache.deleteObject(key7);
        redisCache.deleteObject(key8);
        logger.info("redis 缓存清理成功. redisCache.deleteObject updateDeviceStatus_X");

        DictUtils.clearDictCache();
        logger.info("clearDictCache finished. DictUtils.clearDictCache");

        // 字典表移除重复记录，并添加唯一索引
        logger.info("-------- db update --------");
        try {
            dbMigrateMapper.removeDuplicateDictData();
            dbMigrateMapper.addDictDataUniqueIndex();
            logger.info("dbMigrateMapper.addDictDataUniqueIndex success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addDictDataUniqueIndex " + e.getCause());
        }

        // 数据库检查升级
        try {
            dbMigrateMapper.addEDeviceRemark();
            logger.info("dbMigrateMapper.addEDeviceRemark success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addEDeviceRemark " + e.getCause());
        }
        try {
            dbMigrateMapper.addEDeviceGroupId();
            logger.info("dbMigrateMapper.addEDeviceGroupId success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addEDeviceGroupId " + e.getCause());
        }
        try {
            dbMigrateMapper.addEDeviceMaxDiff();
            logger.info("dbMigrateMapper.addEDeviceMaxDiff success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addEDeviceMaxDiff " + e.getCause());
        }
        try {
            dbMigrateMapper.addEDeviceUnreal();
            logger.info("dbMigrateMapper.addEDeviceUnreal success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addEDeviceUnreal " + e.getCause());
        }

        // 数据库检查升级
        // 创建用户项目关系表 a_building_user_map
        try {
            dbMigrateMapper.genBuildingUserMap();
            dbMigrateMapper.addBuildingUserMapData();
            logger.info("dbMigrateMapper.genBuildingUserMap success");
            logger.info("dbMigrateMapper.addBuildingUserMapData success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.genEnergyDiffAvgHourly " + e.getCause());
        }

        // 补充 device 字段 ajustable_power
        try {
            dbMigrateMapper.addDeviceAjustablePower();
            logger.info("dbMigrateMapper.addDeviceAjustablePower success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addDeviceAjustablePower " + e.getCause());
        }

        // 设备数据历史 indication 长度增加
        try {
            dbMigrateMapper.updateDeviceDataHistoryIndication();
            logger.info("dbMigrateMapper.updateDeviceDataHistoryIndication success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.updateDeviceDataHistoryIndication " + e.getCause());
        }

        // 设备增加关联视频字段 camera_id
        try {
            dbMigrateMapper.addDeviceCameraId();
            logger.info("dbMigrateMapper.addDeviceCameraId success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addDeviceCameraId " + e.getCause());
        }
        try {
            dbMigrateMapper.addDeviceFiles();
            logger.info("dbMigrateMapper.addDeviceFiles success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addDeviceFiles " + e.getCause());
        }

        // 日志增加索引 d_device_oper_log
        try {
            dbMigrateMapper.addDeviceOperLogIndex();
            logger.info("dbMigrateMapper.addDeviceOperLogIndex success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addDeviceOperLogIndex " + e.getCause());
        }

        // 运维表，增加报警关联 warning_id
        try {
            dbMigrateMapper.addDeviceMaintenanceWarningId();
            logger.info("dbMigrateMapper.addDeviceMaintenanceWarningId success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addDeviceMaintenanceWarningId " + e.getCause());
        }

        // 能耗表增加 超标判断 max_diff
        try {
            dbMigrateMapper.addMaxDiffToDeviceEnergy();
            logger.info("dbMigrateMapper.addMaxDiffToDeviceEnergy success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addMaxDiffToDeviceEnergy " + e.getCause());
        }

        // 检查并创建 报修单表 d_repair_order
        try {
            dbMigrateMapper.checkAndCreateDeviceRepairOrder();
            logger.info("dbMigrateMapper.checkAndCreateDeviceRepairOrder success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.checkAndCreateDeviceRepairOrder " + e.getCause());
        }

        // 检查并创建 报修运维Map表 d_repair_maintenance_map
        try {
            dbMigrateMapper.checkAndCreateRepairMaintenanceMap();
            logger.info("dbMigrateMapper.checkAndCreateRepairMaintenanceMap success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.checkAndCreateRepairMaintenanceMap " + e.getCause());
        }

        // 检查并创建 报修评价表 d_evaluate
        try {
            dbMigrateMapper.checkAndCreateDeviceEvaluate();
            logger.info("dbMigrateMapper.checkAndCreateDeviceEvaluate success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.checkAndCreateDeviceEvaluate " + e.getCause());
        }

        // 创建 a_weather_data 表
        try {
            dbMigrateMapper.genWeatherData();
            logger.info("dbMigrateMapper.genWeatherData success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.genWeatherData " + e.getCause());
        }

        // 修改 a_weather_data city_id to weather_id 字段
        try {
            dbMigrateMapper.changeCityId();
            logger.info("dbMigrateMapper.changeCityId success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.changeCityId " + e.getCause());
        }

        // 修改 a_weather_data created_at 为 create_time 字段
        try {
            dbMigrateMapper.changeWeatherCreateTime();
            logger.info("dbMigrateMapper.changeWeatherCreateTime success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.changeWeatherCreateTime " + e.getCause());
        }

        // 修改 a_weather_data updated_at 为 update_time 字段
        try {
            dbMigrateMapper.changeWeatherUpdateTime();
            logger.info("dbMigrateMapper.changeWeatherUpdateTime success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.changeWeatherUpdateTime " + e.getCause());
        }

        // 修改 d_repair_maintenance_map repair_id，maintenance_id 唯一字段
        try {
            dbMigrateMapper.updateRepairMaintenanceMapUnique();
            logger.info("dbMigrateMapper.updateRepairMaintenanceMapUnique success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.updateRepairMaintenanceMapUnique " + e.getCause());
        }

        // 移除 e_warning.operator 必填属性
        try {
            dbMigrateMapper.updateEnergyWarningOperator();
            logger.info("dbMigrateMapper.updateEnergyWarningOperator success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.updateEnergyWarningOperator " + e.getCause());
        }
        try {
            dbMigrateMapper.genEenergyWarningRule();
            logger.info("dbMigrateMapper.genEenergyWarningRule success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.genEenergyWarningRule " + e.getCause());
        }

        // 增加日志长度
        try {
            dbMigrateMapper.updateSysOperLogLen();
            logger.info("dbMigrateMapper.updateSysOperLogLen success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.updateSysOperLogLen " + e.getCause());
        }

        // 增加资料库表
        try {
            dbMigrateMapper.addDocument();
            logger.info("dbMigrateMapper.addDocument success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addDocument " + e.getCause());
        }

        // 增加资料库分组表
        try {
            dbMigrateMapper.addDocumentGroup();
            dbMigrateMapper.addDocumentGroupData();
            logger.info("dbMigrateMapper.addDocumentGroup success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addDocumentGroup " + e.getCause());
        }

        // 增加巡检点位表
        try {
            dbMigrateMapper.addPiPoint();
            dbMigrateMapper.addDictTypeForPiPoint();
            dbMigrateMapper.addDictDataForPiPoint();
            logger.info("dbMigrateMapper.addPiPoint success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addPiPoint " + e.getCause());
        }

        // 增加巡检点路径表
        try {
            dbMigrateMapper.addPiRoute();
            logger.info("dbMigrateMapper.addPiRoute success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addPiRoute " + e.getCause());
        }

        // 增加巡检点路径关系表
        try {
            dbMigrateMapper.addPiRoutePointMap();
            logger.info("dbMigrateMapper.addPiRoutePointMap success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addPiRoutePointMap " + e.getCause());
        }

        // 增加巡检计划表
        try {
            dbMigrateMapper.addPiPlan();
            dbMigrateMapper.addDictTypeForPiPointCycleType();
            dbMigrateMapper.addDictDataForPiPointCycleType();
            dbMigrateMapper.addDictTypeForPiPointTaskDistributeType();
            dbMigrateMapper.addDictDataForPiPointTaskDistributeType();
            dbMigrateMapper.addPiPlan();
            logger.info("dbMigrateMapper.addPiPlan success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addPiPlan " + e.getCause());
        }

        // 增加巡检任务表
        try {
            dbMigrateMapper.addPiTask();
            dbMigrateMapper.addDictTypeForPiTaskStatus();
            dbMigrateMapper.addDictDataForPiTaskStatus();
            logger.info("dbMigrateMapper.addPiTask success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addPiTask pass" + e.getCause());
        }

        // 增加巡检任务详情表
        try {
            dbMigrateMapper.addPiTaskDetail();
            dbMigrateMapper.addDictTypeForCheckedStatus();
            dbMigrateMapper.addDictDataForCheckedStatus();
            logger.info("dbMigrateMapper.addPiTaskDetail success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addPiTaskDetail " + e.getCause());
        }

        // 增加排班值班表
        try {
            dbMigrateMapper.addPiShiftSchedule();
            dbMigrateMapper.addDictTypeForShiftScheduleType();
            dbMigrateMapper.addDictDataForShiftScheduleType();
            logger.info("dbMigrateMapper.addPiShiftSchedule success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addPiShiftSchedule " + e.getCause());
        }

        try {
            dbMigrateMapper.addAItemAlias();
            logger.info("dbMigrateMapper.addAItemAlias success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addAItemAlias " + e.getCause());
        }

        try {
            dbMigrateMapper.addEEnergyDataHistoryDiffFee();
            logger.info("dbMigrateMapper.addEEnergyDataHistoryDiffFee success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addEEnergyDataHistoryDiffFee " + e.getCause());
        }
        try {
            dbMigrateMapper.addEFeePolicyDate();
            logger.info("dbMigrateMapper.addEFeePolicyDate success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addEFeePolicyDate " + e.getCause());
        }

        try {
            dbMigrateMapper.addEEnergyDataHistoryDailyJFPG();
            logger.info("dbMigrateMapper.addEEnergyDataHistoryDailyJFPG success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addEEnergyDataHistoryDailyJFPG " + e.getCause());
        }

        try {
            dbMigrateMapper.addDeviceTaskGroupTag();
            logger.info("dbMigrateMapper.addDeviceTaskGroupTag success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addDeviceTaskGroupTag " + e.getCause());
        }

        try {
            dbMigrateMapper.addTplDevice();
            logger.info("dbMigrateMapper.addTplDevice success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addTplDevice " + e.getCause());
        }
        try {
            dbMigrateMapper.addTplDeviceData();
            logger.info("dbMigrateMapper.addTplDeviceData success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addTplDeviceData " + e.getCause());
        }
        try {
            dbMigrateMapper.addTplDeviceDataWarning();
            logger.info("dbMigrateMapper.addTplDeviceDataWarning success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addTplDeviceDataWarning " + e.getCause());
        }
        try {
            dbMigrateMapper.addDeviceShelfTime();
            logger.info("dbMigrateMapper.addDeviceShelfTime success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.addDeviceShelfTime " + e.getCause());
        }
        try {
            dbMigrateMapper.updateSysUserName();
            logger.info("dbMigrateMapper.updateSysUserName success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.updateSysUserName " + e.getCause());
        }
        try {
            dbMigrateMapper.updateDDeviceSourceId();
            logger.info("dbMigrateMapper. updateDDeviceSourceId success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. updateDDeviceSourceId " + e.getCause());
        }
        try {
            dbMigrateMapper.createDeviceMaintenanceConsumable();
            logger.info("dbMigrateMapper. createDeviceMaintenanceConsumable success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. createDeviceMaintenanceConsumable " + e.getCause());
        }
        try {
            dbMigrateMapper.createDeviceEnergySynchronized();
            logger.info("dbMigrateMapper. createDeviceEnergySynchronized success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. createDeviceEnergySynchronized " + e.getCause());
        }
        try {
            dbMigrateMapper.addDeviceMaintenancePlantId();
            logger.info("dbMigrateMapper. addDeviceMaintenancePlantId success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDeviceMaintenancePlantId " + e.getCause());
        }
        try {
            dbMigrateMapper.createDeviceMaintenancePlan();
            logger.info("dbMigrateMapper. createDeviceMaintenancePlan success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. createDeviceMaintenancePlan " + e.getCause());
        }
        try {
            dbMigrateMapper.addDDeviceDataHistoryInd();
            logger.info("dbMigrateMapper. addDDeviceDataHistoryInd success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDDeviceDataHistoryInd " + e.getCause());
        }
        try {
            dbMigrateMapper.createEnergyConsumptionPrediction();
            logger.info("dbMigrateMapper. createEnergyConsumptionPrediction success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. createEnergyConsumptionPrediction " + e.getCause());
        }
        try {
            dbMigrateMapper.addAItemWarningDataId();
            logger.info("dbMigrateMapper. addAItemWarningDataId success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addAItemWarningDataId " + e.getCause());
        }
        try {
            dbMigrateMapper.addDeviceMaintenanceInds();
            logger.info("dbMigrateMapper. addDeviceMaintenanceInds success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDeviceMaintenanceInds " + e.getCause());
        }
        try {
            dbMigrateMapper.addDeviceMaintenanceIdAuto();
            logger.info("dbMigrateMapper. addDeviceMaintenanceIdAuto success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDeviceMaintenanceIdAuto " + e.getCause());
        }
        try {
            dbMigrateMapper.addItemAlias();
            logger.info("dbMigrateMapper. addItemAlias success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addItemAlias " + e.getCause());
        }
        try {
            dbMigrateMapper.addItemDataAlias();
            logger.info("dbMigrateMapper. addItemDataAlias success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addItemDataAlias " + e.getCause());
        }
        try {
            dbMigrateMapper.addDeviceItemDataMapAlias();
            logger.info("dbMigrateMapper. addDeviceItemDataMapAlias success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDeviceItemDataMapAlias " + e.getCause());
        }


        try {
            dbMigrateMapper.addAWarningRuleAutoFlag();
            logger.info("dbMigrateMapper. addAWarningRuleAutoFlag success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addAWarningRuleAutoFlag " + e.getCause());
        }
        try {
            dbMigrateMapper.addAItemWarningInds();
            logger.info("dbMigrateMapper. addAItemWarningInds success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addAItemWarningInds " + e.getCause());
        }
        try {
            dbMigrateMapper.addDDeviceMaintenanceInds();
            logger.info("dbMigrateMapper. addDDeviceMaintenanceInds success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDDeviceMaintenanceInds " + e.getCause());
        }
        try {
            dbMigrateMapper.updateDeviceMaintenanceType();
            logger.info("dbMigrateMapper. updateDeviceMaintenanceType success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. updateDeviceMaintenanceType " + e.getCause());
        }
        try {
            dbMigrateMapper.updateUserBuildingMapId();
            logger.info("dbMigrateMapper. updateUserBuildingMapId success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. updateUserBuildingMapId " + e.getCause());
        }
        try {
            dbMigrateMapper.addDRepairOrderSUserId();
            logger.info("dbMigrateMapper. addDRepairOrderSUserId success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDRepairOrderSUserId " + e.getCause());
        }
        try {
            dbMigrateMapper.addDDeviceMaintenanceBuildingId();
            logger.info("dbMigrateMapper. addDDeviceMaintenanceBuildingId success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDDeviceMaintenanceBuildingId " + e.getCause());
        }
        try {
            dbMigrateMapper.addDDeviceTaskGroupBuildingId();
            logger.info("dbMigrateMapper. addDDeviceTaskGroupBuildingId success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDDeviceTaskGroupBuildingId " + e.getCause());
        }
        try {
            dbMigrateMapper.addDRepairOrderBuildingId();
            logger.info("dbMigrateMapper. addDRepairOrderBuildingId success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDRepairOrderBuildingId " + e.getCause());
        }
        try {
            dbMigrateMapper.addDUser();
            logger.info("dbMigrateMapper. addDUser success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDUser " + e.getCause());
        }
        try {
            dbMigrateMapper.addDDeviceActivationTime();
            logger.info("dbMigrateMapper. addDDeviceActivationTime success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDDeviceActivationTime " + e.getCause());
        }
        try {
            dbMigrateMapper.addDDeviceScrapTime();
            logger.info("dbMigrateMapper. addDDeviceScrapTime success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDDeviceScrapTime " + e.getCause());
        }
        try {
            dbMigrateMapper.modifyDDeviceActivationTime();
            logger.info("dbMigrateMapper. modifyDDeviceActivationTime success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. modifyDDeviceActivationTime " + e.getCause());
        }
        try {
            dbMigrateMapper.modifyDDeviceScrapTime();
            logger.info("dbMigrateMapper. modifyDDeviceScrapTime success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. modifyDDeviceScrapTime " + e.getCause());
        }
        try {
            dbMigrateMapper.addIotServerMonitor();
            logger.info("dbMigrateMapper. addIotServerMonitor success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addIotServerMonitor " + e.getCause());
        }
        try {
            dbMigrateMapper.addPiTaskTimes();
            logger.info("dbMigrateMapper. addPiTaskTimes success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addPiTaskTimes " + e.getCause());
        }
        try {
            dbMigrateMapper.addDeviceCommunicateWarning();
            logger.info("dbMigrateMapper. addDeviceCommunicateWarning success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDeviceCommunicateWarning " + e.getCause());
        }
        try {
            dbMigrateMapper.addAItemDataKeepSeconds();
            logger.info("dbMigrateMapper. addAItemDataKeepSeconds success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addAItemDataKeepSeconds " + e.getCause());
        }
        try {
            dbMigrateMapper.addEDeviceSynchronizedDataCreatedInd();
            logger.info("dbMigrateMapper. addEDeviceSynchronizedDataCreatedInd success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addEDeviceSynchronizedDataCreatedInd " + e.getCause());
        }

        try {
            dbMigrateMapper.createALFChain();
            logger.info("dbMigrateMapper. createALFChain success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. createALFChain " + e.getCause());
        }
        try {
            dbMigrateMapper.createALFScript();
            logger.info("dbMigrateMapper. createALFScript success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. createALFScript " + e.getCause());
        }
        try {
            dbMigrateMapper.addAWeatherDataColoms();
            logger.info("dbMigrateMapper. addAWeatherDataColoms success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addAWeatherDataColoms " + e.getCause());
        }
        try {
            dbMigrateMapper.addATplDeviceDataPtColoms();
            logger.info("dbMigrateMapper. addATplDeviceDataPtColoms success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addATplDeviceDataPtColoms " + e.getCause());
        }
        try {
            dbMigrateMapper.updateDDeviceTaskRecordContent();
            logger.info("dbMigrateMapper. updateDDeviceTaskRecordContent success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. updateDDeviceTaskRecordContent " + e.getCause());
        }
        try {
            dbMigrateMapper.addATplDeviceDataNote2();
            logger.info("dbMigrateMapper. addATplDeviceDataNote2 success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addATplDeviceDataNote2 " + e.getCause());
        }
        try {
            dbMigrateMapper.addATplDeviceDataUniqueIdx();
            logger.info("dbMigrateMapper. addATplDeviceDataUniqueIdx success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addATplDeviceDataUniqueIdx " + e.getCause());
        }
        // 补充因为分表而失败的表
        try {
            dbMigrateMapper.addDDeviceDataHistory();
            logger.info("dbMigrateMapper. addDDeviceDataHistory success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDDeviceDataHistory " + e.getCause());
        }
        try {
            dbMigrateMapper.addEDeviceDataHistory();
            logger.info("dbMigrateMapper. addEDeviceDataHistory success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addEDeviceDataHistory " + e.getCause());
        }
        try {
            dbMigrateMapper.addEDeviceDataHistoryDaily();
            logger.info("dbMigrateMapper. addEDeviceDataHistoryDaily success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addEDeviceDataHistoryDaily " + e.getCause());
        }
        try {
            dbMigrateMapper.addAPointTableTag();
            logger.info("dbMigrateMapper. addAPointTableTag success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addAPointTableTag " + e.getCause());
        }

        ////////////////////// views ///////////////////////////
        logger.info("-------- update views --------");
        // 创建用能视图 v_e_device_diff_indication_avg_hourly
        try {
            dbMigrateMapper.genEnergyDiffAvgHourlyView();
            logger.info("dbMigrateMapper.genEnergyDiffAvgHourlyView success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper.genEnergyDiffAvgHourlyView " + e.getCause());
        }
        try {
            dbMigrateMapper.deleteItemWarningRuleView();
            logger.info("dbMigrateMapper. deleteItemWarningRuleView success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. deleteItemWarningRuleView " + e.getCause());
        }
        try {
            dbMigrateMapper.addItemWarningRuleView();
            logger.info("dbMigrateMapper. addItemWarningRuleView success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addItemWarningRuleView " + e.getCause());
        }
        try {
            dbMigrateMapper.addDeviceItemDataView();
            logger.info("dbMigrateMapper. addDeviceItemDataView success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDeviceItemDataView " + e.getCause());
        }
        try {
            dbMigrateMapper.deleteValidDeviceWarningView();
            logger.info("dbMigrateMapper. deleteValidDeviceWarningView success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. deleteValidDeviceWarningView " + e.getCause());
        }
        try {
            dbMigrateMapper.addValidDeviceWarningView();
            logger.info("dbMigrateMapper. addValidDeviceWarningView success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addValidDeviceWarningView " + e.getCause());
        }
        try {
            dbMigrateMapper.deleteEDeviceAvgHourlyView();
            logger.info("dbMigrateMapper. deleteEDeviceAvgHourlyView success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. deleteEDeviceAvgHourlyView " + e.getCause());
        }
        try {
            dbMigrateMapper.addEDeviceAvgHourlyView();
            logger.info("dbMigrateMapper. addEDeviceAvgHourlyView success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addEDeviceAvgHourlyView " + e.getCause());
        }

        ////////////////////// triggers ///////////////////////////
        try {
            dbMigrateMapper.deleteItemDataUpdateTrigger();
            logger.info("dbMigrateMapper. deleteItemDataUpdateTrigger success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. deleteItemDataUpdateTrigger " + e.getCause());
        }
        try {
            dbMigrateMapper.addItemDataUpdateTrigger();
            logger.info("dbMigrateMapper. addItemDataUpdateTrigger success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addItemDataUpdateTrigger " + e.getCause());
        }

        /////////////////////// partitions //////////////////////////
        logger.info("-------- update partitions --------");
        try {
            int check = dbMigrateMapper.checkDDeviceDataHistoryPartitionExists(db);
            if(check <= 0 ) {
                try {
                    dbMigrateMapper.updateDDeviceHistoryData();
                    dbMigrateMapper.addDDeviceDataHistoryPrimaryKeys();
                    logger.info("dbMigrateMapper. addDDeviceDataHistoryPrimaryKeys success");
                } catch (Exception e) {
                    logger.info("dbMigrateMapper. addDDeviceDataHistoryPrimaryKeys " + e.getCause());
                }
                dbMigrateMapper.addDDeviceDataHistoryPartitions(db);
            }
            logger.info("dbMigrateMapper. addDDeviceDataHistoryPartitions success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addDDeviceDataHistoryPartitions " + e.getCause());
        }
        try {
            int check = dbMigrateMapper.checkEDeviceDataHistoryPartitionExists(db);
            if(check <= 0 ) {
                try {
                    dbMigrateMapper.updateEDeviceHistoryData();
                    dbMigrateMapper.addEDeviceDataHistoryPrimaryKeys();
                    logger.info("dbMigrateMapper. addEDeviceDataHistoryPrimaryKeys success");
                } catch (Exception e) {
                    logger.info("dbMigrateMapper. addEDeviceDataHistoryPrimaryKeys " + e.getCause());
                }
                dbMigrateMapper.addEDeviceDataHistoryPartitions(db);
            }
            logger.info("dbMigrateMapper. addEDeviceDataHistoryPartitions success");
        } catch (Exception e) {
            logger.info("dbMigrateMapper. addEDeviceDataHistoryPartitions " + e.getCause());
        }

        // 输出java lib 环境目录，安装硬件锁需要
        logger.info("-------- java lib path --------");
        String paths = System.getProperty("java.library.path");
        List<String> pathList = Arrays.asList(paths.split(";"));
        pathList = pathList.stream()
                           .distinct()          // 去重（保留首个出现的元素）
                           .sorted()            // 自然排序（默认升序）
                           .collect(Collectors.toList());
        pathList.forEach(path -> {
            logger.info(path);
        });

        logger.info("doing db migrate finished.");
    }
}

