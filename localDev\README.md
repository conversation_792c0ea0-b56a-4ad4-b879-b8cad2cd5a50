// 脚本安装 部署说明

## 文件位置

### 1.开机自启文件夹下文件

auto.bat

### 2.D:/soft/nginx-1.26.1 下文件

pm2.json pm2bc.json

### 3. D:/soft/localDev 下文件

check_hidden.vbs , checkRun.bat , checkRunBc.bat , run.bat, runbc.bat

确认文件的位置和各个文件中的路径是否对应

## nginx.conf 添加配置,为了监控 bacnet 采集服务，bacnet jar 包时间久了的可能要更新下，可以不配，脚本里面直接写能调通的链接

location /apibc/{
proxy_set_header Host localhost;
proxy_pass http://localhost:8049/;
proxy_http_version 1.1;
proxy_send_timeout 300;
proxy_read_timeout 300;
proxy_connect_timeout 300;
client_max_body_size 500M;
}

## 各文件的作用及 需要修改的部分

1.auto.bat
开机自启服务和采集脚本
2.pm2.json
除了 bacnet 采集的脚本
3.pm2bc.json
bacnet 采集的脚本， name 是 ruoyi-bacnet
4.run.bat
重启所有脚本
5.runbc.bat
重启 bacnet 脚本
6.check_hidden.vbs
隐藏窗口下 运行 checkRun.bat 和 checkRunBc.bat, 防止脚本窗口被关掉导致脚本停止，如果需要测试 监控服务是否正常，可以直接运行 checkRun.bat 和 checkRunBc.bat，运行方法：双击，或者当前文件夹 cmd 窗口 输入脚本名称，回车， 停止方法： ctrl+ c ，看提示输入 y 回车
7.checkRun.bat
作用：监控后端服务和 pm2 进程，后端挂了或者 pm2 list 都不在线时 运行 run.bat
修改： set "msg=[ibms]海螺南区服务重启失败,请抓紧处理" 里面的文字根据项目修改,[ibms] 要保留
curl http://localhost:8044/api/common/base/settings > response.txt 中的链接，验证是否正确

8.checkRunBc.bat
监控 bacnet，调用 http://localhost/apibc/common/base/settings 判断采集是否正常，发现异常 运行 runbc.bat 
修改： set "msg=[ibms]海螺南区 bacnet 采集重启失败,请抓紧处理" 里面的文字根据项目修改,[ibms] 要保留
curl http://localhost:8049/common/base/settings > response.txt 中的链接，验证是否正确

## 注意，编辑脚本时最好不要用 记事本，可能会格式错误导致脚本运行不了

## checkRun.bat 或 checkRunBc.bat 当天重启 6 次 后仍不成功会给 钉钉群里面发信息，如果重启一直不成功，需人工处理，启动前删除 D:/soft/localDev 下面相应的当天文件 pm2...日期.txt 或者 bacnet...日期.txt

## 启动的 vbs 脚本查看方法： 任务管理器里面 搜索 命令处理， 看对应的命令行， 包含 checkRun.bat 或 checkRunBc.bat 


## 部署步骤
1. 各个文件放到对应的位置
2. 单个测试脚本能正常运行
3. 检查 json， bat 文件里面的路径，提示文字，接口连接是否正确，接口在浏览器里面测试下，能调通
4. 创建 任务计划， 参考 https://blog.csdn.net/baidu_17201253/article/details/********* （选择创建任务，不是基本任务，触发器里面选择 每天， 勾选 重复任务间隔，选择 5分钟,脚本选择  check_hidden.vbs）
注意： 调试脚本时 先禁用 任务计划，调试好后再启用
注意修改点： 1. auto.bat 删了check_hidden.vbs，改用 任务计划里面执行
          2. run.bat runBc.bat 里面的  cmd /k 改成 /B cmd /c ，pm2 的窗口会关掉
            3. checkRun.bat , checkRunBc.bat 里面删除了 定时检查，使用 任务计划里面的定时



## 删除日期脚本
脚本里面有 folder=D:\soft\logs 如果日志文件不在 D:\soft\logs 这个文件夹下面，把这个路径修改成你日志文件所在的位置，位置选的不要太上级，脚本会删掉这个文件夹及子级文件夹下面对应的文件
现在会删除  .txt  .log  类型的文件，如果需要删除其他类型的文件， 在 for /r "%folder%" %%f in (*.txt *.log) do  的 *.txt *.log 继续加其他类型 如 *.txt *.log *.md
pm2.json 里面生成的日志文件格式最好统一， 比如都用 txt 格式， 稳定运行时不要用 debug

选择脚本，试运行 deletelogFileNoStopPm2.bat 脚本，如果可以把 日志文件删掉，就可以用 deletelogFileNoStopPm2.bat 这个脚本
如果删不掉，需要运行 deletelogFileStopPm2.bat,这个会把pm2 停掉，后端服务会停掉，任务计划可以设置在 凌晨执行， 如果服务不能停，则不能用删除日志脚本



## 更新日志
2025-07-02   checkRun.bat ， checkRunBc.bat 里面文件路径用了绝对路径，脚本要放到 D:/soft/localDev 下面，不能调整

