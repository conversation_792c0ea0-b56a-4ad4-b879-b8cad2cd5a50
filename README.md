### d_device_list_display字典扩展支持设备详情运行状态点位分组  dataGroup
    eg: {"pageTitle":"空调机组","category":"新风空调","type":"base","deviceType":"XFKT","deviceMainType":"XFKT","displayType":"pt","globalVals":"运行状态,手自动状态,运行模式","hasEnergy":"0","hasLock":"0","summaryType":"device","edit":0,"refreshspan":10,"protoTypeDisplay":"sp3d","dialogType":"small","iconDisplay":"image","displayTypes":["pt","card","list","map"],"viewDetail":"dialog","cardType":"aircondition","hasLock":"1","groupControlIssue":"手动设备无法下发命令","listMoreFieldsNum":8,"dataGroup":[{"key":"状态","val":"device_status"},{"key":"温度","val":"device_temp"}]}

### 标准模型以及规则
  XF11.ns 新风二管制不带变频

  KT12.ns 空调机组 二管制![](https://ibms.lanxing.tech/uploads/doc/两管制空调.png)
    显示点:
      新风阀开度,滤网压差,防冻开关,水阀开度,回风温度,回风湿度,回风二氧化碳,回风阀开度,加湿阀开度,频率反馈,送风温度,送风湿度
    动画点:
      运行状态
        function calc(x) {return { "0": "stop", "1":"play" }[x] || "stop";} return calc;

      
  KT13.ns 空调机组 四管制  ![](https://ibms.lanxing.tech/uploads/doc/四管制空调.png)
    显示点:
      新风阀开度,滤网压差,防冻开关,热水阀开度,冷水阀开度,回风温度,回风湿度,回风二氧化碳,回风阀开度,加湿阀开度,频率反馈,送风温度,送风湿度
    动画点:
      运行状态
        function calc(x) { return { "0": "stop", "1":"play" }[x] || "stop"; } return calc;
       
### DisplayByList2组件修改
    1. 默认展示表格选项改为6个  支持字典修改d_device_list_display -> listMoreFieldsNum
    eg：{"pageTitle":"空调机组","category":"新风空调","type":"base","deviceType":"XFKT","deviceMainType":"XFKT","displayType":"pt","globalVals":"运行状态,手自动状态,运行模式","hasEnergy":"0","hasLock":"0","summaryType":"device","edit":0,"refreshspan":10,"protoTypeDisplay":"sp3d","dialogType":"small","iconDisplay":"image","displayTypes":["pt","card","list","map"],"viewDetail":"dialog","cardType":"aircondition","hasLock":"1","groupControlIssue":"手动设备无法下发命令","listMoreFieldsNum":6}
    2. 支持行内显示下发点位，支持行内编辑下发命令，配置了tag包含shortcut的点位会作为下发点位展示

### 字典系统配置增加ai数据展示 aiControl_dataitems_map
    eg: {"input":{"name":"采集输入","list":[{"name":"室外环境","list":[{"name":"环境温度","isLeaf":true,"deviceList":[{"deviceId":300603,"dataId":30060307}]},{"name":"环境湿度","isLeaf":true,"deviceList":[{"deviceId":300603,"dataId":30060306}]}]}]},"output":{"name":"控制输出","list":[{"name":"系统整体","list":[{"name":"算法 AI使能控制","isLeaf":true,"deviceList":[{"deviceId":300901,"dataId":30060217}]},{"name":"冷水机组 AI使能设置","isLeaf":true,"deviceList":[{"deviceId":300901,"dataId":30060219},{"deviceId":300901,"dataId":30060220},{"deviceId":300901,"dataId":30060221}]},{"name":"冷冻泵 AI使能设置","isLeaf":true,"deviceList":[{"deviceId":300602,"dataId":30060232}]},{"name":"冷却泵 AI使能设置","isLeaf":true,"deviceList":[{"deviceId":300602,"dataId":30060233}]},{"name":"冷却塔 AI使能设置","isLeaf":true,"deviceList":[{"deviceId":300901,"dataId":30060218}]}]}]}}

### 字典d_device_diagram_display增加系统图自定义按钮 moreTabs
    eg:{"pageTitle":"冷源群控","category":"冷源群控3d","refreshSpan":2,"moreTabs":{"12140":[{"icon":"/image/screen/controlCenter_control.png","name":"启停控制","tags":"status,monitor_running_status"},{"icon":"/image/screen/controlCenter_control.png","name":"用能查看","tags":"monitor_totalPower"}],"22141":[{"icon":"/image/screen/controlCenter_control.png","name":"监听点位查看","tags":"monitor"}]}}

### 字典增加预约类型：reservation_type
    1.海世佳大食堂包厢
    2.会议室

### 字典增加预约状态：reservation_status
    1.pending  //待审批
    2.approve   //审批通过
    3.refuse   //审批驳回

### 设备展示配置字典增加群控下发提示语句：d_device_list_display
  1."groupControlIssue":"群控下发限制提示语句"
  eg: {"pageTitle":"公区照明","category":"公区照明","type":"base","deviceType":"ZMGQ","deviceMainType":"ZMGQ","displayType":"map","globalVals":"开关,亮度","hasEnergy":"1","hasLock":"0","ummaryType":"sensor","edit":0,"iconDisplay":"image","displayTypes":["card","list","map"],"refreshspan":10,"viewDetail":"dialog","protoTypeDisplay":"by","dialogType":"small","mapSwiftShow":true,"legendSet":{"url":"/uploads/ibms_2922_2x_ZM_legend.png","position":{"right":100,"bottom":200},"scale":1},"cardDataNum":1,"cardType":"light","groupControlIssue":"群控下发限制提示语句"}

### 新增配置报警分组类型字典：warning_list_type
  1.字典标签（数据标签）:报警类型一级菜单
  2.数据键值:{"name":"全部","type":"all","typelist":[{"dictLabel":"所有","type":"all"}]}   
 name：一级报警菜单名   type：一级报警菜单类型     typelist：二级报警菜单列表 （dictLabel为二级菜单名，type：二级菜单类型）

### 设备展示配置字典增加默认分页长度和默认排序条件设置：d_device_list_display
  1."defaultPageSize":20 //默认分页长度
  2."defaultSortBy":"name"//默认排序条件
  eg: {"pageTitle":"室内照明","category":"demo照明","type":"base","deviceType":"ZMSN","deviceMainType":"ZMSN","displayType":"map","globalVals":"开关,亮度","hasEnergy":"1","hasLock":"0","ummaryType":"sensor","edit":0,"iconDisplay":"image","displayTypes":["card","list","map"],"refreshspan":10,"viewDetail":"dialog","protoTypeDisplay":"by","dialogType":"small","mapSwiftShow":true,"legendSet":{"url":"/uploads/ibms_2922_2x_ZM_legend.png","position":{"right":100,"bottom":200},"scale":1},"cardDataNum":1,"cardType":"light","defaultPageSize":20,"defaultSortBy":"name"}


### 视频巡检字典补充：building_config_n -> video_inspection
    eg: {"tabsSet":["single","four","nine","sixteen"],"plans":[{"name":"主要通道","interval_time":10,"note":"","list":[{"id":3510090,"name":"正门东","position":"室外","resourceId":"1038"},{"id":3510089,"name":"正门西","position":"室外","resourceId":"1038"},{"id":3510003,"name":"1F大厅大屏","position":"1F","resourceId":"1011"},{"id":3520105,"name":"1F大厅西","position":"1F","resourceId":"1011"},{"id":3520118,"name":"1F大厅曲面屏","position":"1F","resourceId":"1011"}]},{"name":"重点房间","interval_time":10,"note":"","list":[{"id":3510004,"name":"A1办公楼南侧车道2(C055)","position":"A6#B1F","resourceId":"12071"}]}]}

### 增加字典：用能概览页面的一些特殊设置：system_configs -> energy_summary_special_settings
    eg: {
          "energyConsumed_proportion_hide": true,//默认显示，值为true或1时隐藏， 控制年度供耗比例 的 用能占比的显示  
          "partition_item_by": "month"//默认年度 分区分项的是年度还是月度
        }

### 增加字典控制分项用能页面数据汇总表格费用字段是否隐藏：system_configs -> energy_summary_fee_hide
    默认显示   值为true或1时隐藏

### 增加字典控制系统接口报错信息是否展示：base_configs -> interface_error_hide
    默认显示   值为true或1时隐藏

### 和风天气城市id列表查看链接
    1.https://www.yuankong.org.cn/staticSource/weatherCityList.json

##设备dmtag中配置keypoint  用于关键数据展示

### 字典中 建筑配置_1(building_config_1)新增 screen_shy_aiControl 上海院ai智控页面左侧数据
{
  "name": "aiControl",
  "history": {
    "name": "历史能耗",
    "historydata": [
      { "date": "2024-05-01", "status": 0, "dailyUse": 0 },
      { "date": "2024-05-02", "status": 1, "dailyUse": 0 },
    ],
    "sql": ""
  },
  "aianalysis": {
    "name": "节能分析",
    "aianalydata": {
      "title": "节能分析",
      "progress": {
        "percentage": 75
      },
      "proportion": {
        "title": "设备实时功率占比",
        "color": ["#2294FE", "#62D1A4", "#FAC980", "#3CCCF9"],
        "data": [
          { "name": "全热回收", "value": 35 },
          { "name": "新风机组", "value": 22 },
          { "name": "热管热回收", "value": 21 },
          { "name": "新风", "value": 22 }
        ]
      }
    },
    "sql": ""
  },
  "electricity": {
    "name": "用电统计",
    "eledata": [
      {
        "name": "本日用电量",
        "val": 28,
        "unit": "Kwh",
        "imgSrc": "/image/screen/ai/p1.png"
      },
      {
        "name": "本月用电量",
        "val": 620,
        "unit": "Kwh",
        "imgSrc": "/image/screen/ai/p2.png"
      },
      {
        "name": "本年用电量",
        "val": 8120,
        "unit": "Kwh",
        "imgSrc": "/image/screen/ai/p3.png"
      }
    ],
    "sql": ""
  }
}



### 字典中新增 运维类型(maintainance_type); ruoyi.js中增加全局方法 getMaintainanceType;
    1. 字典中新增 运维类型(maintainance_type);
    eg: 
    repair: {"title":"维修记录","type":"紧急维修"}
    maintainance: {"title":"保养记录","type":"日常维护,预见性维修,计划性维修,设备改进性维修"}
    increment: {"title":"增值服务","type":"卫生清洁"}
    2. ruoyi.js中增加全局方法 获取当前类型下的字典中配置的运维数据

## 光伏组件中个别数据来源备注
  1.当年发电量  取值于/energy/api/buildingSummaryData接口中光伏发电量的curYear
  2.累计发电量  取值于/device/api/deviceList/deviceList接口中的累计发电量点位
  3.环保贡献    计算公式如下
    if(plant.data["累计发电量"] && plant.data["累计发电量"].dVal > 0) {
        let total = plant.data["累计发电量"].dVal;
        plant.data["累计发电量"].co2 = ((total * 0.475) / 1000).toFixed(2);
        plant.data["累计发电量"].so2 = ((total * 0.03) / 1000).toFixed(2);
        plant.data["累计发电量"].coal = ((total * 0.36) / 1000).toFixed(2);
        plant.data["累计发电量"].smoke = ((total * 0.272) / 1000).toFixed(2);
        plant.data["累计发电量"].tree = ((total * 0.475) / 18.3 / 40).toFixed(2);
      }
      if(plant.data["今年发电量"] && plant.data["今年发电量"].dVal > 0) {
        let year = plant.data["今年发电量"].dVal;
        plant.data["今年发电量"].co2 = ((year * 0.475) / 1000).toFixed(2);
        plant.data["今年发电量"].so2 = ((year * 0.03) / 1000).toFixed(2);
        plant.data["今年发电量"].coal = ((year * 0.36) / 1000).toFixed(2);
        plant.data["今年发电量"].smoke = ((year * 0.272) / 1000).toFixed(2);
        plant.data["今年发电量"].tree = ((year * 0.475) / 18.3 / 40).toFixed(2);
      }


## 12.5  站点天气信息配置
天气以及温湿度相关信息需要在数据库的a_building表中配置city_id字段，
值可以在https://www.yuankong.org.cn/staticSource/weatherCityList.json文档中查找
配置完成后接口可能不会立即查到数据，要等数据库中更新了该地区天气数据后才能查到

## 10.8 会议纪要
2.0 demo 先出来，后面绑点后参考
3.0 嘉兴项目参照 2dh, 完成功能模块

# mysql 安装下载
https://downloads.mysql.com/archives/community/


## node 解决C++ 未安装的问题
npm install -g node-gyp 
npm install --global --production windows-build-tools

更换源
npm config set registry https://registry.npmmirror.com/

环境变量增加 path
C:\Users\<USER>\AppData\Roaming\npm

## Java 1.8 安装
### ubuntu 20
安装JAVA1.8
  sudo apt-get install openjdk-8-jdk
切换JAVA版本
  sudo update-alternatives --config java

### IDEA显示“java XXX 程序包不存在”问题, mvn clean install 又是正常的情况
  找到项目的目录的“xxx.iml”后缀的文件，删除
  找到idea左下角Terminal窗口，输入: mvn idea:module

## pm2 开机启动
pm2 startup
pm2 save
检查 systemctl status pm2-root.service

## windows中重启 nginx
  在nginx安装目录下：
  安全有序停止: nginx.exe -s quit
  启动: start nginx

## NPM 如何换源
  npm config get registry
  npm config set registry https://registry.npmmirror.com/

## 平台简介

基于若依扩展, 使用前后端分离。
前端 vue 框架.


## sso单点登录
  回调地址: http(s)://【服务器】/api/ssologin
  登录账号密码填 11111111q!    $2a$10$WeOkLil49z3bCHkQgnnWYeuvmJ0az.mycGKc9nzVLWBpUC0w6xQbe
  字典 base_configs
    ssologin: Y     --> sso登录总开关
    ssoRedirect: https://www.nb-fuwu.com/   sso登录跳转页面
    ssologinSuccess:                        登录成功后跳转页面
    ssologinFailed:                         登录失败后跳转页面
    userRedirectPage:                       根据用户的 id 跳转到 配置的页面 例： {"3":"/energyMgt/electricity/summary"}
  余姚电力：
    字典 base_configs
      sslLoginPlatform: yydl  写死
      ssoLoginUrl: https://************/data-api/auth
      ssoLoginCode: PRO_015         -> 对应通用的 appId
      sslLoginKey:  yhq5Y...E1l40g  -> 对应通用的 appKey

  武汉党校
    字典 base_configs
      sslLoginPlatform: whdx  写死
      ssoRedirect: http://**********:9995/websso/open/authorize?app_id=BAXT&response_type=code&redirect_uri=http://localhost/#/ssologin
      ssoLoginUrl: http://**********:9995
      ssoLoginCode: BAXT            -> 对应通用的 appId
      sslLoginKey:  24032...Vuuxo6m -> 对应通用的 appKey
      ssologinSuccess: http://localhost
      ssologinFailed: http://localhost/#/ssologinResult?msg=登录失败，请重试。

http://**********:9995/websso/open/authorize?app_id=BAXT&response_type=code&redirect_uri=http://www.baidu.com


## 硬件锁打包开关
  参考文件: D:\work\project\bms\_文档\加密狗
  1. 前端代码 App.vue
    设置 expCheck 不为 null 即可关闭锁检查
  2. 后端代码
    需要安装驱动，添加dll
    linux系统
      1. 32位： 将libJsyunew3.so拷到库目录下，库目录可以用如下命令打印出来 -> System.out.println(System.getProperty("java.library.path"));
      2. 64位: 将libJsyunew3_64.so 拷到LIB64下就可以了
      3. 权限问题
        加密狗需要在SU权限下运行才可以识别，如果在普通用户权限下使用加密狗，需要进行以下操作：
        放一个相应的规则文件到以下文件夹下： /etc/udev/rules.d.
        文件内容如下：
        SUBSYSTEM=="input", GROUP="input", MODE="0666"
        SUBSYSTEM=="usb", ATTRS{idVendor}=="3689", ATTRS{idProduct}=="8762", MODE:="666", GROUP="plugdev"
        KERNEL=="hidraw*", ATTRS{idVendor}=="3689", ATTRS{idProduct}=="8762", MODE="0666", GROUP="plugdev"

        将以上的内容保存为一个新的文件例如：
        /etc/udev/rules.d/51-blink1.rules
        然后拨出设备，运行以下命令就可以了：
        sudo udevadm control --reload-rules

        注意，加密狗设备的PID及VID一定要是16进制及小写，


## 3D 模型参考项目
ToDesk
    738 991 172 / admin159
windows密码 123456
zy_admin / 11111111

web链接直接进行远程控制：
    https://wechat.todesk.com/invite-page?id=5c9iZBTEqDhD_cbZsFMqP

## 3D模型
水阀       public/uploads/xincheng3D/models/waterValve.glb
水表       public/uploads/xincheng3D/models/waterMeter.glb
光伏板     public/uploads/xincheng3D/models/pvModule.glb
射灯       public/uploads/xincheng3D/models/lightLux.glb
暖通开空调  public/uploads/xincheng3D/models/HVAC.glb
普通空调    public/uploads/xincheng3D/models/airCondition.glb

## 3D模型配置
建筑显示3D模型：
    对应字典增加 threeServer
        eg: {"resourceCategory":"3Dmodel","resourceType":"buildingView","background":"#0A141C","modelPath":"/uploads/xincheng3D","environment":"/uploads/xincheng3D/env/xincheng.hdr","metalEnv":"/uploads/xincheng3D/env/V2.0.hdr","floorEnv":"/uploads/xincheng3D/env/floor.hdr","envTexture":{"path":"/uploads/xincheng3D/env/","photos":["photo6.jpg","photo2.jpg","photo6.jpg","photo4.jpg","photo5.jpg","photo1.jpg"]},"gltf":{"path":"/uploads/xincheng3D/draco/","modelPath":"/uploads/xincheng3D/models/xincheng.glb","name":"xincheng"},"defaultFog":{"color":"#0d1721","near":200,"far":1000},"defaultCamara":{"position":{"x":0.819693820394235,"y":28.026887556383599,"z":62.15985362799664},"target":{"x":31.597955607178278,"y":11.14229442068607,"z":6.883441133134474}},"defaultOutline":{"color":"#67C23A","edgeStrength":2,"pulseSpeed":1,"opacity":1,"blurriness":5},"deviceTypes":[{"name":"光伏","icon":"/uploads/ibms/icon/pvPlant.png","deviceTypes":["NBQ","pvModule"]},{"name":"空调","icon":"/uploads/ibms/icon/airCondition.png","deviceTypes":["LY2"]},"energyTypes":{"name":"照明","icon":"/uploads/ibms/icon/light.png","deviceTypes":["light"]}],"energyTypes":[{"name":"电","icon":"/uploads/ibms/icon/building_energy_electricity.png","iconOn":"/uploads/ibms/icon/building_energy_electricity_selected.png","type":"electricity"},{"name":"水","icon":"/uploads/ibms/icon/building_energy_water.png","iconOn":"/uploads/ibms/icon/building_energy_water_selected.png","type":"water"},{"name":"光伏","icon":"/uploads/ibms/icon/building_energy_pvPlant.png","iconOn":"/uploads/ibms/icon/building_energy_pvPlant_selected.png","type":"pvPlant"}],"createDevice":{"iconPath":"/uploads/xincheng3D/img/currentPosition.png"},"hideObjects":["-LC","-LG","-LUX","-signage","-THVP","-valve","-waterPump","elevator","-ACP","-alarm","-distributionCabinets","-FA","-fireHorse","-KTWH","-WJ62P","-Camera","-125Q","-56F","outside2"],{"deviceSummaryTypes": ["NBQ","light","LY2"]}}
    内容说明:
        resourceCategory 对应 a_resource 表 category 字段
        resourceType     对应 a_resource 表 type 字段， 两个条件筛选 3D 模型右变楼层列表
        background       场景背景颜色
        modelPath        模型地址路径
        environment      环境贴图地址
        envTexture       环境盒子贴图地址
        dynamicSemisphereOpt  高亮选中设备时，动态球参数
        metalEnv         金属材质贴图
        gltf             模型具体配置
        defaultFog       雾设置，优化渲染速度
        defaultCamara    初始化摄像机位置 eg: {"defaultCamara":{"position":{"x":17.82,"y":11.03,"z":-52.16},"target":{"x":5.6,"y":11.14,"z":-15.88}}}
        defaultOutline   高亮边框配置
          eg: {
            "color": "#67C23A",              -- 颜色
            "edgeStrength": 3,               -- 边框大小
            "pulseSpeed": 0,                 --
            "opacity": 1,
            "blurriness": 2,
            "visibleEdgeColor": "#27e10e",
            "hiddenEdgeColor": "#27e10e"
          }

        deviceTypes      设备页面，右边设备类型显示配置
          "name": "空调",                                       -- 右边图标名称
          "icon": "/uploads/ibms/icon/airCondition.png",        -- 右边主菜单图标
          "deviceTypes": [],                                    -- 实际设备类型列表
            eg: [
                  {
                    "type": "125Q",                              -- 设备 name 对应 d_device.type 字段
                    "display": "model"                           -- 有大模型，可直接用模型展示，生成变色，发光效果
                  },
                  {
                    "type": "KTWH",
                    "display": "bubble",                         -- 无大模型，则用气泡图标展示，图标位置对应 d_device_resouce_map.other_data 字段
                    "icons": {                                   -- 气泡对应图标，运行,停止,离线,健康,故障,报警,错误
                      "running": "/uploads/ibms/icon/KTWH_running.png",
                      "stop": "/uploads/ibms/icon/KTWH_stop.png",
                      "offline": "/uploads/ibms/icon/KTWH_offline.png",
                      "healthy": "/uploads/ibms/icon/KTWH_healthy.png",
                      "fault": "/uploads/ibms/icon/KTWH_fault.png",
                      "warning": "/uploads/ibms/icon/KTWH_warning.png",
                      "error": "/uploads/ibms/icon/KTWH_error.png",
                    }
                  }
                ]
          "type": "airCondition",                               -- 左上概览对应图标 /uploads/ibms/icon/sb_【XXXX】.png
          "energyType": "electricity",                          -- 左侧用能类型
          "groupName": "空调用电"                                -- 左侧对应用能分组
          "hideLoad": "1",                                      -- 左侧隐藏 用能负荷， 1: 隐藏; 0: 显示
          "subLinks": []                                       -- 子菜单链接， name 为显示文字，link 为页面地址
            eg: [
                  {
                    "name": "空调管理",                         -- 页面显示文字
                    "link": "/deviceMgt/airCondition",         -- 链接地址
                    "target": "innerPage"                      -- 新窗口名字, 如果一样，则刷新原窗口
                  }
                ]
          "callbacks": []                                         -- 菜单进入时，初始化事件
            eg: [
                  {
                    "func": "resetScene",      --> 恢复原场景，并移除所有图标，发光，变色等效果
                    "args": [null]
                  },
                  {
                    func: "opacityModelByName", --->  包含 -pipe 的物体不透明
                    args: [["-pipe"],null,1],
                  },
                  {
                    func: "bloomModels",        --->  包含 -pipe 的物体都发光
                    args: [["-pipe"]],
                  }
                ]
        energyTypes      用能页面，右边用能类型显示配置
        securityTypes     安全页面，右边安全类型显示配置
        environmentTypes  环境页面，右边环境类型显示配置
        maintenanceTypes  运维页面，右边运维类型显示配置

        deviceSummaryTypes      设备页面，左边设备统计配置 {"deviceSummaryTypes": ["NBQ","light","LY2"]}
        securitySummaryTypes    安全页面，左边设备统计配置 {"securitySummaryTypes": ["NBQ","light","LY2"]}
        environmentSummaryTypes 环境页面，左边设备统计配置 {"environmentTypes": ["NBQ","light","LY2"]}
        maintenanceSummaryTypes 运维页面，左边设备统计配置 {"maintenanceTypes": ["NBQ","light","LY2"]}
        createPlant      创建平面 eg: {"createPlant":{"width":100,"height":100,"color":"#008866","pos":{"x":0,"y":0,"z":0},"rot":{"x":0,"y":0,"z":0}}}
        createDevice     模型载入后，是否需要动态添加设备模型到场景，图标/模型
            display      载入模型类型 icon:图标； model:模型
            iconPath     图标地址
            modelPathMap 模型配置 eg {"createDevice":{"display":"model","iconPath":"/uploads/xincheng3D/img/currentPosition.png","modelPathMap":{"NBQ":"/uploads/xincheng3D/models/device-f1-HVAC.glb"}}}
        hideObjects      载入场景后，隐藏物体，优化渲染速度

    a_resource 表 other_data 3D配置(设备页，组态图等3D显示)
        eg: {"gltf":{"path":"/uploads/xincheng3D/draco/","modelPath":"/uploads/xincheng3D/models/water-supply.glb","name":"waterSupply"},"defaultCamara":{"position":{"x":-12.91849635680625,"y":292.5813534705472,"z":276.6138286106745},"target":{"x":-12.314740029014608,"y":-10.491822911230786,"z":7.641056573225179}},"plantName":"building-waterSupply-plane1","statusColor":true}
    内容说明:
        gltf             模型具体配置
        plantName        模型投影配置，地面名称
        statusColor      true/false 标记模型是否要根据 运行报警修改颜色（逻辑通平面图颜色）
        createPlant      配置同上
        createDevice     配置同上

新塍配置:
  {"resourceCategory":"3Dmodel","resourceType":"buildingView","background":"#0A141C","modelPath":"/uploads/xincheng3D","environment":"/uploads/xincheng3D/env/xincheng.hdr","metalEnv":"/uploads/xincheng3D/env/V2.0.hdr","floorEnv":"/uploads/xincheng3D/env/floor.hdr","envTexture":{"path":"/uploads/xincheng3D/env/","photos":["photo6.jpg","photo2.jpg","photo6.jpg","photo4.jpg","photo5.jpg","photo1.jpg"]},"dynamicSemisphereOpt":{"radius":2,"color":"#ffff00","maxScale":1.2,"minScale":0.8,"duration":200},"gltf":{"path":"/uploads/xincheng3D/draco/","modelPath":"/uploads/xincheng3D/models/xincheng.glb","name":"xincheng"},"defaultFog":{"color":"#0d1721","near":200,"far":1000},"defaultCamara":{"position":{"x":0.819693820394235,"y":28.0268875563836,"z":62.15985362799664},"target":{"x":31.597955607178278,"y":11.14229442068607,"z":6.883441133134474}},"defaultOutline":{"color":"#67C23A","edgeStrength":3,"pulseSpeed":0,"opacity":1,"blurriness":2},"deviceTypes":[{"name":"空调","icon":"/uploads/ibms/icon/airCondition.png","deviceTypes":["125Q","ZNCZ"],"type":"airCondition","energyType":"electricity","groupName":"空调用电","hideLoad":"0"},{"name":"照明","icon":"/uploads/ibms/icon/light.png","deviceTypes":["CGZD"],"type":"light","energyType":"electricity","groupName":"照明用电","hideLoad":"0"},{"name":"供水","icon":"/uploads/ibms/icon/waterSupply.png","deviceTypes":["CGPA"],"type":"waterSupply","energyType":"water","groupName":"","hideLoad":"1"},{"name":"排水","icon":"/uploads/ibms/icon/waterDrainage.png","deviceTypes":["CGPA"],"type":"waterDrainage","energyType":"water","groupName":"","hideLoad":"1"},{"name":"大型电器","icon":"/uploads/ibms/icon/device.png","deviceTypes":["CGPA","ZNSF","ZNSB"],"type":"device","energyType":"electricity","groupName":"插座用电","hideLoad":"0"}],"securityTypes":[{"name":"电器火灾","icon":"/uploads/ibms/icon/electricalFire.png","deviceTypes":["KTWH","125Q","ZNCZ"]},{"name":"浸水监测","icon":"/uploads/ibms/icon/immersionWater.png","deviceTypes":["LC"]},{"name":"消防水压","icon":"/uploads/ibms/icon/fireWater.png","deviceTypes":["LUX"]},{"name":"幕墙玻璃","icon":"/uploads/ibms/icon/wallGlass.png","deviceTypes":["none"]}],"environmentTypes":[{"name":"空气质量","icon":"/uploads/ibms/icon/airQuality.png","deviceTypes":["WHWG"]},{"name":"光照度","icon":"/uploads/ibms/icon/illuminance.png","deviceTypes":["CGJS"]},{"name":"温湿度","icon":"/uploads/ibms/icon/temperatureHumidity.png","deviceTypes":["ZNSB","ZNSF","CGPA","125Q","ZNCZ"]}],"maintenanceTypes":[{"name":"资产","icon":"/uploads/ibms/icon/asset.png","deviceTypes":["WHWG","125Q"]},{"name":"能源","icon":"/uploads/ibms/icon/energy.png","deviceTypes":["CGJS","125Q"]},{"name":"设备","icon":"/uploads/ibms/icon/mdevice.png","deviceTypes":["ZNSB","ZNSF","CGPA"]},{"name":"安全","icon":"/uploads/ibms/icon/security.png","deviceTypes":["ZNSB","ZNSF","CGPA"]},{"name":"环境","icon":"/uploads/ibms/icon/environment.png","deviceTypes":["ZNSB","ZNSF","CGPA"]}],"energyTypes":[{"name":"电","icon":"/uploads/ibms/icon/building_energy_electricity.png","iconOn":"/uploads/ibms/icon/building_energy_electricity_selected.png","type":"electricity","mainGroupName":"分项用电","unit":"kWh","smallSystem":[{"name":"电器系统图","link":"/deviceMgt/highPowerEquipment","target":"innerPage"}],"callbacks":[{"func":"opacityScene","args":[0.01]},{"func":"opacityModelByName","args":[["-pipe"],null,1]},{"func":"bloomAll","args":[]},{"func":"bloomModels","args":[["-pipe"]]}]},{"name":"水","icon":"/uploads/ibms/icon/building_energy_water.png","iconOn":"/uploads/ibms/icon/building_energy_water_selected.png","type":"water","mainGroupName":"分项用水","unit":"吨","subLinks":[{"name":"水系统图","link":"/deviceMgt/Water","target":"innerPage"}],"callbacks":[{"func":"opacityScene","args":[0.01]},{"func":"opacityModelByName","args":[["-pipe"],null,1]},{"func":"bloomAll","args":[]},{"func":"bloomModels","args":[["-pipe"]]}]},{"name":"光伏","icon":"/uploads/ibms/icon/building_energy_pvPlant.png","iconOn":"/uploads/ibms/icon/building_energy_pvPlant_selected.png","type":"pvPlant","mainGroupName":"光伏发电","unit":"kWh"}],"createDevice":{"iconPath":"/uploads/xincheng3D/img/currentPosition.png"},"hideObjects":["-LC","-LG","-LUX","-signage","-THVP","-valve","-waterPump","elevator","-ACP","-alarm","-distributionCabinets","-FA","-fireHorse","-KTWH","-WJ62P","-Camera","-125Q","-56F","outside2"],"deviceSummaryTypes":[{"name":"空调内机","icon":"/uploads/ibms/icon/ds_airCondition.png","deviceTypes":["125Q","ZNSF","CGPA"]},{"name":"空调外机","icon":"/uploads/ibms/icon/ds_airCondition2.png","deviceTypes":["ZNSB","ZNSF","CGPA"]},{"name":"照明","icon":"/uploads/ibms/icon/ds_light.png","deviceTypes":[]},{"name":"供水","icon":"/uploads/ibms/icon/ds_waterSupply.png","deviceTypes":["CGPA"]},{"name":"排水","icon":"/uploads/ibms/icon/ds_waterDrainage.png","deviceTypes":["CGPA"]},{"name":"大型电器","icon":"/uploads/ibms/icon/ds_hpe.png","deviceTypes":["ZNSB","ZNSF"]}],"securitySummaryTypes":[{"name":"电器火灾","icon":"/uploads/ibms/icon/ds_device2.png","deviceTypes":["125Q","ZNSB","ZNSF","CGPA"]},{"name":"浸水监测","icon":"/uploads/ibms/icon/ds_immersionWater.png","deviceTypes":["ZNSB","ZNSF","CGPA"]},{"name":"消防水压","icon":"/uploads/ibms/icon/ds_fireWater.png","deviceTypes":["ZNSB","ZNSF","CGPA"]},{"name":"幕墙玻璃","icon":"/uploads/ibms/icon/ds_wallGlass.png","deviceTypes":["ZNSB","ZNSF","CGPA"]}],"environmentSummaryTypes":[{"name":"空气质量","icon":"/uploads/ibms/icon/ds_airQuality.png","deviceTypes":["ZNSB","ZNSF","CGPA"]},{"name":"光照度","icon":"/uploads/ibms/icon/ds_illuminance.png","deviceTypes":["ZNSB","ZNSF","CGPA"]},{"name":"温湿度","icon":"/uploads/ibms/icon/ds_temperatureHumidity.png","deviceTypes":["125Q","ZNSB","ZNSF","CGPA"]}],"maintenanceSummaryTypes":[{"name":"资产","icon":"/uploads/ibms/icon/ds_asset.png","deviceTypes":["125Q","ZNSB","ZNSF","CGPA"]},{"name":"能源","icon":"/uploads/ibms/icon/ds_energy.png","deviceTypes":["ZNSB","ZNSF","CGPA"]},{"name":"设备","icon":"/uploads/ibms/icon/ds_device.png","deviceTypes":["ZNSB","ZNSF","CGPA"]},{"name":"安全","icon":"/uploads/ibms/icon/ds_security.png","deviceTypes":["ZNSB","ZNSF","CGPA"]},{"name":"环境","icon":"/uploads/ibms/icon/ds_environment.png","deviceTypes":["ZNSB","ZNSF","CGPA"]}],"maintenanceInfo":[{"name":"设备保养","icon":"/uploads/ibms/icon/maintenance_info_icon1.png","types":["日常维护","预见性维修","计划性维修","设备改进性维修"]},{"name":"日常巡检","icon":"/uploads/ibms/icon/maintenance_info_icon2.png","types":["日常巡检"]},{"name":"保养计划","icon":"/uploads/ibms/icon/maintenance_info_icon3.png","types":["保养计划"]},{"name":"报修记录","icon":"/uploads/ibms/icon/maintenance_info_icon4.png","types":["紧急维修"]}]}


  3D设备位置配置 d_device_resource_map 表
    other_data -> x,y,z


## 用能配置
  e_device
    type 能耗类型 英文
    unreal 是否是虚拟表，1代表虚拟表
    note 如果是虚拟表，则需要添加计算规则 eg: {30001}+{30002}   括号内为实体表id号
    remark 如果填 collector 代表用能表的采集器，计算设备在离线用，匹配到 d_device.communicateStatus 在离线状态
    max_diff 脏数据过滤配置
  e_device_group
    code  6位数字组成: 01 01 01, 代表三个级的序号
    type  分类大类型(能耗分项,组织机构,建筑区域,用户自定义)，数字类型
    note  一级类标记，subOption: 分项;  partition: 分区; customize: 自定义;
    is_main 一级类标记,且一种能耗类型只要一个Y,  Y: 用来计算总用能

## 用能峰谷时段费率设置
  时段设置:
    字典管理中
      system_configs
        energy_electricity_ratios         配置默认时间
      building_config_1
        energy_electricity_ratios_2024    配置2024年全年生效配置
        energy_electricity_ratios_2024_08 配置2024年08月生效配置
      优先级从下往上，配置说明：
        energy_electricity_ratios 中的 electricity 对应用能类型
        energy_electricity_ratios_2024 中的 2024 对应2024年
        energy_electricity_ratios_2024_08 中的 2024_08 对应2024年8月
  费率设置:
    e_fee_policy 表
      rate_float 字段设置生效费率, 字典中的 key 需要跟时段匹配
      date 字段设置生效时间， eg: 2022-01-01,  按照用能类型中，最近一个时间生效


## 用能报表分摊配置说明
  路由: aggregateAllocation/BMYD   BMYD 对应字典
  组件: energy/dataView/report/hzql/aggregateAllocationSenior
  字典配置:
    e_energy_report_display
      BMYD(举例)
        pageTitle -- 页面标题
        energyType -- 用能类型 electricity/water

        totalRealGroupName -- 分摊总量1, eg: ["重工合计"]
        totalValGroupName --  分摊总量2, eg: ["分项用电", "光伏发电"]
        totalValGroupNameMap -- 分组名称转表格标题, eg: {"分项用电":"市电用电量","光伏发电":"光伏用电量"}

        groupName -- 包含所有子项的父分组名, eg: 部门用电
        formula -- 所有子分组对应的能表公式(计算表格数据), eg: {"总装":"{31001}+{31008}","转子":"{31002}+{31009}+{11244}", ...}
        deviceIds -- 所有涉及到的能表Id, eg: [31001,31008,11002,11003,11004,...]


## 爱柯迪大屏数据配置项
    能源管理
      工厂概况
        用能效率  字典 building_config_1.energyEfficiency
        亩均产值  字典 building_config_1.avgMuValue

      用能构成
        用电总量 昨日 字典 building_config_1.dbElectricityMunicipalYesterday
        用电总量 2023年度: 字典 building_config_1.dbElectricityMunicipal2023
        用电总量 2024年度: 字典 building_config_1.dbElectricityMunicipal2024
        用电总量 2025年度: 字典 building_config_1.dbElectricityMunicipal2025

        能源构成分摊比例(饼图) 2023年度: 字典 building_config_1.dbEnergySubOpts2023
        能源构成分摊比例(饼图) 2024年度: 字典 building_config_1.dbEnergySubOpts2024
        能源构成分摊比例(饼图) 2025年度: 字典 building_config_1.dbEnergySubOpts2025

        用能系统耗能分摊比例(饼图) 昨日: 字典 building_config_1.dbElectricitySubOptsYesterday
        用能系统耗能分摊比例(饼图) 2023年度: 字典 building_config_1.dbElectricitySubOpts2023
        用能系统耗能分摊比例(饼图) 2024年度: 字典 building_config_1.dbElectricitySubOpts2024
        用能系统耗能分摊比例(饼图) 2025年度: 字典 building_config_1.dbElectricitySubOpts2025

      虚拟电厂
        所有数据配置: 字典 building_config_1.nbakdEnergyDemandSummary

      中间大图
        所有数据: 字典 building_config_1.nbakdEnergyView

      系统水平
        所有数据: 字典 building_config_1.nbakdEnergySummary

      碳排统计
        去年数据:   字典 building_config_1.nbadkEnergyCarbonEmissionLastYear
        今年数据:   字典 building_config_1.nbadkEnergyCarbonEmissionYear

      节能降碳
        碳总量 = 用电总量 * 节能率 * 碳电比例

        用电总量 同用电构成配置
        碳电比例 字典 building_config_1.co2ElectricityRate
        碳气比例 字典 building_config_1.co2GasRate
        硫电比例 字典 building_config_1.so2ElectricityRate
        煤电比例 字典 building_config_1.coalElectricityRate
        烟电比例 字典 building_config_1.smokeElectricityRate
        树电比例 字典 building_config_1.treeElectricityRate
        树电比例 字典 building_config_1.treeElectricityRate

        节能率 2023年度: 字典 building_config_1.dbElectricitySaveAvgRate2023
        节能率 2024年度: 字典 building_config_1.dbElectricitySaveAvgRate2024
        节能率 2025年度: 字典 building_config_1.dbElectricitySaveAvgRate2025

        降碳分摊比例(饼图)2023年度: 字典 building_config_1.dbElectricitySaveCo2Opts2023
        降碳分摊比例(饼图)2024年度: 字典 building_config_1.dbElectricitySaveCo2Opts2024
        降碳分摊比例(饼图)2025年度: 字典 building_config_1.dbElectricitySaveCo2Opts2025

      碳资产统计
        2023年数据: 字典 building_config_1.nbadkEnergyCarbonAssetsSummary2023
        2024年数据: 字典 building_config_1.nbadkEnergyCarbonAssetsSummary2024
        2025年数据: 字典 building_config_1.nbadkEnergyCarbonAssetsSummary2025

    虚拟电厂
      光伏出力: 字典 building_config_1.nbadkDemandPvPlant
      空调出力: 字典 building_config_1.nbadkDemandAirCondition
      主要设备处理: 字典 building_config_1.nbadkDemandDevice
      储能出力: 字典 building_config_1.nbadkDemandStorage
      参与概况: 字典 building_config_1.nbadkDemandSummary

      可调负荷
        最大可调负荷: 字典 building_config_1.dbDemandAdjustable
        节能分摊比例: 字典 building_config_1.dbDemandAdjustableSubOpts

      中间大图
        顶部统计: 字典 building_config_1.dbDemandStatistics
        曲线数据: 字典 building_config_1.dbDemandLineData

    虚拟电厂内页
      生产计划列表
        统计数据: 字典 building_config_1.nbadkDeviceProductPlantSummaryData
        表格数据: 字典 building_config_1.nbadkDeviceProductPlantTableList
      设备响应列表(device/nbakd/demandList)
        统计数据: 字典 building_config_1.nbadkDemandListSummaryData
        表格数据: 字典 building_config_1.nbadkDemandListTableList
      设备响应详情(device/nbakd/demandView)
        调节出力: 字典 building_config_1.nbadkDemandAdjustChartData
        事件概况: 字典 building_config_1.nbadkDemandViewEventDetail
        订单响应: 字典 building_config_1.nbadkDemandViewOrderResponse

        注: 下面部分同【虚拟电厂】部分
        中间大图
          曲线数据: 字典 building_config_1.dbDemandLineData
        光伏出力
          数据配置: 字典 building_config_1.nbadkDemandPvPlant
        空调出力
          数据配置: 字典 building_config_1.nbadkDemandAirCondition
        主要设备处理
          数据配置: 字典 building_config_1.nbadkDemandDevice
        储能出力
          数据配置: 字典 building_config_1.nbadkDemandStorage

    设备管理
      设备数据: 字典 building_config_1.nbakdDeviceList

    设备管理内页
      空压机
        图表数据: 字典 building_config_1.nbadkDeviceCompressorDiagramData
        表格数据: 字典 building_config_1.nbadkDeviceCompressorTableData
        右上统计: 字典 building_config_1.nbadkDeviceCompressorSummaryData
        绿色低碳: 字典 building_config_1.nbadkDeviceCompressorGreenData
        供气数据: 字典 building_config_1.nbadkDeviceCompressorAirSupplyData
        露点数据: 字典 building_config_1.nbadkDeviceCompressorDewPointData
        事件窗口: 字典 building_config_1.nbadkDeviceCompressorEventList

      空调
        设备数据: 字典 building_config_1.nbadkDeviceAirConditionData
          id: null,    //id(不能重复)
          name:"",     // 名字
          returnAirTemperature: 0,  // 回风温度
          waterValveFeedback:0,  // 水阀反馈
          workStatus:0,    // 启停，运行状态
          mode:0,          // 冬季
          temperature:0,   // 温度设定
          waterValveStatus:0,// 水阀控制模式
          waterValveOpen:0,    // 水阀开度
          runTime:0,           // 运行时间
          warningStatus:0,     // 故障报警状态
          autoControlStatus:0, // 手自动状态
          pressSafeStatus:0,   // 风机压差状态
          energySavingRate:0,  // 节能率
          energySavingTotal:0, // 累计节能
          energySavingTotalFee:0,    // 累计节约费用
          energySavingTotalCarbon:0, // 累计降碳
        事件窗口: 字典 building_config_1.nbadkDeviceAirConditionEventList

      冷水机组
        图表数据: 字典 building_config_1.nbadkDeviceChillerDiagramData
          // 主图数据
          main: {
            // 主图数据说明
            // 分水器
            waterDistributorName: "分水器",
            waterDistributorIcon: "P",
            waterDistributorValue: 3.5,
            // 集水器
            manifoldName: "集水器",
            manifoldIcon: "P",
            manifoldValue: 3.5,
            // 冷冻回水管1
            freezingReturnPipe1Name: "冷冻回水管",
            freezingReturnPipe1Icon: "P",
            freezingReturnPipe1Value: "3.5",
            freezingReturnPipe1Icon2: "T",
            freezingReturnPipe1Value2: "11.2",
            // 冷冻回水管2
            freezingReturnPipe2Name: "冷冻回水管",
            freezingReturnPipe2Icon: "P",
            freezingReturnPipe2Value: "3.5",
            freezingReturnPipe2Icon2: "T",
            freezingReturnPipe2Value2: "11.2",
            // 冷冻供水管
            freezingSupplyPipeName: "冷冻供水管",
            freezingSupplyPipeIcon: "P",
            freezingSupplyPipeValue: "3.5",
            freezingSupplyPipeIcon2: "T",
            freezingSupplyPipeValue2: "11.5",
            // 离心机
            centrifugeName: "离心机",
            centrifugeValue: "80",
            // 8台冷却塔
            coolingTower: "8台 冷却塔",
          },
          env: [],  // 右上角环境数据
          device: [],  // 左下角设备数据

        效能数据: 字典 building_config_1.nbadkDeviceChillerEfficiencyData
          todayCop: 4,       // 今日 cop
          today: "03.17",    // 今日日期
          todayEnergy: 32,   // 今日耗电量
          todayCool: 32,     // 今日制冷量
          todayRate: 0.6,    // 今日效能比
          chart: {
            xAxis: [], // 趋势 x 轴
            trendsData: [],  // 趋势值
          }

        事件窗口: 字典 building_config_1.nbadkDeviceChillerEventList
        节能控制: 字典 building_config_1.nbadkDeviceChillerEnergySavingData

      余热管理
        设备数据: 字典 building_config_1.nbadkDeviceWasteHeatDiagramData
        重点数据: 字典 building_config_1.nbadkDeviceWasteHeatImportantData
        余热回收: 字典 building_config_1.nbadkDeviceWasteHeatRecoveryData
        出水温度: 字典 building_config_1.nbadkDeviceWasteHeatHotWaterData

      储能系统
        基本信息: 字典 building_config_1.nbadkDeviceEnergyStorageBaseData
          strategy: 充放电策略
          base:     基本信息
          system：  系统运行状态
        事件窗:   字典 building_config_1.nbadkDeviceEnergyStorageEventList

        储能功率曲线:     字典 building_config_1.nbadkDeviceEnergyStorageChart1Data
        PCS平均温度曲线:  字典 building_config_1.nbadkDeviceEnergyStorageChart2Data
        电堆平均SOC曲线:  字典 building_config_1.nbadkDeviceEnergyStorageChart3Data
        电堆平均温度曲线: 字典 building_config_1.nbadkDeviceEnergyStorageChart4Data

        电量指标曲线: 字典 building_config_1.nbadkDeviceEnergyStorageChart5Data
          chargingCapacity0           充电(近七日)
          dischargeCapacity0          放电(近七日)
          chargeDischargePercentage0  充放比例(近七日)
          chargingCapacity1           充电(当月)
          dischargeCapacity1          放电(当月)
          chargeDischargePercentage1  充放比例(当月)

      光伏系统
        电站数据: 字典 building_config_1.nbadkDevicePvPlantData
          // 中间顶部
          infoList: []
          // 中间底部
          summaryList: []
          // 中间数据
          powerUnit: "kW",                 // 功率单位
          inputPower: "286.65",           // 逆变器输入功率
          outputPower: "276.65",          // 逆变器输出功率
          gridConnOutputPower: "266.65",  // 并网柜功率
          energyUnit: "MWh",                 // 发电量单位
          inverterEnergyDaily: "3.31",        // 逆变器日电量
          gridConnCabinetEnergyDaily: "3.29", // 并网柜日电量
        功率曲线: 字典 building_config_1.nbadkDevicePvPlantChart1Data
        发电量曲线: 字典 building_config_1.nbadkDevicePvPlantChart2Data
        事件窗口: 字典 building_config_1.nbadkDevicePvPlantEventList

## 公共key
  天气接口, 数据源设置 building_config_1.weather_data_source 设置 某个气象站设备ID
    气象站设备数据点名 温度/湿度/气压/风速

  全局通知设置, 数据源设置building_config_1.global_notice
    eg: [{"type":"warning","title":"请及时更新电费","content":"请及时更新电费","condition":{"year":"","month":"06","day":"07","hour":"18","minute":""}},{"type":"warning","title":"请及时更新峰谷电时间","content":"请及时更新峰谷电时间","condition":{"year":"","month":"06","day":"07","hour":"18","minute":""}}]
  全局报警设置, 数据源设置building_config_1.global_warning
    eg: {
      "warningShow": true,    // 是否启用
      "alarmVoiceOpen": true, // 打开声音
      "timeSpan": 3000,       // 轮询间隔
      "severity": "一级,二级,三级,离线",   // 监控的级别类型
      "alarm": "一级",        // 红色报警对应的级别
      "notice": "二级,三级",  // 橙色报警对应的级别
      "offline": "离线"       // 黄色报警对应的级别
    }

  电站当年指标(杭汽轮大屏右上角) building_config_1.screen_energy_index 读取 buildingSummaryData 接口，根据 keys 组合成 total 值
    eg: [{"type":"electricity","name":"用电","index":"2381","class":"cb","keys":["electricity","pvPlant"],"total":0,"unit":""},{"type":"water","name":"用水","index":"22002","class":"cgr","keys":["water"],"total":0,"unit":""},{"type":"gas","name":"用气","index":"18564","class":"cy","keys":["gas"],"total":0,"unit":""},{"type":"carbon","name":"碳排放指标","index":"19871","class":"cgy","keys":["electricity","gas"],"total":0,"unit":""}]

  用能大屏中下，多种能耗折线图 building_config_1.energy_summary2_type_list
    组件 @/views/components/cO2View/components/common/EnergySummary2
    eg: [{"name":"用电(kWh)","icon":"/image/energy_consumption_statistics_1.png","keys":["分项用电","光伏发电"],"value":0},{"name":"燃气(m³)","icon":"/image/energy_consumption_statistics_2.png","keys":["分项用气"],"value":0},{"name":"用水(吨)","icon":"/image/energy_consumption_statistics_3.png","keys":["分项用水"],"value":0},{"name":"光伏(kWh)","icon":"/image/energy_consumption_statistics_4.png","keys":["光伏总发电量"],"value":0}]

  节能大屏，左侧节能统计实测数据配置 building_config_1.energy_save_measured_electricity
    eg: {"optimize":[{"totalVal":314.2,"recordedAt":"2024-04-01"},{"totalVal":2105.98,"recordedAt":"2024-04-02"},{"totalVal":937.46,"recordedAt":"2024-04-03"},{"totalVal":1695.26,"recordedAt":"2024-04-04"},{"totalVal":1630,"recordedAt":"2024-04-05"},{"totalVal":1858.28,"recordedAt":"2024-04-06"},{"totalVal":2229.87,"recordedAt":"2024-04-07"},{"totalVal":1020.23,"recordedAt":"2024-04-08"},{"totalVal":1925.26,"recordedAt":"2024-04-09"},{"totalVal":5.31,"recordedAt":"2024-04-10"},{"totalVal":665.55,"recordedAt":"2024-04-11"},{"totalVal":1771.51,"recordedAt":"2024-04-12"},{"totalVal":67.19,"recordedAt":"2024-04-13"},{"totalVal":246.52,"recordedAt":"2024-04-14"},{"totalVal":1948.64,"recordedAt":"2024-04-15"},{"totalVal":1923.19,"recordedAt":"2024-04-16"},{"totalVal":1420.65,"recordedAt":"2024-04-17"},{"totalVal":494.11,"recordedAt":"2024-04-18"},{"totalVal":295.8,"recordedAt":"2024-04-19"},{"totalVal":259.69,"recordedAt":"2024-04-20"},{"totalVal":879.09,"recordedAt":"2024-04-21"},{"totalVal":577.28,"recordedAt":"2024-04-22"},{"totalVal":916.88,"recordedAt":"2024-04-23"},{"totalVal":315.35,"recordedAt":"2024-04-24"},{"totalVal":1054.04,"recordedAt":"2024-04-25"},{"totalVal":193.33,"recordedAt":"2024-04-26"},{"totalVal":1729.15,"recordedAt":"2024-04-27"},{"totalVal":397.39,"recordedAt":"2024-04-28"},{"totalVal":1331.15,"recordedAt":"2024-04-29"},{"totalVal":1834.3,"recordedAt":"2024-04-30"}],"unoptimized":[{"totalVal":385.9,"recordedAt":"2024-04-01"},{"totalVal":2498.62,"recordedAt":"2024-04-02"},{"totalVal":1171.75,"recordedAt":"2024-04-03"},{"totalVal":2065.46,"recordedAt":"2024-04-04"},{"totalVal":1920.14,"recordedAt":"2024-04-05"},{"totalVal":2206.59,"recordedAt":"2024-04-06"},{"totalVal":2764.35,"recordedAt":"2024-04-07"},{"totalVal":1197.56,"recordedAt":"2024-04-08"},{"totalVal":2237.55,"recordedAt":"2024-04-09"},{"totalVal":6.61,"recordedAt":"2024-04-10"},{"totalVal":826.65,"recordedAt":"2024-04-11"},{"totalVal":2144.1,"recordedAt":"2024-04-12"},{"totalVal":81.92,"recordedAt":"2024-04-13"},{"totalVal":303.43,"recordedAt":"2024-04-14"},{"totalVal":2405.36,"recordedAt":"2024-04-15"},{"totalVal":2359.91,"recordedAt":"2024-04-16"},{"totalVal":1751.52,"recordedAt":"2024-04-17"},{"totalVal":584.48,"recordedAt":"2024-04-18"},{"totalVal":344.05,"recordedAt":"2024-04-19"},{"totalVal":299.15,"recordedAt":"2024-04-20"},{"totalVal":1055.51,"recordedAt":"2024-04-21"},{"totalVal":715.38,"recordedAt":"2024-04-22"},{"totalVal":1056.82,"recordedAt":"2024-04-23"},{"totalVal":369.48,"recordedAt":"2024-04-24"},{"totalVal":1237.1,"recordedAt":"2024-04-25"},{"totalVal":223.2,"recordedAt":"2024-04-26"},{"totalVal":1997.26,"recordedAt":"2024-04-27"},{"totalVal":480.98,"recordedAt":"2024-04-28"},{"totalVal":1645.81,"recordedAt":"2024-04-29"},{"totalVal":2110.69,"recordedAt":"2024-04-30"}]}

  大屏设备模块配置 building_config_1.screenDevice  读取配置  可根据最外层键名语义知道卡片配置所属位置，最外层键名后边加数字后缀为替补项，可切换使用
  eg: {"leftTop":{"title":"空调","component":"AirConditioning","strategy":["舒适模式","节能模式","手动模式"],"operationRate":{"icon":"/image/screen/airConditioning_powerOn.png","title":"设备开机率","height":"180px"},"deviceTypes":[{"name":"新风机组","icon":"/image/screen/airConditioning_fan.png","types":["FJSB","DirectStartAHU"],"summary":[{"name":"总数","unit":"台","color":"#2294fe","key":"total"},{"name":"运行","unit":"台","color":"#0dd3a3","key":"workStatusRun"},{"name":"空闲","unit":"台","color":"#00aa1b","key":"workStatusStop"},{"name":"故障","unit":"台","color":"#fcd869","key":"warningStatusWarn"}]},{"name":"风机盘管","icon":"/image/screen/airConditioning_freshAir.png","types":["SD_FJ"],"summary":[{"name":"总数","unit":"台","color":"#2294fe","key":"total"},{"name":"运行","unit":"台","color":"#0dd3a3","key":"workStatusRun"},{"name":"空闲","unit":"台","color":"#00aa1b","key":"workStatusStop"},{"name":"故障","unit":"台","color":"#fcd869","key":"warningStatusWarn"}]}]},"leftBtm":{"title":"照明","component":"Lighting","deviceTypes":[{"name":"公区照明","icon":"/image/screen/lighting_comm.png","types":["SNZM2"],"summary":[{"name":"总数","unit":"台","color":"#2294fe","key":"total"},{"name":"运行","unit":"台","color":"#0dd3a3","key":"workStatusRun"},{"name":"空闲","unit":"台","color":"#00aa1b","key":"workStatusStop"},{"name":"故障","unit":"台","color":"#fcd869","key":"warningStatusWarn"}],"hasPower":[{"name":"总功率","unit":"kW","key":"totalPower"},{"name":"功率密度","unit":"kW/㎡","key":"activePowerPerArea"},{"name":"当前功率","unit":"kW","key":"activePower"}]},{"name":"室内照明","icon":"/image/screen/lighting_comm.png","types":["SNZM2"],"summary":[{"name":"总数","unit":"台","color":"#2294fe","key":"total"},{"name":"运行","unit":"台","color":"#0dd3a3","key":"workStatusRun"},{"name":"空闲","unit":"台","color":"#00aa1b","key":"workStatusStop"},{"name":"故障","unit":"台","color":"#fcd869","key":"warningStatusWarn"}],"hasPower":[{"name":"总功率","unit":"kW","key":"totalPower"},{"name":"功率密度","unit":"kW/㎡","key":"activePowerPerArea"},{"name":"当前功率","unit":"kW","key":"activePower"}]}]},"rightTop":{"title":"送排风机","component":"SupplyExhaustFan","strategy":["舒适模式","节能模式","手动模式"],"operationRate":{"icon":"/image/screen/airConditioning_powerOn.png","title":"设备开机率","height":"170px"},"lineIds":[3002807,3002707],"deviceTypes":[{"name":"送排风机","icon":"/image/screen/supplyExhaustFan.png","types":["SNZM2"],"summary":[{"name":"总数","unit":"台","color":"#2294fe","key":"total"},{"name":"运行","unit":"台","color":"#0dd3a3","key":"workStatusRun"},{"name":"空闲","unit":"台","color":"#00aa1b","key":"workStatusStop"},{"name":"故障","unit":"台","color":"#fcd869","key":"warningStatusWarn"}],"hasPower":[{"name":"总功率","unit":"kW","key":"totalPower"},{"name":"当前功率","unit":"kW","key":"activePower"}]}]},"rightBtm5":{"title":"电梯","component":"Elevator","deviceTypes":[{"name":"电梯","icon":"/image/screen/elevator.png","types":["BPD2"],"summary":[{"name":"总数","unit":"台","color":"#2294fe","key":"total"},{"name":"故障","unit":"台","color":"#fcd869","key":"warningStatusWarn"}]}],"elevatorMaintenanceRemainder":{"title":"距离保养还剩","value":124,"icon":"/image/screen/elevator_maintenance.png","unit":"天"},"elevatorTableData":{"columns":[{"code":"name","label":"电梯名称","width":120},{"code":"level","label":"楼层","width":60},{"code":"status","label":"运行状态"},{"code":"maintenanceStatus","label":"维护状态"}],"data":[{"id":0,"name":"货梯","level":"01","status":"上行","maintenanceStatus":"正常"},{"id":1,"name":"电梯A","level":"01","status":"上行","maintenanceStatus":"正常"}]}},"rightBtm4":{"title":"大型电器","component":"LargeEectrical","operationRate":{"icon":"/image/screen/airConditioning_powerOn.png","title":"设备开机率","height":"180px"},"deviceTypes":[{"name":"大型电器","icon":"/image/screen/supplyExhaustFan.png","types":["cabinetScrollUnit"],"summary":[{"name":"总数","unit":"台","color":"#2294fe","key":"total"},{"name":"运行","unit":"台","color":"#0dd3a3","key":"workStatusRun"},{"name":"空闲","unit":"台","color":"#00aa1b","key":"workStatusStop"},{"name":"故障","unit":"台","color":"#fcd869","key":"warningStatusWarn"}],"hasPower":[{"name":"总功率","unit":"kW","key":"totalPower"},{"name":"当前功率","unit":"kW","key":"activePower"}]}]},"rightBtm3":{"title":"水冷机组","component":"WaterCooling","deviceTypes":[{"name":"冷机","icon":"/image/screen/waterCooling_cooler.png","types":["COOL"],"summary":[{"name":"总数","unit":"台","color":"#2294fe","key":"total"},{"name":"运行","unit":"台","color":"#0dd3a3","key":"workStatusRun"},{"name":"空闲","unit":"台","color":"#00aa1b","key":"workStatusStop"},{"name":"故障","unit":"台","color":"#fcd869","key":"warningStatusWarn"}]},{"name":"冷机","icon":"/image/screen/waterCooling_cooler.png","types":["COOL"],"summary":[{"name":"总数","unit":"台","color":"#2294fe","key":"total"},{"name":"运行","unit":"台","color":"#0dd3a3","key":"workStatusRun"},{"name":"空闲","unit":"台","color":"#00aa1b","key":"workStatusStop"},{"name":"故障","unit":"台","color":"#fcd869","key":"warningStatusWarn"}]},{"name":"冷机","icon":"/image/screen/waterCooling_cooler.png","types":["COOL"],"summary":[{"name":"总数","unit":"台","color":"#2294fe","key":"total"},{"name":"运行","unit":"台","color":"#0dd3a3","key":"workStatusRun"},{"name":"空闲","unit":"台","color":"#00aa1b","key":"workStatusStop"},{"name":"故障","unit":"台","color":"#fcd869","key":"warningStatusWarn"}]}],"waterCoolingTemperature":[{"deviceId":"30027","itemDataId":"3002707","name":"冷冻泵"}]},"rightBtm":{"title":"视频监控","component":"VideoMonitoring","deviceTypes":[{"name":"视频监控","types":["camera"],"summary":[{"name":"设备总数(台)","class":"bubble_1","icon":"/image/screen/videoMonitoring_bubbles_1.png","key":"total"},{"name":"设备在线(台)","class":"bubble_2","icon":"/image/screen/videoMonitoring_bubbles_2.png","key":"communicateStatusOnline"},{"name":"设备离线(台)","class":"bubble_3","icon":"/image/screen/videoMonitoring_bubbles_3.png","key":"communicateStatusOffline"}]}]}}

大屏节能模块配置 building_config_1.screenEnergySave  读取配置
  {"electricitySummary":[{"name":"本日总用电量","icon":"/image/screen/electricity_summary.png","unit":"kWh","key":"electricitytoday","source":{"type":"energy","energyType":"electricity"}},{"name":"本月总用电量","icon":"/image/screen/electricity_summary.png","unit":"kWh","key":"electricitycurMonth","source":{"type":"energy","energyType":"electricity"}},{"name":"本月累计节电量","icon":"/image/screen/electricity_summary.png","unit":"kWh","key":"gascurMonth","source":{"type":"energy","energyType":"gas"}},{"name":"当前实时总功率","icon":"/image/screen/electricity_summary.png","key":"totalPower","source":{"type":"device","params":{"deviceIds":"30001,30002,30003,30004,30005,30006,30007,30008,30009,30010","function":"sum","dataName":"总功率"}}}],"keyIds":"80101001,80101002,80101003,80101004,80101005,80101006,80101007,80101008","coolLoad":[{"deviceId":"30027","itemDataId":"3002707","name":"湿球温度","rgbColor":"34, 148, 254","unit":"℃"},{"deviceId":"30030","itemDataId":"3003007","name":"冷负荷","rgbColor":"240, 215, 137","unit":"kW"}],"coolWaterTemperature":[{"deviceId":"30027","itemDataId":"3002707","name":"1号机组","unit":"℃","rgbColor":"13, 211, 163"},{"deviceId":"30030","itemDataId":"3003007","name":"2号机组","unit":"℃","rgbColor":"13, 211, 163"}],"pumpFrequency":[{"deviceId":"30027","itemDataId":"3002707","name":"冷冻泵","unit":"HZ","rgbColor":"34, 184, 254"},{"deviceId":"30030","itemDataId":"3003007","name":"冷却泵","unit":"HZ","rgbColor":"34, 184, 254"}],"scop":[{"deviceId":"30027","itemDataId":"3002707","name":"scop","unit":"","rgbColor":"34, 148, 254"}],"bimData":{"url":"https://3d.dddtask.cn/enginner-xufeng/dh/scidh3dview-share/index.html?id=aVea8eOIfBYl0iJFKOdZ_w==","show":true},"energyConsumption":{"title":"系统总能耗","dateOptions":[{"label":"实时数据","value":"realTime"},{"label":"当日","value":"today"},{"label":"7日内","value":"halfMonth"}],"chartTypes":[{"value":"bar","label":"柱状图"},{"value":"line","label":"趋势图"}],"chartDataSum":{"unit":"kWh","chartData":[{"name":"非节能模式用电","data":[0,1901,0,1290,0,20,0],"rgbColor":"228, 87, 87"},{"name":"节能模式用电","data":[932,0,934,0,1330,0,1000],"rgbColor":"34, 148, 254"},{"name":"节电量","data":[100,0,234,0,330,0,100],"rgbColor":"142, 162, 185"}]},"analysis":{"title":"动态节能率","progress":{"percentage":15,"unit":"小时"},"proportion":{"title":"设备能耗占比","color":["#2294fe","#3CCCF9","#FAC980","#22B8FE","#9380f7"],"data":[{"value":35,"name":"制冷机组"},{"value":22,"name":"冷冻泵"},{"value":21,"name":"冷却泵"},{"value":22,"name":"冷却塔"},{"value":22,"name":"空调终端"}]}}}}


大屏节能模块配置 building_config_1.screen_menu_with_setting  大屏菜单显示，与旧的菜单字典区别，可设置菜单文字  hide字段为隐藏当前菜单
[{"code":"EnergyView","title":"首页"},{"code":"EnergySaveView","title":"控制中台"},{"code":"DemandView","title":"需求响应","hide":true},{"code":"DeviceView","title":"设备管理"},{"code":"SecurityView","title":"安全管理","hide":true},{"code":"DevopsView","title":"运维管理"}]


  设备定时刷新列表 system_configs.device_refresh_span
    eg: [{"value":null,"label":"不自动刷新数据"},{"value":2,"label":"每2秒刷新数据"},{"value":5,"label":"每5秒刷新数据"},{"value":10,"label":"每10秒刷新数据"},{"value":30,"label":"每30秒刷新数据","selected":true},{"value":60,"label":"每分钟刷新数据"},{"value":300,"label":"每5分钟刷新数据"},{"value":600,"label":"每10分钟刷新数据"}]
  报警页面定时刷新列表 system_configs.warning_refresh_span
    eg: [{"value":null,"label":"不自动刷新数据"},{"value":10,"label":"每10秒刷新数据"},{"value":30,"label":"每30秒刷新数据","selected":true},{"value":60,"label":"每分钟刷新数据"},{"value":300,"label":"每5分钟刷新数据"},{"value":600,"label":"每10分钟刷新数据"}]

  停车管理
    停车场分析曲线 building_config_1.parkingCarChartParams 存加密过的 sql
      eg: {"sql":["E/UEIboyrY3mHvVOOyKN6uljpXlAhHhs6WCT0Dj23336C9g8tqYUpZZboTDS8fFkieyIgoCJv8dNStjIsurSEg==","UqzwehYE+s8Lc/F2lrypxDiS60emSJTAzxz623Te/vVw15Uun7fGu+DaeduNnu0pSNO/5VCS733lZoQkcgBMag==",...],"chart":[{"key":"v","name":"车辆","unit":"辆","type":"line"}]}
    本月停车收费曲线 building_config_1.parkingFeeChartParams 存加密过的 sql
      eg: {"sql":["E/UEIboyrY3mHvVOOyKN6uljpXlAhHhs6WCT0Dj23336C9g8tqYUpZZboTDS8fFkieyIgoCJv8dNStjIsurSEg==","UqzwehYE+s8Lc/F2lrypxDiS60emSJTAzxz623Te/vVw15Uun7fGu+DaeduNnu0pSNO/5VCS733lZoQkcgBMag==",...],"chartType":"line","chart":[{"key":"v","name":"费用","unit":"元","type":"bar"}]}
    缴费记录表格

  公共折线图 views/components/cO2View/components/common/BaseSqlChart.vue
    接收3个参数 title, params, height。 其中 params 包含 sql, chart, data
      sql 为查询的数据语句（需要加密）
      chart 为图表定制参数
      data 为列表，每根线单独配置，目前支持 key(返回列表中某列名字),name(显示的曲线名),unit(显示单位),type(曲线类型)
  公共表格 views/components/cO2View/components/common/BaseSqlTable.vue
    接收3个参数 title, params, height。 其中 params 包含 sql, title
      sql 为查询的数据语句（需要加密）
      title 为表格列描述，filterable=1 代表可查询；type=date 代表是日期组件

## 标准版大屏配置 字典
  能耗大屏 screenEnergy

  节能大屏 screenEnergySave
    用电概览 screenEnergySave.electricitySummary
      多个独立指标配置
        name, icon, unit, key 对应显示
        source 对应数据源
          type energy 用能数据: buildingSummaryData;  device 设备数据: deviceDataSummary
          params: 接口参数
    关键指标 screenEnergySave.keyIds 只用配置需要显示的数据点id, d_device_item_data_map.item_data_id
    冷负荷趋势曲线 screenEnergySave.coolLoad
      多个独立曲线
        deviceId, itemDataId 对应曲线, name 对应前端显示名称(跟实际数据不一定一致)
    冷冻水出水温度 screenEnergySave.coolWaterTemperature 同上
    循环泵频率 screenEnergySave.pumpFrequency 同上
    动态scop screenEnergySave.scop 同上
    eg: {
      "electricitySummary": [
        {
          "name": "本日总用电量",
          "icon": "/image/screen/electricity_summary.png",
          "unit": "kWh",
          "key": "electricitytoday",
          "source": {
            "type": "energy",
            "energyType": "electricity",
          }
        },
        {
          "name": "本月总用电量",
          "icon": "/image/screen/electricity_summary.png",
          "unit": "kWh",
          "key": "electricitycurMonth",
          "source": {
            "type": "energy",
            "energyType": "electricity",
          }
        },
        {
          "name": "本月累计节电量",
          "icon": "/image/screen/electricity_summary.png",
          "unit": "kWh",
          "key": "gascurMonth",
          "source": {
            "type": "energy",
            "energyType": "gas",
          }
        },
        {
          "name": "当前实时总功率",
          "icon": "/image/screen/electricity_summary.png",
          "key": "totalPower",
          "source": {
            "type": "device",
            "params": {
              "deviceIds": "30001,30002,30003,30004,30005,30006,30007,30008,30009,30010",
              "function": "sum",
              "dataName": "总功率",
            },
          }
        }
      ],
      "keyIds": "80101001,80101002,80101003,80101004,80101005,80101006,80101007,80101008",
      "coolLoad": [
        {
          "deviceId": "30027",
          "itemDataId": "3002707",
          "name": "1号机组",
        },
        {
          "deviceId": "30030",
          "itemDataId": "3003007",
          "name": "2号机组",
        },
        {
          "deviceId": "30028",
          "itemDataId": "3002807",
          "name": "3号机组",
        }
      ],
      "coolWaterTemperature": [
        {
          "deviceId": "30027",
          "itemDataId": "3002707",
          "name": "1号机组",
        },
        {
          "deviceId": "30030",
          "itemDataId": "3003007",
          "name": "2号机组",
        },
      ],
      "pumpFrequency": [
        {
          "deviceId": "30027",
          "itemDataId": "3002707",
          "name": "冷冻泵",
        },
        {
          "deviceId": "30030",
          "itemDataId": "3003007",
          "name": "冷却泵",
        },
      ],
      "scop": [
        {
          "deviceId": "30027",
          "itemDataId": "3002707",
          "name": "scop",
        }
      ],
    }

  需求响应 screenDemand

  设备大屏 screenDevice
    配置左右两侧信息
      component 对应显示的组件
      strategy  对应快捷控制策略
      operationRate 对应显示的开机率曲线图
      deviceTypes 对应多个子设备列表统计
        name,icon 对应显示
        types     对应哪些子设备统计
        summary   对应如何统计，已经统计如何显示
        hasPower  对应是否显示功率统计数据
    eg: {
      "leftTop": {
        "title": "空调",
        "component": "AirConditioning",
        "strategy": ["舒适模式","节能模式","手动模式"],
        "operationRate": {
          "icon": "/image/screen/airConditioning_powerOn.png",
          "title": "设备开机率",
          "height": "180px"
        },
        "deviceTypes": [{
          "name": "新风机组",
          "icon": "/image/screen/airConditioning_fan.png",
          "types": ["FJSB", "DirectStartAHU"],
          "summary": [
            {
              "name": "总数",
              "unit": "台",
              "color":"#2294fe",
              "key": "total",
            },
            {
              "name": "运行",
              "unit": "台",
              "color":"#0dd3a3",
              "key": "workStatusRun",
            },
            {
              "name": "空闲",
              "unit": "台",
              "color":"#00aa1b",
              "key": "workStatusStop",
            },
            {
              "name": "故障",
              "unit": "台",
              "color":"#fcd869",
              "key": "warningStatusWarn",
            }
          ],
        }, {
          "name": "风机盘管",
          "icon": "/image/screen/airConditioning_freshAir.png",
          "types": ["SD_FJ"],
          "summary": [
            {
              "name": "总数",
              "unit": "台",
              "color":"#2294fe",
              "key": "total",
            },
            {
              "name": "运行",
              "unit": "台",
              "color":"#0dd3a3",
              "key": "workStatusRun",
            },
            {
              "name": "空闲",
              "unit": "台",
              "color":"#00aa1b",
              "key": "workStatusStop",
            },
            {
              "name": "故障",
              "unit": "台",
              "color":"#fcd869",
              "key": "warningStatusWarn",
            }
          ],
        }]
      },
      "leftBtm": {
        "title":"照明",
        "component": "Lighting",
        "deviceTypes": [
          {
            "name": "公区照明",
            "icon": "/image/screen/lighting_comm.png",
            "types": ["SNZM2"],
            "summary": [
            {
              "name": "总数",
              "unit": "台",
              "color":"#2294fe",
              "key": "total",
            },
            {
              "name": "运行",
              "unit": "台",
              "color":"#0dd3a3",
              "key": "workStatusRun",
            },
            {
              "name": "空闲",
              "unit": "台",
              "color":"#00aa1b",
              "key": "workStatusStop",
            },
            {
              "name": "故障",
              "unit": "台",
              "color":"#fcd869",
              "key": "warningStatusWarn",
            }
          ],
            "hasPower": [
              {
                "name": "总功率",
                "unit": "kW",
                "key": "totalPower",
              },
              {
                "name": "功率密度",
                "unit": "kW/㎡",
                "key": "activePowerPerArea",
              },
              {
                "name": "当前功率",
                "unit": "kW",
                "key": "activePower",
              }
            ],
          },
          {
            "name": "室内照明",
            "icon": "/image/screen/lighting_comm.png",
            "types": ["SNZM2"],
            "summary": [
            {
              "name": "总数",
              "unit": "台",
              "color":"#2294fe",
              "key": "total",
            },
            {
              "name": "运行",
              "unit": "台",
              "color":"#0dd3a3",
              "key": "workStatusRun",
            },
            {
              "name": "空闲",
              "unit": "台",
              "color":"#00aa1b",
              "key": "workStatusStop",
            },
            {
              "name": "故障",
              "unit": "台",
              "color":"#fcd869",
              "key": "warningStatusWarn",
            }
          ],
            "hasPower": [
              {
                "name": "总功率",
                "unit": "kW",
                "key": "totalPower",
              },
              {
                "name": "功率密度",
                "unit": "kW/㎡",
                "key": "activePowerPerArea",
              },
              {
                "name": "当前功率",
                "unit": "kW",
                "key": "activePower",
              }
            ],
          },
        ]
      },
      "rightTop": {
        "title":"送排风机",
        "component": "SupplyExhaustFan",
        "strategy": ["舒适模式","节能模式","手动模式"],
        "operationRate": {
          "icon": "/image/screen/airConditioning_powerOn.png",
          "title": "设备开机率",
          "height": "170px"
        },
        "lineIds": [3002807, 3002707],
        "deviceTypes": [
          {
            "name": "送排风机",
            "icon": "/image/screen/supplyExhaustFan.png",
            "types": ["SNZM2"],
            "summary": [
            {
              "name": "总数",
              "unit": "台",
              "color":"#2294fe",
              "key": "total",
            },
            {
              "name": "运行",
              "unit": "台",
              "color":"#0dd3a3",
              "key": "workStatusRun",
            },
            {
              "name": "空闲",
              "unit": "台",
              "color":"#00aa1b",
              "key": "workStatusStop",
            },
            {
              "name": "故障",
              "unit": "台",
              "color":"#fcd869",
              "key": "warningStatusWarn",
            }
          ],
            "hasPower": [
              {
                "name": "总功率",
                "unit": "kW",
                "key": "totalPower",
              },
              {
                "name": "当前功率",
                "unit": "kW",
                "key": "activePower",
              }
            ],
          },
        ]
      },
      "rightBtm": {
        "title":"视频监控",
        "component": "VideoMonitoring",
        "deviceTypes": [
          {
            "name": "视频监控",
            "types": ["SB"],
            "summary": [{
              "name": "设备总数(台)",
              "class": "bubble_1",
              "icon": "/image/screen/videoMonitoring_bubbles_1.png",
              "key": "total"
            }, {
              "name": "设备在线(台)",
              "class": "bubble_2",
              "icon": "/image/screen/videoMonitoring_bubbles_2.png",
              "key": "communicateStatusOnline"
            }, {
              "name": "设备离线(台)",
              "class": "bubble_3",
              "icon": "/image/screen/videoMonitoring_bubbles_3.png",
              "key": "communicateStatusOffline"
            }]
          },
        ]
      }
    }

    安全大屏 screenSecurity

    运维大屏 screenDevops

### 大华icc开放平台数据接入 景德镇鱼山码头
  运行脚本:
    node src/iccSpider.js [buildingId] [dbName] [debug]
  设备在 a_item 里面有例子, a_item_data 的 name 固定不能变, alias 可以随便填
    330001  -> 门禁
    333001  -> 停车场
    336001  -> 报警机
  对应配置:
    字典 building_config_[buildingId] 中增加配置
        icc_server                    api 地址 eg: https://172.16.10.5
        icc_username                  用户名
        icc_password                  密码明文
        icc_grantType                 登录模式 eg: password
        icc_clientId                  申请的用户ID eg: ibms1
        icc_clientSecret              申请的用户Key eg: 05dc30af-50e7-4846-8469-b5bb2a81e682
        icc_timeout                   api 请求超时(毫秒) eg: 10000
        icc_apiVersion                针对大部分接口的版本, eg: 1.0.0
        icc_orgCode                   组织编号 eg: 01
        icc_accessControlCollectorId  门禁下发指令时 对应采集器编号

### 科斯曼能耗平台数据接入 常州领航大厦能耗接口封装
  运行脚本:
    node src/ecsSpider.js [buildingId] [dbName] [debug]
  注: a_point_table.func_list  对应数据点编码 eg: 40001 -> 当前冷量
  设备在 a_item 里面有例子
    34001 -> 水表
    34101 -> 电表
    34201 -> 冷热表
  对应配置:
    字典 building_config_[buildingId] 中增加配置
        ksmecs_server                 api 地址 eg: http://************:8002
        ksmecs_collectorId            对应采集器ID

### bacnet 数据接入
  项目 conn-bacnet
  application.yml 配置:
    bacnet:
      enabledJob: true        // 启用定时任务
      enabledRead: true       // 启动读任务
      enabledWrite: true      // 启动写任务
      localIp: *************  // 当前运行主机IP
      subnet: *************   // 网络中掩码，如果扫描不到，可以试试 0.0.0.0
      udpPort: 47808          // udp端口号
      instanceNum: 911        // 当前运行启动bacnet客户端 设备ID, 可随便设置(跟真实重复也无所谓)
      collectorIds: 12        // bacnet对应采集器ID, 可多个, 逗号分隔

  对应数据库配置
  a_item
    .code                   // 非必填
  a_item_data               // 正常配置, 读写点分开
  a_point_table
    .collector_id           // 采集ID
    .device_id              // 设备ID, yabe连接后, 设备描述后面的 [] 中的数字
    .addr                   // 点位Instance ID(必填), yabe 连接后, 点位描述后面的数字
    .func                   // 点位数据类型(必填), 目前支持 analog-value, analog-input, analog-output, binary-value, binary-input, binary-output, characterstring-value, multi-state-value, multi-state-input, multi-state-output
    .data_group             // 点位的优先级(非必填), 只能填写 0 ~ 15 之间的数字, 不填为默认15

### 采集端说明
1. 采集器在线检查脚本
      node src/ipDeviceCheck.js 65 ly_jx_xc debug
    对应数据库配置:
      1. a_item 增加采集器设备
      2. a_item_data 增加采集器点, name固定为fullPath; val填设备访问地址，比如: http://*************
      3. d_device 增加对应采集器设备
      4. d_device_item_data_map 绑定设备点
      5. e_device 增加对应采集器设备，且 e表设备Id 同 d表设备Id。 (注: remark 填 collector 这个固定，代表采集器设备)
      6. 字典系统配置，补充对应采集器的判断规则 system_configs.update_device_status_1

#### 组合点配置说明
1. 多个点组合拼接
  新建个 point_table.type 是 c 的点 
    func_list 里面
    [["funcStr","?+''+?+''+?","{3004506}","{3004307}","{3004308}"]]
      第一个参数是  表明funcStr 这个方法
      第二个参数是  组合的公式  是  三个数字 拼接
      后面的参数就是  对应的 三个数的 id 号  

#### 能耗数据修正
  1. 前提条件，数据有，并且可以通过各种渠道获取
    比如 d_device_data_history 有监控数据，或者用户有提供 excel 表格数据
    insert into e_device_synchronized_data (item_id, item_data_id, val, recorded_at)
    select device_id as item_id, item_data_id, indication as val, recorded_at from d_device_data_history
    where item_data_id=60905 and recorded_at > "2025-04-16"
    GROUP BY device_id, DATE_FORMAT(recorded_at, "%y-%m-%d %H")
  2. 填充修正记录表 e_device_synchronized_data, 主要填入 item_id(e_device.id), item_data_id(a_item_data.id), val(累计值), recorded_at(记录时间)
     需要修正数据记录，caculated 设置成 0； 不需要修复的改成 1。
  3. 启动数采脚本 node src/dbReCaculateEnergy.js [port] [db] [debug], 关联对应的数据库，等待程序运行完成
  4. 核对数据 select * from e_energy_data_history where device_id = XXX and recorded_at >= XXX and recorded_at < XXX ORDER BY recorded_at desc


## 修改备注
#### 1. 移除后端图片验证码逻辑
ruoyi-framework/src/main/java/com/ruoyi/framework/web/service/SysLoginService.java



#### 2. enum 类型 生成的 domain 实体, 需要手工修改 $colomn.dataType 


#### 3. vue 代码, selectDictLabel 第一个参数中 dict 必须是固定 key
    ```javascript
    opts = [
        {
            dictValue: XXX,
            dictLabel: XXX,
        },
    ];
    this.selectDictLabel(opts, row.stateConn){}
    ```

#### 4. vue 路由动态加载
默认数据: router/index.js<br/>
动态数据: store/modules/permissions.js<br/>

#### 5. vue 页面布局 layout
***存在bug 配置多级菜单后, 子页面出现layout bug***
AppMain  // 右边主页面<br/>
Navbar  // 主页顶部banner(面包屑+退出登录)<br/>
Settings  // 右上角个人, 下拉菜单 [布局设置] 弹出内容<br/>
Sidebar  // 左侧菜单<br/>
TagsView  // 主页顶部 打开的页面tab标签<br/>

#### 6. vue 部分页面隐藏顶,左菜单 

#### 7. vue 三级菜单bug fix

#### 8. 移除 druid
1. pom.xml 添加依赖 <br />
```xml
    <dependency>
        <groupId>org.mybatis.spring.boot</groupId>
        <artifactId>mybatis-spring-boot-starter</artifactId>
        <version>1.3.2</version>
    </dependency>
```
2. 添加配置 application.yml
```aidl
...
    driver-class-name: com.mysql.jdbc.Driver
    url: jdbc:
    username: 
    password: 
```
3. 去除启动配置 ruoyi-framework 中的 @Configuration 注解
```aidl
/**/config/DruidConfig
/**/config/properties/DruidProperties
```

#### 9. 其他说明
cwing.cn 天翼iot接入
第三方应用包括获取数据及下发指令，则需要通过 SDK/API 接口与平台打通并实现相关功能，具体操作参考文档: 
    https://www.ctwing.cn/yykf/121
JAVA Sdk 使用说明:
    https://www.ctwing.cn/yykf/124#see
引入sdk命令:
mvn install:install-file -Dfile=ctg-ag-sdk-core-${SDK版本}.jar -DpomFile=ctg-ag-sdk-core-${SDK版本}.pom.xml
mvn org.apache.maven.plugins:maven-install-plugin:2.5.1:install-file -Dfile=ag-sdk-biz-${SDK版本}-SNAPSHOT.jar -DpomFile=ag-sdk-biz-${SDK版本}-SNAPSHOT.pom.xml
mvn install:install-file -Dfile=ctg-ag-sdk-core-2.5.0-20211018.013334-41.jar -DpomFile=ctg-ag-sdk-core-2.5.0-20211018.013334-41.pom.xml

### 10. 问题
#### 1. JAVA 启动报错 Result Maps collection already contains value for
    进入 .m2/XXX   删除 ruoyi 目录

#### 2. 若依多级菜单设置后，页面不正常
https://blog.csdn.net/asdf2222a/article/details/112977018
https://blog.csdn.net/Decline1/article/details/111601122
    1. 添加一个内容如下的文件 XXX.vue
        <template >
          <router-view />
        </template>
    2. 将多级父菜单的 "组件路径" 设置成 XXX.vue。 注：可先保存成菜单，输入组件路径内容后，再保存成目录


智向 API
userId, API_KEY(thirdKey) 登录会, 个人信息中能找到


### 3D 模型相关
#### 1. three3D 接入
  字典增加配置 system_configs / building_config_1
    threeServer: {
      "background": "#0A141C",
      "modelPath": "/uploads/xincheng3D",
      "environment": "/uploads/xincheng3D/env/brown_photostudio_02_2k.hdr",
      "metalEnv": "/uploads/xincheng3D/env/V2.0.hdr",
      "floorEnv": "/uploads/xincheng3D/env/floor.hdr",
      "envTexture": {
        "path": "/uploads/xincheng3D/env/",
        "photos": [ 'photo6.jpg', 'photo2.jpg', 'photo6.jpg', 'photo4.jpg', 'photo5.jpg', 'photo1.jpg' ]
      },
      "gltf": {
        "path": "/uploads/xincheng3D/draco/",
        "modelPath": "/uploads/xincheng3D/models/xincheng.glb",
        "name": "xincheng"
      },
      "defaultFog": {
        "color": "#0d1721",
        "near": 200,
        "far": 1000
      },
      "defaultCamara": {
        position: { x:17.819693820394235,y:11.026887556383599,z:-52.15985362799664},
        target:{x:5.597955607178278,y:11.14229442068607,z:-15.883441133134474}
      },
      "defaultOutline": {
        "color": "#67C23A",
        "edgeStrength": 2
      },
    }
#### 2. cbim 接入
  字典增加配置 system_configs / building_config_1
    bimServer: {
        "server": "https://bms.lanxing.tech/raas",
        "itemVersionId": 92239,
        "scenePath": "tw5vIN8CEwbhClwi+Qv//0zEdg4ah83eFjrlo5yP5RIUwHN0pQZUIRQsIlK7jWPiRczO0BMurT+8Y86D9Ufipl0glkgad1e4cN6weL/4VMVqH5+EsqRzysMXloQ=",
        "sceneId": 92239,
        "isDebug": false,
        "majors": ["冷源", "热源", "空调末端", "照明", "智慧消防", "给排水", "送排风", "地下车库环境办公室环境室外环境", "电梯", "门禁", "停车", "报警监控", "用电", "用水", "用气", "巡更", "计算机网络系统"],
        "device": [{
            "icon": "/uploads/ibms/icon/airCondition.png",
            "type": "DirectStartAHU",
            "name": "空调",
            "major": "暖通,风道末端",
            "display": "list"
        }, {
            "icon": "/uploads/ibms/icon/light.png",
            "type": "light",
            "name": "照明",
            "major": "",
            "display": "list"
        }, {
            "icon": "/uploads/ibms/icon/cool.png",
            "type": "cool",
            "name": "冷源",
            "major": "暖通",
            "display": "card"
        }, {
            "icon": "/uploads/ibms/icon/water.png",
            "type": "waterTreatmentDevice",
            "name": "水处理",
            "major": "给排水,管道,管件",
            "display": "card"
        }, {
            "icon": "/uploads/ibms/icon/pvPlant.png",
            "type": "pvPlant",
            "name": "光伏",
            "floor": "五层(16.80m),机械设备,类型 1",
            "display": "card"
        }, {
            "icon": "/uploads/ibms/icon/energyStorage.png",
            "type": "energyStorage",
            "name": "储能",
            "floor": "一层(0.00m),停车场",
            "display": "card"
        }]
    }


### 11 设备配置相关说明
#### 设备列表+卡片显示配置 字典 d_device_list_display
    displayKeys: 可单独配置需要显示的数据点，卡片上显示的三个属性值，如果没有，则按oid取前3个
    protoTypeDisplay: 设备详情点击跳转组态的呈现模式, 默认map。 可选值: map, 3d, by, mult
    viewDetail: 设备详情，点击跳转模式，默认dialog, 弹详情框:dialog; 打开组态页:prototype
    dialogType: 详情显示模式, 普通小弹框 default; 大详情弹框 mult

    showImages: "0",// 是否启用原理图+现场图标签
    hasLock: "0",   // 是否支持锁定功能
    hasEnergy: "0", // 是否支持能耗数据显示
    hasWarning: "1", // 是否支持报警管理显示
    hasDeviceInfo: "1", // 是否支持设备信息显示
    hasMaintenance: "1", // 是否支持运维相关tab显示  报修管理、巡检管理、保养管理

    summaryType: "device", // 默认统计样式, device 会多开关的统计 , device,sensor

#### 设备组态显示配置  字典 d_device_diagram_display
     displayTypes: 系统图上默认切换按钮 ["map","by","3d","list","pt","iframe","sp3d"]
      map: 设备地图，电子地图，平面图
      pt:  原理图. (左边设备列表，右边原理图)

#### 通用表格模块 字典 comm_table_display
    路由最后一个对应字典 label， 配置 {pageTitle:"", tableParams:{"title":[],"sql":[]}, height:""}
      tableParams说明: 包含多个列的解释
        label: 显示出来的列名
        prop:  对应查询出来的列名
        filterable: 是否支持查询，会在表格上面生成一个 搜索输入框
        type: date/datetimerange, 是否支持时间查询，会在表格上面生成一个 时间搜索框，date(查询日期); datetimerange(查询范围)
        mapper: type为 select时，支持通过 mapper 配置下拉框内容
      eg:
        "tableParams": {
          "title": [
            {
              "label": "停车场",
              "prop": "parkKey"
            },
            {
              "label": "车牌号",
              "prop": "carNo",
              "filterable": "1"
            },
            {
              "label": "离/入场",
              "prop": "recordTypeStr",
              "filterable": "1",
              "type": "select",
              "mapper": {
                "进场":"进场",
                "离场":"离场"
              }
            },
            {
              "label": "入场时间",
              "prop": "enterTime",
              "filterable": "1",
              "type": "datetimerange"
            }
          ]
        }

    组件位置  views/device/dataView/DeviceCommTable

#### 平面图上设备颜色判定逻辑
    //   优先判定在线，再判定告警，再判断开关
    d.status = "healthy";
    // 在线 离线
    if(d.communicateStatus == '离线') {
      d.status = "offline";
    } else {
      if(d.workStatus.indexOf('停') >= 0 || d.workStatus.indexOf('关') >= 0) {
        d.status = "stop";
      } else if(d.workStatus.indexOf('运') >= 0 || d.workStatus.indexOf('开') >= 0 || d.workStatus.indexOf('启') >= 0 ) {
        d.status = "running";
      }
      if(d.warningStatus != "健康") {
        d.status = "warning";
      }
    }

#### 数据表说明
d_device_item_data_map 中 tag 值
    包含 operatorLog 的是变更时需要记录设备操作日志的数据点 (注: 需要创建数据库对应的 trigger 才能触发记录日志)
    包含 status  的是设备运行状态的数据点, 用来计算是否开关, 离线等状态
    包含 evn     的是显示在设备的环境实时
    包含 warn    的是显示在设备实时报警状态里面
    包含 fault   的是设备故障状态的数据点, 脚本通过 a_warning_rule.severity 来确定报的是告警还是故障
    包含 hidden  的是前端不显示的数据
    包含 more    的是前端列表不显示，点击详情显示
    包含 map     的是平面地图上，会在设备图标旁边显示对应数据（值+单位）
    包含 online  的作为在线数据判定点。1:判断时间是否符合，2:判断具体值是否包含"在离线"关键字
    包含 monitor 的会画曲线(可多个)
    包含 power   的会画曲线(可多个)  功率趋势
    包含 current 的会画曲线(可多个)  电流趋势
    包含 voltage 的会画曲线(可多个)  电压趋势
    包含 runCount    的代表设备运行次数
    包含 errorCount  的代表设备故障次数
    包含 warnCount   的代表设备报警次数
    包含 runHour     的代表设备总运行小时
    包含 shortcut    的代表会出现在设备详情小弹框的快捷操作
    包含 autoState   的代表手自动状态
    包含 elevatorFloor   的代表电梯楼层
    包含 elevatorDirection   的代表电梯运行方向
    包含 airConditionRoomtemp   的代表空调、风机盘管、新风机组的房间温度
    包含 airConditionMode   的代表空调、风机盘管、新风机组的模式： 制冷/制热
    包含 airConditionTemp  的代表空调、风机盘管、新风机组的送风温度 控制
    包含 airConditionAutomatic  的代表空调、风机盘管、新风机组的手自动状态
    包含 airConditionReturnTemp  的代表空调、风机盘管回风温度
    包含 airConditionWindspeed  的代表空调、风机盘管、新风机组的风速
    包含 lightControlMode  代表照明的模式
    包含 lightOrderColumn 代表照明的下发点位
    包含 lightAllControl  代表照明的全开/关
    包含   的代表空调、风机盘管、新风机组的风速
    包含 anim        的代表会在 speed3d 原理图上，执行 deviceAnimation 动画操作。 eg: window.vue.$bus.$emit('updateScene','{"type":"deviceAnimation","data":{"120011501":{"name":"1200115-运行状态","action":"stop"}}}')
    包含 rot         的代表会在 speed3d 原理图上，执行 deviceProps 动画操作。 eg: window.vue.$bus.$emit("updateScene", '{"type":"deviceProps","data":{"120011504":{"1200115-新风阀全关状态":{"x":"60"}}}}');
    
    包含 chart   的会在图上显示，比如3d,2d等，主要用来对接 iframe 3d模型模式



  d_device_item_data_map 中 note 值 计算输出给特殊情况用
    eg: 输出运行停止状态动画所需要的值
      function calc(x) {
        // 将 输入值， 换成成 动画的播放停止状态
        return { "0": "stop", "1":"play" }[x] || "stop";
      }
      return calc;
    eg: 输出风阀开关量动画所需要的值
      function calc(x) {
        // 将 0 ~ 100 的百分比， 换成成 x 轴的 0 ~ 90 度的开合状态
        return { "x":  parseInt(x * 90/100) };
      }
      return calc;
    eg: 输出液位高度
      function calc (x) {
        let obj={'000':3,'100':1,'010':6,'011':10};
        return {"y":obj[x]}
      };
      return calc;


d_device resource_id 可给设备绑定原理图, 一类设备一个原理图. eg:[空调机组图/风机盘管图/新风机组图等]
    注： d_device_recource_map 中需要有 resource 和 device 的关联记录

e_device
  note 虚拟表的计算公式，不填的不计算
  tag  表的尖峰平谷标记位
  unreal 虚拟表标记位

e_device_group 中 is_main 为 Y 的是用来做总用能统计的组标记

一键场景+联动场景 src/dbUpdate.js 执行
    通过设备类型，对应场景列表，对应到具体定时/联动任务列表
d_device.type => d_scence.id => device_task
d_device_task 设备定时任务, nodejs 轮循执行; status=1 为激活的任务;
    1. 执行 this.getDicts("d_device_task"), 获取所有任务列表
    2. 页面对应任务名 匹配 dictLabel, 找到当前页面任务
    3. 通过任务字典 dictValue 值(任务Ids), 获取任务列表

报警检测:
a_point_table.data_name == a_item_data.name 且 val 满足条件
a_point_table.func 值说明:
    1. diffValByTimeSpan: 时间区间内数据变化率 (MaxTimeVal - MinTimeVal) / (MaxTime - MinTime)
a_point_table.val 值说明:
    1. energyWeekDayHourlyAvg: 当期星期当期小时区间内, 设备平均改变值(工作日,非工作日不同; 凌晨,工作时间段不同)

系统用户 sys_user 表，支持 user_name 字段存入 md5 后的字符串， md5 加密工具可用 https://www.jyshare.com/crypto/md5/
    eg: ssoUser -> d8807c74ec002517285038cfa1729c21

INSERT INTO `a_warning_rule`(`name`, `description`, `key`, `val`, `compare`, `severity`, `err_msg`, `solution_ref`, `tag`, `time_span`, `func`, `created_at`, `updated_at`) VALUES ('短时间内水流过大', '检查管道是否龙头未关闭', '用水量', '1', '>=', '一般', NULL, NULL, NULL, 10, 'diffValByTimeSpan', '2021-04-21 17:58:47', '2021-04-21 17:50:21');




水表漏水检测:
    配置注意:
        1. 确认 a_item, a_item_data 设备以及点位信息是水表以及对应用量数据
        2. 确认 a_item_data.name, a_a_point_table.data_name, a_warning_rule.key 一致, 采集时时检测报警
        3. 确认 a_item_rule_map 有设备报警映射
        4. 配置好 a_warning_rule 对应数据
    执行逻辑:
        1. 每五分钟更新, 载入设备报警规则, 包含单次匹配逻辑和区间段数据匹配逻辑
        2. 接收采集点信息, 根据报警规则, 缓存对应设备需要的区间内最长的数据(一分钟一条)
        2.1. 循环报警规则, 匹配符合报警条件的数据, 并生成报警记录, 否则尝试修复对应报警记录


设备场景
d_scene 场景列表
d_scene_map  场景任务map
d_device_task  具体任务列表

具体 demo 可查看 test/test_task.js
1. 任务模板：
···
    1. 定时自动执行：匹配检测时间点，当所有条件都满足时触发任务
    {
      "holidays": "0",      // 是否节假日。 0:非节假日; 1:只有节假日; '':所有日期
      "weekend": "0",       // 是否周末。 0:非周末; 1:只有周末; '':所有日期
      "year": "",           // 匹配年
      "month": "",          // 匹配月
      "day": "",            // 匹配日
      "weekDay": "",        // 匹配星期，0 1 ... 5 6
      "hour": "",           // 匹配小时，0 ~ 23
      "minute": "",         // 匹配分钟，0 ~ 59
      "sleepTime": "30",    // 检测间隔，(当前时间 - 最近一次执行的任务的时间)，单位分钟
      "actions": [          // 需要执行的动作列表
        {
          "deviceDataName": "运行状态",
          "value": "1"
        },
        {
          "deviceDataName": "运行模式",
          "value": "1"
        },
        {
          "deviceDataName": "设定温度",
          "value": "20"
        },
        {
          "deviceDataName": "风量设定",
          "value": "3"
        }
      ],
      "condition": {        // 联动条件，可为空
      }
    }

    2. 联动触发执行：当某些条件成立时，触发任务。
    如果有多个联动, 则
    {
      "holidays": "0",
      "weekend": "0",
      "year": "",
      "month": "",
      "day": "",
      "weekDay": "",
      "hour": "",
      "minute": "",
      "sleepTime": "30",
      "deviceIds": "",
      "actions": [
        {
          "deviceDataName": "运行状态",
          "value": "0"
        }
      ],
      "condition": {        // 联动条件，目前只支持一类条件
        "deviceType": "",   // 对应设备类型
        "deviceIds": "2001,2002,2003",    // 设备IDs，如果为空，则为当前类型下所有设备
        "deviceDataName": "温度",  // 设备点位名
        "isMember": "1",    // 触发条件。 1.单一条件; 2.全部条件
        "compare": "<=",     // 对比公式
        "value": "24",      // 对比值
      }
    }
···


#### 10. 数采设置
远程同步数据：
    1. ruoyi-backendServer/node_modbus/src/conf/sysConfig.js 如果配置了 mysql2, 则会启动同步数据到 mysql2 对应数据库
读取配置：
    1. 读01，地址位减1；写05，地址位减1。 eg: modbus 上显示 1101，需要配置成 1100
    2. 读03，地址位不变；写16，地址位减1。 eg: modbus 上显示 1401，读需要配置成 1401，写需要配置成 1400
    3. 读01：[["booleanToInt"]]；读03：[["swapToBE","3210"],["F32BE"],["divide", 1, 2]]
    4. 写05：[["intToBoolean"]]；写16：[["divide", 1, 2],["float2Buff"],["swapToBE","1032"]]
调试文件：
    ./test/test_modbus.js

[["intToBuffer"],["swapToBE","10"]]

[["swapToBE","01"],["readBitInBuffer"],["readBitVal",[],1,{"0":0,"1":1,"2":2,"4":4,"8":8}]]
[["parseBitMapVal",{"0":0,"1":1,"2":2,"4":4,"8":8}]]

[["swapToBE","01"],["I16BE"]]
[["intToBuffer"],["swapToBE","10"]]

[["swapToBE","01"],["readBitInBuffer"],["readBitVal",0,1,{"0":0,"1":1,"2":2,"4":4,"8":8}]]

[["swapToBE","10"],["readBitInBuffer"],["readBitMapVal",[],1,0,{"0":1,"1":2,"2":3,"3":4,"4":5,"5":6,"6":7,"7":8,"8":9,"9":10,"10":11,"11":12,"12":13,"13":14,"14":15,"15":16}]]

[["swapToBE","01"],["readBitInBuffer"],["readBitVal",0,1,{"0":0,"1":1,"2":2,"4":4,"8":8}]]

[["swapToBE","1032"],["U32BE"],["divide", 100, 2]]

读取地址 [["U16BE"]]
写入地址 [["intToBuffer16"],["swapToBE","10"]]


#### 11. redis 配置
下载 Redis-x64-3.2.100.zip, 进入目录执行:
redis-server.exe --service-install redis.windows.conf


#### 12. 泰伦项目 鸿雁智慧路灯 添加依赖包
ruoyi-dataCollector 模块
mvn install:install-file -DgroupId=com.shuncom.api -DartifactId=shuncom-java-sdk -Dversion=1.2.0-RELEASE -Dpackaging=jar -Dfile=packages/shuncom-java-sdk-1.2.0-RELEASE.jar -DpomFile=packages/shuncom-java-sdk-1.2.0-RELEASE.pom 

mvn install:install-file -DgroupId=com.shuncom.api -DartifactId=shuncom-java-sdk -Dversion=1.2.0-RELEASE -Dpackaging=jar -Dfile=shuncom-java-sdk-1.2.0-RELEASE.jar -DpomFile=shuncom-java-sdk-1.2.0-RELEASE.pom 


SDK下载lib包地址 https://application.ctwing.cn/#/app-dev-details/90160/THIRD 
mvn install:install-file -Dfile=packages/ctg-ag-sdk-core-2.5.0-20220512.061430-51.jar -DpomFile=packages/ctg-ag-sdk-core-2.5.0-20220512.061430-51.pom.xml
mvn org.apache.maven.plugins:maven-install-plugin:2.5.1:install-file -Dfile=packages/ag-sdk-biz-90160.tar.gz-20221109.094047-SNAPSHOT.jar -DpomFile=packages/ag-sdk-biz-90160.tar.gz-20221109.094047-SNAPSHOT.pom.xml

达梦数据库驱动依赖
mvn install:install-file -DgroupId=com.dm -DartifactId=DmJdbcDriver -Dversion=8.1.3.162 -Dpackaging=jar -Dfile=packages/DmJdbcDriver18.jar

windows  .m2 位置
C:\Users\<USER>\.m2\repository\com


注： 一定要在 cmd 窗口执行， idea terminal 会出现 
[ERROR] Failed to execute goal org.apache.maven.plugins:maven-install-plugin:2.4:install-file (default-cli) on project ruoyi: The specified file 'C:\work\project\bms\shuncom-java-sdk-1' not exists -> [Help 1]
[ERROR]
[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
[ERROR] Re-run Maven using the -X switch to enable full debug logging.
[ERROR]
[ERROR] For more information about the errors and possible solutions, please read the following articles:


#### 13. 数据库触发器
##### 1. 设备线下操作改变状态，触发记录日志，只针对设备类操作
···
    SHow TRIGGERs;
    DROP TRIGGER item_data_update_to_log;

    CREATE TRIGGER item_data_update_to_log AFTER UPDATE ON a_item_data FOR EACH ROW
    BEGIN
        DECLARE v_deviceId VARCHAR(50);
        DECLARE v_dataName VARCHAR(255);
        DECLARE v_deviceType VARCHAR(255);
        DECLARE v_dataTag VARCHAR(255);
        DECLARE v_dataValueMapper VARCHAR(500);
        DECLARE v_js JSON;

        -- 条件检查
        IF NEW.val != OLD.val AND NEW.func = 'read' AND (NEW.locked IS NULL OR NEW.locked != 1) THEN
            -- 查询数据并赋值给局部变量
            SELECT
                m.device_id, m.name, d.type, IFNULL(m.tag,''), IFNULL(da.note,'')
            INTO
                v_deviceId, v_dataName, v_deviceType, v_dataTag, v_dataValueMapper
            FROM
                d_device_item_data_map m
                    LEFT JOIN d_device d ON m.device_id = d.id
                    LEFT JOIN a_item_data da ON m.item_data_id = da.id
            WHERE
                m.item_data_id = NEW.id
                LIMIT 1;
            -- debug 使用
            -- insert into d_trigger_log (notes) VALUES (v_deviceId);
            -- 仅当查询到数据且标签匹配时记录日志
            IF (v_deviceId IS NOT NULL AND v_dataTag LIKE '%operatorLog%') THEN
                SET v_js = JSON_OBJECT(
                    'dataId', NEW.id,
                    'dataName', v_dataName,
                    'dataValueAfter', NEW.val,
                    'dataValueBefore', OLD.val,
                    'dataValueMapper', v_dataValueMapper
                );

                INSERT INTO d_device_oper_log (
                    device_id, device_type, title, business_type,
                    method, request_method, operator_type,
                    oper_name, oper_param, json_result, status, oper_time
                ) VALUES (
                    v_deviceId, v_deviceType, CONCAT('设备变更--', v_dataName), 2,
                    'db trigger', 'USER_OPERATE', '3',
                    'USER', '', v_js, '0', NOW()
                );
            END IF;
        END IF;
    END;
···

#### 14. ssh 证书 (https)
    生成和配置 ssl 证书到 nginx 服务器，参考 有道文档，学习笔记 > 其他整理 > ssl 证书创建以及配置

#### 15. 中设操作手册
    平台功能操作，参考 有道文档，学习笔记 > 其他整理 > 中设3D使用总结

#### 16. 通过电流表计算累计电流 （金华横店影视城）
    1. application.xml 需要开启定时任务
        quartz.caculateEnergySwitch: true
    2. a_item 表对应的电流表设备，  name 字段必须包含 “电流表” 关键字
    3. a_item_data 表对应设备点位， 必须有 name 包含 “电流” “总电量” 关键字的 两条记录， 且电量记录 id = 电流记录 id + 1

#### 17. 电气设备类型(d_device_type)
    17.1 排序编号影响(从小到大排): 设备卡片，告警，资产等
    17.2 remark 字段说明
      electricity 大屏模块 运维事件 电气设备
      security 大屏模块 运维事件 安全设备
      warning 运维管理 显示设备
      assets 资产管理 显示设备
      energy 用能管理 需求侧响应配置
      strategy 支持策略设备种类
      devOps 维保知识库 显示设备
      offlineAlert 离线报警 需要显示的设备种类

#### 17.1 设备主控台界面配置
    菜单组件地址: device/dataView/DeviceMain
    主参数引用: 载入父菜单设备类型 /#/deviceMgt2/【KTSL2】/main
    读取父菜单配置: d_device_list_display.【KTSL2】 对应的配置
      举例: {"pageTitle":"空调(水冷)", "display":"LY2,KTJZ2,XFJZ2,LQT2", "globalVals":"启停控制,温度设定", "globalAutoModelName":"手自动模式"}
        pageTitle: 页面标题
        display: 需要显示的多个子设备(二级分组)
        deviceType: 全部支持的设备类型，map 会体现多种设备共存
        deviceMainType: 当前主要设备类型
        category: a_resource 当前选中的资源
        type: a_resource 当前选中的资源
        globalVals: 群控点名字，对应于 d_device_item_data_map.name
        globalAutoModelName: 相关策略名，对应于 d_device_task_group.name
        displayType: 默认呈现方式
        displayTypes: 所有呈现方式 ["card","map","list","by","3d","sp3d"]  其中 map/by/3d/sp3d 只能出现一个
        defaultScale: 默认载入map的缩放比例
        mapSwift: map 快捷开关显示
        viewDetail   设备详情跳转模式, dialog(基本弹框), prototype(跳转组态)       
        protoTypeDisplay: 打开设备弹框详情，跳转组态图类型, map, by, 3d, mult
        dialogType  设备弹框详情样式，普通样式不填，复杂样式:mult
        cardDataNum  卡片形式下数据展示个数
        forceUpdate  1,0; 是否强制下发指令(即使数据没变化，也会下发指令)
        iconDisplay: map上图标显示 dot/icon/image, 注: 配成image模式，会自动替换 d_device.icon 地址中的 default 为实际设备状态
          d_device_resource_map
            x_axis: map 左边距; 3d x坐标
            y_axis: map 上边距; 3d y坐标
            z_axis: map 无; 3d z坐标
            other_data:
              top: map 上边距 会覆盖 x_axis
              left: map 左边距 会覆盖 y_axis
              width: map 图标宽
              height: map 图标高
              imgWidth: map image 模式中图片宽
              imgHeight: map image 模式中图片高
              padding: map image 模式中图片边距


#### 17.2 策略管理编辑联动设备关联
    字典增加 device_strategy_type_map
    数据增加 数据标签 -- 策略执行设备类型
            数据键值 -- 联动设备类型，可多个

#### 18. 能耗管理(e_device)字段说明
tag 写入 unreal 代表 虚拟表
note 写入计算公式 {123} * 10  代表读取 e_device.id = 123 的日数据 * 10 写入当前虚拟表的日数据中

#### 19. 智向照明用能数据
e_device.description 对应 beacool_device_energy_daily.mac_id，则会每小时生成当日能耗数据

#### 20. mysql8 only_full_group_by
set @@sql_mode='STRICT_TRANS_TABLES,ALLOW_INVALID_DATES,ERROR_FOR_DIVISION_BY_ZERO';
set @@lower_case_table_names=1;





## 原项目在线体验

- admin/admin123  
- 陆陆续续收到一些打赏，为了更好的体验已用于演示服务器升级。谢谢各位小伙伴。

演示地址：http://ruoyi.vip  
文档地址：http://doc.ruoyi.vip

bms 命名规则：
wl -> 水管

bim 命名规则：
    建筑: b[X]          建筑X
    楼层: b[X]f[Y]      建筑X的Y层
    电线: e[X]          园区X路线 (配电1, 配电2...)
          b[X]e[Y]     建筑X的Y路线 (照明用电, 空调用电...)
          b[X]f[Y]e[Z] 建筑X的Y层的Z线路（三楼照明用电...）
    水管: w[X]          园区X管道 (生活用水,消防用水,雨水...)
          b[X]w[Y]     建筑X的Y管道 (生活用水, 办公用水...)
          b[X]f[Y]w[Z] 建筑X的Y层的Z管道（三楼办公用水...）
    设备: d[ID]         ID: d_device.id


## 配置项目
1. 添加平台配置
    a, sys_dict_type 插入系统配置 
      INSERT INTO `bms`.`sys_dict_type`(`dict_id`, `dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (114, '系统配置', 'system_configs', '0', 'admin', '2021-05-11 11:21:31', '', NULL, NULL);
    b, 添加配置数据
      INSERT INTO `bms`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (155, 0, 'title', '智慧建筑管控平台', 'system_configs', NULL, NULL, 'N', '0', 'admin', '2021-05-11 11:23:11', '', NULL, NULL);
      INSERT INTO `bms`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (156, 0, 'bim_ws_server', 'ws://172.106.201.19:10398/', 'system_configs', NULL, NULL, 'N', '0', 'admin', '2021-05-11 11:24:35', 'admin', '2021-05-11 11:25:30', NULL);
      INSERT INTO `bms`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (157, 0, 'dashboard_display', '2D', 'system_configs', NULL, NULL, 'N', '0', 'admin', '2021-05-11 11:25:01', '', NULL, '2D;3D;3DH');
      INSERT INTO `bms`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (158, 0, 'dashboard_bg_img', '/uploads/sxsw_dashboard_bg.jpeg', 'system_configs', NULL, NULL, 'N', '0', 'admin', '2021-05-11 11:25:59', '', NULL, NULL);
      INSERT INTO `bms`.`sys_dict_data`(`dict_code`, `dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`) VALUES (159, 0, 'dashboard_bg_video', '/uploads/sxsw_dashboard_bg_360.mp4', 'system_configs', NULL, NULL, 'N', '0', 'admin', '2021-05-11 11:26:19', '', NULL, NULL);

2. 添加建筑信息 a_building 
    INSERT INTO `bms`.`a_building`(`id`, `code`, `name`, `type`, `province`, `city`, `city_id`, `build_year`, `floor_num`, `person`, `full_area`, `area`, `refrigeration_area`, `heating_area`, `longitude`, `latitude`, `address`, `owner`, `intro`, `photo_url`, `insert_date`, `monitoring`, `created_at`, `updated_at`) VALUES (1, '110101A001', '青藤书屋', 'A', '浙江', '绍兴', '1246', '2020-01-01 12:34:56', 3, 60, 400, 400.00, 400.00, 400.00, '120.5983', '29.9313', '越城区大乘弄10号', '人民卫生出版社有限公司', '<p>青藤书屋是一处具有园林特色的中国传统民居建筑。位于浙江省绍兴市区前观巷大乘弄10号，属于中国明代杰出的文学家和艺术家徐渭的故居。《山阴县新志》载：“青藤书屋，前明徐渭故宅”。青藤书屋现为浙江省重点文物保护单位、绍兴市爱国主义教育基地。2006年05月，青藤书屋和徐渭墓作为明代古建筑，被国务院批准列入第六批全国重点文物保护单位名单。</p>', NULL, '2021-04-13 12:34:56', 'Y', '2021-04-13 12:34:56', '2021-05-10 21:07:47');

3. 添加建筑资源，楼层平面图，设备原理图
    INSERT INTO `bms`.`a_resource`(`id`, `building_id`, `category`, `type`, `oid`, `name`, `val`, `width`, `height`, `note`, `other_data`, `created_at`, `updated_at`, `source_id`) VALUES (1, 1, '楼层', NULL, 0, '一层', '/uploads/sxsw_f1.jpeg', 1323, 618, '', '{\"deviceCount\":35,\"fmapId\":null,\"height\":698,\"mapId\":65,\"mapName\":\"乌牛供电所一楼\",\"mapType\":0,\"mapUrl\":\"http://marsiot.oss-cn-shanghai.aliyuncs.com/2d/1609555089000.png\",\"originCoordinateX\":0,\"originCoordinateY\":0,\"scale\":2.2686,\"width\":688}', '2020-11-09 17:53:32', '2021-04-28 09:09:44', '65');
    INSERT INTO `bms`.`a_resource`(`id`, `building_id`, `category`, `type`, `oid`, `name`, `val`, `width`, `height`, `note`, `other_data`, `created_at`, `updated_at`, `source_id`) VALUES (10, 1, '空调机组', NULL, 0, '空调机组图', '/uploads/device-bg.jpg', 1200, 750, NULL, '{\n  \"objects\": [\n    {\n      \"name\": \"整体管道top\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"49px\",\n        \"left\": \"257px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_6.gif\"\n    },\n    {\n      \"name\": \"整体管道topleft\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"50px\",\n        \"left\": \"465px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_2.gif\"\n    },\n    {\n      \"name\": \"整体管道topleft1\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"50px\",\n        \"left\": \"125px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_2.gif\"\n    },\n    {\n      \"name\": \"整体管道topleft2\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"50px\",\n        \"left\": \"75px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_1.gif\"\n    },\n    {\n      \"name\": \"整体管道topright\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"50px\",\n        \"left\": \"580px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_2.gif\"\n    },\n    {\n      \"name\": \"整体管道topright1\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"50px\",\n        \"left\": \"720px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_2.gif\"\n    },\n    {\n      \"name\": \"整体管道topright2\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"50px\",\n        \"left\": \"810px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_2.gif\"\n    },\n    {\n      \"name\": \"整体管道topright3\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"50px\",\n        \"left\": \"960px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_4.gif\"\n    },\n    {\n      \"name\": \"整体管道mid1\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"242px\",\n        \"left\": \"282px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_5.gif\"\n    },\n    {\n      \"name\": \"整体管道mid2\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"350px\",\n        \"left\": \"282px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_5.gif\"\n    },\n    {\n      \"name\": \"整体管道bottom\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"456px\",\n        \"left\": \"258px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_3.gif\"\n    },\n    {\n      \"name\": \"整体管道bottomleft\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"481px\",\n        \"left\": \"465px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_2.gif\"\n    },\n    {\n      \"name\": \"整体管道bottomleft2\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"481px\",\n        \"left\": \"125px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_2.gif\"\n    },\n    {\n      \"name\": \"整体管道bottomleft3\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"481px\",\n        \"left\": \"75px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_1.gif\"\n    },\n    {\n      \"name\": \"整体管道bottomright\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"481px\",\n        \"left\": \"580px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_2.gif\"\n    },\n    {\n      \"name\": \"整体管道bottomright1\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"481px\",\n        \"left\": \"720px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_2.gif\"\n    },\n    {\n      \"name\": \"整体管道bottomright2\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"481px\",\n        \"left\": \"815px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_2.gif\"\n    },\n    {\n      \"name\": \"整体管道bottomright3\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"481px\",\n        \"left\": \"965px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_2.gif\"\n    },\n    {\n      \"name\": \"整体管道bottomright3\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"481px\",\n        \"left\": \"965px\"\n      },\n      \"zInde\": 100,\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/duct_2.gif\"\n    },\n    {\n      \"name\": \"风机图示\",\n      \"type\": \"vicon\",\n      \"dataName\": \"运行状态\",\n      \"position\": {\n        \"top\": \"512px\",\n        \"left\": \"730px\"\n      },\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"urls\": {\n        \"0\": \"/images/IdcsGif/kongtiao-xinfeng/fan_l_1.gif\",\n        \"1\": \"/images/IdcsGif/kongtiao-xinfeng/fan_l_1f.gif\"\n      }\n    },\n    {\n      \"name\": \"回风温度Label\",\n      \"type\": \"vtext\",\n      \"dataName\": \"回风温度Label\",\n      \"position\": {\n        \"top\": \"230px\",\n        \"left\": \"790px\"\n      },\n      \"size\": \"14px\",\n      \"weight\": \"bold\",\n      \"color\": \"#ffffff\",\n      \"data\": {\n        \"dVal\": \"回风温度\",\n        \"dDataUnit\": \"\"\n      }\n    },\n    {\n      \"name\": \"回风温度\",\n      \"type\": \"vtext\",\n      \"dataName\": \"回风温度\",\n      \"position\": {\n        \"top\": \"255px\",\n        \"left\": \"790px\"\n      },\n      \"size\": \"16px\",\n      \"weight\": \"bold\",\n      \"color\": \"#50f100\"\n    },\n    {\n      \"name\": \"回风湿度Label\",\n      \"type\": \"vtext\",\n      \"dataName\": \"回风湿度Label\",\n      \"position\": {\n        \"top\": \"230px\",\n        \"left\": \"890px\"\n      },\n      \"size\": \"14px\",\n      \"weight\": \"bold\",\n      \"color\": \"#ffffff\",\n      \"data\": {\n        \"dVal\": \"回风湿度\",\n        \"dDataUnit\": \"\"\n      }\n    },\n    {\n      \"name\": \"回风湿度\",\n      \"type\": \"vtext\",\n      \"dataName\": \"回风湿度\",\n      \"position\": {\n        \"top\": \"255px\",\n        \"left\": \"890px\"\n      },\n      \"size\": \"16px\",\n      \"weight\": \"bold\",\n      \"color\": \"#50f100\"\n    },\n    {\n      \"name\": \"传感器-回风\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"125px\",\n        \"left\": \"825px\"\n      },\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/fan/tempSensorBottom.gif\"\n    },\n    {\n      \"name\": \"送风温度Label\",\n      \"type\": \"vtext\",\n      \"dataName\": \"送风温度Label\",\n      \"position\": {\n        \"top\": \"415px\",\n        \"left\": \"915px\"\n      },\n      \"size\": \"14px\",\n      \"weight\": \"bold\",\n      \"color\": \"#ffffff\",\n      \"data\": {\n        \"dVal\": \"送风温度\",\n        \"dDataUnit\": \"\"\n      }\n    },\n    {\n      \"name\": \"送风温度\",\n      \"type\": \"vtext\",\n      \"dataName\": \"送风温度\",\n      \"position\": {\n        \"top\": \"440px\",\n        \"left\": \"915px\"\n      },\n      \"size\": \"16px\",\n      \"weight\": \"bold\",\n      \"color\": \"#50f100\"\n    },\n    {\n      \"name\": \"送风湿度Label\",\n      \"type\": \"vtext\",\n      \"dataName\": \"送风湿度Label\",\n      \"position\": {\n        \"top\": \"415px\",\n        \"left\": \"1015px\"\n      },\n      \"size\": \"14px\",\n      \"weight\": \"bold\",\n      \"color\": \"#ffffff\",\n      \"data\": {\n        \"dVal\": \"送风湿度\",\n        \"dDataUnit\": \"\"\n      }\n    },\n    {\n      \"name\": \"送风湿度\",\n      \"type\": \"vtext\",\n      \"dataName\": \"回风湿度\",\n      \"position\": {\n        \"top\": \"440px\",\n        \"left\": \"1015px\"\n      },\n      \"size\": \"16px\",\n      \"weight\": \"bold\",\n      \"color\": \"#50f100\"\n    },\n    {\n      \"name\": \"传感器-送风\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"555px\",\n        \"left\": \"950px\"\n      },\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/fan/tempSensorBottom.gif\"\n    },\n    {\n      \"name\": \"回风阀反馈Label\",\n      \"type\": \"vtext\",\n      \"dataName\": \"回风阀反馈Label\",\n      \"position\": {\n        \"top\": \"265px\",\n        \"left\": \"190px\"\n      },\n      \"size\": \"14px\",\n      \"weight\": \"bold\",\n      \"color\": \"#ffffff\",\n      \"data\": {\n        \"dVal\": \"回风阀开度\",\n        \"dDataUnit\": \"\"\n      }\n    },\n    {\n      \"name\": \"回风阀反馈\",\n      \"type\": \"vtext\",\n      \"dataName\": \"回风阀反馈\",\n      \"position\": {\n        \"top\": \"295px\",\n        \"left\": \"190px\"\n      },\n      \"size\": \"16px\",\n      \"weight\": \"bold\",\n      \"color\": \"#50f100\"\n    },\n    {\n      \"name\": \"回风阀开关\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"280px\",\n        \"left\": \"289px\"\n      },\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/md_u_0.gif\"\n    },\n    {\n      \"name\": \"水阀阀反馈Label\",\n      \"type\": \"vtext\",\n      \"dataName\": \"水阀阀反馈Label\",\n      \"position\": {\n        \"top\": \"660px\",\n        \"left\": \"530px\"\n      },\n      \"size\": \"14px\",\n      \"weight\": \"bold\",\n      \"color\": \"#ffffff\",\n      \"data\": {\n        \"dVal\": \"水阀开度\",\n        \"dDataUnit\": \"\"\n      }\n    },\n    {\n      \"name\": \"水阀反馈\",\n      \"type\": \"vtext\",\n      \"dataName\": \"水阀反馈\",\n      \"position\": {\n        \"top\": \"680px\",\n        \"left\": \"530px\"\n      },\n      \"size\": \"16px\",\n      \"weight\": \"bold\",\n      \"color\": \"#50f100\"\n    },\n    {\n      \"name\": \"水阀\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"512px\",\n        \"left\": \"635px\"\n      },\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/coil_c_1.gif\"\n    },\n    {\n      \"name\": \"过滤网Label\",\n      \"type\": \"vtext\",\n      \"dataName\": \"过滤网Label\",\n      \"position\": {\n        \"top\": \"415px\",\n        \"left\": \"490px\"\n      },\n      \"size\": \"14px\",\n      \"weight\": \"bold\",\n      \"color\": \"#ffffff\",\n      \"data\": {\n        \"dVal\": \"过滤网报警\",\n        \"dDataUnit\": \"\"\n      }\n    },\n    {\n      \"name\": \"滤网报警1\",\n      \"type\": \"vtext\",\n      \"dataName\": \"滤网报警1\",\n      \"position\": {\n        \"top\": \"440px\",\n        \"left\": \"490px\"\n      },\n      \"size\": \"16px\",\n      \"weight\": \"bold\",\n      \"color\": \"#50f100\"\n    },\n    {\n      \"name\": \"过滤网\",\n      \"type\": \"vdecoration\",\n      \"position\": {\n        \"top\": \"512px\",\n        \"left\": \"490px\"\n      },\n      \"iconSize\": {\n        \"width\": \"auto\",\n        \"height\": \"auto\"\n      },\n      \"url\": \"/images/IdcsGif/kongtiao-xinfeng/filter_l_1.gif\"\n    }\n  ]\n}', '2021-04-17 14:45:25', '2021-05-11 10:15:48', NULL);

4. 创建采集器
    INSERT INTO `bms`.`a_collector`(`id`, `code`, `name`, `description`, `building_id`, `auto_send`, `upload_cycle`, `heartbeat_cycle`, `timeout`, `secret_key`, `state_conn`, `created_at`, `updated_at`) VALUES (1, '127.0.0.1:502', 'techcon01', '采集器1#', 1, 'Y', 300, 60, 30, NULL, 'offline', '2020-10-30 12:34:56', '2021-04-15 20:20:49');
    INSERT INTO `bms`.`a_collector`(`id`, `code`, `name`, `description`, `building_id`, `auto_send`, `upload_cycle`, `heartbeat_cycle`, `timeout`, `secret_key`, `state_conn`, `created_at`, `updated_at`) VALUES (10, 'beacool', 'beacoolSpider', 'bc爬虫', 1, 'Y', NULL, NULL, NULL, NULL, 'online', '2021-01-07 15:08:24', '2021-01-07 15:08:39');

5. 创建实体设备
    INSERT INTO `bms`.`a_item`(`id`, `collector_id`, `code`, `name`, `description`, `ignore_warning`, `icon`, `icon_status`, `state_conn`, `state_work`, `created_at`, `updated_at`) VALUES (1, 1, '1', '空调机组1#', '空调机组1#', 'N', 'images/icon-dianbiao.png', '{}', 'online', 'healthy', '2020-10-30 00:00:00', '2021-04-15 20:24:23');
    INSERT INTO `bms`.`a_item`(`id`, `collector_id`, `code`, `name`, `description`, `ignore_warning`, `icon`, `icon_status`, `state_conn`, `state_work`, `created_at`, `updated_at`) VALUES (10, 1, '73', '73生活用水', '生活用水', 'N', 'images/icon-dianbiao.png', NULL, 'online', 'healthy', '2021-01-02 14:53:02', '2021-02-28 11:04:05');
    INSERT INTO `bms`.`a_item`(`id`, `collector_id`, `code`, `name`, `description`, `ignore_warning`, `icon`, `icon_status`, `state_conn`, `state_work`, `created_at`, `updated_at`) VALUES (1001, 1, '1001', '排风机1#', '排风机1#', 'N', 'images/icon-dianbiao.png', '{}', 'online', 'healthy', '2021-04-21 09:55:30', '2021-04-21 14:28:56');
    INSERT INTO `bms`.`a_item`(`id`, `collector_id`, `code`, `name`, `description`, `ignore_warning`, `icon`, `icon_status`, `state_conn`, `state_work`, `created_at`, `updated_at`) VALUES (2001, 1, '2001', '温感1#', '温感1#', 'N', 'images/icon-dianbiao.png', '{}', 'online', 'healthy', '2021-04-21 14:28:55', '2021-04-21 14:29:21');

6. 创建实体点位数据
    INSERT INTO `lp_ry`.`a_item_data`(`id`, `item_id`, `name`, `val`, `default_val`, `data_type`, `data_unit`, `coefficient`, `note`, `func`, `has_sync`, `created_at`, `updated_at`, `error_note`) VALUES (1, 1, '1#正向有用功', '2449.18', NULL, 'float', 'kwh', 1.0000, NULL, 'read', 'Y', '2020-10-30 09:17:08', '2021-05-12 11:40:20', NULL);
    INSERT INTO `lp_ry`.`a_item_data`(`id`, `item_id`, `name`, `val`, `default_val`, `data_type`, `data_unit`, `coefficient`, `note`, `func`, `has_sync`, `created_at`, `updated_at`, `error_note`) VALUES (10, 10, '73#用水', '4.013', NULL, 'float', '吨', 1.0000, NULL, 'read', 'Y', '2020-11-10 11:02:20', '2021-05-12 11:40:20', NULL);
    INSERT INTO `lp_ry`.`a_item_data`(`id`, `item_id`, `name`, `val`, `default_val`, `data_type`, `data_unit`, `coefficient`, `note`, `func`, `has_sync`, `created_at`, `updated_at`, `error_note`) VALUES (1136, 16, '开关状态', '0', NULL, 'int', '', 1.0000, '1:开;0:关', 'read', 'Y', '2021-01-08 09:20:30', '2021-03-07 23:59:01', NULL);
    INSERT INTO `lp_ry`.`a_item_data`(`id`, `item_id`, `name`, `val`, `default_val`, `data_type`, `data_unit`, `coefficient`, `note`, `func`, `has_sync`, `created_at`, `updated_at`, `error_note`) VALUES (1137, 16, '开关状态更新', '0', NULL, 'int', '', 1.0000, '1:开;0:关', 'write', 'Y', '2021-01-08 09:20:30', '2021-02-02 15:59:54', NULL);
    INSERT INTO `lp_ry`.`a_item_data`(`id`, `item_id`, `name`, `val`, `default_val`, `data_type`, `data_unit`, `coefficient`, `note`, `func`, `has_sync`, `created_at`, `updated_at`, `error_note`) VALUES (1138, 16, '光照度', '100', NULL, 'int', '', 1.0000, '', 'read', 'Y', '2021-01-08 09:20:30', '2021-03-07 23:59:01', NULL);
    INSERT INTO `lp_ry`.`a_item_data`(`id`, `item_id`, `name`, `val`, `default_val`, `data_type`, `data_unit`, `coefficient`, `note`, `func`, `has_sync`, `created_at`, `updated_at`, `error_note`) VALUES (2520, 151, '运行状态', '1', NULL, 'int', NULL, 1.0000, '1:开;0:关', 'read', 'Y', '2021-01-10 11:38:00', '2021-05-12 11:40:10', NULL);
    INSERT INTO `lp_ry`.`a_item_data`(`id`, `item_id`, `name`, `val`, `default_val`, `data_type`, `data_unit`, `coefficient`, `note`, `func`, `has_sync`, `created_at`, `updated_at`, `error_note`) VALUES (2521, 151, '运行状态更新', '1', NULL, 'int', NULL, 1.0000, '1:开;0:关', 'write', 'Y', '2021-01-10 11:38:00', '2021-05-12 11:40:10', NULL);
    INSERT INTO `lp_ry`.`a_item_data`(`id`, `item_id`, `name`, `val`, `default_val`, `data_type`, `data_unit`, `coefficient`, `note`, `func`, `has_sync`, `created_at`, `updated_at`, `error_note`) VALUES (2522, 151, '运行模式', '1', NULL, 'int', NULL, 1.0000, '1:制冷;2:除湿;3:送风;4:制热;', 'read', 'Y', '2021-01-11 15:01:03', '2021-05-12 11:40:10', NULL);
    INSERT INTO `lp_ry`.`a_item_data`(`id`, `item_id`, `name`, `val`, `default_val`, `data_type`, `data_unit`, `coefficient`, `note`, `func`, `has_sync`, `created_at`, `updated_at`, `error_note`) VALUES (2523, 151, '运行模式更新', '1', NULL, 'int', NULL, 1.0000, '1:制冷;2:除湿;3:送风;4:制热;', 'write', 'Y', '2021-01-11 15:02:09', '2021-05-12 11:40:10', NULL);
    INSERT INTO `lp_ry`.`a_item_data`(`id`, `item_id`, `name`, `val`, `default_val`, `data_type`, `data_unit`, `coefficient`, `note`, `func`, `has_sync`, `created_at`, `updated_at`, `error_note`) VALUES (2524, 151, '风量设定', '2', NULL, 'int', NULL, 1.0000, '1:高风;2:中风;3:低风;', 'read', 'Y', '2021-01-11 15:02:51', '2021-05-12 11:40:10', NULL);
    INSERT INTO `lp_ry`.`a_item_data`(`id`, `item_id`, `name`, `val`, `default_val`, `data_type`, `data_unit`, `coefficient`, `note`, `func`, `has_sync`, `created_at`, `updated_at`, `error_note`) VALUES (2525, 151, '风量设定更新', '2', NULL, 'int', NULL, 1.0000, '1:高风;2:中风;3:低风;', 'write', 'Y', '2021-01-11 15:03:34', '2021-05-12 11:40:11', NULL);
    INSERT INTO `lp_ry`.`a_item_data`(`id`, `item_id`, `name`, `val`, `default_val`, `data_type`, `data_unit`, `coefficient`, `note`, `func`, `has_sync`, `created_at`, `updated_at`, `error_note`) VALUES (2526, 151, '设定温度', '25.00', NULL, 'int', NULL, 1.0000, NULL, 'read', 'Y', '2021-01-11 15:04:19', '2021-05-12 11:40:11', NULL);
    INSERT INTO `lp_ry`.`a_item_data`(`id`, `item_id`, `name`, `val`, `default_val`, `data_type`, `data_unit`, `coefficient`, `note`, `func`, `has_sync`, `created_at`, `updated_at`, `error_note`) VALUES (2527, 151, '设定温度更新', '25.00', NULL, 'int', NULL, 1.0000, NULL, 'write', 'Y', '2021-01-11 15:04:37', '2021-05-12 11:40:11', NULL);
    INSERT INTO `lp_ry`.`a_item_data`(`id`, `item_id`, `name`, `val`, `default_val`, `data_type`, `data_unit`, `coefficient`, `note`, `func`, `has_sync`, `created_at`, `updated_at`, `error_note`) VALUES (2529, 151, '报警代码', '0', NULL, 'string', NULL, 1.0000, NULL, 'read', 'Y', '2021-01-11 15:07:02', '2021-05-12 11:40:11', NULL);

7. 创建数据采集模板
    INSERT INTO `bms`.`a_point_table`(`id`, `collector_id`, `item_data_id`, `data_name`, `addr`, `device_id`, `length`, `data_group`, `func`, `func_list`, `type`) VALUES (1, 1, 1, '运行状态', 1102, 1, 1, 1102, '01', '[[\"booleanToInt\"]]', 'r');
    INSERT INTO `bms`.`a_point_table`(`id`, `collector_id`, `item_data_id`, `data_name`, `addr`, `device_id`, `length`, `data_group`, `func`, `func_list`, `type`) VALUES (12, 1, 12, '新风温度', 1303, 1, 2, 1301, '03', '[[\"swapToBE\",\"3210\"],[\"F32BE\"],[\"divide\", 1, 2]]', 'r');
    INSERT INTO `bms`.`a_point_table`(`id`, `collector_id`, `item_data_id`, `data_name`, `addr`, `device_id`, `length`, `data_group`, `func`, `func_list`, `type`) VALUES (21, 1, 21, '温度设定更新', 1400, 1, 2, 1401, '16', '[[\"divide\", 1, 2],[\"float2Buff\"],[\"swapToBE\",\"1032\"]]', 'w');
    INSERT INTO `bms`.`a_point_table`(`id`, `collector_id`, `item_data_id`, `data_name`, `addr`, `device_id`, `length`, `data_group`, `func`, `func_list`, `type`) VALUES (22, 1, 22, '季节设定', 1403, 1, 2, 1401, '03', '[[\"swapToBE\",\"3210\"],[\"F32BE\"]]', 'r');
    INSERT INTO `bms`.`a_point_table`(`id`, `collector_id`, `item_data_id`, `data_name`, `addr`, `device_id`, `length`, `data_group`, `func`, `func_list`, `type`) VALUES (2001, 1, 2001, '温度', 1305, 1, 2, 1301, '03', '[[\"swapToBE\",\"3210\"],[\"F32BE\"],[\"divide\", 1, 2]]', 'r');

    INSERT INTO `bms`.`a_point_table`(`id`, `collector_id`, `item_data_id`, `data_name`, `addr`, `device_id`, `length`, `data_group`, `func`, `func_list`, `type`) VALUES (35, 3, 1520, '2_1运行状态', 40093, 1, 1, 40093, '03', '[[\"swapToBE\",\"01\"],[\"readBitInBuffer\"],[\"readBitVal\",0,{\"0\":0,\"1\":1}]]', 'r');
    INSERT INTO `bms`.`a_point_table`(`id`, `collector_id`, `item_data_id`, `data_name`, `addr`, `device_id`, `length`, `data_group`, `func`, `func_list`, `type`) VALUES (36, 3, 1522, '2_2运转模式', 40094, 1, 1, 40093, '03', '[[\"swapToBE\",\"01\"],[\"readBitInBuffer\"],[\"readBitMapVal\",[],1,{\"0\":0,\"1\":1,\"2\":2,\"3\":3,\"4\":4}]]', 'r');
    INSERT INTO `bms`.`a_point_table`(`id`, `collector_id`, `item_data_id`, `data_name`, `addr`, `device_id`, `length`, `data_group`, `func`, `func_list`, `type`) VALUES (37, 3, 1524, '2_3风量', 40095, 1, 1, 40093, '03', '[[\"swapToBE\",\"01\"],[\"readBitInBuffer\"],[\"readBitMapVal\",[],1,{\"0\":0,\"1\":1,\"2\":2,\"3\":3,\"4\":4}]]', 'r');
    INSERT INTO `bms`.`a_point_table`(`id`, `collector_id`, `item_data_id`, `data_name`, `addr`, `device_id`, `length`, `data_group`, `func`, `func_list`, `type`) VALUES (38, 3, 1526, '2_4设定温度', 40099, 1, 2, 40093, '03', '[[\"swapToBE\",\"3210\"],[\"I16BE\"],[\"divide\", 1, 2]]', 'r');
    INSERT INTO `bms`.`a_point_table`(`id`, `collector_id`, `item_data_id`, `data_name`, `addr`, `device_id`, `length`, `data_group`, `func`, `func_list`, `type`) VALUES (41, 3, 1521, '2_1r运行状态', 40169, 1, 1, 40093, '16', '[[\"parseBitVal\",0,{\"0\":0,\"1\":1}]]', 'w');
    INSERT INTO `bms`.`a_point_table`(`id`, `collector_id`, `item_data_id`, `data_name`, `addr`, `device_id`, `length`, `data_group`, `func`, `func_list`, `type`) VALUES (42, 3, 1523, '2_2r运行模式', 40170, 1, 1, 40093, '16', '[[\"parseBitMapVal\",1,{\"0\":0,\"1\":1,\"2\":2,\"3\":3,\"4\":4}]]', 'w');
    INSERT INTO `bms`.`a_point_table`(`id`, `collector_id`, `item_data_id`, `data_name`, `addr`, `device_id`, `length`, `data_group`, `func`, `func_list`, `type`) VALUES (43, 3, 1525, '2_3r风量', 40171, 1, 1, 40093, '16', '[[\"parseBitMapVal\",1,{\"0\":0,\"1\":1,\"2\":2,\"3\":3}]]', 'w');
    INSERT INTO `bms`.`a_point_table`(`id`, `collector_id`, `item_data_id`, `data_name`, `addr`, `device_id`, `length`, `data_group`, `func`, `func_list`, `type`) VALUES (44, 3, 1526, '2_4r设定温度', 40173, 1, 2, 40093, '16', '[[\"divide\", 1, 2]]', 'w');

11. 设备报警规则

12. 设备报警关联


13. 创建显示设备
    INSERT INTO `bms`.`d_device`(`id`, `type`, `source_id`, `name`, `description`, `note`, `tag`, `icon`, `thumb`, `pictures`, `status`, `activation_time`, `scrap_time`, `life`, `building_id`, `resource_id`, `created_at`, `updated_at`) VALUES (1, 'airCondition', '', '空调机组#1', '空调机组#1', NULL, NULL, '/uploads/icon-airCondition.png', NULL, NULL, 'Y', NULL, NULL, '100%', 1, 2, '2021-01-07 17:51:53', '2021-05-11 10:19:52');
    INSERT INTO `bms`.`d_device`(`id`, `type`, `source_id`, `name`, `description`, `note`, `tag`, `icon`, `thumb`, `pictures`, `status`, `activation_time`, `scrap_time`, `life`, `building_id`, `resource_id`, `created_at`, `updated_at`) VALUES (1001, 'blower', '', '送排风机#1', '送排风机#1', NULL, NULL, '/uploads/icon-blower.png', NULL, NULL, 'Y', NULL, NULL, '100%', 1, NULL, '2021-04-21 14:24:55', '2021-05-11 18:49:44');
    INSERT INTO `bms`.`d_device`(`id`, `type`, `source_id`, `name`, `description`, `note`, `tag`, `icon`, `thumb`, `pictures`, `status`, `activation_time`, `scrap_time`, `life`, `building_id`, `resource_id`, `created_at`, `updated_at`) VALUES (2001, 'surroundings', '', '温度传感器#1', '温度传感器#1', NULL, NULL, '/uploads/icon-surroundings.png', NULL, NULL, 'Y', NULL, NULL, '100%', 1, NULL, '2021-04-21 14:27:26', '2021-05-11 10:31:33');

14. 创建显示设备绑点
    INSERT INTO `bms`.`d_device_item_data_map`(`id`, `device_id`, `item_data_id`, `oid`, `name`, `relate_item_data_id`, `tag`, `note`, `created_at`, `updated_at`) VALUES (1, 1, 1, 0, '运行状态', NULL, 'status', NULL, '2021-01-08 11:51:26', '2021-04-21 15:29:18');
    INSERT INTO `bms`.`d_device_item_data_map`(`id`, `device_id`, `item_data_id`, `oid`, `name`, `relate_item_data_id`, `tag`, `note`, `created_at`, `updated_at`) VALUES (2, 1, 2, 2, '故障状态', NULL, 'more', NULL, '2021-01-08 11:51:26', '2021-04-17 21:23:38');
    INSERT INTO `bms`.`d_device_item_data_map`(`id`, `device_id`, `item_data_id`, `oid`, `name`, `relate_item_data_id`, `tag`, `note`, `created_at`, `updated_at`) VALUES (3, 1, 3, 2, '手自动状态', NULL, 'more', NULL, '2021-01-08 11:51:26', '2021-04-17 21:23:38');
    INSERT INTO `bms`.`d_device_item_data_map`(`id`, `device_id`, `item_data_id`, `oid`, `name`, `relate_item_data_id`, `tag`, `note`, `created_at`, `updated_at`) VALUES (4, 1, 4, 2, '滤网报警1', NULL, 'more', NULL, '2021-01-08 11:51:26', '2021-04-17 21:23:38');
    INSERT INTO `bms`.`d_device_item_data_map`(`id`, `device_id`, `item_data_id`, `oid`, `name`, `relate_item_data_id`, `tag`, `note`, `created_at`, `updated_at`) VALUES (5, 1, 5, 2, '滤网报警2', NULL, 'more', NULL, '2021-01-08 11:51:26', '2021-04-17 21:23:38');
    INSERT INTO `bms`.`d_device_item_data_map`(`id`, `device_id`, `item_data_id`, `oid`, `name`, `relate_item_data_id`, `tag`, `note`, `created_at`, `updated_at`) VALUES (1001, 1001, 1001, 0, '运行状态', NULL, 'status', NULL, '2021-01-08 11:51:26', '2021-04-21 15:29:22');
    INSERT INTO `bms`.`d_device_item_data_map`(`id`, `device_id`, `item_data_id`, `oid`, `name`, `relate_item_data_id`, `tag`, `note`, `created_at`, `updated_at`) VALUES (1002, 1001, 1002, 2, '故障状态', NULL, 'more', NULL, '2021-01-08 11:51:26', '2021-04-21 14:35:42');
    INSERT INTO `bms`.`d_device_item_data_map`(`id`, `device_id`, `item_data_id`, `oid`, `name`, `relate_item_data_id`, `tag`, `note`, `created_at`, `updated_at`) VALUES (1003, 1001, 1003, 2, '手自动状态', NULL, '', NULL, '2021-01-08 11:51:26', '2021-04-21 14:55:10');
    INSERT INTO `bms`.`d_device_item_data_map`(`id`, `device_id`, `item_data_id`, `oid`, `name`, `relate_item_data_id`, `tag`, `note`, `created_at`, `updated_at`) VALUES (1004, 1001, 1004, 2, '滤网报警1', NULL, 'more', NULL, '2021-01-08 11:51:26', '2021-04-21 14:36:53');
    INSERT INTO `bms`.`d_device_item_data_map`(`id`, `device_id`, `item_data_id`, `oid`, `name`, `relate_item_data_id`, `tag`, `note`, `created_at`, `updated_at`) VALUES (1005, 1001, 1005, 0, '机组启动', 1006, '', NULL, '2021-01-08 11:51:26', '2021-04-21 14:55:13');
    INSERT INTO `bms`.`d_device_item_data_map`(`id`, `device_id`, `item_data_id`, `oid`, `name`, `relate_item_data_id`, `tag`, `note`, `created_at`, `updated_at`) VALUES (2001, 2001, 2001, 0, '当前温度', NULL, 'status,map', NULL, '2021-01-08 11:51:26', '2021-04-21 15:34:39');

15. 创建设备地图关联
    INSERT INTO `bms`.`d_device_resource_map`(`id`, `device_id`, `resource_id`, `x_axis`, `y_axis`, `z_axis`, `other_data`, `note`, `created_at`, `updated_at`) VALUES (1, 1, 1, 43.8743248, 368.9433894, NULL, NULL, NULL, '2021-01-07 19:01:35', '2021-04-17 15:47:05');
    INSERT INTO `bms`.`d_device_resource_map`(`id`, `device_id`, `resource_id`, `x_axis`, `y_axis`, `z_axis`, `other_data`, `note`, `created_at`, `updated_at`) VALUES (2, 1, 2, NULL, NULL, NULL, NULL, NULL, '2021-04-17 15:47:10', '2021-04-21 16:58:04');
    INSERT INTO `bms`.`d_device_resource_map`(`id`, `device_id`, `resource_id`, `x_axis`, `y_axis`, `z_axis`, `other_data`, `note`, `created_at`, `updated_at`) VALUES (1001, 1001, 1, 43.8743248, 268.9433894, NULL, NULL, NULL, '2021-04-21 14:51:19', '2021-04-21 14:51:28');
    INSERT INTO `bms`.`d_device_resource_map`(`id`, `device_id`, `resource_id`, `x_axis`, `y_axis`, `z_axis`, `other_data`, `note`, `created_at`, `updated_at`) VALUES (2001, 2001, 1, 63.8743248, 178.9433894, NULL, NULL, NULL, '2021-04-21 14:51:31', '2021-04-21 14:51:52');

16. 场景创建
    INSERT INTO `bms`.`d_scene`(`id`, `device_type`, `name`, `icon`, `type`, `condition_compose_type`, `status`, `created_at`, `updated_at`) VALUES (1, 'airCondition', '空调每日定时关机', 'close', 1, '1', 1, '2021-05-04 08:31:23', '2021-05-07 14:14:42');
    INSERT INTO `bms`.`d_scene`(`id`, `device_type`, `name`, `icon`, `type`, `condition_compose_type`, `status`, `created_at`, `updated_at`) VALUES (2, 'airCondition', '夏季供冷节假日', 'cool', 1, '1', 1, '2021-05-04 08:33:04', '2021-05-04 08:38:39');
    INSERT INTO `bms`.`d_scene`(`id`, `device_type`, `name`, `icon`, `type`, `condition_compose_type`, `status`, `created_at`, `updated_at`) VALUES (3, 'light', '工作日自动开灯', 'open', 1, '1', 1, '2021-05-07 18:24:34', '2021-05-07 18:24:59');

17. 执行任务创建
    INSERT INTO `lp_ry`.`d_device_task`(`id`, `name`, `type`, `note`, `other_data`, `recorded_content`, `recorded_at`) VALUES (1, '定时关机-19', 1, '超过晚上7点, 空调自动关闭', '{\n  \"holidays\": \"\",\n  \"weekend\": \"\",\n  \"year\": \"\",\n  \"month\": \"\",\n  \"day\": \"\",\n  \"weekDay\": \"\",\n  \"hour\": \"19\",\n  \"minute\": \"\",\n  \"sleepTime\": \"60\",\n  \"actions\": [\n    {\n      \"deviceType\": \"airCondition\",\n      \"deviceIds\": \"\",\n      \"deviceDataName\": \"运行状态\",\n      \"value\": \"0\"\n    }\n  ]\n}', '2651,1;2655,1;2659,1', '2021-05-08 10:44:59');
    INSERT INTO `lp_ry`.`d_device_task`(`id`, `name`, `type`, `note`, `other_data`, `recorded_content`, `recorded_at`) VALUES (2, '定时关机-23', 1, '超过晚上11点, 空调自动关闭', '{\n  \"holidays\": \"\",\n  \"weekend\": \"\",\n  \"year\": \"\",\n  \"month\": \"\",\n  \"day\": \"\",\n  \"weekDay\": \"\",\n  \"hour\": \"23\",\n  \"minute\": \"\",\n  \"sleepTime\": \"60\",\n  \"actions\": [\n    {\n      \"deviceType\": \"airCondition\",\n      \"deviceIds\": \"\",\n      \"deviceDataName\": \"运行状态\",\n      \"value\": \"0\"\n    }\n  ]\n}', NULL, NULL);
    INSERT INTO `lp_ry`.`d_device_task`(`id`, `name`, `type`, `note`, `other_data`, `recorded_content`, `recorded_at`) VALUES (3, '工作日空调自动制冷', 2, '环境温度超过30度，空调全部启动', '{\n  \"holidays\": \"0\",\n  \"weekend\": \"0\",\n  \"year\": \"\",\n  \"month\": \"\",\n  \"day\": \"\",\n  \"weekDay\": \"\",\n  \"hour\": \"\",\n  \"minute\": \"\",\n  \"sleepTime\": \"30\",\n  \"deviceType\": \"airCondition\",\n  \"deviceIds\": \"\",\n  \"actions\": [\n    {\n      \"deviceDataName\": \"运行状态\",\n      \"value\": \"1\"\n    },\n    {\n      \"deviceDataName\": \"运行模式\",\n      \"value\": \"1\"\n    },\n    {\n      \"deviceDataName\": \"设定温度\",\n      \"value\": \"20\"\n    },\n    {\n      \"deviceDataName\": \"风量设定\",\n      \"value\": \"3\"\n    }\n  ],\n  \"condition\": {\n    \"deviceType\": \"\",\n    \"deviceIds\": \"2001,2002,2003\",\n    \"deviceDataName\": \"温度\",\n    \"isMember\": \"1\",\n    \"compare\": \">=\",\n    \"value\": \"30\",\n  }\n}', NULL, NULL);
    INSERT INTO `lp_ry`.`d_device_task`(`id`, `name`, `type`, `note`, `other_data`, `recorded_content`, `recorded_at`) VALUES (7, '工作日空调自动关闭', 1, '环境温度低于26度，空调全部关闭', '{\n  \"holidays\": \"0\",\n  \"weekend\": \"0\",\n  \"year\": \"\",\n  \"month\": \"\",\n  \"day\": \"\",\n  \"weekDay\": \"\",\n  \"hour\": \"\",\n  \"minute\": \"\",\n  \"sleepTime\": \"30\",\n  \"deviceType\": \"airCondition\",\n  \"deviceIds\": \"\",\n  \"actions\": [\n    {\n      \"deviceDataName\": \"运行状态\",\n      \"value\": \"0\"\n    }\n  ],\n  \"condition\": {\n    \"deviceType\": \"\",\n    \"deviceIds\": \"2001,2002,2003\",\n    \"deviceDataName\": \"温度\",\n    \"isMember\": \"1\",\n    \"compare\": \"<\",\n    \"value\": \"26\",\n  }\n}', NULL, NULL);
    INSERT INTO `lp_ry`.`d_device_task`(`id`, `name`, `type`, `note`, `other_data`, `recorded_content`, `recorded_at`) VALUES (8, '定时开灯-17', 1, '工作日，超过晚上5点, 自动开灯', '{\n  \"holidays\": \"\",\n  \"weekend\": \"\",\n  \"year\": \"\",\n  \"month\": \"\",\n  \"day\": \"\",\n  \"weekDay\": \"\",\n  \"hour\": \"19\",\n  \"minute\": \"\",\n  \"sleepTime\": \"60\",\n  \"actions\": [\n    {\n      \"deviceDataName\": \"运行状态\",\n      \"value\": \"0\"\n    }\n  ]\n}', NULL, NULL);
    INSERT INTO `lp_ry`.`d_device_task`(`id`, `name`, `type`, `note`, `other_data`, `recorded_content`, `recorded_at`) VALUES (9, '联动开灯', 2, NULL, '{\n  \"holidays\": \"\",\n  \"weekend\": \"\",\n  \"year\": \"\",\n  \"month\": \"\",\n  \"day\": \"\",\n  \"weekDay\": \"\",\n  \"hour\": \"19\",\n  \"minute\": \"\",\n  \"sleepTime\": \"60\",\n  \"actions\": [\n    {\n      \"deviceDataName\": \"运行状态\",\n      \"value\": \"0\"\n    }\n  ],\n  \"condition\": {        // 联动条件，目前只支持一类条件\n      \"deviceType\": \"\",   // 对应设备类型\n      \"deviceIds\": \"2001,2002,2003\",    // 设备IDs，如果为空，则为当前类型下所有设备\n      \"deviceDataName\": \"温度\",  // 设备点位名\n      \"isMember\": \"1\",    // 触发条件。 1.单一条件; 2.全部条件\n      \"compare\": \">=\",    // 对比公式\n      \"value\": \"30\",      // 对比值\n    }\n}', NULL, NULL);

18. 场景任务关联
    INSERT INTO `lp_ry`.`d_scene_map`(`id`, `scene_id`, `task_id`, `device_ids`, `oid`, `created_at`, `updated_at`) VALUES (1, 1, 1, NULL, 1, '2021-05-07 14:15:21', '2021-05-07 14:16:23');
    INSERT INTO `lp_ry`.`d_scene_map`(`id`, `scene_id`, `task_id`, `device_ids`, `oid`, `created_at`, `updated_at`) VALUES (2, 1, 2, NULL, 2, '2021-05-07 14:15:38', '2021-05-07 14:15:38');
    INSERT INTO `lp_ry`.`d_scene_map`(`id`, `scene_id`, `task_id`, `device_ids`, `oid`, `created_at`, `updated_at`) VALUES (4, 3, 8, '2005,2006,2007', 0, '2021-05-08 09:04:24', '2021-05-08 10:52:53');
    INSERT INTO `lp_ry`.`d_scene_map`(`id`, `scene_id`, `task_id`, `device_ids`, `oid`, `created_at`, `updated_at`) VALUES (6, 3, 9, '2005,2006', 0, '2021-05-08 10:55:28', '2021-05-08 10:56:10');








迭代内容
  1. 设备如果是开启状态 status = 1，不显示离线
  2. 设备修改开关，需要马上反馈到 device 表字段更新


## 组态数据配置
字典管理 》 设备列表显示配置 》 对应的页签 点 修改 》 数据键值里面 添加 "protoTypeDisplay":"by" （可选值:map，3d，by）

表 a_resource   other_data 字段  {"deviceId":7224484764960}   设置组态模版id
表 d_device_resource_map 的 device_id( 设备id： 表d_device 的id字段  ) 和 resource_id ( 表 a_resource 的id ) 相关联


## speed3d 数据格式
数据面板
{
"type": "deviceData",
"data": {
    "设备id": {
        "数据点id(dId)": "数据值(dVal)",
        "param2": "stop",
        "param3": "offline"
    },
    "123": {
        "param1": "runing",
        "param2": "stop",
        "param3": "offline"
        }
    }
}

设备状态（runing,stop,offline,fault,warning）
{
"type": "deviceStatus",
"data": {
    "设备id": "状态值",
    "9102002": "stop",
    "9102003": "offline",
    "9102004": "fault"
    }
}

## 根据用户id重定向页面
    1.菜单管理里面添加重定向菜单，路由地址如 /newpage/userpage ，组件文件 dashboard/loginRedirect
    1.基础配置 的 loginRedirect 配置值 /newpage/userpage（重定向的页面，具体以项目配置的路由为准）
    2.基础配置 的  userRedirectPage 配置值 {"1":"/energyMgt/electricity/summary"} （用户id：路由地址）

## 用 iframe 链接配置路由页面 
    1.例iframe 链接为 https://ibms.lanxing.tech，在 ibms上的 系统工具》查询测试》base64编码 进行编码为 aHR0cHM6Ly9pYm1zLmxhbnhpbmcudGVjaA==
    2.配置菜单 路由地址为 aHR0cHM6Ly9pYm1zLmxhbnhpbmcudGVjaA== ， 组件为 device/monitor/components/FrameInside/index
    3.点击这个菜单页面内就会显示 https://ibms.lanxing.tech 的内容

## 用 图片链接 链接配置路由页面
    1.例图片的链接为 /uploads/qd_dt_l6/sys_a.jpg 在 ibms上的 系统工具》查询测试》base64编码 进行编码为 L3VwbG9hZHMvcWRfZHRfbDYvc3lzX2EuanBn
    2.配置菜单 路由地址为 L3VwbG9hZHMvcWRfZHRfbDYvc3lzX2EuanBn ， 组件为 device/monitor/components/FrameInside/picture 
    3.点击这个菜单页面内就会显示 /uploads/qd_dt_l6/sys_a.jpg 这图片


## speed3d 编辑注意事项
    1. 物体属性，动画的 设备编码 写 数据点名称，动画和 动态属性的旋转名称 写 数据点名称
        动画： pause-暂停 stop-停止 play-播放
        多个动态属性相同时，可以设置相同的 设备编码 和  动态属性的旋转名称
        动画数据：   {
                "type": "deviceAnimation",
                "data": {
                    设备编码: {
                        "name": 动画名称,
                        "action": "stop"
                    }
                }
             }
        物体属性数据： {
                "type": "deviceProps",
                "data": {
                    设备编码: {
                        旋转: {"x": 60}
                    }
                }
             }
    2.设置状态：模型的 设备编码 写 设备id，需要设置 场景设置》状态设置（设置对应状态下模型的颜色，动画，边框），勾选 后处理
        数据： {
                "type": "deviceStatus",
                "data": {
                    设备编码: "offline"
                }
            }
    3.数据面板：设备编码 写 设备id，编辑数据绑定，value 写数据点名称
        数据：{
                "type": "deviceData",
                "data": {
                    设备编码: {
                        "自动状态": "自动",
                        "故障状态": "正常",
                        "运行状态": "停止",
                        "启停控制": "关闭",
                        "风阀": "0 %"
                    }
                }
            }
## 回路位置 相关配置
    按钮显示配置： 字典管理 》 设备列表显示配置 》 对应页面配置 》 添加 "hasEditPosition": "1", （"0"： 不显示按钮， "1"： 显示按钮，不填写不显示）
    回路位置修改页面数据配置： d_device_item_data_map 表里面需要修改的数据的  tag 里面添加 positionEdit

## 系统图编辑添加 按钮
    1. 支持类型： displayType ： map 或者 sp3d
    2. 按钮的文字在 data的dVal 里面设置，不写会默认为 下发 文字
    3. 现在还只支持下发全部可控数据点

## rtsp视频 配置
    1.和 camera 视频区别  fullpath 名称改成 rtsp， val值写 rtsp流地址 例：rtsp://admin:a1234567@**********/Streaming/Channels/101
    2. 启  node video-rtsp2Websocket.js 2156 ，2156 为端口号，默认的Websocket服务地址是 ws://localhost:2156，  端口不是 2156 时需配置字典
    3. 字典管理 》 系统配置 里面加 rtspServer, 值是 ws://服务ip:node端口

## 将设备信息转成二维码
   运行: node deviceToqrcode.js [buildingId] [dbName] [serverName] [debug]
       dbName： 数据库名 例 ly_jx_hl
       serverName： 站点名称 例 hailuo.yuankong.org.cn
   运行后会在当前文件夹下面生成一个 qrCode 文件夹，生成的二维码图片在qrCode下面

## 补 e_energy_data_history_daily 数据
    1. 修改config.url 和 config.headers.Authorization 和 length（从当前日期往前推的需要修改的 天数）
    2. 运行 node updateEnergyDailyData.js [buildingId] [dbName] [debug]

## 创建群控系统图页面
    1. 创建菜单  组件 device/dataView/Prototype.vue
    2. 字典管理 》 设备系统图配置 》 新增 
        例： {"pageTitle":"冷源","category":"冷源3d","type":"sp3d", "displayTypes": ["sp3d","list"],"displayType":"sp3d"}
    3. 配置 a_resource  val 填写 3d文件 路径
    4. resource 和 设备id 关联
    5. 3d 文件编辑的时候对应模型上 属性面板 》 对象属性 》 设备编码  上填写对应的 设备id，页面上点模型可以弹详情框

## 修改 a_item_data 中 has_sync 为 N 的更新点，改成 Y,并修改对应点的值
    修改数据库名， debug 可带可不带
    运行脚本 node syncItemDataHasSync.js ly_ibms debug

## 自动生成工单设置
    1. 字典管理 》 系统配置 》 device_warning_auto_maintenance  设置  {"checkTime":300}   // 设置多少秒后自动生成工单
    2. 设置 a_warning_rule 表里面的 auto_flag // 是否自动转工单标记 1:自动, 0: 不自动转
    

               


