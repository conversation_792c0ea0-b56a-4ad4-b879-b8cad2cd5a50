package com.ruoyi.base.controller.api;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.base.service.IDictConfigService;
import com.ruoyi.base.service.IUtilService;
import com.ruoyi.base.service.UtilsService;
import com.ruoyi.base.utils.UptimeMonitor;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.DictUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sign.Base64;
import com.ruoyi.common.utils.sign.DongleUtils;
import com.ruoyi.common.utils.sign.RsaUtils;
import com.ruoyi.framework.config.ServerConfig;
import com.ruoyi.framework.web.service.EmailSenderService;
import com.ruoyi.system.domain.SysConfig;
import com.ruoyi.system.mapper.SysConfigMapper;
import com.ruoyi.system.service.ISysDictTypeService;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.nio.file.attribute.FileTime;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.jar.Attributes;
import java.util.jar.Manifest;

@RestController
public class CommController {

    private static final Logger log = LoggerFactory.getLogger(CommController.class);

    @Autowired
    private ServerConfig serverConfig;

    @Autowired
    IUtilService utilService;

    @Autowired
    EmailSenderService mailSender;

    @Autowired
    private ISysDictTypeService dictTypeService;

    @Autowired
    private IDictConfigService dictConfigService;

    // 验证码类型
    @Value("${ruoyi.autoLoginCheck:0}")
    private String autoLoginCheck;

    @Value("${ruoyi.licenseKey:智能楼控项目}")
    private String licenseKey;

    @Value("${ruoyi.licenseCode:}")
    private String licenseCode;

    @Autowired
    private RuoYiConfig ruoYiConfig;

    @Autowired
    private SysConfigMapper sysConfigMapper;

    /**
     * sysBaseConfig
     */
    @GetMapping("/common/base/settings")
    public AjaxResult settings() {
        AjaxResult ajax = AjaxResult.success();
        Map data = new HashMap<>();
        data.put("autoLoginCheck", autoLoginCheck);
        List<SysDictData> list = dictTypeService.selectDictDataByType("base_configs");
        if(list != null) {
            for (SysDictData sd: list) {
                data.put(sd.getDictLabel(), sd.getDictValue());
            }
        }
        ajax.put("data", data); // 测试连redis
        ajax.put("runTime", UptimeMonitor.getUptime());   // 反馈程序运行时长
        List<SysConfig> sysConfig = sysConfigMapper.selectConfigList(new SysConfig()); // 测试连数据库
        ajax.put("sysConfig", sysConfig);
        return ajax;
    }

    // 发送邮件接口
    @ApiOperation("发送邮件")
    @RequestMapping(value="/test/command/api/sendMail", method = RequestMethod.POST)
    public AjaxResult sendMail(HttpServletRequest request) {
        SimpleMailMessage message = new SimpleMailMessage();
        String mailTo = request.getParameter("mailTo");
        String subject = request.getParameter("subject");
        String content = request.getParameter("content");
        List<String> mailList = Arrays.asList(mailTo.split(","));
        String[] sendTo = mailList.toArray(new String[mailList.size()]);

        try {
            boolean isSend = mailSender.sendEmail(subject, sendTo, null, content, null);
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
        return AjaxResult.success();
    }

    // 透传接口
    @ApiOperation("透传接口，支持前端代理请求三方服务器数据")
    @RequestMapping(value="/common/thirdRequest", method = RequestMethod.POST)
    public AjaxResult thirdRequest(@RequestParam(value = "query") String query,
                                   HttpServletRequest request) {
        AjaxResult ajax = AjaxResult.success();
        Map data = new HashMap<>();
        try {
            query = Base64.getBase64Decode(query);
            JSONObject queryObj = JSONUtil.parseObj(query);
            String res = null;
            String method = "get";
            String url = queryObj.get("url").toString();
            if(null != queryObj && !queryObj.isEmpty()) {
                String _method = queryObj.get("method").toString();
                if("post".equals(StringUtils.lowerCase(_method))) {
                    method = "post";
                }
            }
            if("post".equals(method)) {
                res = utilService.sendPostRequest(queryObj);
            } else {
                res = utilService.sendGetRequest(url);
            }
            return AjaxResult.success("", res);
        } catch (Exception ex) {
            return AjaxResult.error();
        }
    }

    // 刷新字典缓存
    @GetMapping("/refreshDictCache")
    public AjaxResult refreshDictCache() {
        AjaxResult ajax = AjaxResult.success();
        DictUtils.clearDictCache();
        log.info("刷新字典缓存成功");
        return ajax;
    }

    // 获取公钥 前端用来密码加密
    @GetMapping("/publicKey")
    public AjaxResult publicKey() {
        AjaxResult ajax = AjaxResult.success();
        ajax.put("publicKey", RsaUtils.getPublicKey());
        ajax.put("sPublicKey", RsaUtils.getPublicKeyStr2());
        ajax.put("runTime", UptimeMonitor.getUptime());
        Long version = 0l;
        try {
            URL location = CommController.class.getProtectionDomain().getCodeSource().getLocation();

            URL jarUrl = this.getClass().getProtectionDomain().getCodeSource().getLocation();
            // 判断是否为 jar 协议（即运行在 jar 中）
            if ("jar".equals(location.getProtocol())) {
                // 运行在 jar 包中
                File jarFile = new File(location.toURI());
                version = jarFile.lastModified();
            } else {
                // 开发模式下运行，可能为 file 协议
                File classDir = new File(location.getPath());

                // 方式1：查找 pom.xml（适用于 Maven 项目）
                File projectRoot = findProjectRoot(classDir);
                if (projectRoot != null) {
                    File pomFile = new File(projectRoot, "pom.xml");
                    if (pomFile.exists()) {
                        version = pomFile.lastModified();
                    }
                }

                // 方式2：如果找不到 pom.xml，可以找主类 .java 或 .class 文件
                String classNamePath = CommController.class.getName().replace('.', '/') + ".class";
                URL classUrl = CommController.class.getClassLoader().getResource(classNamePath);
                if (classUrl != null && "file".equals(classUrl.getProtocol())) {
                    Path classPath = new File(classUrl.toURI()).toPath();
                    BasicFileAttributes attr = Files.readAttributes(classPath, BasicFileAttributes.class);
                    version = attr.lastModifiedTime().toMillis();
                }
            }
        } catch (Exception e) {
            // pass
        }
        ajax.put("version", ruoYiConfig.getVersion()+"."+new SimpleDateFormat("yyyyMMddHH").format(version));
        return ajax;
    }

    // 尝试向上查找项目根目录（包含 pom.xml）
    private static File findProjectRoot(File dir) {
        File current = dir;
        while (current != null) {
            if (new File(current, "pom.xml").exists()) {
                return current;
            }
            current = current.getParentFile();
        }
        return null;
    }

    // 验证授权码是否过期
    @GetMapping("/checkLicenseExpired")
    public AjaxResult checkLicenseExpired(HttpServletRequest request) {
        if(DongleUtils.checkDongleReady()) {
            String expirationDate = dictConfigService.getLicenseExpDate();
            String today = DateUtils.getDate();
            if(StringUtils.isNotEmpty(expirationDate)) {
                long diffDay = DateUtils.getDateDifference(expirationDate, today, "days");
                if(diffDay >= 0) {
                    return AjaxResult.success("success", diffDay);
                } else {
                    return AjaxResult.success("授权已过期" + Math.abs(diffDay) + "天", 0);
                }
            } else {
                return AjaxResult.success("授权码不匹配", -1);
            }
        } else {
            // 尝试读取授权码的过期时间
            try {
                String md = UtilsService.genMd(licenseKey);
                String publicKey = RsaUtils.getPublicKeyStr2();
                String md5Str = RsaUtils.decryptByPublicKey(publicKey, licenseCode);
                String expDate = md5Str.replace(md, "");
                String today = DateUtils.getDate();
                long diffDay = 0;
                if(StringUtils.isNotEmpty(expDate)) {
                    diffDay = DateUtils.getDateDifference(expDate, today, "days");
                }
                if(diffDay >= 0) {
                    return AjaxResult.success("success", diffDay);
                } else {
                    return AjaxResult.success("授权已过期" + Math.abs(diffDay) + "天", 0);
                }
            } catch (Exception e) {
                return AjaxResult.success("授权码不正确", -3);
            }
        }
    }

    @GetMapping("/checkLibPath")
    public AjaxResult checkLibPath(HttpServletRequest request) {
        String libraryPath = System.getProperty("java.library.path");
        return AjaxResult.success("请接入硬件锁", libraryPath);
    }

    // 获取资源信息
    @GetMapping("/common/base/resouce")
    public AjaxResult getResouce(HttpServletRequest request) {
        String buildingId = request.getParameter("buildingId");
        String category = request.getParameter("category");
        String type = request.getParameter("type");
        String groupName = request.getParameter("groupName");
        String name = request.getParameter("name");
        String note = request.getParameter("note");
        List list = utilService.getResouce(buildingId, category, type, groupName, name, note);
        return AjaxResult.success(list);
    }

    // 获取当前应用的md码
    @GetMapping("/application")
    public AjaxResult getAppApplication(HttpServletRequest request) throws Exception {
        String md = UtilsService.genMd(licenseKey);
        return AjaxResult.success("success", md);
    }

}
