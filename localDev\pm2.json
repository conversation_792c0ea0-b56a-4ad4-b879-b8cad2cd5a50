{"apps": [{"name": "ruoyi-admin", "script": "D:/jdk/jdk1.8.0_221/bin/java.exe", "args": ["-Xms256m", "-Xmx512m", "-jar", "-Dspring.config.location=D:/soft/backend/config/application.yml", "D:/soft/backend/ruoyi-admin.jar"], "out_file": "D:/soft/logs/ruoyi-admin.txt", "error_file": "D:/soft/logs/ruoyi-admine.txt", "instances": 1, "autorestart": false, "exec_interpreter": "", "exec_mode": "fork", "watch": false}, {"name": "collect-dbUp", "script": "D:/soft/collect/src/dbUpdate.js", "args": "ly_jx_hl", "out_file": "D:/soft/logs/dbUpdate.txt", "error_file": "D:/soft/logs/dbUpdatee.txt", "instances": 1, "watch": true, "merge_logs": true, "exec_interpreter": "node", "exec_mode": "fork", "max_memory_restart": "256M", "restart_delay": 5000, "min_uptime": "30s"}, {"name": "collect-httpDataServerListener", "script": "D:/soft/collect/src/httpDataServerListener.js", "args": "8001 ly_jx_hl rw debug1", "out_file": "D:/soft/logs/httpDataServerListener.txt", "error_file": "D:/soft/logs/httpDataServerListenere.txt", "instances": 1, "watch": true, "merge_logs": true, "exec_interpreter": "node", "exec_mode": "fork", "max_memory_restart": "256M", "restart_delay": 5000, "min_uptime": "30s"}, {"name": "rtsp2Websocket", "script": "D:/soft/collect/src/video-rtsp2Websocket.js", "args": "2156", "out_file": "D:/soft/logs/rtsp2Websocket_logs.txt", "error_file": "D:/soft/logs/rtsp2Websocket_logse.txt", "instances": 1, "watch": true, "merge_logs": true, "exec_interpreter": "node", "exec_mode": "fork", "max_memory_restart": "256M", "restart_delay": 5000, "min_uptime": "30s"}, {"name": "collect-mqServer", "script": "d:/soft/collect/src/mqServer.js", "args": "1883", "out_file": "D:/soft/logs/mqServer.txt", "error_file": "D:/soft/logs/mqServere.txt", "instances": 1, "watch": true, "merge_logs": true, "exec_interpreter": "node", "exec_mode": "fork", "max_memory_restart": "256M", "restart_delay": 5000, "min_uptime": "30s"}, {"name": "r-0", "script": "d:/soft/collect/src/modbusRead.js", "args": "0 ly_jx_hl", "out_file": "D:/soft/logs/modbusRead0.txt", "error_file": "D:/soft/logs/modbusRead0e.txt", "exec_interpreter": "node", "exec_mode": "fork", "max_memory_restart": "256M", "restart_delay": 5000, "min_uptime": "30s"}, {"name": "r-1", "script": "d:/soft/collect/src/modbusRead.js", "args": "1 ly_jx_hl", "out_file": "D:/soft/logs/modbusRead1.txt", "error_file": "D:/soft/logs/modbusRead1e.txt", "exec_interpreter": "node", "exec_mode": "fork", "max_memory_restart": "256M", "restart_delay": 5000, "min_uptime": "30s"}, {"name": "r-2", "script": "d:/soft/collect/src/modbusRead.js", "args": "2 ly_jx_hl", "out_file": "D:/soft/logs/modbusRead2.txt", "error_file": "D:/soft/logs/modbusRead2e.txt", "exec_interpreter": "node", "exec_mode": "fork", "max_memory_restart": "256M", "restart_delay": 5000, "min_uptime": "30s"}, {"name": "r-3", "script": "d:/soft/collect/src/modbusRead.js", "args": "3 ly_jx_hl", "out_file": "D:/soft/logs/modbusRead3.txt", "error_file": "D:/soft/logs/modbusRead3e.txt", "exec_interpreter": "node", "exec_mode": "fork", "max_memory_restart": "256M", "restart_delay": 5000, "min_uptime": "30s"}, {"name": "r-4", "script": "d:/soft/collect/src/modbusRead.js", "args": "4 ly_jx_hl", "out_file": "D:/soft/logs/modbusReade.txt", "error_file": "D:/soft/logs/modbusRead4e.txt", "exec_interpreter": "node", "exec_mode": "fork", "max_memory_restart": "256M", "restart_delay": 5000, "min_uptime": "30s"}, {"name": "r-5", "script": "d:/soft/collect/src/modbusRead.js", "args": "5 ly_jx_hl", "out_file": "D:/soft/logs/modbusRead5.txt", "error_file": "D:/soft/logs/modbusRead5e.txt", "exec_interpreter": "node", "exec_mode": "fork", "max_memory_restart": "256M", "restart_delay": 5000, "min_uptime": "30s"}, {"name": "r-6", "script": "d:/soft/collect/src/modbusRead.js", "args": "6 ly_jx_hl", "out_file": "D:/soft/logs/modbusRead6.txt", "error_file": "D:/soft/logs/modbusRead6e.txt", "exec_interpreter": "node", "exec_mode": "fork", "max_memory_restart": "256M", "restart_delay": 5000, "min_uptime": "30s"}, {"name": "r-7", "script": "d:/soft/collect/src/modbusRead.js", "args": "7 ly_jx_hl", "out_file": "D:/soft/logs/modbusRead7.txt", "error_file": "D:/soft/logs/modbusRead7e.txt", "exec_interpreter": "node", "exec_mode": "fork", "max_memory_restart": "256M", "restart_delay": 5000, "min_uptime": "30s"}, {"name": "r-8", "script": "d:/soft/collect/src/modbusRead.js", "args": "8 ly_jx_hl", "out_file": "D:/soft/logs/modbusRead8.txt", "error_file": "D:/soft/logs/modbusRead8e.txt", "exec_interpreter": "node", "exec_mode": "fork", "max_memory_restart": "256M", "restart_delay": 5000, "min_uptime": "30s"}, {"name": "nginx", "script": "D:/soft/nginx-1.26.1/nginx.exe", "args": ["-c", "D:/soft/nginx-1.26.1/conf/nginx.conf"], "out_file": "D:/soft/logs/nginx.txt", "error_file": "D:/soft/logs/nginxe.txt", "instances": 1, "exec_interpreter": "", "exec_mode": "fork", "watch": false}, {"name": "collect-pm2ServerMonitor", "script": "D:/soft/collect/src/pm2ServerMonitor.js", "args": "ly_jx_hl 海螺南地块 debug1", "out_file": "D:/soft/logs/pm2ServerMonitor.txt", "error_file": "D:/soft/logs/pm2ServerMonitore.txt", "instances": 1, "watch": true, "merge_logs": true, "exec_interpreter": "node", "exec_mode": "fork", "max_memory_restart": "256M", "restart_delay": 5000, "min_uptime": "30s"}]}