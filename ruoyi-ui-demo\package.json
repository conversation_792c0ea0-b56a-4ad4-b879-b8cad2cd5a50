{"name": "ibms", "version": "3.4.0", "description": "IBMS系统", "author": "sam", "license": "MIT", "scripts": {"dev": "set NODE_OPTIONS=--max-old-space-size=4096 && vue-cli-service serve", "build:prod": "vue-cli-service build", "build:local": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --mode local", "build:stage": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@amap/amap-vue": "^2.0.13", "@riophae/vue-treeselect": "0.4.0", "animate.css": "^4.1.1", "axios": "0.21.0", "cbim-render-sdk": "^1.5.85", "clipboard": "2.0.6", "core-js": "^3.22.2", "crypto-js": "^4.1.1", "echarts": "^5.1.2", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "element-ui": "2.15.0", "file-saver": "^2.0.2", "flv.js": "^1.6.2", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "iview": "^3.5.4", "js-base64": "^2.6.4", "js-beautify": "1.13.0", "js-cookie": "2.2.1", "jsencrypt": "3.0.0-rc.1", "jsmind": "^0.8.5", "jsoneditor": "^10.2.0", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "moment": "^2.29.1", "nprogress": "0.2.0", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "three": "^0.157.0", "url-loader": "^4.1.1", "vue": "2.6.12", "vue-ckplayer": "^1.0.1", "vue-count-to": "^1.0.13", "vue-cropper": "0.5.5", "vue-grid-layout": "^2.4.0", "vue-qr": "^4.0.9", "vue-router": "3.4.9", "vuedraggable": "2.24.3", "vuex": "3.6.0", "xlsx": "^0.16.9"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "chalk": "4.1.0", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "html-webpack-plugin": "^4.5.2", "husky": "^8.0.3", "lint-staged": "10.5.3", "md5-node": "^1.0.1", "mockjs": "^1.1.0", "runjs": "4.4.2", "sass": "1.32.0", "sass-loader": "10.1.0", "script-ext-html-webpack-plugin": "^2.1.5", "svg-sprite-loader": "5.1.1", "vue-codemirror": "^4.0.6", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}