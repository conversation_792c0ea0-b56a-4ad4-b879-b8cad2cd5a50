<template>
	<view class="main">
		<view class="card">
			<image class="bg" :src="`${api}/image/miniStatic/${pageType}_scan_bg.png`" mode="aspectFill"></image>
			<image class="scan" :src="`${api}/image/miniStatic/${pageType}_scan.png`" mode="aspectFit" @click="scan"></image>
		</view>
		<view class="cont">
			<view class="menus">
				<view class="menus_item" v-for="(item, index) in doorSub" :key="index" @click="goPageByType(item.type)">
					<image class="icon" :src="item.icon" mode="aspectFit"></image>
					<view class="name">{{ item.label }}</view>
				</view>
			</view>
			<!-- 统计 -->
			<view class="statistics">
				<view class="chart">
					<MonthDataHorizonBarVue ref="monthDataHorizonBarVue" :title="horizonBarTitle" :type="maintainanceTypeData.type" :pageType="pageType"/>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { scanTheCode, getDataFromUrl } from '@/utils/tools.js'
import MonthDataHorizonBarVue from './stats/monthDataHorizonBar.vue';
import {getDictDataByParams} from '@/api/commonApi.js'
const api = uni.$g.VUE_APP_BASE_API;
const monthDataHorizonBarVue = ref(null)
const pageType = ref("repair");
const doorSub = computed(() => {
	return [
		{
			label: '报修单记录',
			type: 'records',
			icon: "../../static/menu_repair.png"
		},
		{
			label: '报修单申请',
			type: 'apply',
			icon: "../../static/repair_apply.png"
		},
		{
			label: '报修单统计',
			type: 'stats',
			icon: "../../static/repair_statistics.png"
		}
	]
})
const horizonBarTitle = ref("报修单统计")
const deviceId = ref('');
const maintainanceTypeData = ref({
	title: "维修记录",
	type: "紧急维修",
})
const scan = () => {
	scanTheCode(res => {
		let data = {};
		try {
			data = JSON.parse(res.trim())
		} catch (error) {}
		deviceId.value = data.deviceId;
		uni.navigateTo({
			url: `/pages/maintanance/add?deviceId=${deviceId.value}&type=${maintainanceTypeData.value.type}`
		})
	})
}

const goPageByType = (type) => {
	let url = '/pages/maintanance/index';
	switch (type) {
		case 'records':
			url = `/pages/maintanance/list?type=${maintainanceTypeData.value.type}`
			break;
		case 'apply':
			url = `/pages/maintanance/add?type=${maintainanceTypeData.value.type}`
			break;
		case 'stats':
			url = `/pages/maintanance/stats/index?type=${maintainanceTypeData.value.type}&pageType=${pageType.value}`
			break;
		default:
			url = '/pages/maintanance/index'
			break;
	}
	uni.navigateTo({
		url
	})
}

const setMaintainanceData = (type) => {
	getDictDataByParams({
		dictType: "maintainance_type",
		dictLabel: type
	}).then(res => {
		try {
			const dt = JSON.parse(res.rows[0].dictValue);
			maintainanceTypeData.value = dt;
		} catch (error) {}
	});
}
onMounted(() => {
	//$on 一定要写道 $emit执行之前
	uni.$on('refresh', () => {
		monthDataHorizonBarVue.value && monthDataHorizonBarVue.value.getRepairOrderList && monthDataHorizonBarVue.value.getRepairOrderList();
	})
	const { type = 'repair' } = getDataFromUrl()
	pageType.value = type;
	setMaintainanceData(type);
})
</script>

<style lang="scss" scoped>
$shodow: 0px 2px 6px 1px #ddd;

.main {
	height: 100vh;
	background-color: #fff;

	.card {
		width: 100%;
		height: 450rpx;
		text-align: center;
		position: relative;

		.bg {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 450rpx;
		}

		.scan {
			width: 360rpx;
			height: 360rpx;
			z-index: 1;
			position: relative;
			top: 20rpx;
		}
	}

	.cont {
		background: #F0F2F5;
		position: absolute;
		width: 100%;
		top: 420rpx;
		border-top-right-radius: 40rpx;
		border-top-left-radius: 40rpx;
		overflow: hidden;

		.menus {
			display: flex;
			justify-content: space-between;
			align-items: center;
			background-color: #fff;
			padding: 40rpx 66rpx;

			&_item {
				text-align: center;

				image {
					width: 96rpx;
					height: 96rpx;
				}

				.name {
					font-size: 28rpx;
					color: #2D3033;
				}
			}
		}

		.statistics {
			margin-top: 24rpx;
			background-color: #fff;
			padding: 24rpx 40rpx;

			canvas {
				width: 100%;
			}
		}
	}

}


.chart {
	width: 100%;
}
</style>