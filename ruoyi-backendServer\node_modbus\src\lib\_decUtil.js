'use strict';

const lenMap = {
    I16BE: 2,
    U16BE: 2,
    I32BE: 4,
    U32BE: 4,
    F32BE: 4,
    D64BE: 8
};

const decUtil = {
}

decUtil.register = function(funcName, func) {
    module.exports[funcName] = func;
}

decUtil.registerLength = function(type, len) {
        lenMap[type] = len;
    }

decUtil.getLen = function(type) {
        if (lenMap.hasOwnProperty(type)) {
            return lenMap[type];
        }
        throw new Error(`getLen(${type}), unknown type`);
    }

decUtil.stringToInt16 = function(string) {
        let lowString = string.slice(0, 2);
        let highString = string.slice(2);
        return parseInt(highString + lowString, 16);
    }

decUtil.swapToBE = function(buf, order) {
        // examples:
        // order = '10'; // 两个字节，高位在前
        // order = '1032'; // 四个字节，前两个字节是低位，高位在前，后两个字节是高位，高位在前
        // order = '76543210'; // 八个字节，高位在前
        if (buf.length !== order.length) {
            throw new Error(`swapToBE: buf.length[${buf.length}] != order.length[${order.length}].`);
        }

        switch (order) {
            case '10':
            case '3210':
            case '76543210':
                return buf;
            default: break;
        }

        let newBuf = Buffer.allocUnsafe(buf.length);
        let i = buf.length;

        order.split('').forEach(pos => {
            newBuf[pos] = buf[--i];
        });

        return newBuf;
    }

decUtil.slice = function(buf, start, end) {
        if (!start) {
            start = 0;
        }
        if (!end) {
            end = buf.length;
        }
        return buf.slice(start, end);
    }

decUtil.I8 = function(buf) {
        return buf.readInt8();
    }

decUtil.I16BE = function(buf) {
        return buf.readInt16BE();
    }

decUtil.I32BE = function(buf) {
        return buf.readInt32BE();
    }

decUtil.U8 = function(buf) {
        return buf.readUInt8();
    }

decUtil.U16BE = function(buf) {
        return buf.readUInt16BE();
    }

decUtil.U32BE = function(buf) {
        return buf.readUInt32BE();
    }

decUtil.F32BE = function(buf) {
        return buf.readFloatBE();
    }

decUtil.D64BE = function(buf) {
        return buf.readDoubleBE();
    }

decUtil.booleanToInt = function(val) {
        return val ? 1 : 0;
    }

decUtil.intToBoolean = function(val) {
        return val == 1 ? true : false;
    }

decUtil.float2Buff = function(val) {
        let tmp = Buffer.alloc(4);
        tmp.writeFloatBE(val);
        return tmp;
    }

decUtil.hex = function(buf, start, end) {
        if (!start) {
            start = 0;
        }
        if (!end) {
            end = buf.length;
        }
        return buf.toString('hex', start, end);
    }

decUtil.mask = function(value, mask) {
        return value & mask;
    }

decUtil.readBitInBuffer = function(buffer, offset) {
        let bits = [];
        for (let i = 0; i < Math.min(buffer.length, buffer.readUInt8(0) + 1); i++) {
            for (let j = 0; j < 8; j++) {
                bits.push((buffer.readUInt8(i) >> j) & 0x1);
            }
        }
        return offset ? bits[offset] : bits;
    }

decUtil.buffer2Bin = function(buffer, isReverse = false) {
        let newBuffer = Buffer.from(buffer);
        if (isReverse) {
            newBuffer.reverse();
        }
        let bin = "";
        for (let index = 0; index < newBuffer.length; index++) {
            let dec = newBuffer[index];
            bin += dec.toString(2).padStart(8, "0");
        }
        return bin;
    }
    // buffer 转二进制 array， 从低往高数位数
decUtil.buffer2Array = function(buffer, isReverse = true) {
        let newBuffer = Buffer.from(buffer);
        if (isReverse) {
            newBuffer.reverse();
        }
        let bin = "";
        for (let index = 0; index < newBuffer.length; index++) {
            let dec = newBuffer[index];
            bin += dec.toString(2).padStart(8, "0");
        }
        return bin.split("");
    }

decUtil.shift = function(value, shift) {
        return shift > 0 ? value << shift : value >> shift;
    }

decUtil.normalize = function(val, iMin, iMax, oMin, oMax) {
        if (val <= iMin) {
            return oMin;
        }
        if (val >= iMax) {
            return oMax;
        }
        if (val === 0) {
            return 0;
        }
        return (oMax - oMin) / (iMax - iMin) * (val - iMin) + oMin;
    }

decUtil.divide = function(value, units, decimal) {
        return (value / units).toFixed(decimal);
    }
decUtil.multiply = function(value, units, decimal) {
        return (value * units).toFixed(decimal);
    }
decUtil.add = function(value, units, decimal) {
    return (value + units).toFixed(decimal);
}

decUtil.round = function(val, precision) {
        let factor = Math.pow(10, precision);
        let intVal = Math.round(val * factor);
        let rndVal = intVal / factor;
        return rndVal;
    }

decUtil.toString = function(buf, format) {
        return buf.toString(format);
    }

decUtil.toJson = function(val) {
        return JSON.parse(val);
    }

decUtil.intToBuffer = function(val) {
        let buf = Buffer.from([val]);
        return buf;
    }

decUtil.intToBuffer16 = function(val) {
        let buf = Buffer.alloc(2);
        buf.writeUInt16BE(val);
        return buf;
    }

decUtil.intToBuffer32 = function(val) {
        let buf = Buffer.alloc(4);
        buf.writeUInt32BE(val);
        return buf;
    }

decUtil.floatToBuffer32 = function(val) {
        let buf = Buffer.alloc(4);
        buf.writeFloatBE(val);
        return buf;
    }

decUtil.doubleToBuffer64 = function(val) {
        let buf = Buffer.alloc(8);
        buf.writeDoubleBE(val);
        return buf;
    }

decUtil.strToBuffer = function(str, forma) {
        if(typeof forma != "undefined") {
            return Buffer.from(str, forma);
        } else {
            return Buffer.from(str);
        }
    }

decUtil.floatToBuffer32 = function(val) {
        let buf = Buffer.alloc(4);
        buf.writeFloatBE(val, 0);
        return buf;
    }

decUtil.toBits = function(buffer) {
        let bits = [];
        for (let i = buffer.length; i > 0; i--) {
            for (let j = 0; j < 8; j++) {
                bits.push((buffer.readUInt8(i - 1) >> j) & 0x1);
            }
        }
        return bits;
    }
    
    /** 读取第X个bit位的值 */
    //[["swapToBE","01"],["readBitInBuffer"],["readBitNum",0,{"0":0,"1":1}]]
decUtil.readBitNum = function(bitArray, ind) {
        if(bitArray.length > ind) {
            return bitArray[ind];
        }
        return null;
    }

    /** read bitVal use */
    //[["swapToBE","01"],["readBitInBuffer"],["readBitVal",0,{"0":0,"1":1}]]
decUtil.readBitVal = function(bitArray, ind, map) {
        if(bitArray.length > ind) {
            return map[bitArray[ind]];
        }
        return null;
    }
    /** write bitVal use */
    //[["parseBitVal",0,{"0":0,"1":1}]]
decUtil.parseBitVal = function(val, ind, map) {
        let bufVal = -1;
        for(let k in map) {
            if(map[k] == val) {
                bufVal = k << ind;
            }
        }
        if(bufVal >= 0) {
            return bufVal;
        } else {
            return null;
        }
    }

    /** read bitMap use */
    //[["swapToBE","10"],["readBitInBuffer"],['readBitMapVal',[],1,0,{"0":1,"1":2,"2":3,"3":4,"4":5}]]
decUtil.readBitMapVal = function(bitArray, ignore=[], checkVal=1, defautVal=0, map) {
        for(let i=0; i<bitArray.length; i++) {
            if(ignore.indexOf(i) >= 0) {
                // 忽略的 pass
            } else {
                if(bitArray[i] == checkVal) {
                    return map[i];
                }
            }
        }
        return defautVal != undefined ? defautVal : null;
    }
    /** write bitMap use */
    // [["parseBitMapVal",{"0":0,"1":1,"2":2,"3":3,"4":4}]]
decUtil.parseBitMapVal = function(val, map) {
        let bufVal = -1;
        for(let k in map) {
            if(map[k] == val) {
                bufVal = 1 << k;
            }
        }
        if(bufVal >= 0) {
            return bufVal;
        } else {
            return null;
        }
    }

    // read json string
    // [["parseJsonVal","params"],["parseJsonVal","ActiveEnergyImportInPhaseL1"]]
decUtil.parseJsonVal = function(val, key) {
        let valObj = val;
        if(val && typeof val == "string") {
            try {
                valObj = JSON.parse(val);
            } catch(e) {
                // pass
            }
        }
        try {
            return valObj.hasOwnProperty(key) ? valObj[key] : null;
        } catch(e) {
            return null;
        }
    }
    // 进制转换
decUtil.parseInt = function(val, radix) {
        return parseInt(val, radix);
    }
    // 字符串数字转换
decUtil.parseFloat = function(val) {
        return parseFloat(val);
    }
    // 单个整数转 array，只支持int输入，适配批量指令下发
decUtil.valToArray = function(val) {
        if(typeof val == "number" || typeof val == "string") {
            try {
                val = parseInt(val);
                return [val];
            } catch(e) {
                
                return val;
            }
        } else {
            return val;
        }
    }
    // 字符映射转换 val to val
decUtil.val2Val = function(val, map) {
        return map[val];
    }

    // 余姚舜能空调，温度转换，读取
    // 1.温度值：（0～+99°C）
    // 高字节为整数：0～+99； 
    // 低字节为小数：0/5；
    // 例如：温度值为25.5°C，  则为19 05；温度值为5.0°C，  则为05 00；
decUtil.yysnTemp = function(buff) {
        let d1 = buff[0];
        let d2 = buff[1];
        let d1v = d1; // parseInt(d1,16);
        let d2v = d2; // parseInt(d2,16);
        return parseFloat(d1v+"."+d2v);
    }
    // 温度转buff，写入
decUtil.yysnTemp2Buff = function(val) {
        let d = val.toString();
        let dl = d.split(".");
        let d1 = dl[0];
        let d2 = 0;
        if(dl.length > 1) {
            d2 = dl[1];
        }
        let newBuf = Buffer.allocUnsafe(2);
        newBuf.writeUInt8(d1, 0);
        newBuf.writeUInt8(d2, 1);
        return newBuf;
    }

    // dlt645数据转换
    // inputs buff
    // adjust 需要增减的值  电表需要减 0x33
    // 输出字符串
decUtil.BCD2String = function(inputs, adjust) {
        let len = inputs.length;
        let buf = Buffer.alloc(len);
        for(let i = 0; i<len; i++) {
            buf[len-1-i] = (inputs[i] + parseInt(adjust) >= 0) ? inputs[i] + parseInt(adjust) : (inputs[i] + 16 + parseInt(adjust));
        }
        return buf.toString("hex");
    }

    // 武汉小学项目 电表数据读取
    // 数据处理： 
    //   高位：00 00(16 进制) = 0 (10 进制)
    //   低位：30 26(16 进制) = 12326 (10 进制)
    //   因此该仪表二次测有功电能为：(0×65536 + 12326)*0.01 = 123.26
decUtil.whxxAmmeter = function(buff) {
        let b = Buffer.from(buff.toString("hex"),"hex");
        let d1 = b.slice(0,2).readUInt16BE();
        let d2 = b.slice(2,4).readUInt16BE();
        return parseFloat((d1*65536+d2)*0.01);
    }

    // obix 嘉兴学院，str to xmlStr
    // 格式固定为: <real val='2' /> 或 <bool val='true' />
decUtil.jxxyObixToXml = function(val, type) {
        return '<' + type + ' val="' + val + '" />';
    }

    // 景德镇 电表 map to int
decUtil.jdzStrToInt = function(val, map) {
        return map[val];
    }
    // 景德镇 电表 map to int
decUtil.jdzIntToStr = function(val, map) {
        return map[val];
    }

    // 金华小镇照明crc校验, buffer中所有数字相加，取低位1位
decUtil.bufferSumLow = function(buff) {
        let sum = 0;
        for(let i = 0; i < buff.length; i++) {
            sum += buff[i];
        }
        console.log(sum);
        let hex = sum.toString(16);
        return hex.substr(hex.length-2);
    }

    // 计算组合点 -- 针对两个2进制结果，组合成 00 ~ 11 四种结果值的情况
    // [["binary2Vals","{73103}","{73104}"]]
    // 比如需要两个点拼接后，得出另外一个数据
    // eg: ui3: 1,  ui4 : 0 => 10 => ui8 = 2
decUtil.binary2Vals = function(val, data1, data2) {
        let d1 = parseInt(data1);
        let d2 = parseInt(data2);
        if(d1 >=0 & d1 <= 1 && d2 >= 0 && d2 <= 1) {
            return parseInt(d1+''+d2, 2);
        } else {
            console.log("binary2Vals data format error", data1, data2);
            return null;
        }
    }
    // 计算组合点 -- 针对多个采集点求和逻辑
    // [["sumVals","{3004607}","{3004507}","{3004307}","{3004207}"...]]
decUtil.sumVals = function() {
        if(arguments && arguments.length > 2) {
            let sum = 0;
            for(let i=1; i<arguments.length; i++) {
                try {
                    sum += parseFloat(arguments[i] || 0);
                } catch(e) {
                    // pass
                }
            }
            return sum;
        } else {
            console.log("sumVals arguments error", arguments);
            return null;
        }
    }
    // 计算组合点 -- 计算 and 值，多个点同为1 则为1，否则为0
    // [["checkAllOne","{3004607}","{3004507}","{3004307}","{3004207}"...]]
decUtil.checkAllOne = function() {
        if(arguments && arguments.length >= 2) {
            for (var i = 1; i < arguments.length; i++) {
                if (arguments[i] != 1) {
                    return 0; // 如果发现有不为1的元素，返回0
                }
            }
            return 1;
        } else {
            console.log("checkAllOne arguments error", arguments);
            return null;
        }
    }
// 计算组合点 -- 针对多个采集点求和逻辑
// [["funcStr","?+10-?","{3004507}","{3004307}"]]
// [["funcStr","?+''+?+''+?","{3004506}","{3004307}","{3004308}"]]
decUtil.funcStr = function() {
    if(arguments && arguments.length > 2) {
        let funcStr = arguments[1];
        let params = Array.from(arguments);
        params = params.slice(2, params.length);
        funcStr = funcStr.replace(/\?/g, () => `${params.shift()}`);
        let res = null;
        try {
            res = eval(funcStr);
        } catch (e) {
            // pass
        }
        return res;
    } else {
        console.log("funcStr arguments error", arguments);
        return null;
    }
}

// keepSeconds 为关键字, 
// 功能同 funcStr, 计算公式值。  
decUtil.keepSeconds = function() {
    if(arguments && arguments.length > 2) {
        let funcStr = arguments[1];
        let params = Array.from(arguments);
        params = params.slice(2, params.length);
        funcStr = funcStr.replace(/\?/g, () => `${params.shift()}`);
        let res = null;
        try {
            res = eval(funcStr);
        } catch (e) {
            // pass
        }
        return res;
    } else {
        console.log("funcStr arguments error", arguments);
        return null;
    }
}

// 三元判断取值
//     [["sanyuanVal","0","{60402}","充电","放电"]]
//     [["sanyuanVal","0","{60402}","{60404}","{60405}"]]
decUtil.sanyuanVal = function() {
    if(arguments && arguments.length > 2) {
        let params = Array.from(arguments);
        let res = params[2] > params[1] ? params[3] : params[4];
        return res;
    } else {
        console.log("sanyuanVal error", arguments);
        return null;
    }
}


// 判断取值  eg判断 =0  >0  <0 并指定输出    对比值 可能是一个区间值  建科院
//     [["statusVal","0","{60402}","充电","空闲","放电"]]
//     [["statusVal","0","{60402}","{60404}","{60405}","{60406}"]]
//     [["sanyuanVal","[0, 0.1]","{60402}","充电","空闲","放电"]]
decUtil.statusVal = function() {
    if(arguments && arguments.length > 2) {
        let params = Array.from(arguments);
        let res;
        let compareVal = [];
        try {
            compareVal = JSON.parse(params[1]);
        } catch (error) {
            console.log("对比值JSON.parse失败");
            return null;
        }
        if(Array.isArray(compareVal)){
            let min = compareVal[0];
            let max = compareVal[compareVal.length - 1];
            if(params[2] >= max){
                res = params[3]
            } else if(params[2] < max && params[2] >= min) {
                res = params[4]
            } else {
                res = params[5]
            }
        } else {
            if(params[2] > params[1]){
                res = params[3]
            } else if(params[2] == params[1]){
                res = params[4]
            } else {
                res = params[5]
            }
        }
        return res;
    } else {
        console.log("statusVal error", arguments);
        return null;
    }
}


// 由数据值，得到map中对应值
decUtil.findVal = function(val, map) {
    return map[val];
}

decUtil.abs = function(val) {
    return Math.abs(val);
}

// 解析返回, 皓骐贴牌，智能照明 读Val
// 1~8，9~18
decUtil.haoqiCanRVal = function(val, circuitNo) {
    val = val.slice(0,2);
    let t = decUtil.buffer2Bin(val);
    return decUtil.readBitNum(t, (circuitNo-1), {"0":0,"1":1});
}

// 生成写入指令, 皓骐贴牌，智能照明 写Val
decUtil.haoqiCanWVal = function(val, circuitNo) {
    let res = decUtil.buffer2Bin(Buffer.from("00000000", "hex"));
    let s = (circuitNo - 1) * 2;
    let x = val == 1 ? "10" : "01";
    res = res.substring(0, s) + x + res.substring(s+2);
    let resVal = parseInt(res, 2);
    let reshex = resVal.toString(10);
    return reshex;
}

// 解析写入的返回,
decUtil.haoqiCanWRVal = function(val) {
    val.slice(0,2);
    let t = decUtil.buffer2Bin(val);
    return decUtil.readBitNum(t, (circuitNo-1), {"0":0,"1":1});
}

// 生成整个模块写入指令, 皓骐贴牌，智能照明 全写Val
decUtil.haoqiCanAWVal = function(val) {
    return val == 1 ? parseInt("AAAAAAAA",16) : parseInt("55555555",16);
}

// 电建百花谷 电梯
// 楼层 双字节 40002 只读 ASSIC 码格式，15~8 位为十位 7~0 位为个位
decUtil.djElevator = function(buff) {
    let d1 = buff[0];
    let d2 = buff[1];
    return (String.fromCharCode(d1)+String.fromCharCode(d2)).trim();
}

// 智能插座 数据转 json
// 格式固定为: 
// {
//   "method": "thing.service.property.set",
//   "id": "1117817718",
//   "params": {
//     "AC_Switch": 0
//   },
//   "version": "1.0.0"
// }
decUtil.techConTCACC10 = function(val, type) {
        let json = {
          "method": "thing.service.property.set",
          "id": (new Date().getTime()).toString(),
          "params": {
          },
          "version": "1.0.0"
        };
        json.params[type] = val;
        return json;
    }

decUtil.reverse = function(value) {
        return value.reverse();
    }

// ["parseBitValWithOldVal", 4]: 输入是两个值，第一个指是读取的数据值，第二个值是要写入的bit位值，4指第几个bit位开始
decUtil.parseBitValWithOldVal = function(val, bitNum) {
        let oldVal = val[0];
        let writeBitVal = val[1];
        let old2Val = decUtil.buffer2Bin(oldVal);
        let new2Val = old2Val.substring(0,bitNum) + writeBitVal + old2Val.substring(bitNum+writeBitVal.length);
        return parseInt(new2Val, 2);
    }

decUtil.batch = function(data, funcList) {
        if(funcList && funcList.length > 0) {
            for (let i = 0, n = funcList.length; i < n; i++) {
                if (funcList[i].length === 0) {
                    continue;
                }
                let func = decUtil[funcList[i][0]];
                if (!func) {
                    throw new Error(`decUtil[${funcList[i][0]}] not exist`);
                }
                let args = Array.prototype.concat([data], funcList[i].slice(1));
                data = func.apply(null, args);
                //data = func(...args);
            }
        }
        return data;
    };

module.exports = decUtil;

module.exports.register = decUtil.register;
module.exports.registerLength = decUtil.registerLength;
module.exports.getLen = decUtil.getLen;
module.exports.stringToInt16 = decUtil.stringToInt16;
module.exports.swapToBE = decUtil.swapToBE;
module.exports.slice = decUtil.slice;
module.exports.I8 = decUtil.I8;
module.exports.I16BE = decUtil.I16BE;
module.exports.I32BE = decUtil.I32BE;
module.exports.U8 = decUtil.U8;
module.exports.U16BE = decUtil.U16BE;
module.exports.U32BE = decUtil.U32BE;
module.exports.F32BE = decUtil.F32BE;
module.exports.D64BE = decUtil.D64BE;
module.exports.booleanToInt = decUtil.booleanToInt;
module.exports.intToBoolean = decUtil.intToBoolean;
module.exports.float2Buff = decUtil.float2Buff;
module.exports.hex = decUtil.hex;
module.exports.mask = decUtil.mask;
module.exports.readBitInBuffer = decUtil.readBitInBuffer;
module.exports.buffer2Bin = decUtil.buffer2Bin;
module.exports.buffer2Array = decUtil.buffer2Array;
module.exports.shift = decUtil.shift;
module.exports.normalize = decUtil.normalize;
module.exports.divide = decUtil.divide;
module.exports.multiply = decUtil.multiply;
module.exports.add = decUtil.add;
module.exports.round = decUtil.round;
module.exports.reverse = decUtil.reverse;
module.exports.toString = decUtil.toString;
module.exports.toJson = decUtil.toJson;
module.exports.intToBuffer = decUtil.intToBuffer;
module.exports.intToBuffer16 = decUtil.intToBuffer16;
module.exports.intToBuffer32 = decUtil.intToBuffer32;
module.exports.floatToBuffer32 = decUtil.floatToBuffer32;
module.exports.doubleToBuffer64 = decUtil.doubleToBuffer64;
module.exports.strToBuffer = decUtil.strToBuffer;
module.exports.floatToBuffer32 = decUtil.floatToBuffer32;
module.exports.toBits = decUtil.toBits;
module.exports.readBitNum = decUtil.readBitNum;
module.exports.readBitVal = decUtil.readBitVal;
module.exports.parseBitVal = decUtil.parseBitVal;
module.exports.readBitMapVal = decUtil.readBitMapVal;
module.exports.parseBitMapVal = decUtil.parseBitMapVal;
module.exports.parseJsonVal = decUtil.parseJsonVal;
module.exports.parseInt = decUtil.parseInt;
module.exports.parseFloat = decUtil.parseFloat;
module.exports.valToArray = decUtil.valToArray;
module.exports.val2Val = decUtil.val2Val;
module.exports.yysnTemp = decUtil.yysnTemp;
module.exports.yysnTemp2Buff = decUtil.yysnTemp2Buff;
module.exports.BCD2String = decUtil.BCD2String;
module.exports.whxxAmmeter = decUtil.whxxAmmeter;
module.exports.jxxyObixToXml = decUtil.jxxyObixToXml;
module.exports.jdzStrToInt = decUtil.jdzStrToInt;
module.exports.jdzIntToStr = decUtil.jdzIntToStr;
module.exports.bufferSumLow = decUtil.bufferSumLow;
module.exports.binary2Vals = decUtil.binary2Vals;
module.exports.sumVals = decUtil.sumVals;
module.exports.checkAllOne = decUtil.checkAllOne;
module.exports.findVal = decUtil.findVal;
module.exports.abs = decUtil.abs;
module.exports.haoqiCanRVal = decUtil.haoqiCanRVal;
module.exports.haoqiCanWVal = decUtil.haoqiCanWVal;
module.exports.haoqiCanWRVal = decUtil.haoqiCanWRVal;
module.exports.haoqiCanAWVal = decUtil.haoqiCanAWVal;
module.exports.djElevator = decUtil.djElevator;
module.exports.funcStr = decUtil.funcStr;
module.exports.techConTCACC10 = decUtil.techConTCACC10;
module.exports.keepSeconds = decUtil.keepSeconds;
module.exports.parseBitValWithOldVal = decUtil.parseBitValWithOldVal;
module.exports.batch = decUtil.batch;
