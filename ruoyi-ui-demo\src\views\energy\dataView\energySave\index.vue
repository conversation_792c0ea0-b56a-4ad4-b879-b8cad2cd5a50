<template>
  <div class="energy-summary">
    <div class="main-card">
      <h3 v-if="title">
        <span v-text="title"></span>
        <el-divider></el-divider>
      </h3>
      <div class="energy_elec_wrap">
        <el-row :gutter="16">
          <el-col :span="12">
            <AnalysisDataView></AnalysisDataView>
          </el-col>
          <el-col :span="12">
            <ItemizedEnergySavingRate></ItemizedEnergySavingRate>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <TotalElectricity></TotalElectricity>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import AnalysisDataView from './components/analysisDataView.vue';
import ItemizedEnergySavingRate from './components/itemizedEnergySavingRate.vue';
import TotalElectricity from './components/totalElectricity.vue';
export default {
  components: {
    AnalysisDataView,
    ItemizedEnergySavingRate,
    TotalElectricity
  },
  data() {
    return {
      title: "节电概览",
      tabsActive: 'overview',
      tabs: [
        {
          code: 'overview',
          name: '总览'
        },
        {
          code: 'waterChillingUnit',
          name: '冷水机组'
        },
        {
          code: 'multiple',
          name: '多联机'
        },
        {
          code: 'light',
          name: '照明'
        },
      ],
    };
  },
};
</script>

<style scoped lang="scss">
::v-deep {
  .el-tabs__nav-wrap::after {
    background-color: #6C8097 !important;
    height: 1px;
  }
}

.analysis_overview {
  padding: 16px;

  .title {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 18px;
    color: #FFFFFF;
    line-height: 22px;
  }

  .main {
    background: #06101F;
    padding: 24px;
  }
}
</style>
