<template>
  <div class="dvHeader" style="height:80px; z-index: 1002;">
    <el-row class="flex" style="width: 100%;">
      <el-col :span="6">
        <div style="display: inline-block; width: 200%; padding-left: 15px; height:45px;" @click="goPage">
          <!-- <div class="_logo" v-text="project.name"></div> -->
          <el-image
            v-if="project.title_logo"
            style="height: 36px; vertical-align: bottom; position: relative; "
            :src="project.title_logo"
            fit="contain" />
          <div class="_logos" v-text="project.title"></div>
        </div>
        <div>
          <BuildingGroup class="pull-left" />
          <!-- <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container pull-left ml10 text-white" @toggleClick="toggleSideBar" /> -->
        </div>
      </el-col>

      <el-col :span="12" class="menus">
        <top-nav id="topmenu-container" class="topmenu-container" />
      </el-col>

      <el-col :span="6" class="flex" style="justify-content: flex-end;">
        <Notice :alarmVoiceOpen="alarmVoiceOpen" />
        <div @click="toggleAlarmVoice" style="display: inline-block; cursor: pointer;">
          <el-tooltip  :content="alarmVoiceOpen ? '点击关闭报警声音' : '点击开启报警声音'"
                      placement="bottom" popper-class="custom_tooltip">
            <img src="/image/alarm_icon.png" alt="报警"
                 :class="['alarm-toggle-btn', { 'alarm-active': alarmVoiceOpen }]">
          </el-tooltip>
        </div>
        <DateTime />
        <Weather class="innerWeather" />
        <div class="right-menu" style="display:none1;">
          <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
            <div class="avatar-wrapper">
              <el-image class="user-avatar"
                :src="avatar"
                fit="cover">
                <div slot="error" class="image-slot">
                  <img src="/face.png" class="user-avatar" />
                </div>
                </el-image>
              <div v-text="name" class="user-name" />
              <i class="el-icon-caret-bottom" />
            </div>
            <el-dropdown-menu slot="dropdown">
              <router-link to="/system/profile">
                <el-dropdown-item>个人中心</el-dropdown-item>
              </router-link>
              <el-dropdown-item @click.native="setting = true">
                <span>布局设置</span>
              </el-dropdown-item>
              <el-dropdown-item divided @click.native="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import Breadcrumb from '@/components/Breadcrumb';
import TopNav from '@/components/TopNav';
import Hamburger from '@/components/Hamburger';
import Screenfull from '@/components/Screenfull';
import SizeSelect from '@/components/SizeSelect';
import Search from '@/components/HeaderSearch';
import BuildingGroup from '@/components/BuildingGroup';
import DateTime from "@/views/components/cO2View/components/common/DateTimeInside";
import Weather from "@/views/components/cO2View/components/common/Weather";
import Notice from "@/components/Notice";

export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    BuildingGroup,
    DateTime,
    Weather,
    Notice,
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'avatar',
      'name',
      'device'
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    }
  },
  data() {
    return {
      project: this.gf.projectSettings(),
      linkTo: '/',
      alarmVoiceOpen: true
    }
  },
  mounted () {
    this.ableToScreen();
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    toggleAlarmVoice() {
      console.log('toggleAlarmVoice 被调用，当前状态:', this.alarmVoiceOpen);
      this.alarmVoiceOpen = !this.alarmVoiceOpen;
      console.log('toggleAlarmVoice 切换后状态:', this.alarmVoiceOpen);
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/index';
        })
      })
    },
    ableToScreen () {
      const configs = (this.gf.getLocalObject("dict_base_configs") || {});
      const loginRedirect = ((configs.data || []).find(item => item.dictLabel === 'loginRedirect') || {}).dictValue;
      const screenForbiden = ((configs.data || []).find(item => item.dictLabel === 'screenForbiden') || {}).dictValue;
      if(loginRedirect && screenForbiden === 'true'){
        this.linkTo = `/${loginRedirect}`
      }
    },
    goPage () {
      const currentPath = this.$route.path;
      if(currentPath !== this.linkTo){
        this.$router.push(this.linkTo)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dvHeader { }
._logos { display: inline-block; height: 40px; }
.navbar {
  height: 100px;
  overflow: hidden;
  position: relative;
  border-bottom:1px solid #666;

  .sidebar-title { font-size: 20px; float:left; margin: 0.67em 20px; }

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }
}

.right-menu {
  margin: 0px 10px 0 0;
  &:focus {
    outline: none;
  }

  .right-menu-item {
    display: inline-block;
    padding: 0 8px;
    height: 100%;
    font-size: 18px;
    color: #999;
    vertical-align: text-bottom;

    &.hover-effect {
      cursor: pointer;
      transition: background .3s;

      &:hover {
        background: rgba(0, 0, 0, .025)
      }
    }
  }

  .avatar-container {
    margin-right: 30px;

    .avatar-wrapper {
      margin-top: 5px;
      position: relative;

      .user-avatar {
        cursor: pointer;
        width: 30px;
        height: 30px;
        margin-top:5px;
        border-radius: 10px;
      }
      .user-name {
        font-size: 12px;
        color: #fff;
        word-break: keep-all;
      }

      .el-icon-caret-bottom {
        cursor: pointer;
        position: absolute;
        right: -20px;
        top: 12px;
        font-size: 16px;
      }
    }
  }
}


</style>
