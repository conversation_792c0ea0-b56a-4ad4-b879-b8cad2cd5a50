<script>
import Device from "@/views/device/monitor/components/Device/index";

export default {
  mixins: [Device],
  props: {
    "_deviceMainType": {
      type: String,
      default: '',
    },
    "_deviceCategory": {
      type: String,
      default: '',
    },
    "_showTabs": {
      type: Boolean,
      default: true,
    }
  },
  data () {
      return {
          pageTitle: "设备列表",
          buildingId: this.gf.getBuildingId(), //建筑ID
          deviceType: "", // device type
          category: "园区", // resource category
          type: "base",
          displayType: "card",  // 显示模式，列表/2D
          statusKey: "",
          summaryType: "sensor",

          globalTaskName: "",  // 

          mapNameShow: false, // 2D地图左侧，地图名
          mapDeviceShow: true, // 2D地图，设备显示
          mapSwiftShow: false, // 2D地图右侧，设备开关

          showTabs: true,
          deviceTypeCopy: ""
      }
  },
  created () {
    if(!this._deviceMainType) {
      let r = this.$route.path.split("/");
      this.deviceMainType = r[r.length-1];
    } else {
      this.deviceMainType = this._deviceMainType;
    }
    this.deviceTypeCopy = this.deviceMainType;
    if(this._deviceCategory){
      this.category = this._deviceCategory
    }
    this.showTabs = this._showTabs;

    this.$bus.$on("toggleTabs", (showTabs) => {
      console.log("toggleTabs", showTabs);
      this.showTabs = showTabs;
      try{
          var ev = document.createEvent('Event');
          ev.initEvent('resize', true, true);
          window.dispatchEvent(ev);
      }catch (e) {
      }
    });
  },
  beforeDestroy() {
    this.$bus.$off("toggleTabs");
  },
  methods: {
  }
}

// {"title":"CO浓度","deviceType":"CO","deviceMainType":"CO","category":"楼层","type":"base","displayType":"list","statusKey":"CO","summaryType":"sensor"}
</script>
