<template>
	<view class="park-info">
		<view class="tabs">
			<view :class="{'active' : parkInfoTabsActive === item.code}" v-for="(item,index) in parkInfoTabs"
				:key="index" @click="parkInfoTabsActive = item.code">
				{{item.name}}
			</view>
		</view>
		<view class="cont">
			<block v-if="parkInfoTabsActive === 'shops'">
				<!-- <image class="empty" src="@/static/shop_empty.png" mode="aspectFill"></image> -->
				<view class="shoplist">
					<view class="list" v-for="(item,index) in shops" :key="index" @click="goDetail(item.id)">
						<image class="logo" :src="`${api}${item.avatar}`" mode="aspectFill"></image>
						<view class="rt">
							<view class="title">{{item.name}}</view>
							<view class="local">{{item.address}}</view>
							<!-- <image class="go" src="@/static/right.png" mode="aspectFit"></image> -->
						</view>
					</view>
				</view>
			</block>
			<block v-if="parkInfoTabsActive === 'intro'">
				<view class="intro">
					<image :src="`${api}/image/miniStatic/intro.png`" mode="aspectFill"></image>
					<view class="text">
						<view class="text_name">海创商务大厦</view>
						<view class="text_msg">
							我司（上海创玏置业有限公公司）开发建设的位于嘉定区双丁路688弄1-8号“海创商务大楼”为商业项目，具体门牌号为嘉定区双丁路688弄1-8号，占地面积13738.00平方米，总建筑面积66262.62平方米，其中地上建筑面积41923.80平方米，地下车位建筑面积24338.82平方米。车位共382个，其中地下368个，地上14个。绿化率20%，消防设施配备：消火栓系统；喷淋系统；自动报警系统；防排烟系统；应急照明系统；疏散指示系统；
						</view>
					</view>
					<view class="text">
						<view class="text_name">海创商务大楼</view>
						<view class="text_msg">
							我司（上海创玥置业有限公公司）开发建设的位于嘉定区塔里木路55弄1-7号“海创商务大楼”为商业项目，具体门牌号为嘉定区塔里木路55弄1-7号，占地面积11351.80平方米，总建筑面积54665.10平方米，其中地上建筑面积34790.97平方米，地下车位建筑面积19874.13平方米，车位共314个，其中地下310个，地上4个；绿化率20%，消防设施配备：消火栓系统；喷淋系统；自动报警系统；防排烟系统；应急照明系统；疏散指示系统；
						</view>
					</view>
					<view class="text">
						<view class="text_name">交通路网</view>
						<view class="text_msg"> 驾车30分钟至虹桥枢纽，10分钟至嘉闵高架；距离11号线嘉定新城站约1.5km，距离嘉闵线丰茂路站（在建中）约2km；</view>
					</view>
					<image :src="`${api}/image/miniStatic/supporting_map.png`" mode="aspectFill"></image>
				</view>
			</block>
			<block v-if="parkInfoTabsActive === 'property'">
				<view class="intro rich">
					<rich-text :nodes="propertyIntro.content"></rich-text>
				</view>
			</block>
			<block v-if="parkInfoTabsActive === 'companys'">
				<view class="intro companys">
					<Companys />
				</view>
			</block>
		</view>
	</view>
</template>

<script setup>
	import { ref, onMounted } from 'vue'
	import { getDataFromUrl } from '@/utils/tools.js'
	import { getMerchantsList, getNewsList } from '@/api/commonApi';
	import Companys from "@/pages/components/companys.vue"
	import {formatRichText} from '@/utils/tools.js'
	const api = uni.$g.VUE_APP_BASE_API;
	const parkInfoTabs = [
		{
			name: "园区介绍",
			code: "intro"
		},
		{
			name: "物业介绍",
			code: "property"
		},
		{
			name: "海创荟",
			code: "shops"
		},
		{
			name: "入驻企业",
			code: "companys"
		},
	]
	const shops = ref([]);
	const propertyIntro = ref({})
	const parkInfoTabsActive = ref('intro');
	const getMerchantsListFun = () => {
		getMerchantsList().then(({ rows = [] }) => {
			const shopsList = rows.filter(item => item.category === "配套资源");
			shopsList.forEach(item => {
				const imgs = item.imageUrl ? item.imageUrl.split(',') : ""
				item.avatar = imgs && imgs[0] ? imgs[0] : "";
			});
			shops.value = [...shopsList];
		})
	}
	const getPropertyIntroFun = () =>{
		getNewsList().then(({rows = []}) =>{
			propertyIntro.value = rows.find(item => item.category === '物业介绍') || {};
			propertyIntro.value.content = formatRichText(propertyIntro.value.content)
		})
	}
	const goDetail = (id) => {
		uni.navigateTo({
			url: `/pages/building/companysDetail?id=${id}`
		});
	}
	onMounted(() => {
		const { tabsActiveCode = 'shops' } = getDataFromUrl()
		parkInfoTabsActive.value = tabsActiveCode;
		getMerchantsListFun();
		getPropertyIntroFun();
	})
</script>

<style lang="scss" scoped>
	.park-info {
		background-color: #fff;
		margin-top: 24rpx;
		padding: 0 22rpx;
		height: calc(100vh - 24rpx);

		.tabs {
			display: flex;
			align-items: center;

			&>view {
				color: #5A6066;
				padding: 16rpx 32rpx;
				font-size: 28rpx;
				&.active {
					color: #2294FE;
					position: relative;

					&::after {
						content: "";
						display: block;
						width: 54rpx;
						height: 6rpx;
						background: linear-gradient(90deg, #2294FE 0%, rgba(34, 148, 254, 0) 100%);
						position: absolute;
						left: 50%;
						transform: translate(-50%, 12rpx);
					}
				}
			}

		}

		.cont {
			text-align: center;
			margin-top: 28rpx;
			overflow-y: auto;
			height: calc(100% - 110rpx);

			.empty {
				width: 446rpx;
				height: 328rpx;
			}

			.shoplist {
				.list {
					display: flex;
					align-items: flex-start;
					padding-bottom: 28rpx;
					.logo {
						width: 110rpx;
						height: 110rpx;
						margin-right: 30rpx;
						flex-shrink: 0;
					}

					.rt {
						position: relative;
						width: 100%;
						height: 110rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						text-align: left;
						border-bottom: 2rpx solid #F0F2F5;
						padding-bottom: 28rpx;
						.title {
							font-size: 32rpx;
							color: #000000;
						}
						.local {
							font-size: 24rpx;
							color: #879099;
						}
						.go {
							width: 36rpx;
							height: 36rpx;
							position: absolute;
							top: 0;
							right: 20rpx;
						}
					}

				}
			}

			.intro {
				padding-bottom: 24rpx;
				&.companys {
					padding-bottom: 0rpx;
				}
				image {
					width: 100%;
					height: 290rpx;
					border-radius: 24rpx;
					overflow: hidden;
				}

				.text {
					font-size: 28rpx;
					color: #879099;
					text-align: left;
					margin: 20rpx 0 30rpx;

					.text_name {
						font-weight: 500;
						font-size: 28rpx;
						color: #2D3033;
						margin-bottom: 12rpx;
					}

					.text_msg {
						line-height: 42rpx;
					}
				}
			}
		}
	}
</style>

<style scoped lang="scss">
	.rich {
		width: 100%;
		background: #fff;
		height: 100vh;
		text-align: center;
		font-size: 32rpx;
		text-indent: 24px;
		
	}

	::v-deep {
		.ql-align-center {
			text-align: center;
		}

		.ql-align-right {
			text-align: right;
		}

		.ql-align-left {
			text-align: left;
		}

		.ql-img {
			width: 100%;
			display: block;
		}
		.rich-text-container {
			width: calc(100% - 64rpx);
			margin-left: 32rpx;
			line-height: 46rpx;
		}
		.ql-size-small {
		  font-size: 0.75em;
		}
		.ql-size-large {
		  font-size: 1.5em;
		}
		.ql-size-huge {
		  font-size: 2.5em;
		}
	}
</style>