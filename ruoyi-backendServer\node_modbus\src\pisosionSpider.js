/**
 正大天晴项目 对接 潘诺佩亚 温湿度数据
 功能:
 运行: node src/pisosionSpider.js [ind] [dbName] [debug]

 字典 building_config_1 中增加配置
 pis_server：   服务器地址，  https://api.dayufeng.cn
 pis_username： 用户名
 pis_password： 密码
 pis_collectorId： 采集器id
 pis_topic： 把数据以mqtt转发出去的topic


 // 1分钟执行一次
 syncDeviceData(); -> 同步所有数据

 // 1小时重启一次


 */

// create an empty modbus client
const schedule = require("node-schedule");
var moment = require("moment");

const Db = require("./mydb");
const renjiApi = require("./lib/pisosionApi");
const helper = require("./helper");
const mqtt = require("mqtt");

const CollectorConf = require("./conf/collectorConfig2").collectorConfig();
const sysConfig = require("./conf/sysConfig").sysConfig();
const config = {};

const ind = process.argv[2] > 0 ? process.argv[2] : 1;
const port = process.argv[3] && process.argv[3] != "debug" ? process.argv[3] : 8001;
const dbName = process.argv[4] && process.argv[4] != "debug" ? process.argv[4] : null;
global.isDebug = process.argv[5] == "debug" ? true : false;

const conf = CollectorConf[ind];

// 参数替换 覆盖之前的配置
if (dbName) {
    sysConfig.mysql.database = dbName;
}
// 替换端口
if (port) {
    sysConfig.localServer.port = port;
}

var _db = new Db(sysConfig.mysql);
// 双数据库同步
if (typeof sysConfig.mysql2 != "undefined") {
    _db.createServer2(sysConfig.mysql2);
}

var mqttClient = null;
// i接口实例，启动加载配置后，初始化
let _pxServ = null;

var pxConfigSql = `
    select
        *
    from sys_dict_data
    where dict_type = ? and dict_label like "pis_%";
`;
var codeSql = `
    SELECT
        code
    FROM a_item
    where collector_id = ?
`;

var dbUpdateSql = `
    SELECT
        i.code,
        i.alias,
        i.description,
        i.name,
        d.id as item_data_id,
        d.name as item_data_name,
        d.val as item_data_val,
        ifnull(d.max_val, '') max_val,
        ifnull(d.min_val, '') min_val,
        p.func_list,
        p.func
    FROM a_collector c
    LEFT JOIN a_item i on c.id = i.collector_id
    LEFT JOIN a_item_data d on i.id = d.item_id
    LEFT JOIN a_point_table p on d.id = p.item_data_id
    where c.id = ?
`;

// 全局变量
var gtmp = {};
var dbDataList = [];

// 读取配置 获取对应配置
async function readApiConfig() {
    if (gtmp.readApiConfig) {
        // 任务未执行完成, 等待下次轮训
        return false;
    }
    // 初始化服务
    gtmp.readApiConfig = true;
    try {
        let list = await _db.doSql(pxConfigSql, ["building_config_1"]);
        if (list.length > 0) {
            for (let i = 0; i < list.length; i++) {
                let conf = list[i];
                if (conf.dict_label == "pis_server") {
                    config.server = conf.dict_value;
                }
                if (conf.dict_label == "pis_username") {
                    config.username = conf.dict_value;
                }
                if (conf.dict_label == "pis_password") {
                    config.password = conf.dict_value;
                }
                if (conf.dict_label == "pis_collectorId") {
                    config.collectorId = conf.dict_value;
                }
            }
        }
        helper.debug("read db config", config);
        // 实例化接口服务
        _pxServ = new renjiApi.RjApi(config);

        await _pxServ.getToken();
        clientInit()
    } catch (e) {
        console.trace(e);
        helper.info("readApiConfig error", e.message);
        gtmp.readApiConfig = false;
        process.exit(1);
    }
    gtmp.readApiConfig = false;
}

function  clientInit() {
    mqttClient = mqtt.connect(conf.host, conf);
    mqttClient.on('connect', function () {
        console.log(new Date(), "[数据采集平台]:连接mqtt服务");
    });
    mqttClient.on('message', function (topic, message) {
        let data = message.toString("utf8");
        if(topic == conf.prefix){
        }
    });
    mqttClient.subscribe(conf.prefix, function(err, res){
        if(err){
            console.log(new Date(), "[数据采集平台]:主题订阅出错:" + conf.prefix);
        } else {
            console.log("订阅成功", res);
        }
    });
}
async function getData() {
    dbDataList = await _db.doSql(dbUpdateSql, [config.collectorId]);
}

async function syncDeviceData() {
    if (gtmp.syncDeviceData) {
        // 任务未执行完成, 等待下次轮训
        return false;
    }
    // 初始化服务
    gtmp.syncDeviceData = true;
    try {
        // 调用 api 获取数据
        let apiDataRes = await _pxServ.getDeviceData();
        let apiData = (apiDataRes.data && apiDataRes.data.data) || [];
        for (let i = 0; i < apiData.length; i++) {
            let data = apiData[i]
            if (data.data_list && data.data_list.length) {
                for (let j = 0; j < data.data_list.length; j++) {
                    let listItem = data.data_list[j]
                    if (dbDataList.length) {
                        for (let k = 0; k < dbDataList.length; k++) {
                            let cp = dbDataList[k];
                            if (data.shebeibianhao == cp.code && listItem.custom_type == cp.func) {
                                let val = listItem.data;
                                // 更新数据点位
                                helper.debug("[sync to db]", cp.item_data_id, val);
                                // 统一数据入库
                                await helper.syncItemData2Db({
                                    id: cp.item_data_id,
                                    val: val,
                                    updatedAt: moment().format("YYYY-MM-DD HH:mm:ss"),
                                });
                                let msg = {
                                    area:cp.code,
                                    id:cp.alias,
                                    it:cp.description,
                                    cmd:"control",
                                    values: {}
                                }
                                msg.values[cp.func.toUpperCase()] = val
                                helper.debug('推送数据。。。。。。。。。。',cp.item_data_id,msg)
                                // mqtt 推数据
                                mqttClient.publish(conf.writeTopic, JSON.stringify(msg));
                            }else {
                                // helper.debug("no.........",listItem);
                            }
                        }
                    }
                }
            }
        }

    } catch (e) {
        console.trace(e);
        helper.info("syncDeviceData error", e.message);
        gtmp.syncDeviceData = false;
    }
    gtmp.syncDeviceData = false;
}
async function start() {
    // 1分钟执行一次    30 * * * * *
    schedule.scheduleJob("1 */1 * * * *", () => {
        syncDeviceData();
        helper.log("syncDeviceData success");
    });
    // 半小时执行一次
    schedule.scheduleJob("1 */30 * * * *", () => {
        getData();
        helper.log("generaterEnergyData success");
    });
    // 1小时执行一次
    schedule.scheduleJob("1 1 * * * *", () => {
        helper.log("Auto restart server");
        process.exit(1);
    });

    // 数据服务初始化
    helper.initDataServer(sysConfig);
    helper.info("initDataServer success");

    // 日志服务连接至远程logstash
    helper.connectLogstash(sysConfig);
    helper.info("connectLogstash success");

    await readApiConfig();
    await getData();
    await syncDeviceData();
    helper.info("syncDeviceData success");
    helper.info("start success");
}

start();
