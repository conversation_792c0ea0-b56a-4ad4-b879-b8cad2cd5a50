<template>
	<view class="main">
		<view class="card">
			<image class="bg" :src="`${api}/image/miniStatic/${pageType}_scan_bg.png`" mode="aspectFill"></image>
		</view>
		<view class="cont">
			<view class="menus">
				<view class="menus_item" v-for="(item, index) in doorSub" :key="index" @click="goPageByType(item.type)">
					<image class="icon" :src="item.icon" mode="aspectFit"></image>
					<view class="name">{{ item.label }}</view>
				</view>
			</view>
			<view class="statistics">
				<view class="chart">
					<MonthDataHorizonBarVue ref="monthDataHorizonBarVue" :title="horizonBarTitle"/>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { scanTheCode, getDataFromUrl } from '@/utils/tools.js'
import MonthDataHorizonBarVue from './stats/monthDataHorizonBar.vue';
import {getDictDataByParams} from '@/api/commonApi.js'
const api = uni.$g.VUE_APP_BASE_API;
const monthDataHorizonBarVue = ref(null)
const pageType = ref("application");
const doorSub = computed(() => {
	return [
		{
			label: '预约单记录',
			type: 'records',
			icon: "../../static/menu_repair.png"
		},
		{
			label: '预约单申请',
			type: 'apply',
			icon: "../../static/repair_apply.png"
		}
	]
})
const horizonBarTitle = ref("预约单统计")
const deviceId = ref('');
const scan = () => {
	scanTheCode(res => {
		let data = {};
		try {
			data = JSON.parse(res.trim())
		} catch (error) {}
		deviceId.value = data.deviceId;
		uni.navigateTo({
			url: `/pages/application/add?deviceId=${deviceId.value}`
		})
	})
}

const goPageByType = (type) => {
	let url = '/pages/application/index';
	switch (type) {
		case 'records':
			url = `/pages/application/list`
			break;
		case 'apply':
			url = `/pages/application/add`
			break;
		default:
			url = '/pages/application/index'
			break;
	}
	uni.navigateTo({
		url
	})
}

onMounted(() => {
	//$on 一定要写道 $emit执行之前
	uni.$on('refresh-horizonBar', () => {
		console.log('fresh')
		monthDataHorizonBarVue.value && monthDataHorizonBarVue.value.getList && monthDataHorizonBarVue.value.getList();
	})
	//增值服务
	uni.setNavigationBarColor({
		frontColor: '#ffffff',
		backgroundColor: '#22B8FE'
	})
})
</script>

<style lang="scss" scoped>
$shodow: 0px 2px 6px 1px #ddd;

.main {
	height: 100vh;
	background-color: #fff;

	.card {
		width: 100%;
		height: 450rpx;
		text-align: center;
		position: relative;

		.bg {
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 450rpx;
		}

		.scan {
			width: 360rpx;
			height: 360rpx;
			z-index: 1;
			position: relative;
			top: 20rpx;
		}
	}

	.cont {
		background: #F0F2F5;
		position: absolute;
		width: 100%;
		top: 420rpx;
		border-top-right-radius: 40rpx;
		border-top-left-radius: 40rpx;
		overflow: hidden;

		.menus {
			display: flex;
			justify-content: space-around;
			align-items: center;
			background-color: #fff;
			padding: 40rpx 66rpx;

			&_item {
				text-align: center;

				image {
					width: 96rpx;
					height: 96rpx;
				}

				.name {
					font-size: 28rpx;
					color: #2D3033;
				}
			}
		}

		.statistics {
			margin-top: 24rpx;
			background-color: #fff;
			padding: 24rpx 40rpx;

			canvas {
				width: 100%;
			}
		}
	}

}


.chart {
	width: 100%;
}
</style>