/**
 山东科大中天安控api 天津大学项目
 功能:
 1. 服务同步数据到本地库
 运行: node src/kddbSpider.js [buildingId] [port] [dbName] [debug]

 字典 building_config_[buildingId] 中增加配置
 kddb_server                    api 地址
 kddb_systemnum                 请求 token 用
 kddb_collectorId               采集器id

 // 一天执行一次
 syncData(); -> 同步所有数据

 */


const schedule = require('node-schedule');
var moment = require('moment');

const Db = require("./mydb");
const dbApi = require('./lib/kddbApi');
const helper = require("./helper");
const decUtil = require('./lib/_decUtil');

const sysConfig = require('./conf/sysConfig').sysConfig();
const config = {};

const buildingId = process.argv[2] > 0 ? process.argv[2] : 1;
const port = process.argv[3] && process.argv[3] != "debug" ? process.argv[3] : null;
const dbName = process.argv[4] && process.argv[4] != "debug" ? process.argv[4] : null;
global.isDebug = process.argv[5] == "debug" ? true : false;

// 替换端口
if(port) {
    sysConfig.localServer.port = port;
}
// 参数替换 覆盖之前的配置
if(dbName) {
    sysConfig.mysql.database = dbName;
}

var _db = new Db(sysConfig.mysql);
// 双数据库同步
if(typeof sysConfig.mysql2 != "undefined") {
    _db.createServer2(sysConfig.mysql2);
}

// i接口实例，启动加载配置后，初始化
let _dbServ = null;

var bydConfigSql = `
    select
        *
    from sys_dict_data
    where dict_type = ? and dict_label like "kddb%";
`;

var pointListSql = `
    SELECT
        i.name,
        d.id as item_data_id,
        d.name as item_data_name,
        d.val as item_data_val,
        p.func_list,
        p.func,
        d.updated_at
    FROM a_collector c
    LEFT JOIN a_item i on c.id = i.collector_id
    LEFT JOIN a_item_data d on i.id = d.item_id
    LEFT JOIN a_point_table p on d.id = p.item_data_id
    where c.id = ?
`
// 电表数据入库
var dbSql = {
    "check":"select * from d_kd_db_record where roomtag=? and endtime=?",
    "insert": "insert into d_kd_db_record (roomtag, zhaddress, zhxm, stattime, endtime, startcode, endcode,jfmx,glvalue,ylvalue,updated_at) values (?,?,?,?,?,?,?,?,?,?,?)",
    "update": "update d_kd_db_record set updated_at = ? where roomtag=?"
}

// 全局变量
var gtmp = {};

// 读取配置，根据 buildingId 获取对应配置
async function readApiConfig() {
    if(gtmp.readApiConfig) {
        // 任务未执行完成, 等待下次轮训
        return false;
    }
    // 初始化服务
    gtmp.readApiConfig = true;
    try {
        let list = await _db.doSql(bydConfigSql, ["building_config_"+buildingId]);
        if(list.length > 0) {
            for(let i=0; i<list.length; i++) {
                let conf = list[i];
                if(conf.dict_label == "kddb_server") {
                    config.server = conf.dict_value;
                }
                if(conf.dict_label == "kddb_systemnum") {
                    config.systemnum = conf.dict_value;
                }
                if(conf.dict_label == "kddb_collectorId") {
                    config.collectorId = conf.dict_value;
                }
            }
        }
        helper.debug("read db config", config);
        // 实例化接口服务
        _dbServ = new dbApi.dbApi(config);
    } catch (e) {
        console.trace(e);
        helper.info("readApiConfig error", e.message);
        gtmp.readApiConfig = false;
    }
    gtmp.readApiConfig = false;
}

// 同步数据
async function syncData() {
    if(gtmp.syncData) {
        // 任务未执行完成, 等待下次轮训
        return false;
    }
    // 初始化服务
    gtmp.syncData = true;
    try {
        await getToken();
        // 同步实时数据
        await syncDataToDb();

    } catch (e) {
        helper.info("syncData_error", e.message);
        gtmp.syncData = false;
    }
    gtmp.syncData = false;
}

// 入库前数据处理
function dealVals(list) {
    return list.map( v => {
        if(typeof v == "undefined") {
            return null;
        }
        return v;
    })
}

async function getToken(){
    await _dbServ.getToken(config.systemnum);
}

// 同步数据到item_data
async function syncDataToDb(){
    let pointList = await _db.doSql(pointListSql, [config.collectorId]);

    let data = {
        starttime: moment().subtract(1, 'days').format("YYYY-MM-DD") + ' 09:00:00',
        endtime: moment().format("YYYY-MM-DD")+ ' 11:00:00'
    }
    let acDetail = await _dbServ.getYdlByTime(data);
    if(acDetail && acDetail.resultvalue) {
        for(let i=0; i<acDetail.resultvalue.length; i++) {
            try {
                let d = acDetail.resultvalue[i];
                let _res = await _db.insertOrUpdate(dbSql, {
                    "check":  [d.roomtag,moment(d.endtime,'YYYY/M/D HH:mm:ss').format("YYYY-MM-DD HH:mm:ss")],
                    "insert":dealVals([d.roomtag,d.zhaddress,d.zhxm,moment(d.stattime,'YYYY/M/D HH:mm:ss').format("YYYY-MM-DD HH:mm:ss"),moment(d.endtime,'YYYY/M/D HH:mm:ss').format("YYYY-MM-DD HH:mm:ss"),d.startcode,d.endcode,d.jfmx,d.glvalue,d.ylvalue,moment().format("YYYY-MM-DD HH:mm:ss")]),
                    "update": [moment().format("YYYY-MM-DD HH:mm:ss"),d.roomtag],
                });
                helper.debug(_res);
                for(let j=0; j<pointList.length; j++) {
                    let p = pointList[j]
                    // 每天更新一次
                    if(moment(p.updated_at).format("YYYY-MM-DD") != moment().format("YYYY-MM-DD")){
                        let val = Number(d.startcode) + Number(d.glvalue) - Number(d.endcode)
                        if(p.func == d.roomtag){
                            // 累计用电量
                            helper.debug("[sync to db]", p.item_data_id, d.ylvalue);
                            await helper.syncItemData2Db({
                                id: p.item_data_id,
                                val: Number(p.item_data_val) + val,
                                updatedAt: moment().format("YYYY-MM-DD HH:mm:ss"),
                            });
                        }else if(p.func == d.roomtag + '_today'){
                            // 今日用电量
                            await helper.syncItemData2Db({
                                id: p.item_data_id,
                                val: val,
                                updatedAt: moment().format("YYYY-MM-DD HH:mm:ss"),
                            });
                        }else if(p.func == d.roomtag + '_endcode'){
                            // 今日用电量
                            await helper.syncItemData2Db({
                                id: p.item_data_id,
                                val: Number(d.endcode),
                                updatedAt: moment().format("YYYY-MM-DD HH:mm:ss"),
                            });
                        }

                    }

                }
            } catch(e) {
                helper.log("syncAccssControl acDetail error", e);
            }
        }
    }
}

async function run(){
    await readApiConfig();
    await getToken();
    await syncData();
}


async function start() {
    // 每1秒一次
    schedule.scheduleJob('*/1 * * * * *',()=>{
        //helper.log('syncDbWriteToDevice success');
    });

    // 3分钟执行一次    30 * * * * *
    schedule.scheduleJob('1 */3 * * * *',()=>{
        // getToken();
        // syncData();
        // helper.log('syncData success');
    });
    // 每天执行一次
    schedule.scheduleJob('1 1 12 * * *',()=>{
        run()
        helper.log('Auto restart server');
    });
    schedule.scheduleJob('1 1 11 * * *',()=>{
        helper.log('Auto restart server');
        process.exit(1);
    });

    // 数据服务初始化
    helper.initDataServer(sysConfig);
    helper.info("initDataServer success");

    // mqtt连接，系统互通各种消息通道
    helper.connectMqtt("mqtt");
    helper.info("connectMqtt success");

    // 日志服务连接至远程logstash
    helper.connectLogstash(sysConfig);
    helper.info("connectLogstash success");

    // influxdb初始化，记录实时数据点
    helper.initInfluxdb("influxdb");
    helper.info("init Influxdb success");

    helper.info("syncData success");
    helper.info("start success");
};

start();

// 获取列表
async function test() {
    await readApiConfig();

    let res = await _dbServ.GetLastData();
    console.log("-----  accessControlList  ------");
    console.log(JSON.stringify(res));

}

// test();
