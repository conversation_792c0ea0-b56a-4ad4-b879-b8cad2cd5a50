<template>
  <div>
    <!-- 建筑分组 -->
    <el-tabs class="resourceTabsParent" tab-position="top" v-model="activeResourceGroup"
      @tab-click="handleResourceGroupChange">
      <el-tab-pane :label="g" :name="g" v-for="g in resourceGroupList">
        <span slot="label" class="tabTitle" v-show="showTabs">
          <i class="el-icon-office-building" size="mini"></i>
          {{ g }}
        </span>

        <!-- 分组内楼层 -->
        <el-tabs class="resourceTabs" tab-position="left" v-model="activeResourceId" @tab-click="handleResourceChange"
          style="height: 700px;">
          <el-tab-pane :key="f.id + ''" :name="f.id + ''" v-if="f.groupName == g" v-for="f in resourceList">
            <span slot="label" class="tabTitle" v-show="showTabs">
              {{ f.name }}
            </span>

            <div class="mt10">
              <el-pagination background class="pull-left" layout="total, sizes, prev, pager, next"
                :current-page.sync="pageNum" :page-size="pageSize" :total="deviceTotal" @current-change="changePage"
                @size-change="sizeChange" />

              <div class="pull-right">
                <!-- <div class="pull-left" style="position: relative;top: 4px;margin-right: 16px;">
                  敏捷下发锁<i :class="locked ? 'el-icon-lock' : 'el-icon-unlock'" class="ml5"></i>
                </div> -->
                <DeviceResoureEdit class="pull-left" v-hasRole="['admin', 'operator']" :deviceList="deviceList"
                  :deviceType="deviceMainType" :resourceId="activeResourceId"
                  @handleResoureEditClose="handleResoureEditClose"
                  style="position: relative;top: -8px;margin-right: 16px;" />
                <el-tooltip class="mark mr20" effect="dark" popper-class="custom_tooltip"
                  content="敏捷下发开启后将不再弹框询问是否确认下发命令，开启敏捷下发需要填写登录密码验证" placement="top-start">
                  <el-button plain size="small" type="primary" style="position: relative;padding-right: 28px;"
                  @click="quickOrderChange">敏捷下发<i :class="locked ? 'el-icon-lock' : 'el-icon-unlock'" class="ml5"
                    color="red" style="font-size: 16px;position: absolute;right: 6px;top: 7px;"></i></el-button>
                </el-tooltip>
                <el-button class="mr20" size="small" type="primary" @click="handleUpdateData">刷新数据</el-button>
                <el-button class="mr10" size="small" type="warning" plain @click="exportTable">导出数据</el-button>

                <!-- <el-popover class="mr10" placement="left" title="表格选项" width="200" trigger="click">
                  <el-checkbox-group style="max-height: 480px; overflow: auto;" v-model="tableShowTitles"
                    @change="tableTitlesChange">
                    <el-checkbox class="m10" v-for="t in tableFullTitles" :label="t" :value="t"></el-checkbox>
                  </el-checkbox-group>
                  <el-button slot="reference" type="text" icon="el-icon-s-tools" size="small"
                    v-hasPermi="['device:*:*']">表格选项</el-button>
                </el-popover> -->
              </div>
              <div class="clearfix mb10"></div>

              <div class="p10" :key="`${g}-${f.id}`">
                <el-table id="tb" :data="filterDevice" :row-key="getRowKey" stripe>
                  <el-table-column label="设备名称" prop="name" sortable />
                  <el-table-column label="回路控制" prop="controls" sortable width="600">
                    <template slot-scope="scope">
                      <div style="width: 100%;display: flex;align-items: center;">
                        <el-tooltip v-for="(t, index) in scope.row.shortcutTitles" :key="index" class="mark"
                          effect="dark" popper-class="custom_tooltip" :content="`${t.dmName}（${t.dmId}）`"
                          placement="top-start">
                          <div :class="['light', getBgClass(scope.row, t.dmName)]"
                            @click="changeStatus(scope.row, t.dmName)" />
                        </el-tooltip>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column v-if="modelColumn.label" :label="modelColumn.label" :prop="modelColumn.tag"
                    sortable />
                  <el-table-column label="位置" prop="position" sortable>
                    <template slot-scope="scope">
                      {{ scope.row["position"] }}
                      <i class="el-icon-edit text-success" type="primary" style="cursor: pointer; margin-top: 1px;" @click="editPosition(scope.row)"></i>
                    </template>
                  </el-table-column>
                  <el-table-column label="状态" sortable width="120">
                    <template slot-scope="scope">
                      <el-tooltip
                        placement="top-start"
                        trigger="hover">
                        <div slot="content">
                          <!-- <div v-if="scope.row.workStatus">开关状态:  {{ scope.row.workStatus }} </div>
                          <div>告警状态:  {{ scope.row.warningStatus }} </div> -->
                          <div v-if="scope.row.communicateStatus">通讯状态:  {{ scope.row.communicateStatus }} </div>
                        </div>
                        <el-tag color="#2294fe2e" size="mini" type="success" style="margin:2px 5px 2px 0;">
                          <!-- <svg-icon v-if="scope.row.workStatus" icon-class="deviceRunning" class="titleIcon mr5" :class="scope.row.dRuning+'Txt'" />
                          <svg-icon icon-class="deviceWarning" class="titleIcon mr5" :class="scope.row.dStatus+'Txt'" /> -->
                          <svg-icon icon-class="deviceOnline" class="titleIcon" :class="scope.row.dOnline+'Txt'" />
                        </el-tag>
                      </el-tooltip>
                      <el-tooltip
                        placement="top-start"
                        trigger="hover"
                        v-if="scope.row.strategyList && scope.row.strategyList.length > 0">
                        <div slot="content">
                          <div v-for="s in scope.row.strategyList" v-text="s.name" />
                        </div>
                        <el-tag color="#2294fe2e" size="mini" type="primary" style="margin:2px 2px 2px 0; font-size: 10px;">
                            <i style="color: #ffffffa6;">AI</i>
                        </el-tag>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <el-table-column :key="`${t}-${timestamp}`" v-for="t in tableShowTitles" :prop="t" :label="t"
                    width="100" sortable />

                  <el-table-column label="操作" align="center" width="180">
                    <template slot-scope="scope">
                      <el-button size="mini" plain type="primary" icon2="el-icon-view"
                        @click="handleAllOpen(scope.row)">全开</el-button>
                      <el-button size="mini" class="ml10" plain type="danger" icon2="el-icon-view"
                        @click="handleAllClose(scope.row)">全关</el-button>
                      <el-button size="mini" class="ml10" plain icon2="el-icon-view"
                        @click="handleDetailShow(scope.row)">详情</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
    </el-tabs>
    <el-dialog :visible.sync="open" width="700px" :before-close="dialogClose">
      <div slot="title" style="cursor: move; position: relative; z-index: 10;">
        {{ '(' + activeDetail.id + ')' + " " + activeDetail.name }}
        <el-tooltip class="item" effect="dark" content="修改设备名称" placement="top" v-hasPermi="['device:device:edit']"
          v-hasRole="['admin', 'operator']">
          <i class="el-icon-edit ml10 text-success" type="primary" style="cursor: pointer; margin-top: 1px;"
            @click="showEditDevice = true"></i>
        </el-tooltip>
      </div>
      <div class="flex-row flex-align-v detail_fields">
        <div>节点编号：{{ activeDetail.description }}</div>
        <div>通讯状态：{{ activeDetail.communicateStatus }}</div>
      </div>
      <div class="flex-row flex-align-v mt10" v-if="hasAllControl">
        <el-button plain size="mini" @click="handleAllOpen(activeDetail)">全开</el-button>
        <el-button type="danger" plain size="mini" class="ml20" @click="handleAllClose(activeDetail)">全关</el-button>
      </div>
      <el-table :data="activeDetail.shortcutTitles" stripe size="mini" class="mt20">
        <el-table-column label="回路" prop="dmName" sortable />
        <el-table-column label="开关" prop="dVal" sortable>
          <template slot-scope="scope">
            <el-switch v-model="scope.row.dVal" active-color="#13ce66" inactive-color="#ff4949"
              :active-value="getSwitchValue(scope.row.otherRDataMap, 'active')"
              :inactive-value="getSwitchValue(scope.row.otherRDataMap, 'inactive')"
              @change="switchChange(scope.row.dmName, true)" />
          </template>
        </el-table-column>

        <el-table-column label="状态" prop="name" sortable>
          <template slot-scope="scope">
            <div :class="['light', 'small', getBgClassByItem(scope.row.valStr || '')]" />
          </template>
        </el-table-column>

        <el-table-column label="控制回路" prop="dmNote" sortable>
          <template slot-scope="scope">
            {{ scope.row.dmNote }}<i class="el-icon-edit ml10 text-success" type="primary"
              style="cursor: pointer; margin-top: 1px;" @click="editNote(scope.row.dmId, scope.row.dmNote)"></i>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>
    <editDeviceInfo :activeDevice="activeDetail" :showEditDeviceInfo="showEditDevice"
      @updateDeviceList="updateDeviceList">
    </editDeviceInfo>
  </div>
</template>

<script>
import { updateDeviceDatas } from "@/api/device/apis";
import DisplayBase from "@/views/device/monitor/components/Device/DisplayBase.js";
import { checkUser } from '@/api/login';
import { getPubKey } from '@/utils/auth'
import { encrypt } from '@/utils/jsencrypt';
import editDeviceInfo from '@/views/device/monitor/components/Device/components/editDeviceInfo.vue'
import { cloneDeep } from 'lodash';
import { updateDeviceItemDataMap} from "@/api/device/deviceItemDataMap";
import { updateDevice } from "@/api/device/device";
export default {
  props: {

  },
  mixins: [DisplayBase], // 继承父模块
  components: { editDeviceInfo },
  data() {
    return {
      tableFullTitles: [],
      tableShowTitles: [],
      shortcutTitles: [],
      dataNum: 0,  // 默认显示 3 个数据
      showExtend: false,
      expandRowKeys: [],
      timestamp: this.$moment().valueOf(),
      tableTitlesCopy: null,
      filterDevice2: [],
      tag: "lightOrderColumn",//开关点位tag
      modelColumn: {
        tag: "lightControlMode",//控制模式
        name: "控制模式",
        label: ""
      },
      allControlTag: "lightAllControl",
      locked: true,
      statusMap: [
        {
          name: "off",
          maybe: ['停', '关'],
          code: "",
          drRealVal: 0
        },
        {
          name: "on",
          maybe: ['开', '启', '运'],
          code: "",
          drRealVal: 1
        }
      ],
      open: false,
      form: {},
      activeDetail: {},
      showEditDevice: false,
      detailList: [],
      showEditNote: false
    };
  },
  created() {
  },
  mounted() {
    this.getDicts("d_device_type").then(response => {
      this.typeOptions = response.data;
    });
    this.pageSize = this.defaultPageSize;
  },
  destroyed() {
  },
  computed: {
    hasAllControl () {
      const dt = (this.activeDetail.deviceDataBase || []).find(item => item.dmTag.indexOf(this.allControlTag) > -1);
      return !!dt
    }
  },
  methods: {
    // 根据点位数据生成表列
    addTableColum() {
      this.tableFullTitles = [];
      const arr = []
      if (this.deviceList.length > 0) {
        // 通过第一个设备，创建表格 title
        let fd = this.deviceList[0];
        const deviceDataBaseFilter = fd.deviceDataBase.filter(item => item.dmTag.indexOf(this.tag) === -1 && item.dmTag.indexOf(this.modelColumn.tag) === -1 && item.dmTag.indexOf(this.allControlTag) === -1);
        // 是否显示更多
        this.showExtend = deviceDataBaseFilter.length > this.dataNum;
        deviceDataBaseFilter.forEach((dd, i) => {
          this.tableFullTitles.push(dd.dmName);

          if (i < this.dataNum) {
            arr.push(dd.dmName);
          }
        });
        this.deviceList.map((d, di) => {
          d.deviceDataBase.map((dd, i) => {
            d[dd.dmName] = dd.valStr;
            if (dd.dmTag.indexOf(this.modelColumn.tag) > -1) {
              d[this.modelColumn.tag] = dd.valStr;
              this.modelColumn.label = dd.dmName;
            }
            if (i == 0) {
              this.$set(d, "数据时间", dd.dUpdatedAt)
            }
          });
          const shortcutTitles = d.deviceDataBase.filter(item => item.dmTag.indexOf(this.tag) > -1);
          d.shortcutTitles = shortcutTitles;
          return d;
        });
      }
      this.tableShowTitles = this.tableTitlesCopy || arr;
      this.timestamp = this.$moment().valueOf();
      return this.deviceList;
    },

    // 所有种类设备
    async getResourceDeviceList() {
      await this.getResourceMainDeviceList(undefined);
      this.addTableColum();
      if(this.open){
        let row = this.deviceList.find(item => item.id === this.activeDetail.id)
        let data = cloneDeep(row);
        this.activeDetail = data;
      }
    },

    // 设备类型字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(this.typeOptions, row.type);
    },

    handleUpdateData() {
      this.getResourceDeviceList();
    },

    exportTable() {
      this.et('tb', "设备数据");
    },

    handleSortChange({ prop, order }) {
      this.sortColumn = prop;
      this.sortOrder = order === 'ascending' ? 'asc' : 'desc';
    },
    handleExpandChange(row, expandedRows) {
      if (expandedRows.length > 0) {
        this.expandRowKeys = [row.id]
      } else {
        this.expandRowKeys = []
      }
    },
    getRowKey(row) {
      return row.id; // 每行的唯一标识是id字段
    },
    tableTitlesChange(list) {
      this.tableTitlesCopy = [...list];
    },
    quickOrderChange() {
      if (this.locked) {
        this.$prompt('', '请输入登录密码', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /[^]{6}$/,
          inputErrorMessage: '密码格式不正确',
          inputType: 'password',
        }).then(({ value }) => {
          let publicKey = getPubKey();
          let pwd = encrypt(value, publicKey);
          // 校验用户密码
          checkUser({ pwd: pwd })
            .then(res => {
              console.log(res);
              if (res.data == "success") {
                this.$message.success("敏捷下发已开启,请谨慎操作");
                this.locked = !this.locked;
              } else {
                this.$message({
                  showClose: true,
                  type: 'warning',
                  message: '操作失败: ' + res.data,
                });
              }
            }).catch(e => {
              this.$message({
                showClose: true,
                type: 'warning',
                message: '请求失败，请重试。' + e.message,
              });
            });
        }).catch(() => {
          //
        });
      } else {
        this.locked = !this.locked;
        this.$message.success("敏捷下发已关闭");
      }
    },
    getBgClass(row, prop) {
      let val = ((row.deviceDataBase || []).filter(item => item.dmTag.indexOf(this.tag) > -1).find(item => item.dmName === prop) || {}).valStr || "";
      const it = this.statusMap.find(item => {
        const had = item.maybe.find(i => val.indexOf(i) > -1);
        return had
      });
      return `zm_${it ? it.name : 'off'}`
    },
    getBgClassByItem(valStr) {
      const it = this.statusMap.find(item => {
        const had = item.maybe.find(i => valStr.indexOf(i) > -1);
        return had
      });
      return `zm_${it ? it.name : 'off'}`
    },
    changeStatus(row, itemName, status, isSwitch) {
      if (!this.locked) {
        this.doOrder(row, itemName, status, isSwitch);
        return;
      }
      this.$confirm("确定保存更改?", {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.doOrder(row, itemName, status, isSwitch)
      })
    },
    doOrder(row, itemName, status, isSwitch) {
      let param = {};
      let deviceDataBase = this.filterDevice.find(item => item.id == row.id).deviceDataBase || [];
      if(isSwitch){
        deviceDataBase = row.shortcutTitles
      }
      let itemData = {};
      if (itemName === "all") {
        let arr = deviceDataBase.filter(item => item.dmTag.indexOf(this.allControlTag) > -1);
        itemData = arr[0]
      } else {
        let arr = deviceDataBase.filter(item => item.dmTag.indexOf(this.tag) > -1);
        itemData = arr.find(item => item.dmName === itemName);
      }
      param.id = itemData.drId;
      let onVal = "";
      let offVal = "";
      const it = this.statusMap.find(item => item.name === "on");
      for (const k in itemData.otherRDataMap) {
        let res = it.maybe.some(item => itemData.otherRDataMap[k].indexOf(item) > -1);
        if (res) {
          onVal = k;
          break;
        }
      }
      const it2 = this.statusMap.find(item => item.name === "off");
      for (const k in itemData.otherRDataMap) {
        let res = it2.maybe.some(item => itemData.otherRDataMap[k].indexOf(item) > -1);
        if (res) {
          offVal = k;
          break;
        }
      }
      console.log("执行", onVal, offVal, param);
      param.val = itemData.dVal != onVal ? onVal : offVal;
      if (itemName === "all") {
        param.val = status === 'on' ? onVal : offVal;
      }
      if (isSwitch) {
        param.val = itemData.dVal;
      }

      if (!param.id) {
        this.$message.warning("无指令下发");
        return;
      }
      updateDeviceDatas({ dataList: JSON.stringify([param]) }).then(responses => {
        this.$message.success("下发成功");
      })
    },

    handleAllOpen(row) {
      this.changeStatus(row, "all", 'on')
    },
    handleAllClose(row) {
      this.changeStatus(row, "all", 'off')
    },
    handleDetailShow(row) {
      let data = cloneDeep(row);
      this.activeDetail = data;
      this.open = true;
    },
    dialogClose() {
      this.activeDetail = {};
      this.open = false;
    },
    updateDeviceList(data) {
      this.showEditDevice = false
      if (data) {
        this.refreshList()
      }
    },
    getSwitchValue(theMap, status) {
      let type = status === 'active' ? 'on' : 'off';
      const it = this.statusMap.find(item => item.name === type);
      let val = "";
      for (const k in theMap) {
        if (it.maybe.some(item => theMap[k].indexOf(item) > -1)) {
          val = k;
          break; // 找到后退出循环
        }
      };
      return val;
    },
    switchChange(dmName, isSwitch) {
      console.log(this.activeDetail, "handleDetailShow")
      this.changeStatus(this.activeDetail, dmName, '', isSwitch)
    },
    editNote(id, val) {
      this.$prompt('请输入控制回路', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: val,
      }).then(({ value }) => {
        console.log(value)
        updateDeviceItemDataMap({
          note: value,
          id
        }).then(response => {
          this.handleUpdateData();
          this.$message.success("更新成功");
        })
      })
    },
    editPosition (row) {
      this.$prompt('请输入位置信息', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: row.position,
      }).then(({ value }) => {
        updateDevice({
          id: row.id,
          position: value
        }).then(res => {
          this.handleUpdateData();
          this.$message.success("位置信息更新成功");
        })
      })
    }
  }
}
</script>
<style scoped lang="scss">
.light {
  width: 40px;
  height: 40px;
  cursor: pointer;
  &.small {
  width: 30px;
  height: 30px;
  }
  &.zm_on {
    background: url('/image/zm_on.png') no-repeat center;
    background-size: contain;
  }

  &.zm_off {
    background: url('/image/zm_off.png') no-repeat center;
    background-size: contain;
  }
}

.detail_fields {
  &>div {
    flex: 1;
    color: #ffffffb0;
  }
}
</style>
