<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.base.mapper.DbMigrateMapper">

    <update id="addDeviceAjustablePower">
        ALTER TABLE `d_device` ADD COLUMN `ajustable_power` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '可调功率 单位kW（需求侧响应计算）';
    </update>

    <delete id="removeDuplicateDictData">
        delete FROM sys_dict_data
        WHERE dict_code NOT IN (
            SELECT dict_code FROM (
                SELECT max(dict_code) AS dict_code
                FROM sys_dict_data
                GROUP BY dict_label, dict_type
            ) AS temp
        );
    </delete>

    <update id="addDictDataUniqueIndex">
        ALTER TABLE `sys_dict_data` ADD UNIQUE INDEX `l_t_u`(`dict_label`, `dict_type`);
    </update>

    <update id="genEnergyDiffAvgHourlyView">
        CREATE VIEW `v_e_device_diff_indication_avg_hourly` AS
        SELECT
            `h`.`id` AS `id`,
            `h`.`device_id` AS `deviceId`,
            sum( `h`.`diff_indication` ) AS `sum(diff_indication)`,
            count( `h`.`device_id` ) AS `count(device_id)`,
            (sum( `h`.`diff_indication` ) / count(date_format( `h`.`recorded_at`, '%w %H' ))) AS `avgDiffIndication`,
            date_format( `h`.`recorded_at`, '%w' ) AS `weekDay`,
            date_format( `h`.`recorded_at`, '%H' ) AS `dayHour`
        FROM `e_energy_data_history` `h`
        GROUP BY `h`.`device_id`, date_format( `h`.`recorded_at`, '%w %H')
    </update>

    <update id="addEDeviceRemark">
        ALTER TABLE `e_device` ADD COLUMN `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '设备种类标记，在d_device_type对应';
    </update>
    <update id="addEDeviceGroupId">
        ALTER TABLE `e_device` ADD COLUMN `group_id` int NULL DEFAULT NULL COMMENT '分组ID, 同一块表，分尖峰平谷';
    </update>
    <update id="addEDeviceMaxDiff">
        ALTER TABLE `e_device` ADD COLUMN `max_diff` decimal(20,0) NULL DEFAULT NULL COMMENT '过滤设置，超过max的为脏数据';
    </update>
    <update id="addEDeviceUnreal">
        ALTER TABLE `e_device` ADD COLUMN `unreal` tinyint NULL DEFAULT NULL COMMENT '是否为虚拟表';
    </update>

    <update id="genBuildingUserMap">
        CREATE TABLE `a_building_user_map`  (
            `id` int(0) NOT NULL AUTO_INCREMENT ,
            `building_id` int(0) NULL DEFAULT NULL,
            `user_id` int(0) NULL DEFAULT NULL,
            `permission` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;
    </update>

    <insert id="addBuildingUserMapData">
        INSERT INTO a_building_user_map (user_id, building_id)
        SELECT u.user_id, b.id
        FROM sys_user u
        JOIN a_building b
    </insert>

    <update id="updateDeviceDataHistoryIndication">
        -- 设备数据历史 indication 长度增加
        ALTER TABLE `d_device_data_history` MODIFY COLUMN `indication` decimal(20, 4) NULL DEFAULT NULL;
    </update>

    <update id="addDeviceCameraId">
        -- 设备增加关联视频字段
        ALTER TABLE `d_device` ADD COLUMN `camera_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '监控ID';
        -- ALTER IGNORE TABLE `d_device` ADD COLUMN `camera_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '监控ID';
    </update>
    <update id="addDeviceFiles">
        ALTER TABLE `d_device` ADD COLUMN `files` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件列表';
    </update>

    <update id="addDeviceOperLogIndex">
        -- 增加日志索引
        ALTER TABLE `d_device_oper_log` ADD INDEX `itm`(`oper_time`), ADD INDEX `idid`(`device_id`), ADD INDEX `itt`(`title`);
    </update>


    <update id="addDeviceMaintenanceWarningId">
        -- 增加运维报警关联
        ALTER TABLE `d_device_maintenance` ADD COLUMN `warning_id` int NULL DEFAULT NULL COMMENT '报警ID' AFTER `device_id`;
    </update>

    <update id="addMaxDiffToDeviceEnergy">
        -- 增加能耗脏数据值设置，diff 超过此值则认为是脏数据
        ALTER TABLE `e_device` ADD COLUMN `max_diff` decimal(20, 0) NULL DEFAULT NULL COMMENT '过滤设置，超过max的为脏数据' AFTER `group_id`;
    </update>


    <update id="checkAndCreateDeviceRepairOrder">
        CREATE TABLE IF NOT EXISTS `d_repair_order`  (
           `id` int NOT NULL AUTO_INCREMENT COMMENT '主键编号(自增)',
           `order_id` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报修单编号',
           `content` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '问题描述',
           `address` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '故障地址',
           `device_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备编号',
           `building` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备楼栋',
           `floor_level` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备楼层',
           `files` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '上传文件列表',
           `d_user_id` int NULL DEFAULT NULL COMMENT '报修人',
           `apply_by` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报修人姓名',
           `repair_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报修类型 电气维修、水管维修、墙面维修、其他维修',
           `company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报修人公司',
           `operator` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作员',
           `status` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行状态 new;doing;finished',
           `mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
           `priority` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '期望程度:紧急维修、正常维修',
           `finished_at` datetime NULL DEFAULT NULL COMMENT '结束时间',
           `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
           `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
           PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;
    </update>

    <update id="checkAndCreateRepairMaintenanceMap">
        CREATE TABLE IF NOT EXISTS `d_repair_maintenance_map`  (
             `id` int NOT NULL AUTO_INCREMENT,
             `repair_id` int NULL DEFAULT NULL,
             `maintenance_id` int NULL DEFAULT NULL,
             PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;
    </update>

    <update id="checkAndCreateDeviceEvaluate">
        -- 增加报修评价表
        CREATE TABLE IF NOT EXISTS `d_evaluate`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `order_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报修单编号',
            `content` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '评价内容',
            `score` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '满意度评分',
            `files` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '相关文件',
            `created_at` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
            `updated_at` datetime NULL DEFAULT NULL,
            PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;
    </update>
    <update id="genWeatherData">
        CREATE TABLE `a_weather_data`  (
           `id` bigint(0) NOT NULL AUTO_INCREMENT,
           `weather_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
           `condition` char(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
           `condition_id` int(0) NULL DEFAULT NULL,
           `icon` int(0) NULL DEFAULT NULL,
           `humidity` int(0) NULL DEFAULT NULL,
           `real_feel` int(0) NULL DEFAULT NULL,
           `sun_rise` timestamp(0) NULL DEFAULT NULL,
           `sun_set` timestamp(0) NULL DEFAULT NULL,
           `temp` int(0) NULL DEFAULT NULL,
           `uvi` int(0) NULL DEFAULT NULL,
           `timestamp` timestamp(0) NULL DEFAULT NULL,
           `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
           `update_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
           PRIMARY KEY (`id`) USING BTREE,
           INDEX `weather_id`(`weather_id`) USING BTREE,
           INDEX `i_updatetime`(`update_time`) USING BTREE,
           INDEX `idx_updatetime_weatherid`(`update_time`, `weather_id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;
    </update>
    <update id="changeCityId">
        ALTER TABLE `a_weather_data` CHANGE COLUMN `city_id` `weather_id` varchar(255) NOT NULL;
    </update>

    <update id="changeWeatherIdType">
        ALTER TABLE `a_weather_data` MODIFY COLUMN `weather_id` varchar(255) NOT NULL;
    </update>

    <update id="changeWeatherCreateTime">
        ALTER TABLE `a_weather_data` CHANGE COLUMN `created_at` `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP;
    </update>

    <update id="changeWeatherUpdateTime">
        ALTER TABLE `a_weather_data` CHANGE COLUMN `updated_at` `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP;
    </update>

    <update id="updateRepairMaintenanceMapUnique">
        ALTER TABLE `d_repair_maintenance_map` ADD UNIQUE INDEX `uid`(`repair_id`, `maintenance_id`);
    </update>

    <update id="updateEnergyWarningOperator">
        ALTER TABLE `e_warning` MODIFY COLUMN `operator` int NULL COMMENT '操作员ID';
    </update>
    <update id="genEenergyWarningRule">
        CREATE TABLE `e_warning_rule`  (
            `id` int(0) NOT NULL AUTO_INCREMENT,
            `building_id` int(0) NOT NULL COMMENT '建筑ID',
            `group_id` int(0) NULL DEFAULT NULL COMMENT '用能分组ID',
            `device_id` int(0) NULL DEFAULT NULL COMMENT '能耗设备ID',
            `type` int(0) NOT NULL COMMENT '告警规则 1: 小时用量过高; 2: 日用量过高; 3: 月用量过高;',
            `val` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '比较直',
            `severity` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '五级' COMMENT '严重等级',
            `err_msg` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报警信息',
            `solution_ref` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '解决方式',
            `tag` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标签',
            `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            `updated_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
            `mail_to` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送邮件给， 逗号分割多个',
            `sms_to` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送短信给， 逗号分割多个手机号',
            `sms_template` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '短信模板',
            `warn_start` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '00:00:00' COMMENT '报警开始检查 00:00:00',
            `warn_end` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '23:59:59' COMMENT '报警结束检查 23:59:59',
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用能报警规则' ROW_FORMAT = Dynamic;
    </update>
    <update id="updateSysOperLogLen">
        ALTER TABLE `sys_oper_log` MODIFY COLUMN `json_result` longtext NULL COMMENT '返回参数';
    </update>

    <insert id="addDocument">
        CREATE TABLE IF NOT EXISTS `a_document`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `oid` int NULL DEFAULT 0 COMMENT '显示顺序',
            `group_id` int NOT NULL COMMENT '分组ID',
            `name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
            `file_list` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件地址',
            `note` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '备注信息',
            `create_by` int NULL DEFAULT NULL,
            `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;
    </insert>

    <insert id="addDocumentGroup">
        CREATE TABLE IF NOT EXISTS `a_document_group`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `parent_id` int NULL DEFAULT 0 COMMENT '父ID',
            `ancestors` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '祖籍列表',
            `oid` int NULL DEFAULT 0 COMMENT '序号',
            `name` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分组名',
            `status` int NULL DEFAULT 0 COMMENT '状态（0正常 1停用）',
            `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;
    </insert>
    <insert id="addDocumentGroupData">
        INSERT INTO `a_document_group`
            (id, parent_id, ancestors, oid, name, status, created_at, updated_at)
        VALUES
            (1, 0, '0', 0, '使用说明书', 0, '2024-08-04 11:46:01', '2024-08-04 11:46:01'),
            (2, 0, '0', 0, '操作手册', 0, '2024-08-04 11:46:14', '2024-08-04 11:46:14'),
            (3, 0, '0', 0, '维护说明书', 0, '2024-08-04 11:46:46', '2024-08-04 11:46:46'),
            (4, 0, '0', 0, '巡检说明书', 0, '2024-08-04 11:46:46', '2024-08-04 11:46:46'),
            (5, 0, '0', 0, '紧急操作说明', 0, '2024-08-04 11:46:46', '2024-08-04 11:46:46'),
            (6, 0, '0', 0, '采购合同', 0, '2024-08-04 11:46:46', '2024-08-04 11:46:46'),
            (7, 0, '0', 0, '其他资料', 0, '2024-08-04 11:46:46', '2024-08-04 11:46:46');
    </insert>

    <insert id="addPiPoint">
        CREATE TABLE `pi_point`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '点名称',
            `type` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '点类型',
            `position` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '点位置',
            `device_id` int NULL DEFAULT NULL COMMENT '设备ID',
            `note` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注信息',
            `building_id` int NULL DEFAULT NULL COMMENT '建筑ID',
            `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
    </insert>
    <insert id="addDictTypeForPiPoint">
        INSERT INTO `sys_dict_type`
            (`dict_name`, `dict_type`, `status`, `create_by`, `update_by`, `update_time`, `remark`)
        VALUES
            ('巡检点类型', 'inspection_point_type', '0', 'admin', '', NULL, NULL);
    </insert>
    <insert id="addDictDataForPiPoint">
        INSERT INTO `sys_dict_data`
            (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
        VALUES
            (0, '设备巡检', 'device', 'inspection_point_type', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL),
            (0, '环境巡检', 'env', 'inspection_point_type', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL),
            (0, '安全巡检', 'security', 'inspection_point_type', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL);
    </insert>

    <insert id="addPiRoute">
        CREATE TABLE `pi_route`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `oid` int NOT NULL DEFAULT 0 COMMENT '排序',
            `name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '线路名称',
            `note` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注信息',
            `building_id` int NULL DEFAULT NULL COMMENT '建筑ID',
            `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
    </insert>

    <insert id="addPiRoutePointMap">
        CREATE TABLE `pi_route_point_map`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `route_id` int NULL DEFAULT NULL COMMENT '线路ID',
            `point_id` int NULL DEFAULT NULL COMMENT '巡检点ID',
            `oid` int NOT NULL DEFAULT 0 COMMENT '排序序号',
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
    </insert>

    <insert id="addPiPlan">
        CREATE TABLE `pi_plan`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '计划名称',
            `route_id` int NOT NULL COMMENT '巡检线路Id',
            `cycle_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '巡检周期. monthDay:按日;weekDay:按周',
            `cycle_data` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '巡检周期数据 json',
            `start_hour` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '巡检任务开始时间',
            `start_time` datetime NULL DEFAULT NULL COMMENT '生效日期',
            `end_time` datetime NULL DEFAULT NULL COMMENT '失效日期',
            `executors` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '执行员Ids',
            `execute_type` int NULL DEFAULT NULL COMMENT '执行方式。 0:所有人;1:随机人;2:当班人;',
            `note` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注信息',
            `building_id` int NULL DEFAULT NULL COMMENT '建筑ID',
            `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
    </insert>
    <insert id="addDictTypeForPiPointCycleType">
        INSERT INTO `sys_dict_type`
            (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
        VALUES
            ('巡检周期类型', 'inspection_cycle_type', '0', 'admin', NOW(), '', NULL, NULL);
    </insert>
    <insert id="addDictDataForPiPointCycleType">
        INSERT INTO `sys_dict_data`
            (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
        VALUES
            (0, '所有人', '0', 'inspection_task_distribute_type', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL),
            (0, '第一人', '1', 'inspection_task_distribute_type', NULL, NULL, 'N', '0', 'admin', '2024-08-08 00:04:22', '', NULL, NULL),
            (0, '当班人', '2', 'inspection_task_distribute_type', NULL, NULL, 'N', '0', 'admin', '2024-08-08 00:04:31', '', NULL, NULL);
    </insert>
    <insert id="addDictTypeForPiPointTaskDistributeType">
        INSERT INTO `sys_dict_type`
            (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
        VALUES
            ('巡检任务分配方式', 'inspection_task_distribute_type', '0', 'admin', NOW(), '', NULL, NULL);
    </insert>
    <insert id="addDictDataForPiPointTaskDistributeType">
        INSERT INTO `sys_dict_data`
            (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
        VALUES
            (0, '按周', 'weekDay', 'inspection_cycle_type', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL),
            (0, '按月', 'monthDay', 'inspection_cycle_type', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL);
    </insert>

    <insert id="addPiTask">
        CREATE TABLE `pi_task`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `plan_id` int NULL DEFAULT NULL COMMENT '计划Id',
            `executor` int NULL DEFAULT NULL COMMENT '计划执行人',
            `executor_real` int NULL DEFAULT NULL COMMENT '实际执行人',
            `start_time` datetime NULL DEFAULT NULL COMMENT '开始时间',
            `end_time` datetime NULL DEFAULT NULL,
            `status` int NULL DEFAULT 0 COMMENT '任务状态. 0:未开始;1:执行中;2:已完成;3:已忽略',
            `status_result` int NULL DEFAULT NULL COMMENT '执行结果状态. 0:正常;1:异常;',
            `note` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注信息',
            `building_id` int NULL DEFAULT NULL COMMENT '建筑ID',
            `create_by` int NULL DEFAULT NULL COMMENT '创建人. 空为系统创建',
            `created_at` datetime NULL DEFAULT NULL,
            `updated_at` datetime NULL DEFAULT NULL,
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
    </insert>
    <insert id="addDictTypeForPiTaskStatus">
        INSERT INTO `sys_dict_type`
            (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
        VALUES
            ('巡检任务状态', 'inspection_task_status', '0', 'admin', NOW(), '', NULL, NULL);
    </insert>
    <insert id="addDictDataForPiTaskStatus">
        INSERT INTO `sys_dict_data`
            (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
        VALUES
            (0, '未开始', '0', 'inspection_task_status', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL),
            (0, '执行中', '1', 'inspection_task_status', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL),
            (0, '已完成', '2', 'inspection_task_status', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL),
            (0, '已忽略', '3', 'inspection_task_status', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL);
    </insert>

    <insert id="addPiTaskDetail">
        CREATE TABLE `pi_task_detail`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `task_id` int NULL DEFAULT NULL COMMENT '任务Id',
            `point_id` int NULL DEFAULT NULL COMMENT '巡检点Id',
            `maintenance_id` int NULL DEFAULT NULL COMMENT '运维单Id',
            `executor` int NULL DEFAULT NULL COMMENT '执行人',
            `deal_time` datetime NULL DEFAULT NULL COMMENT '执行时间',
            `status` int NULL DEFAULT 0 COMMENT '巡检结果. 0:正常;1:异常;',
            `note` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注信息',
            `file_list` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '相关文件',
            `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
    </insert>
    <insert id="addDictTypeForCheckedStatus">
        INSERT INTO `sys_dict_type`
            (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
        VALUES
            ('巡检检查结果状态', 'inspection_checked_status', '0', 'admin', '2024-08-08 00:14:39', '', NULL, NULL);
    </insert>
    <insert id="addDictDataForCheckedStatus">
        INSERT INTO `sys_dict_data`
            (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
        VALUES (0, '正常', '0', 'inspection_checked_status', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL),
               (0, '异常', '1', 'inspection_checked_status', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL);
    </insert>

    <insert id="addPiShiftSchedule">
        CREATE TABLE `pi_shift_schedule`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `user_id` int NULL DEFAULT NULL,
            `shift_type` int NULL DEFAULT NULL COMMENT '班次类型',
            `start_time` datetime NULL DEFAULT NULL COMMENT '当班开始时间',
            `end_time` datetime NULL DEFAULT NULL COMMENT '当班结束时间',
            `note` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注信息',
            `building_id` int NULL DEFAULT NULL COMMENT '建筑ID',
            `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
    </insert>
    <insert id="addDictTypeForShiftScheduleType">
        INSERT INTO `sys_dict_type`
            (`dict_name`, `dict_type`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
        VALUES
            ('排班班次类型', 'shift_schedule_type', '0', 'admin', NOW(), '', NULL, NULL);
    </insert>
    <insert id="addDictDataForShiftScheduleType">
        INSERT INTO `sys_dict_data`
            (`dict_sort`, `dict_label`, `dict_value`, `dict_type`, `css_class`, `list_class`, `is_default`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
        VALUES
            (0, '白班',  '{\"startTime\":\"08:00\",\"endTime\":\"17:00\"}', 'shift_schedule_type', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL),
            (0, '中班',  '{\"startTime\":\"17:00\",\"endTime\":\"23:00\"}', 'shift_schedule_type', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL),
            (0, '夜班',  '{\"startTime\":\"23:00\",\"endTime\":\"08:00\"}', 'shift_schedule_type', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL),
            (0, '大白班', '{\"startTime\":\"08:00\",\"endTime\":\"20:00\"}', 'shift_schedule_type', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL),
            (0, '大夜班', '{\"startTime\":\"20:00\",\"endTime\":\"08:00\"}', 'shift_schedule_type', NULL, NULL, 'N', '0', 'admin', NOW(), '', NULL, NULL);
    </insert>

    <update id="addAItemAlias">
        ALTER TABLE `a_item` ADD COLUMN `alias` varchar(255) NULL COMMENT '别名' AFTER `name`;
    </update>
    <update id="addEEnergyDataHistoryDiffFee">
        ALTER TABLE `e_energy_data_history` ADD COLUMN `diff_fee` decimal(20,3) NULL COMMENT '费用' AFTER `diff_indication`;
    </update>
    <update id="addEFeePolicyDate">
        ALTER TABLE `e_fee_policy` ADD COLUMN `date` varchar(20) NULL COMMENT '影响日期' AFTER `unit`;
    </update>
    <update id="addEEnergyDataHistoryDailyJFPG">
        ALTER TABLE `e_energy_data_history_daily`
            ADD COLUMN `total` decimal(20, 3) NULL DEFAULT 0 AFTER `other_data`,
            ADD COLUMN `totalFee` decimal(20, 3) NULL DEFAULT 0 AFTER `total`,
            ADD COLUMN `j` decimal(20, 3) NULL DEFAULT 0 AFTER `totalFee`,
            ADD COLUMN `jFee` decimal(20, 3) NULL DEFAULT 0 AFTER `j`,
            ADD COLUMN `f` decimal(20, 3) NULL DEFAULT 0 AFTER `jFee`,
            ADD COLUMN `fFee` decimal(20, 3) NULL DEFAULT 0 AFTER `f`,
            ADD COLUMN `p` decimal(20, 3) NULL DEFAULT 0 AFTER `fFee`,
            ADD COLUMN `pFee` decimal(20, 3) NULL DEFAULT 0 AFTER `p`,
            ADD COLUMN `g` decimal(20, 3) NULL DEFAULT 0 AFTER `pFee`,
            ADD COLUMN `gFee` decimal(20, 3) NULL DEFAULT 0 AFTER `g`;
    </update>

    <update id="addDeviceTaskGroupTag">
        ALTER TABLE `d_device_task_group`
            ADD COLUMN `tag` varchar(255) NULL COMMENT '标签(可用来分组)' AFTER `note`;
    </update>

    <update id="addTplDevice">
        CREATE TABLE `a_tpl_device`  (
            `id` int(0) NOT NULL AUTO_INCREMENT COMMENT '模板编号',
            `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '模板名称',
            `collector_id` bigint(0) NULL DEFAULT NULL COMMENT '所属采集器ID',
            `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备名称',
            `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
            `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '设备类型',
            `brand` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌',
            `model` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '型号',
            `note` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
            `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小图标',
            `thumb` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缩略图',
            `building_id` int(0) NULL DEFAULT NULL COMMENT '建筑ID',
            `resource_id` int(0) NULL DEFAULT NULL COMMENT '设备详情图ID',
            `files` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件列表',
            `service_life` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '使用寿命(年)',
            `energy_lever` int(0) NOT NULL DEFAULT 0 COMMENT '能耗等级，越低，越优先关闭',
            `rated_power` decimal(20, 4) NULL DEFAULT NULL COMMENT '额定功率 单位kW（关闭前计算）',
            `ajustable_power` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '可调功率 单位kW（需求侧响应计算）',
            `is_energy` int(0) NULL DEFAULT NULL COMMENT '是否为能耗设备',
            `energy_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '能耗类型',
            `create_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
            `status` int(0) NULL DEFAULT NULL COMMENT '状态(0:不可用，1:可用)',
            `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
    </update>
    <update id="addTplDeviceData">
        CREATE TABLE `a_tpl_device_data`  (
            `id` int(0) NOT NULL AUTO_INCREMENT,
            `template_id` int(0) NOT NULL COMMENT '模板关联ID',
            `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据点名',
            `alias` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据点别名',
            `data_type` enum('int','float','string','boolean') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'string' COMMENT '数据类型',
            `data_unit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据单位',
            `note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
            `func` enum('read','write') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'read' COMMENT '读写标识',
            `type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'r' COMMENT '读r;写w;计算c;',
            `oid` int(0) NOT NULL DEFAULT 0 COMMENT '排序',
            `tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据标签, 包含status值的记录可以反馈设备d当前的运行状态',
            `is_primary_energy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否为主要能耗',
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
    </update>
    <update id="addTplDeviceDataWarning">
        CREATE TABLE `a_tpl_device_data_warning`  (
            `id` int(0) NOT NULL AUTO_INCREMENT,
            `device_data_id` int(0) NULL DEFAULT NULL COMMENT '数据点ID',
            `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报警名称',
            `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报警描述',
            `val` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '阈值',
            `compare` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '比较方式',
            `severity` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '一级等级最高；四级等级最低 \'一级\',\'二级\',\'三级\',\'四级\',\'五级\',\'故障\'',
            `err_msg` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '错误信息',
            `solution_ref` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '解决办法',
            `tag` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
            `time_span` int(0) NULL DEFAULT NULL,
            `func` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
            `mail_to` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送邮件给',
            `sms_to` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送短信给',
            `sms_template` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '短信模板',
            `warn_start` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报警开始检查 00:00:00',
            `warn_end` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '报警结束检查 23:59:59',
            PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;
    </update>

    <update id="addDeviceShelfTime">
        ALTER TABLE `d_device`
            ADD COLUMN `shelf_time` varchar(255) NULL COMMENT '质保时间';
    </update>

    <update id="updateSysUserName">
        ALTER TABLE sys_user
            MODIFY COLUMN `user_name` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户账号';
    </update>

    <update id="updateDDeviceSourceId">
        ALTER TABLE `d_device`
            MODIFY COLUMN `source_id` char(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '来源';
    </update>

    <update id="createDeviceMaintenanceConsumable">
        CREATE TABLE `d_device_maintenance_consumable` (
            `id` int NOT NULL AUTO_INCREMENT,
            `category` varchar(255) DEFAULT NULL COMMENT '种类',
            `name` varchar(255) DEFAULT NULL COMMENT '名称',
            `specification` varchar(255) DEFAULT NULL COMMENT '规格型号',
            `unit` varchar(255) DEFAULT NULL COMMENT '单位',
            `quantity` int DEFAULT NULL COMMENT '数量',
            `note` text COMMENT '备注',
            `maintenance_id` int DEFAULT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci;
    </update>

    <update id="createDeviceEnergySynchronized">
        -- 能耗历史补充临时记录表(用来修复能耗旧数据用)
        CREATE TABLE `e_device_synchronized_data` (
          `id` int NOT NULL AUTO_INCREMENT,
          `item_id` int DEFAULT NULL COMMENT '设备Id',
          `item_data_id` int DEFAULT NULL COMMENT '数据Id',
          `val` varchar(255) DEFAULT NULL,
          `other_data` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '完整数据记录',
          `caculated` tinyint DEFAULT '0' COMMENT '是否已经同步过; 0:未同步;1:已同步',
          `recorded_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '数据采集时间',
          `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
          `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          UNIQUE KEY `u_id_val_time` (`item_data_id`,`val`,`recorded_at`),
          KEY `i_did` (`item_data_id`),
          KEY `i_t` (`recorded_at`),
          KEY `i_c` (`caculated`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci;
    </update>

    <update id="addDeviceMaintenancePlantId">
        ALTER TABLE `d_device_maintenance`
            ADD COLUMN `plan_id` int NULL DEFAULT NULL COMMENT '保养计划ID' AFTER `warning_id`;
    </update>

    <update id="createDeviceMaintenancePlan">
        -- 维保计划，一个设备可以设置多个计划，并通过计划，生成运维日常维护工单
        CREATE TABLE `d_device_maintenance_plan` (
            `id` int NOT NULL AUTO_INCREMENT COMMENT 'ID',
            `device_id` int DEFAULT NULL COMMENT '设备ID',
            `content` varchar(5000) DEFAULT NULL COMMENT '维保内容',
            `period` int DEFAULT NULL COMMENT '保养周期，单位天',
            `operator` int DEFAULT NULL COMMENT '责任人，如果填了，自动填充维保单人',
            `status` enum('Y','N') DEFAULT 'Y' COMMENT '是否有效',
            `files` varchar(5000) DEFAULT NULL COMMENT '相关附件',
            `note` varchar(5000) DEFAULT NULL COMMENT '备注信息',
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    </update>

    <update id="addDDeviceDataHistoryInd">
        ALTER TABLE `d_device_data_history`
            ADD INDEX `_itid_rd`(`item_data_id` ASC, `recorded_at` ASC) USING BTREE;
    </update>

    <update id="createEnergyConsumptionPrediction">
        CREATE TABLE `e_energy_consumption_prediction` (
            `id` int NOT NULL AUTO_INCREMENT,
            `building_id` int DEFAULT NULL,
            `group_id` int DEFAULT NULL,
            `device_id` int DEFAULT NULL,
            `month` int DEFAULT NULL,
            `day` int DEFAULT NULL,
            `hour` int DEFAULT NULL,
            `temperature` decimal(20,2) DEFAULT NULL,
            `humidity` int DEFAULT NULL,
            `real_feel` int DEFAULT NULL,
            `energy_consumption` decimal(20,2) DEFAULT NULL COMMENT '实际能耗',
            `energy_prediction` decimal(20,2) DEFAULT NULL COMMENT '预测能耗',
            `recorded_at` datetime DEFAULT NULL COMMENT '实际发生时间',
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    </update>

    <update id="addAItemWarningDataId">
        ALTER TABLE `a_item_warning`
            ADD COLUMN `item_data_id` int NULL DEFAULT NULL COMMENT '数据点ID' AFTER `item_id`;
    </update>

    <update id="addDeviceMaintenanceInds">
        ALTER TABLE `d_device_maintenance`
            ADD INDEX `idid`(`device_id`),
            ADD INDEX `iwid`(`warning_id`),
            ADD INDEX `ipid`(`plan_id`),
            ADD INDEX `itp`(`type`),
            ADD INDEX `ist`(`status`);
    </update>

    <update id="addDeviceMaintenanceIdAuto">
        ALTER TABLE `d_device_maintenance`
            MODIFY COLUMN `id` bigint NOT NULL AUTO_INCREMENT;
    </update>

    <update id="addItemAlias">
        ALTER TABLE `a_item`
            ADD COLUMN `alias` varchar(200) DEFAULT NULL COMMENT '别名' AFTER `name`;
    </update>
    <update id="addItemDataAlias">
        ALTER TABLE `a_item_data`
            ADD COLUMN `alias` varchar(200) DEFAULT NULL COMMENT '别名' AFTER `name`;
    </update>
    <update id="addDeviceItemDataMapAlias">
        ALTER TABLE `d_device_item_data_map`
            ADD COLUMN `alias` varchar(200) DEFAULT NULL COMMENT '别名' AFTER `name`;
    </update>

    <delete id="deleteItemWarningRuleView">
        DROP VIEW IF EXISTS v_a_item_warning_rule_map;
    </delete>
    <update id="addItemWarningRuleView">
        CREATE VIEW v_a_item_warning_rule_map AS
        select
            rm.id as rmId,
            rm.rule_id as rmRuleId,
            rm.item_id as rmItemId,
            wr.auto_flag as rmAutoFlag,
            wr.id as ruleId,
            wr.name as ruleName,
            wr.description as ruleDesc,
            wr.key as ruleKey,
            wr.val as ruleVal,
            wr.compare as ruleCompare,
            wr.severity as ruleSecerity,
            wr.err_msg as ruleErrMsg,
            wr.solution_ref as ruleSolutionRef,
            t.id as itemId,
            t.name as itemName,
            td.id as itemDataId,
            td.name as itemDataName,
            td.val as dataVal,
            td.updated_at as dataUpdatedAt
        from a_item_rule_map rm
            left join a_warning_rule wr on wr.id = rm.rule_id
            LEFT JOIN a_item t on t.id = rm.item_id
            LEFT JOIN a_item_data td on t.id = td.item_id and wr.key = td.name
        -- 正确的报警规则（绑定设备且数据点匹配）
        -- where wr.id is not null and t.id is not null and td.id is not null
    </update>

    <update id="addDeviceItemDataView">
        CREATE VIEW v_d_device_item_data AS
        SELECT
            dm.id as dmId,
            dm.device_id as dmDeviceId,
            dm.item_data_id as dmItemDataId,
            d.id as deviceId,
            d.type as deviceType,
            d.name as deviceName,
            d.description as deviceDescription,
            d.contact as deviceContact,
            d.mobile as deviceMobile,
            d.company as deviceCompany,
            d.maintenance as deviceMaintenance,
            d.position as devicePosition,
            d.icon as deviceIcon,
            d.warning_status as warningStatus,
            d.building_id as buildingId,
            ar.name as deviceLocation,
            dd.id as itemDataId,
            dd.name as itemDataName,
            dd.alias as itemDataAlias,
            dd.val as itemDataVal,
            dd.data_unit as itemDataUnit,
            dd.updated_at as itemDataUpdatedAt
        from d_device_item_data_map dm
            LEFT JOIN d_device d on dm.device_id = d.id
            LEFT JOIN d_device_resource_map drm on d.id = drm.device_id
            LEFT JOIN a_resource ar on drm.resource_id = ar.id
            LEFT JOIN a_item_data dd on dd.id = dm.item_data_id
        where d.id is not null -- 有效的设备
            and dd.id is not null -- 有效的数据点
        GROUP BY d.id, dm.item_data_id
    </update>

    <delete id="deleteValidDeviceWarningView">
        DROP VIEW IF EXISTS v_valid_device_warning;
    </delete>
    <update id="addValidDeviceWarningView">
        -- 有效的设备报警记录
        CREATE VIEW v_valid_device_warning AS
        SELECT
            w.id,
            w.item_id as itemId,
            ifnull(w.item_data_id, r.itemDataId) itemDataId,
            d.deviceId,
            w.warning_category as warningCategory,
            w.severity,
            w.compare,
            w.err_msg as errMsg,
            w.solution_ref as solutionRef,
            w.has_fixed as hasFixed,
            w.operator,
            w.flow_status as flowStatus,
            w.note,
            w.reported_at as reportedAt,
            w.fixed_at as fixedAt,
            w.created_at as createdAt,
            w.updated_at as updatedAt,
            r.ruleVal,
            r.ruleCompare,
            r.rmAutoFlag,
            d.deviceType,
            d.deviceName,
            d.deviceDescription,
            d.deviceContact,
            d.deviceMobile,
            d.deviceCompany,
            d.deviceMaintenance,
            d.devicePosition,
            d.deviceIcon,
            d.warningStatus,
            d.buildingId,
            d.deviceLocation,
            d.itemDataVal,
            d.itemDataUnit,
            d.itemDataUpdatedAt
        from a_item_warning w
        left join v_a_item_warning_rule_map r on w.item_id = r.itemId and w.warning_category = r.ruleName
        left join v_d_device_item_data d on d.itemDataId = r.itemDataId
        where
            -- 正确的报警规则（绑定设备且数据点匹配）
            r.ruleId is not null
            and r.itemId is not null
            -- 有数据点的设备
            and d.deviceId IS NOT NULL
        group by w.id
    </update>

    <update id="addAItemWarningInds">
        ALTER TABLE `a_item_warning`
            ADD INDEX `irp`(`reported_at`),
            ADD INDEX `ihf`(`has_fixed`),
            ADD INDEX `ifs`(`flow_status`),
            ADD INDEX `iuid`(`operator`),
            ADD INDEX `ise`(`severity`),
            ADD INDEX `iwc`(`warning_category`),
            ADD INDEX `iitid`(`item_id`),
            ADD INDEX `idid`(`item_data_id`),
            ADD INDEX `ifd`(`fixed_at`);
    </update>

    <update id="addDDeviceMaintenanceInds">
        ALTER TABLE `d_device_maintenance`
            ADD INDEX `idid`(`device_id`),
            ADD INDEX `iwid`(`warning_id`),
            ADD INDEX `ipid`(`plan_id`),
            ADD INDEX `itp`(`type`),
            ADD INDEX `ist`(`status`);
    </update>
    <update id="updateDeviceMaintenanceType">
        ALTER TABLE `d_device_maintenance`
            MODIFY COLUMN `type` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '日常维护' COMMENT '维保分类'
    </update>
    <update id="updateUserBuildingMapId">
        ALTER TABLE `a_building_user_map`
            MODIFY COLUMN `id` int NOT NULL AUTO_INCREMENT FIRST;
    </update>
    <update id="addDRepairOrderSUserId">
        ALTER TABLE `d_repair_order`
            ADD COLUMN `s_user_id` varchar(100) DEFAULT NULL COMMENT '保修人(系统用户ID)' AFTER `d_user_id`;
    </update>
    <update id="addAWarningRuleAutoFlag">
        ALTER TABLE `a_warning_rule`
            ADD COLUMN `auto_flag` int NULL DEFAULT 0 COMMENT '是否自动转工单标记 1:自动' AFTER `tag`;
    </update>
    <update id="addDDeviceMaintenanceBuildingId">
        ALTER TABLE `d_device_maintenance`
            ADD COLUMN `building_id` int NULL DEFAULT 1 COMMENT '建筑ID' AFTER `id`;
    </update>
    <update id="addDDeviceTaskGroupBuildingId">
        ALTER TABLE `d_device_task_group`
            ADD COLUMN `building_id` int NULL DEFAULT 1 COMMENT '建筑ID' AFTER `id`;
    </update>
    <update id="addDRepairOrderBuildingId">
        ALTER TABLE `d_repair_order`
            ADD COLUMN `building_id` int NULL DEFAULT 1 COMMENT '建筑ID' AFTER `id`;
    </update>
    <update id="addDUser">
        CREATE TABLE `d_user`  (
           `id` int NOT NULL AUTO_INCREMENT COMMENT '编号',
           `name` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
           `full_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户真实姓名',
           `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电话',
           `role` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户角色(员工、维修员、安保员、管理员)',
           `address` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户地址',
           `status` tinyint NULL DEFAULT 0 COMMENT '用户状态（0正常 1停用）',
           `platform_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户平台来源',
           `open_id` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户在当前开放应用内的唯一标识',
           `union_id` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用于识别用户在多个应用或不同公众号之间的唯一性',
           `profile_photo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户头像',
           `regist_time` datetime NULL DEFAULT NULL COMMENT '注册时间',
           `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
           `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
           PRIMARY KEY (`id`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;
    </update>
    <update id="addDDeviceActivationTime">
        ALTER TABLE `d_device`
            ADD COLUMN `activation_time` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '启用时间' AFTER `status`;
    </update>
    <update id="addDDeviceScrapTime">
        ALTER TABLE `d_device`
            ADD COLUMN `scrap_time` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报废时间' AFTER `activation_time`;
    </update>
    <update id="modifyDDeviceActivationTime">
        ALTER TABLE `d_device`
            MODIFY COLUMN `activation_time` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '启用时间';
    </update>
    <update id="modifyDDeviceScrapTime">
        ALTER TABLE `d_device`
            MODIFY COLUMN `scrap_time` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '报废时间';
    </update>
    <select id="addIotServerMonitor">
        CREATE TABLE `a_iot_server_monitor` (
            `id` int NOT NULL AUTO_INCREMENT,
            `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '监控名',
            `note` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '备注',
            `process_list` json DEFAULT NULL COMMENT '所有进程状态',
            `ip` json DEFAULT NULL COMMENT '本地ip(s)信息JSON',
            `system_info` json DEFAULT NULL COMMENT '软硬件信息 JSON',
            `cpu_usage` json DEFAULT NULL COMMENT 'cpu使用率 JSON',
            `disk_space` json DEFAULT NULL COMMENT '磁盘使用率',
            `disk_io` json DEFAULT NULL COMMENT '磁盘Io',
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `ict` (`created_at`)
        ) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;
    </select>

    <update id="addPiTaskTimes">
        ALTER TABLE `pi_task`
            MODIFY COLUMN `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP,
            MODIFY COLUMN `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;
    </update>

    <update id="addDeviceCommunicateWarning">
        CREATE TABLE `d_device_communicate_warning` (
            `id` int NOT NULL AUTO_INCREMENT,
            `building_id` int DEFAULT NULL,
            `device_id` int DEFAULT NULL,
            `device_type` varchar(150) DEFAULT NULL,
            `warning_category` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'DEVICE-OFFLINE' COMMENT '报警类型',
            `has_fixed` enum('Y','N') CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '是否修复',
            `reported_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '离线时间',
            `fixed_at` datetime DEFAULT NULL COMMENT '修复时间',
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `bid` (`building_id`),
            KEY `did` (`device_id`),
            KEY `dty` (`device_type`),
            KEY `dr` (`reported_at`),
            KEY `df` (`has_fixed`),
            KEY `dfx` (`fixed_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    </update>

    <update id="addAItemDataKeepSeconds">
        ALTER TABLE `a_item_data`
            ADD COLUMN `keep_second` int NULL COMMENT '持续X秒未变化' AFTER `locked`;
    </update>

    <update id="addEDeviceSynchronizedDataCreatedInd">
        ALTER TABLE `e_device_synchronized_data` ADD INDEX `ica`(`created_at`);
    </update>

    <delete id="deleteEDeviceAvgHourlyView">
        DROP VIEW IF EXISTS v_e_device_diff_indication_avg_hourly;
    </delete>
    <update id="addEDeviceAvgHourlyView">
        CREATE VIEW v_e_device_diff_indication_avg_hourly AS
        SELECT
            `h`.`id` AS `id`,
            `h`.`device_id` AS `deviceId`,
            sum( `h`.`diff_indication` ) AS diffIndication,
            count( `h`.`device_id` ) AS deviceCount,
            (sum( `h`.`diff_indication` ) / count( date_format( `h`.`recorded_at`, '%w %H' ))) AS `avgDiffIndication`,
            date_format( `h`.`recorded_at`, '%w' ) AS `weekDay`,
            date_format( `h`.`recorded_at`, '%H' ) AS `dayHour`,
            `h`.`recorded_at` as recordedAt
        FROM
            `e_energy_data_history` `h`
        GROUP BY
            `h`.`device_id`,
            date_format(`h`.`recorded_at`,'%w %H')
    </update>

    <update id="updateDDeviceHistoryData">
        update d_device_data_history set recorded_at=created_at where recorded_at IS NULL;
    </update>

    <update id="addDDeviceDataHistoryPrimaryKeys">
        ALTER TABLE `d_device_data_history`
            MODIFY COLUMN `recorded_at` datetime NOT NULL,
            DROP PRIMARY KEY,
            ADD PRIMARY KEY (`id`, `recorded_at`) USING BTREE;
    </update>

    <select id="checkDDeviceDataHistoryPartitionExists" resultType="int">
        SELECT count(*) FROM information_schema.partitions
        WHERE table_name = 'd_device_data_history'
          and table_schema=#{database}
          AND partition_name IS NOT NULL;
    </select>

    <update id="addDDeviceDataHistoryPartitions">
        ALTER TABLE d_device_data_history
            PARTITION BY RANGE COLUMNS(recorded_at) (
            PARTITION p_before VALUES LESS THAN ('2024-01-01'), -- 2024年以前数据

            PARTITION p202401 VALUES LESS THAN ('2024-02-01'),
            PARTITION p202402 VALUES LESS THAN ('2024-03-01'),
            PARTITION p202403 VALUES LESS THAN ('2024-04-01'),
            PARTITION p202404 VALUES LESS THAN ('2024-05-01'),
            PARTITION p202405 VALUES LESS THAN ('2024-06-01'),
            PARTITION p202406 VALUES LESS THAN ('2024-07-01'),
            PARTITION p202407 VALUES LESS THAN ('2024-08-01'),
            PARTITION p202408 VALUES LESS THAN ('2024-09-01'),
            PARTITION p202409 VALUES LESS THAN ('2024-10-01'),
            PARTITION p202410 VALUES LESS THAN ('2024-11-01'),
            PARTITION p202411 VALUES LESS THAN ('2024-12-01'),
            PARTITION p202412 VALUES LESS THAN ('2025-01-01'),  -- 2025年以前数据

            PARTITION p202501 VALUES LESS THAN ('2025-02-01'),
            PARTITION p202502 VALUES LESS THAN ('2025-03-01'),
            PARTITION p202503 VALUES LESS THAN ('2025-04-01'),
            PARTITION p202504 VALUES LESS THAN ('2025-05-01'),
            PARTITION p202505 VALUES LESS THAN ('2025-06-01'),
            PARTITION p202506 VALUES LESS THAN ('2025-07-01'),
            PARTITION p202507 VALUES LESS THAN ('2025-08-01'),
            PARTITION p202508 VALUES LESS THAN ('2025-09-01'),
            PARTITION p202509 VALUES LESS THAN ('2025-10-01'),
            PARTITION p202510 VALUES LESS THAN ('2025-11-01'),
            PARTITION p202511 VALUES LESS THAN ('2025-12-01'),
            PARTITION p202512 VALUES LESS THAN ('2026-01-01'),  -- 2026年以前数据

            PARTITION p202601 VALUES LESS THAN ('2026-02-01'),
            PARTITION p202602 VALUES LESS THAN ('2026-03-01'),
            PARTITION p202603 VALUES LESS THAN ('2026-04-01'),
            PARTITION p202604 VALUES LESS THAN ('2026-05-01'),
            PARTITION p202605 VALUES LESS THAN ('2026-06-01'),
            PARTITION p202606 VALUES LESS THAN ('2026-07-01'),
            PARTITION p202607 VALUES LESS THAN ('2026-08-01'),
            PARTITION p202608 VALUES LESS THAN ('2026-09-01'),
            PARTITION p202609 VALUES LESS THAN ('2026-10-01'),
            PARTITION p202610 VALUES LESS THAN ('2026-11-01'),
            PARTITION p202611 VALUES LESS THAN ('2026-12-01'),
            PARTITION p202612 VALUES LESS THAN ('2027-01-01'),  -- 2027年以前数据

            PARTITION p202701 VALUES LESS THAN ('2027-02-01'),
            PARTITION p202702 VALUES LESS THAN ('2027-03-01'),
            PARTITION p202703 VALUES LESS THAN ('2027-04-01'),
            PARTITION p202704 VALUES LESS THAN ('2027-05-01'),
            PARTITION p202705 VALUES LESS THAN ('2027-06-01'),
            PARTITION p202706 VALUES LESS THAN ('2027-07-01'),
            PARTITION p202707 VALUES LESS THAN ('2027-08-01'),
            PARTITION p202708 VALUES LESS THAN ('2027-09-01'),
            PARTITION p202709 VALUES LESS THAN ('2027-10-01'),
            PARTITION p202710 VALUES LESS THAN ('2027-11-01'),
            PARTITION p202711 VALUES LESS THAN ('2027-12-01'),
            PARTITION p202712 VALUES LESS THAN ('2028-01-01'),  -- 2028年以前数据

            PARTITION p202801 VALUES LESS THAN ('2028-02-01'),
            PARTITION p202802 VALUES LESS THAN ('2028-03-01'),
            PARTITION p202803 VALUES LESS THAN ('2028-04-01'),
            PARTITION p202804 VALUES LESS THAN ('2028-05-01'),
            PARTITION p202805 VALUES LESS THAN ('2028-06-01'),
            PARTITION p202806 VALUES LESS THAN ('2028-07-01'),
            PARTITION p202807 VALUES LESS THAN ('2028-08-01'),
            PARTITION p202808 VALUES LESS THAN ('2028-09-01'),
            PARTITION p202809 VALUES LESS THAN ('2028-10-01'),
            PARTITION p202810 VALUES LESS THAN ('2028-11-01'),
            PARTITION p202811 VALUES LESS THAN ('2028-12-01'),
            PARTITION p202812 VALUES LESS THAN ('2029-01-01'),  -- 2029年以前数据

            PARTITION p202901 VALUES LESS THAN ('2029-02-01'),
            PARTITION p202902 VALUES LESS THAN ('2029-03-01'),
            PARTITION p202903 VALUES LESS THAN ('2029-04-01'),
            PARTITION p202904 VALUES LESS THAN ('2029-05-01'),
            PARTITION p202905 VALUES LESS THAN ('2029-06-01'),
            PARTITION p202906 VALUES LESS THAN ('2029-07-01'),
            PARTITION p202907 VALUES LESS THAN ('2029-08-01'),
            PARTITION p202908 VALUES LESS THAN ('2029-09-01'),
            PARTITION p202909 VALUES LESS THAN ('2029-10-01'),
            PARTITION p202910 VALUES LESS THAN ('2029-11-01'),
            PARTITION p202911 VALUES LESS THAN ('2029-12-01'),
            PARTITION p202912 VALUES LESS THAN ('2030-01-01'),  -- 2030年以前数据

            PARTITION p203001 VALUES LESS THAN ('2030-02-01'),
            PARTITION p203002 VALUES LESS THAN ('2030-03-01'),
            PARTITION p203003 VALUES LESS THAN ('2030-04-01'),
            PARTITION p203004 VALUES LESS THAN ('2030-05-01'),
            PARTITION p203005 VALUES LESS THAN ('2030-06-01'),
            PARTITION p203006 VALUES LESS THAN ('2030-07-01'),
            PARTITION p203007 VALUES LESS THAN ('2030-08-01'),
            PARTITION p203008 VALUES LESS THAN ('2030-09-01'),
            PARTITION p203009 VALUES LESS THAN ('2030-10-01'),
            PARTITION p203010 VALUES LESS THAN ('2030-11-01'),
            PARTITION p203011 VALUES LESS THAN ('2030-12-01'),
            PARTITION p203012 VALUES LESS THAN ('2031-01-01'),  -- 2031年以前数据

            PARTITION p203101 VALUES LESS THAN ('2031-02-01'),
            PARTITION p203102 VALUES LESS THAN ('2031-03-01'),
            PARTITION p203103 VALUES LESS THAN ('2031-04-01'),
            PARTITION p203104 VALUES LESS THAN ('2031-05-01'),
            PARTITION p203105 VALUES LESS THAN ('2031-06-01'),
            PARTITION p203106 VALUES LESS THAN ('2031-07-01'),
            PARTITION p203107 VALUES LESS THAN ('2031-08-01'),
            PARTITION p203108 VALUES LESS THAN ('2031-09-01'),
            PARTITION p203109 VALUES LESS THAN ('2031-10-01'),
            PARTITION p203110 VALUES LESS THAN ('2031-11-01'),
            PARTITION p203111 VALUES LESS THAN ('2031-12-01'),
            PARTITION p203112 VALUES LESS THAN ('2032-01-01'),  -- 2032年以前数据

            PARTITION p203201 VALUES LESS THAN ('2032-02-01'),
            PARTITION p203202 VALUES LESS THAN ('2032-03-01'),
            PARTITION p203203 VALUES LESS THAN ('2032-04-01'),
            PARTITION p203204 VALUES LESS THAN ('2032-05-01'),
            PARTITION p203205 VALUES LESS THAN ('2032-06-01'),
            PARTITION p203206 VALUES LESS THAN ('2032-07-01'),
            PARTITION p203207 VALUES LESS THAN ('2032-08-01'),
            PARTITION p203208 VALUES LESS THAN ('2032-09-01'),
            PARTITION p203209 VALUES LESS THAN ('2032-10-01'),
            PARTITION p203210 VALUES LESS THAN ('2032-11-01'),
            PARTITION p203211 VALUES LESS THAN ('2032-12-01'),
            PARTITION p203212 VALUES LESS THAN ('2033-01-01'),  -- 2033年以前数据

            PARTITION p203301 VALUES LESS THAN ('2033-02-01'),
            PARTITION p203302 VALUES LESS THAN ('2033-03-01'),
            PARTITION p203303 VALUES LESS THAN ('2033-04-01'),
            PARTITION p203304 VALUES LESS THAN ('2033-05-01'),
            PARTITION p203305 VALUES LESS THAN ('2033-06-01'),
            PARTITION p203306 VALUES LESS THAN ('2033-07-01'),
            PARTITION p203307 VALUES LESS THAN ('2033-08-01'),
            PARTITION p203308 VALUES LESS THAN ('2033-09-01'),
            PARTITION p203309 VALUES LESS THAN ('2033-10-01'),
            PARTITION p203310 VALUES LESS THAN ('2033-11-01'),
            PARTITION p203311 VALUES LESS THAN ('2033-12-01'),
            PARTITION p203312 VALUES LESS THAN ('2034-01-01'),  -- 2034年以前数据

            PARTITION p203401 VALUES LESS THAN ('2034-02-01'),
            PARTITION p203402 VALUES LESS THAN ('2034-03-01'),
            PARTITION p203403 VALUES LESS THAN ('2034-04-01'),
            PARTITION p203404 VALUES LESS THAN ('2034-05-01'),
            PARTITION p203405 VALUES LESS THAN ('2034-06-01'),
            PARTITION p203406 VALUES LESS THAN ('2034-07-01'),
            PARTITION p203407 VALUES LESS THAN ('2034-08-01'),
            PARTITION p203408 VALUES LESS THAN ('2034-09-01'),
            PARTITION p203409 VALUES LESS THAN ('2034-10-01'),
            PARTITION p203410 VALUES LESS THAN ('2034-11-01'),
            PARTITION p203411 VALUES LESS THAN ('2034-12-01'),
            PARTITION p203412 VALUES LESS THAN ('2035-01-01'),  -- 2035年以前数据

            PARTITION p203501 VALUES LESS THAN ('2035-02-01'),
            PARTITION p203502 VALUES LESS THAN ('2035-03-01'),
            PARTITION p203503 VALUES LESS THAN ('2035-04-01'),
            PARTITION p203504 VALUES LESS THAN ('2035-05-01'),
            PARTITION p203505 VALUES LESS THAN ('2035-06-01'),
            PARTITION p203506 VALUES LESS THAN ('2035-07-01'),
            PARTITION p203507 VALUES LESS THAN ('2035-08-01'),
            PARTITION p203508 VALUES LESS THAN ('2035-09-01'),
            PARTITION p203509 VALUES LESS THAN ('2035-10-01'),
            PARTITION p203510 VALUES LESS THAN ('2035-11-01'),
            PARTITION p203511 VALUES LESS THAN ('2035-12-01'),
            PARTITION p203512 VALUES LESS THAN ('2036-01-01'),  -- 2036年以前数据

            PARTITION p_future VALUES LESS THAN MAXVALUE  -- 兜底分区
        );
    </update>

    <update id="updateEDeviceHistoryData">
        update e_energy_data_history set recorded_at=created_at where recorded_at IS NULL;
    </update>

    <update id="addEDeviceDataHistoryPrimaryKeys">
        ALTER TABLE `e_energy_data_history`
            MODIFY COLUMN `recorded_at` datetime NOT NULL,
            DROP PRIMARY KEY,
            ADD PRIMARY KEY (`id`, `recorded_at`) USING BTREE;
    </update>

    <select id="checkEDeviceDataHistoryPartitionExists" resultType="int">
        SELECT count(*) FROM information_schema.partitions
        WHERE table_name = 'e_energy_data_history'
          and table_schema=#{database}
          AND partition_name IS NOT NULL;
    </select>

    <update id="addEDeviceDataHistoryPartitions">
        ALTER TABLE e_energy_data_history
            PARTITION BY RANGE COLUMNS(recorded_at) (
            PARTITION p_before VALUES LESS THAN ('2024-01-01'), -- 2024年以前数据

            PARTITION p202401 VALUES LESS THAN ('2024-02-01'),
            PARTITION p202402 VALUES LESS THAN ('2024-03-01'),
            PARTITION p202403 VALUES LESS THAN ('2024-04-01'),
            PARTITION p202404 VALUES LESS THAN ('2024-05-01'),
            PARTITION p202405 VALUES LESS THAN ('2024-06-01'),
            PARTITION p202406 VALUES LESS THAN ('2024-07-01'),
            PARTITION p202407 VALUES LESS THAN ('2024-08-01'),
            PARTITION p202408 VALUES LESS THAN ('2024-09-01'),
            PARTITION p202409 VALUES LESS THAN ('2024-10-01'),
            PARTITION p202410 VALUES LESS THAN ('2024-11-01'),
            PARTITION p202411 VALUES LESS THAN ('2024-12-01'),
            PARTITION p202412 VALUES LESS THAN ('2025-01-01'),  -- 2025年以前数据

            PARTITION p202501 VALUES LESS THAN ('2025-02-01'),
            PARTITION p202502 VALUES LESS THAN ('2025-03-01'),
            PARTITION p202503 VALUES LESS THAN ('2025-04-01'),
            PARTITION p202504 VALUES LESS THAN ('2025-05-01'),
            PARTITION p202505 VALUES LESS THAN ('2025-06-01'),
            PARTITION p202506 VALUES LESS THAN ('2025-07-01'),
            PARTITION p202507 VALUES LESS THAN ('2025-08-01'),
            PARTITION p202508 VALUES LESS THAN ('2025-09-01'),
            PARTITION p202509 VALUES LESS THAN ('2025-10-01'),
            PARTITION p202510 VALUES LESS THAN ('2025-11-01'),
            PARTITION p202511 VALUES LESS THAN ('2025-12-01'),
            PARTITION p202512 VALUES LESS THAN ('2026-01-01'),  -- 2026年以前数据

            PARTITION p202601 VALUES LESS THAN ('2026-02-01'),
            PARTITION p202602 VALUES LESS THAN ('2026-03-01'),
            PARTITION p202603 VALUES LESS THAN ('2026-04-01'),
            PARTITION p202604 VALUES LESS THAN ('2026-05-01'),
            PARTITION p202605 VALUES LESS THAN ('2026-06-01'),
            PARTITION p202606 VALUES LESS THAN ('2026-07-01'),
            PARTITION p202607 VALUES LESS THAN ('2026-08-01'),
            PARTITION p202608 VALUES LESS THAN ('2026-09-01'),
            PARTITION p202609 VALUES LESS THAN ('2026-10-01'),
            PARTITION p202610 VALUES LESS THAN ('2026-11-01'),
            PARTITION p202611 VALUES LESS THAN ('2026-12-01'),
            PARTITION p202612 VALUES LESS THAN ('2027-01-01'),  -- 2027年以前数据

            PARTITION p202701 VALUES LESS THAN ('2027-02-01'),
            PARTITION p202702 VALUES LESS THAN ('2027-03-01'),
            PARTITION p202703 VALUES LESS THAN ('2027-04-01'),
            PARTITION p202704 VALUES LESS THAN ('2027-05-01'),
            PARTITION p202705 VALUES LESS THAN ('2027-06-01'),
            PARTITION p202706 VALUES LESS THAN ('2027-07-01'),
            PARTITION p202707 VALUES LESS THAN ('2027-08-01'),
            PARTITION p202708 VALUES LESS THAN ('2027-09-01'),
            PARTITION p202709 VALUES LESS THAN ('2027-10-01'),
            PARTITION p202710 VALUES LESS THAN ('2027-11-01'),
            PARTITION p202711 VALUES LESS THAN ('2027-12-01'),
            PARTITION p202712 VALUES LESS THAN ('2028-01-01'),  -- 2028年以前数据

            PARTITION p202801 VALUES LESS THAN ('2028-02-01'),
            PARTITION p202802 VALUES LESS THAN ('2028-03-01'),
            PARTITION p202803 VALUES LESS THAN ('2028-04-01'),
            PARTITION p202804 VALUES LESS THAN ('2028-05-01'),
            PARTITION p202805 VALUES LESS THAN ('2028-06-01'),
            PARTITION p202806 VALUES LESS THAN ('2028-07-01'),
            PARTITION p202807 VALUES LESS THAN ('2028-08-01'),
            PARTITION p202808 VALUES LESS THAN ('2028-09-01'),
            PARTITION p202809 VALUES LESS THAN ('2028-10-01'),
            PARTITION p202810 VALUES LESS THAN ('2028-11-01'),
            PARTITION p202811 VALUES LESS THAN ('2028-12-01'),
            PARTITION p202812 VALUES LESS THAN ('2029-01-01'),  -- 2029年以前数据

            PARTITION p202901 VALUES LESS THAN ('2029-02-01'),
            PARTITION p202902 VALUES LESS THAN ('2029-03-01'),
            PARTITION p202903 VALUES LESS THAN ('2029-04-01'),
            PARTITION p202904 VALUES LESS THAN ('2029-05-01'),
            PARTITION p202905 VALUES LESS THAN ('2029-06-01'),
            PARTITION p202906 VALUES LESS THAN ('2029-07-01'),
            PARTITION p202907 VALUES LESS THAN ('2029-08-01'),
            PARTITION p202908 VALUES LESS THAN ('2029-09-01'),
            PARTITION p202909 VALUES LESS THAN ('2029-10-01'),
            PARTITION p202910 VALUES LESS THAN ('2029-11-01'),
            PARTITION p202911 VALUES LESS THAN ('2029-12-01'),
            PARTITION p202912 VALUES LESS THAN ('2030-01-01'),  -- 2030年以前数据

            PARTITION p203001 VALUES LESS THAN ('2030-02-01'),
            PARTITION p203002 VALUES LESS THAN ('2030-03-01'),
            PARTITION p203003 VALUES LESS THAN ('2030-04-01'),
            PARTITION p203004 VALUES LESS THAN ('2030-05-01'),
            PARTITION p203005 VALUES LESS THAN ('2030-06-01'),
            PARTITION p203006 VALUES LESS THAN ('2030-07-01'),
            PARTITION p203007 VALUES LESS THAN ('2030-08-01'),
            PARTITION p203008 VALUES LESS THAN ('2030-09-01'),
            PARTITION p203009 VALUES LESS THAN ('2030-10-01'),
            PARTITION p203010 VALUES LESS THAN ('2030-11-01'),
            PARTITION p203011 VALUES LESS THAN ('2030-12-01'),
            PARTITION p203012 VALUES LESS THAN ('2031-01-01'),  -- 2031年以前数据

            PARTITION p203101 VALUES LESS THAN ('2031-02-01'),
            PARTITION p203102 VALUES LESS THAN ('2031-03-01'),
            PARTITION p203103 VALUES LESS THAN ('2031-04-01'),
            PARTITION p203104 VALUES LESS THAN ('2031-05-01'),
            PARTITION p203105 VALUES LESS THAN ('2031-06-01'),
            PARTITION p203106 VALUES LESS THAN ('2031-07-01'),
            PARTITION p203107 VALUES LESS THAN ('2031-08-01'),
            PARTITION p203108 VALUES LESS THAN ('2031-09-01'),
            PARTITION p203109 VALUES LESS THAN ('2031-10-01'),
            PARTITION p203110 VALUES LESS THAN ('2031-11-01'),
            PARTITION p203111 VALUES LESS THAN ('2031-12-01'),
            PARTITION p203112 VALUES LESS THAN ('2032-01-01'),  -- 2032年以前数据

            PARTITION p203201 VALUES LESS THAN ('2032-02-01'),
            PARTITION p203202 VALUES LESS THAN ('2032-03-01'),
            PARTITION p203203 VALUES LESS THAN ('2032-04-01'),
            PARTITION p203204 VALUES LESS THAN ('2032-05-01'),
            PARTITION p203205 VALUES LESS THAN ('2032-06-01'),
            PARTITION p203206 VALUES LESS THAN ('2032-07-01'),
            PARTITION p203207 VALUES LESS THAN ('2032-08-01'),
            PARTITION p203208 VALUES LESS THAN ('2032-09-01'),
            PARTITION p203209 VALUES LESS THAN ('2032-10-01'),
            PARTITION p203210 VALUES LESS THAN ('2032-11-01'),
            PARTITION p203211 VALUES LESS THAN ('2032-12-01'),
            PARTITION p203212 VALUES LESS THAN ('2033-01-01'),  -- 2033年以前数据

            PARTITION p203301 VALUES LESS THAN ('2033-02-01'),
            PARTITION p203302 VALUES LESS THAN ('2033-03-01'),
            PARTITION p203303 VALUES LESS THAN ('2033-04-01'),
            PARTITION p203304 VALUES LESS THAN ('2033-05-01'),
            PARTITION p203305 VALUES LESS THAN ('2033-06-01'),
            PARTITION p203306 VALUES LESS THAN ('2033-07-01'),
            PARTITION p203307 VALUES LESS THAN ('2033-08-01'),
            PARTITION p203308 VALUES LESS THAN ('2033-09-01'),
            PARTITION p203309 VALUES LESS THAN ('2033-10-01'),
            PARTITION p203310 VALUES LESS THAN ('2033-11-01'),
            PARTITION p203311 VALUES LESS THAN ('2033-12-01'),
            PARTITION p203312 VALUES LESS THAN ('2034-01-01'),  -- 2034年以前数据

            PARTITION p203401 VALUES LESS THAN ('2034-02-01'),
            PARTITION p203402 VALUES LESS THAN ('2034-03-01'),
            PARTITION p203403 VALUES LESS THAN ('2034-04-01'),
            PARTITION p203404 VALUES LESS THAN ('2034-05-01'),
            PARTITION p203405 VALUES LESS THAN ('2034-06-01'),
            PARTITION p203406 VALUES LESS THAN ('2034-07-01'),
            PARTITION p203407 VALUES LESS THAN ('2034-08-01'),
            PARTITION p203408 VALUES LESS THAN ('2034-09-01'),
            PARTITION p203409 VALUES LESS THAN ('2034-10-01'),
            PARTITION p203410 VALUES LESS THAN ('2034-11-01'),
            PARTITION p203411 VALUES LESS THAN ('2034-12-01'),
            PARTITION p203412 VALUES LESS THAN ('2035-01-01'),  -- 2035年以前数据

            PARTITION p203501 VALUES LESS THAN ('2035-02-01'),
            PARTITION p203502 VALUES LESS THAN ('2035-03-01'),
            PARTITION p203503 VALUES LESS THAN ('2035-04-01'),
            PARTITION p203504 VALUES LESS THAN ('2035-05-01'),
            PARTITION p203505 VALUES LESS THAN ('2035-06-01'),
            PARTITION p203506 VALUES LESS THAN ('2035-07-01'),
            PARTITION p203507 VALUES LESS THAN ('2035-08-01'),
            PARTITION p203508 VALUES LESS THAN ('2035-09-01'),
            PARTITION p203509 VALUES LESS THAN ('2035-10-01'),
            PARTITION p203510 VALUES LESS THAN ('2035-11-01'),
            PARTITION p203511 VALUES LESS THAN ('2035-12-01'),
            PARTITION p203512 VALUES LESS THAN ('2036-01-01'),  -- 2036年以前数据

            PARTITION p_future VALUES LESS THAN MAXVALUE  -- 兜底分区
        );
    </update>

    <update id="createALFChain">
        CREATE TABLE IF NOT EXISTS `a_lf_chain` (
            `id` bigint NOT NULL AUTO_INCREMENT,
            `application_name` varchar(255) DEFAULT NULL,
            `chain_name` varchar(255) DEFAULT NULL,
            `chain_desc` varchar(255) DEFAULT NULL,
            `el_data` longtext,
            `enable` tinyint DEFAULT NULL,
            `execute_cycle_second` int DEFAULT NULL COMMENT '执行周期，每次执行完停止时间间隔',
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `result` varchar(5000) DEFAULT NULL COMMENT '最后一次执行结果',
            `recorded_at` datetime DEFAULT NULL COMMENT '最后一次完成时间',
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4;
    </update>

    <update id="createALFScript">
        CREATE TABLE IF NOT EXISTS `a_lf_script` (
            `id` bigint NOT NULL AUTO_INCREMENT,
            `application_name` varchar(255) CHARACTER SET utf8 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '应用名称',
            `script_id` varchar(255) CHARACTER SET utf8 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '脚本ID',
            `script_name` varchar(255) CHARACTER SET utf8 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '脚本名称',
            `notes` varchar(255) DEFAULT NULL COMMENT '脚本备注说明',
            `script_data` longtext CHARACTER SET utf8 COLLATE utf8mb4_general_ci COMMENT '脚本内容',
            `script_type` varchar(255) CHARACTER SET utf8 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '脚本类型script',
            `language` varchar(255) CHARACTER SET utf8 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '脚本语言java/js',
            `enable` tinyint DEFAULT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4;
    </update>

    <update id="addAWeatherDataColoms">
        ALTER TABLE `a_weather_data`
            ADD COLUMN `wind_360` varchar(255) NULL COMMENT '风向360角度' AFTER `uvi`,
            ADD COLUMN `wind_direction` varchar(255) NULL COMMENT '风向' AFTER `wind_360`,
            ADD COLUMN `wind_scale` varchar(255) NULL COMMENT '风力等级' AFTER `wind_direction`,
            ADD COLUMN `wind_speed` varchar(255) NULL COMMENT '风速，公里/小时' AFTER `wind_scale`,
            ADD COLUMN `precip` varchar(255) NULL COMMENT '过去1小时降水量，默认单位：毫米' AFTER `wind_speed`,
            ADD COLUMN `pressure` varchar(255) NULL COMMENT '大气压强，默认单位：百帕' AFTER `precip`,
            ADD COLUMN `vis` varchar(255) NULL COMMENT '能见度，默认单位：公里' AFTER `pressure`,
            ADD COLUMN `cloud` varchar(255) NULL COMMENT '云量，百分比数值。可能为空' AFTER `vis`,
            ADD COLUMN `dew` varchar(255) NULL COMMENT '露点温度。可能为空' AFTER `cloud`;
    </update>

    <update id="addATplDeviceDataPtColoms">
        ALTER TABLE `a_tpl_device_data`
            ADD COLUMN `p_device_id` int NULL AFTER `is_primary_energy`,
            ADD COLUMN `p_addr` int NULL AFTER `p_device_id`,
            ADD COLUMN `p_length` int NULL AFTER `p_addr`,
            ADD COLUMN `p_data_group` varchar(50) NULL AFTER `p_length`,
            ADD COLUMN `p_func` varchar(255) NULL AFTER `p_data_group`,
            ADD COLUMN `p_func_list` varchar(1000) NULL AFTER `p_func`;
    </update>

    <update id="updateDDeviceTaskRecordContent">
        ALTER TABLE `d_device_task`
            MODIFY COLUMN `recorded_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '最后一次执行结果';
    </update>
    <update id="addATplDeviceDataNote2">
        ALTER TABLE `a_tpl_device_data`
            ADD COLUMN `note2` varchar(500) NULL COMMENT '备注(Map表)' AFTER `note`;
    </update>
    <update id="addATplDeviceDataUniqueIdx">
        ALTER TABLE `a_tpl_device_data`
            ADD UNIQUE INDEX `nu`(`template_id` ASC, `name` ASC) USING BTREE;
    </update>

    <insert id="addDDeviceDataHistory">
        CREATE TABLE IF NOT EXISTS `d_device_data_history` (
            `id` int NOT NULL AUTO_INCREMENT,
            `device_id` int DEFAULT NULL COMMENT '设备ID',
            `item_data_id` int DEFAULT NULL COMMENT '数据点位ID',
            `indication` decimal(20,4) DEFAULT NULL,
            `other_data` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
            `recorded_at` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`,`recorded_at`) USING BTREE,
            KEY `_recorded_at` (`recorded_at`) USING BTREE,
            KEY `_device_id` (`device_id`) USING BTREE,
            KEY `_device_record` (`device_id`,`recorded_at`) USING BTREE,
            KEY `_item_data_id` (`item_data_id`) USING BTREE,
            KEY `_indication` (`indication`) USING BTREE,
            KEY `_itid_rd` (`item_data_id`,`recorded_at`) USING BTREE
        ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='设备历史数据'
    </insert>
    <insert id="addEDeviceDataHistory">
        CREATE TABLE IF NOT EXISTS `e_energy_data_history` (
            `id` int NOT NULL AUTO_INCREMENT,
            `device_id` int NOT NULL,
            `indication` decimal(20,4) DEFAULT NULL,
            `diff_indication` decimal(20,4) DEFAULT NULL,
            `diff_fee` decimal(20,4) NOT NULL DEFAULT '0.000' COMMENT '费用',
            `other_data` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '其他数据 (峰谷标记)',
            `recorded_at` datetime NOT NULL,
            `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`,`recorded_at`) USING BTREE,
            KEY `_recorded_at` (`recorded_at`) USING BTREE,
            KEY `_device_id` (`device_id`) USING BTREE,
            KEY `_diff_indication` (`diff_indication`) USING BTREE,
            KEY `_created_at` (`created_at`) USING BTREE,
            KEY `_indication` (`indication`) USING BTREE,
            KEY `_rid` (`id` DESC,`recorded_at` DESC,`device_id`) USING BTREE,
            KEY `_device_record` (`device_id`,`recorded_at` DESC) USING BTREE
        ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='能耗数据历史'
    </insert>
    <insert id="addEDeviceDataHistoryDaily">
        CREATE TABLE IF NOT EXISTS  `e_energy_data_history_daily`  (
            `id` int NOT NULL AUTO_INCREMENT,
            `device_id` int NOT NULL,
            `indication` decimal(20, 4) NULL DEFAULT NULL,
            `diff_indication` decimal(20, 4) NULL DEFAULT NULL,
            `diff_fee` decimal(20, 3) NOT NULL DEFAULT 0.000 COMMENT '费用',
            `other_data` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '其他数据 (峰谷标记)',
            `total` decimal(20, 3) NULL DEFAULT 0.000,
            `totalFee` decimal(20, 3) NULL DEFAULT 0.000,
            `j` decimal(20, 3) NULL DEFAULT 0.000,
            `jFee` decimal(20, 3) NULL DEFAULT 0.000,
            `f` decimal(20, 3) NULL DEFAULT 0.000,
            `fFee` decimal(20, 3) NULL DEFAULT 0.000,
            `p` decimal(20, 3) NULL DEFAULT 0.000,
            `pFee` decimal(20, 3) NULL DEFAULT 0.000,
            `g` decimal(20, 3) NULL DEFAULT 0.000,
            `gFee` decimal(20, 3) NULL DEFAULT 0.000,
            `recorded_at` date NULL DEFAULT NULL,
            `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`) USING BTREE,
            INDEX `_recorded_at`(`recorded_at` ASC) USING BTREE,
            INDEX `_device_id`(`device_id` ASC) USING BTREE,
            INDEX `_diff_indication`(`diff_indication` ASC) USING BTREE,
            INDEX `_created_at`(`created_at` ASC) USING BTREE,
            INDEX `_device_record`(`device_id` ASC, `recorded_at` ASC) USING BTREE,
            INDEX `_indication`(`indication` ASC) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '能耗日历史数据' ROW_FORMAT = DYNAMIC;
    </insert>
    <update id="addAPointTableTag">
        ALTER TABLE `a_point_table`
            ADD COLUMN `tag` varchar(200) NULL COMMENT '是否需要载入读点数据' AFTER `type`;
    </update>
    <delete id="deleteItemDataUpdateTrigger">
        DROP TRIGGER item_data_update_to_log;
    </delete>
    <update id="addItemDataUpdateTrigger">
        CREATE TRIGGER item_data_update_to_log AFTER UPDATE ON a_item_data FOR EACH ROW
        BEGIN
            DECLARE v_deviceId VARCHAR(50);
            DECLARE v_dataName VARCHAR(255);
            DECLARE v_deviceType VARCHAR(255);
            DECLARE v_dataTag VARCHAR(255);
            DECLARE v_dataValueMapper VARCHAR(500);
            DECLARE v_js JSON;

            -- 条件检查
            IF NEW.val != OLD.val AND NEW.func = 'read' AND (NEW.locked IS NULL OR NEW.locked != 1) THEN
                -- 查询数据并赋值给局部变量
                SELECT
                    m.device_id, m.name, d.type, IFNULL(m.tag,''), IFNULL(da.note,'')
                INTO
                    v_deviceId, v_dataName, v_deviceType, v_dataTag, v_dataValueMapper
                FROM
                    d_device_item_data_map m
                        LEFT JOIN d_device d ON m.device_id = d.id
                        LEFT JOIN a_item_data da ON m.item_data_id = da.id
                WHERE
                    m.item_data_id = NEW.id
                    LIMIT 1;
                -- debug 使用
                -- insert into d_trigger_log (notes) VALUES (v_deviceId);
                -- 仅当查询到数据且标签匹配时记录日志
                IF (v_deviceId IS NOT NULL AND v_dataTag LIKE '%operatorLog%') THEN
                    SET v_js = JSON_OBJECT(
                        'dataId', NEW.id,
                        'dataName', v_dataName,
                        'dataValueAfter', NEW.val,
                        'dataValueBefore', OLD.val,
                        'dataValueMapper', v_dataValueMapper
                    );

                    INSERT INTO d_device_oper_log (
                        device_id, device_type, title, business_type,
                        method, request_method, operator_type,
                        oper_name, oper_param, json_result, status, oper_time
                    ) VALUES (
                        v_deviceId, v_deviceType, CONCAT('设备变更--', v_dataName), 2,
                        'db trigger', 'USER_OPERATE', '3',
                        'USER', '', v_js, '0', NOW()
                    );
                END IF;
            END IF;
        END;
    </update>
</mapper>
