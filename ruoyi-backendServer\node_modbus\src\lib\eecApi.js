/*
    常州中以国际 对接 泰杰赛 ba，能耗 数据
   文档 https://eecapi.techsel.net/docs/api/api-1e7chu9b2psfg
*/

const request = require("request");
const querystring = require("querystring");

const helper = require("../helper");

function SerApi(opts) {
    var that = this;

    this.opts = {
        fakeData: false, // 是否模拟数据返回
        timeout: 30000,
        accessToken: null,
        ...opts,
    };

    // 服务器返回错误码描述
    this.ajaxStatusCodes = {
        0: "success",
    };

    // 从 localstorage 恢复数据
    this._init_ = function () {};
}

SerApi.prototype._ajaxData = async function (param) {
    var that = this;
    return new Promise(function (resolve, reject) {
        that.ajaxData(
            param,
            function (data) {
                resolve(data);
            },
            function (err) {
                reject(err);
            }
        );
    });
};

SerApi.prototype.removePropertyOfNull = function (obj) {
    Object.keys(obj).forEach(item => {
        if (obj[item] == null) {
            delete obj[item];
        }
    });
    return obj;
};

SerApi.prototype.ajaxData = function (param, successFunc, errorFunc) {
    let that = this;
    let bodyData = null
    if(param.url.indexOf('/get/token')>-1){
        bodyData = querystring.stringify(param.data)
    }else {
        bodyData = JSON.stringify(param.data)
    }
    // 所有请求逻辑
    var query = {
        url: param.url,
        method: param.method.toUpperCase(),
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        body: bodyData,
        timeout: this.opts.timeout,
        rejectUnauthorized: false, // 禁用证书验证
    };
    // 如果有 token 则补充头
    if(this.opts.accessToken) {
        query.headers.Authorization = "bearer " + this.opts.accessToken;
    }
    if (param.method.toUpperCase() == "GET") {
        query.url = query.url + "?" + querystring.stringify(param.data);
    }
    helper.debug("ajaxData query", query);

    return request(query, function (error, response, body) {
        if (error) {
            if (typeof errorFunc == "function") {
                errorFunc(error);
            } else {
                that.errorFunc(error);
            }
            return;
        }
        // 成功后执行
        var res = JSON.parse(body);
        if (res) {
            if (typeof successFunc == "function") {
                successFunc(res);
            }
        } else {
            if (typeof errorFunc == "function") {
                errorFunc(res);
            } else {
                that.errorFunc(res);
            }
        }
    });
};

SerApi.prototype.errorFunc = function (res) {
    helper.log("errorFunc", res);
    return new Error("server error: " + JSON.stringify(res));
};

// 1.1 获取token
SerApi.prototype.getToken = async function () {
    let that = this;
    let res = await this._ajaxData({
        url: that.opts.server + "/api/terminal/User/Login",
        method: "post",
        data: {
            LoginName: that.opts.username,
            Password: that.opts.password,
            Domain: that.opts.server
        },
    });
    that.opts.accessToken = res.data.accessToken && res.data.accessToken.tokenContent;
    helper.debug('............token',res)
    return res;
};
// 1.1 获取实时值，多点批量
SerApi.prototype.getDeviceData = async function (ids) {
    let that = this;
    let res = await this._ajaxData({
        url: that.opts.server + "/api/RealTimeData/BulkGet",
        method: "post",
        data: {
            pointIds: ids,
            hasShowTime:true
        },
    });
    helper.debug('............getDeviceData',res)
    return res;
};

// 1.2 设置值，单点
// {
//     "pointId": "",
//     "value": ""
// }
SerApi.prototype.setDeviceData = async function (data) {
    let that = this;
    let res = await this._ajaxData({
        url: that.opts.server + "/api/RealTimeData/Set",
        method: "post",
        data: data,
    });
    helper.debug('............setDeviceData',res)
    return res;
};

module.exports.SerApi = SerApi;
