/**
  启动本地服务，各种采集脚本统一数据入库
  参数  [port] [dbName] [readWrite] [batchNum] [isDebug]

 [readWrite] 说明:
    包含 rw，则读取数据，更新读的点的同时，会同步更新对应的写的点
    包含 wr，则下发写数据完成时，更新写的点的同时，会同步更新对应的读的点

 [batchNum] 每30秒定时批量入库，每次的数据量，可根据不同机器性能调节，默认 500 

  接收数据，直接入库（不做decUtil处理）
    {
      id: 73101,
      val: 1,
      updatedAt: '2024-08-09 17:24:37',
      calc: 1,
    }
*/

'use strict';

const schedule = require('node-schedule');
const http = require('http');
const util = require('util');
const url=require('url');
const qs = require("querystring");
const EventEmitter = require('events');
const moment = require('moment');
const redis = require("redis");

const Db = require("./mydb2");
const decUtil = require('./lib/_decUtil');
const helper = require("./helper");

const sysConfig = require('./conf/sysConfig').sysConfig();

const port = process.argv[2] > 0 ? process.argv[2] : sysConfig.localServer.port;
const dbName = process.argv[3] && process.argv[3] != "debug" ? process.argv[3] : null;
const updateWritePoint = process.argv[4] && process.argv[4].indexOf("rw") >= 0 ? true : false;
const updateReadPoint = process.argv[4] && process.argv[4].indexOf("wr") >= 0 ? true : false;
const batchNum = process.argv[5] && Number.isInteger(process.argv[5]) ?process.argv[5] : 500;
global.isDebug = process.argv[4] == "debug" || process.argv[5] == "debug" || process.argv[6] == "debug" ? true : false;

let cacheKey = sysConfig.redis && sysConfig.redis.cacheKey || "cacheItemDataVal:";

// 参数替换 覆盖之前的配置
if(dbName) {
  sysConfig.mysql.database = dbName;
}
if(port > 0) {
  sysConfig.localServer.port = port;
}

var _db = new Db(sysConfig.mysql);
// 双数据库同步
if(typeof sysConfig.mysql2 != "undefined") {
    _db.createServer2(sysConfig.mysql2);
}

let redisClient = null;

let itemDataMap = {};
var dataPoints = []; // 缓存设备数据
var itemWarningMap = {}; // 缓存设备报警数据

let app = null;
// 全局变量
var gtmp = {};
// 缓存报过的警, 如果一直未变化, 则不会重复报
var cacheWarning = {};
// 缓存设备最近几次数据
var cacheData = {};
// 最近一次采集缓存的数据，不一样的实时更新数据库，一样的定时更新数据库
var cacheLatestData = {};
let _success = {
    code: 0,
    status: 200,
    msg: "",
    result: [],
};
let offLineSecond = sysConfig.offLineSecond ? sysConfig.offLineSecond : 30 * 60; // 30分钟


var sqlAll = `
    select
        it.*,
        ifnull(it.val,'') val,
        ifnull(it.func,'') as func,
        i.collector_id,
        ifnull(it.max_val, '') max_val,
        ifnull(it.min_val, '') min_val,
        ifnull(p.func_list,'') func_list,
        date_format(it.updated_at,'%Y-%m-%d %H:%i:%s') as updated_at
    from a_item_data it
    left join a_item i on it.item_id = i.id
    left join a_point_table p on p.item_data_id = it.id
    where it.updated_at > DATE_ADD(now(), interval -10 hour)
`;
var sql = `
    select
        it.*,
        ifnull(it.func,'') as func,
        i.collector_id,
        ifnull(it.max_val, '') max_val,
        ifnull(it.min_val, '') min_val,
        ifnull(p.func_list,'') func_list
    from a_item_data it
    left join a_item i on it.item_id = i.id
    left join a_point_table p on p.item_data_id = it.id
    where it.id = ?
`;
var itemWarningRuleSql = `
    SELECT
        rm.rule_id as ruleId,
        i.id as itemId,
        i.name as itemName,
        wr.name as wName,
        wr.description as wDescription,
        wr.key as wKey,
        wr.val as wVal,
        wr.time_span as wTimeSpan,
        wr.func as wFunc,
        wr.compare as wCompare,
        wr.severity as wSeverity,
        wr.err_msg as wErrMsg,
        wr.solution_ref as wSolutionRef,
        wr.tag as wTag
    from a_item_rule_map rm
    LEFT JOIN a_item i on rm.item_id = i.id
    LEFT JOIN a_warning_rule wr on wr.id = rm.rule_id
`;
var warningSql = {
    "check": `select * from a_item_warning where item_id = ? and warning_category = ? and has_fixed = 'N' order by reported_at desc`,
    "insert": `insert into a_item_warning (item_id, item_data_id, compare, warning_category, severity, err_msg, solution_ref, reported_at) values (?,?,?,?,?,?,?,?)`,
    "updateFix": `update a_item_warning set updated_at = ?, compare = ?, has_fixed = 'Y', fixed_at = now() where id= ?`,
    "updateLastData": `update a_item_warning set updated_at = ?, compare = ? where id= ?`,
}

// 初始化时，从 db 拉取 10 小时内的数据并缓存
// updated_at 超过 offlineSecond ? first 置为 updated : first 置为 updated_at - keep_seconds
async function updateCaches() {
  // 查找点位，有则入库
  let res = await _db.doSql(sqlAll);
  if(res && res.length > 0) {
    for(let i = 0; i < res.length; i++) {
      let d = res[i];
      let cp = {
        ...d,
        data_name: d.name,
        item_data_id: d.id,
      };
      cacheLatestData[d.id] = {
        cp: cp,
        val: d.val,
        realVal: d.val,
        updated: d.updated_at,
        first: moment().diff(moment(d.updated_at)) > offLineSecond ? d.updated_at : moment(d.updated_at).add(0-d.keep_second, "second").format("YYYY-MM-DD HH:mm:ss"),
        start: false,  // 第一次载入
      }
    }
  }
  helper.debug("updateCaches", Object.keys(cacheLatestData).length);
}

// 更新采集相关数据
async function updateWarningDatas() {
    // 获取设备故障列表
    let itemWarningRes = await _db.doSql(itemWarningRuleSql);
    helper.info("报警数据更新，共 ", itemWarningRes.length, "个故障检测点。");

    if(itemWarningRes.length > 0) {
        for(let i =0; i<itemWarningRes.length; i++) {
            let iw = itemWarningRes[i];
            if(typeof itemWarningMap[iw["itemId"]] == "undefined") {
                itemWarningMap[iw["itemId"]] = [iw];
            } else {
                itemWarningMap[iw["itemId"]].push(iw);
            }
        }
    }
}

// 启动 redis 连接
async function connectRedis() {
  if(sysConfig.hasOwnProperty("redis")) {
    try {
      let opt = sysConfig;
      redisClient = redis.createClient(opt.redis);
      await redisClient.connect();
    } catch(e) {
      helper.log("AppServer _connectRedis error", e.message);
      redisClient = null; // redis 连接失败，不影响正常逻辑
    }
  }
}

function AppServer(options) {
  this.options = options;
  this.cacheData = {};
  this._createServer();
}
util.inherits(AppServer, EventEmitter);

AppServer.prototype._createServer = function() {
  this.server = http.createServer(function(req,res){
    if(req.method == "GET") {
      // get 参数
      var queryObj = {};
      try {
        var query = url.parse(req.url).query;
        var queryObj = qs.parse(query);
      } catch(e) {

        // pass
      }
      helper.debug("get: ", JSON.stringify(queryObj));
      app._handleGetReq(req, res, queryObj);
    } else if(req.method == "POST") {
      var body = {};
      req.setEncoding('utf8');
      req.on('data', function (chunk) {
          body += chunk;
      });
      req.on('end', function () {
        // post 参数
        try {
          // 去掉body前面的字符串
          if(body.startsWith("[object Object]")) {
            body = body.replace("[object Object]", "");
          }
          // 尝试解析 Content-Type：application/json 数据
          if(typeof body == "string") {
            try{
              body = JSON.parse(body);
            } catch(e) {
              // pass
            }
          }
          // 尝试解析 Content-Type：application/x-www-form-urlencoded 数据
          if(typeof body == "string") {
            try{
              body = qs.parse(body);
              body = JSON.parse(JSON.stringify(body));
            } catch(e) {
              // pass
            }
          }
        } catch (e) {
          // pass
          helper.log(e.message);
        }
        helper.debug("post: ", JSON.stringify(body));
        app._handlePostReq(req, res, body);
      });
    }
  });
  this.server.listen(this.options.localServer.port, this.options.localServer.host, () => {
      helper.log('服务器启动成功，可以访问: ' + this.options.localServer.host + ':' + this.options.localServer.port);
  });
}

/**私有函数，提供uid-设备映射关系，注意出于保护目的该接口将简单验证header中token字段 */
AppServer.prototype._handleGetReq = function(req, res, queryObj) {
  let result = JSON.parse(JSON.stringify(_success));
  if(req.url.indexOf('/lastData') == 0) {
    // 服务是否活跃，最近一次获取数据时间离现在的毫秒数
    res.writeHead(200,{"Content-Type":"application/json;charset=utf-8"});
    result.result = cacheLatestData;
    res.write(JSON.stringify(result));
    res.end();
  } else if(req.url.indexOf('/cacheData') == 0) {
    // 服务是否活跃，最近一次获取数据时间离现在的毫秒数
    res.writeHead(200,{"Content-Type":"application/json;charset=utf-8"});
    result.result = cacheData;
    res.write(JSON.stringify(result));
    res.end();
  } else if(req.url.indexOf('/saveData') == 0) {
      // 服务是否活跃，最近一次获取数据时间离现在的毫秒数
      res.writeHead(200,{"Content-Type":"application/json;charset=utf-8"});
      syncLastData();
      res.write("success");
      res.end();
  } else if(req.url.indexOf('/warningMap') == 0) {
    res.writeHead(200,{"Content-Type":"application/json;charset=utf-8"});
    // 尝试解析出请求的网关ID
    let rl = req.url.split('/');
    var itemId = rl[rl.length-1];
    if (itemId != "warningMap") {
      result.result = {}
      result.result[itemId] = itemWarningMap[itemId];
    } else {
      result.result = itemWarningMap;
    }
    res.write(JSON.stringify(result));
    res.end();
  } else if(req.url.startsWith(this.options.localServer.path)) {
    // 针对有人物联 usr.cn 做的校验
    res.writeHead(200,{"Content-Type":"application/json;charset=utf-8"});
    res.write(queryObj && queryObj["verify"] ? queryObj.verify : "");
    res.end();
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain', 'Access-Control-Allow-Methods': 'GET,POST', 'Access-Control-Allow-Origin': '*' });
    res.end('404 not found');
  }
}

/**私有函数，处理设备上报的数据内容，并分发到不同的消息队列中 */
AppServer.prototype._handlePostReq = async function(req, res, data) {
  let result = JSON.parse(JSON.stringify(_success));
  // 监听数据的 path
  if(req.url.indexOf('/calcVal') == 0) {
    console.log(data);
      // 计算数据的下发值
      let funcList = data.funcList;
      let val = data.val;
      let realVal = val;
      try {
        realVal = dealWithFunclist(val, {
          val: val,
          func_list: funcList,
        }, 1);
      } catch(e) {
        // pass
      }
      res.writeHead(200,{"Content-Type":"application/json;charset=utf-8"});
      res.write(realVal);
      res.end();
  } else if(req.url.startsWith(this.options.localServer.path)) {
    // 服务是否活跃，最近一次获取数据时间离现在的毫秒数
    res.writeHead(200,{"Content-Type":"application/json;charset=utf-8"});
    result.result = await this._handlePostData(data);
    res.write(JSON.stringify(result));
    res.end();
  } else {
      res.writeHead(403, { 'Content-Type': 'text/plain', 'Access-Control-Allow-Methods': 'GET,POST', 'Access-Control-Allow-Origin': '*' });
      res.end('403 FORBIDDEN');
  }
}

AppServer.prototype._handlePostData = async function(data) {
  let that = this;
  // 尝试解析数据
  if(typeof data == "object") {
      helper.remoteLog("httpServerListener-"+sysConfig.localServer.name+"-"+port, JSON.stringify({
        db: dbName,
        port: port,
        data: data,
      }));

      helper.debug("_handlePostData", data);
      try {
        // 支持打包接收数据
        if(Array.isArray(data)) {
          for(let i=0; i<data.length; i++) {
            let d = data[i];
            await dealPostData(d);
          }
        } else {
          await dealPostData(data);
        }
      } catch(e) {
        console.trace(e);
        helper.debug("_handlePostData error: ", JSON.stringify(e.message), " data: ", data);
        return e.message;
      }
    return 'success';
  }
  return 'failed';
}

// 处理单个数据
// 写操作直接刷新keepSecond
// 读操作，判断距离旧 updated > offlineSecons ? first 置为 updated : 保持原 first
async function dealPostData(data) {
  try {
    let val = data.val;
    let updated = moment(data.updatedAt).format('YYYY-MM-DD HH:mm:ss') || moment().format('YYYY-MM-DD HH:mm:ss');
    let itemDataId = data.id;

    if(cacheLatestData[itemDataId]
      && cacheLatestData[itemDataId].cp
      && cacheLatestData[itemDataId].cp.func == 'read'
      && cacheLatestData[itemDataId].val == val
      && cacheLatestData[itemDataId].start) {
      // pass
      cacheLatestData[itemDataId].updated = updated;
      cacheLatestData[itemDataId].hasUpdated = false;  // 标记给定时任务做更新动作
      cacheLatestData[itemDataId].start = true;
      helper.debug("cacheLatestData[itemDataId] && cacheLatestData[itemDataId].val == val", val, cacheLatestData[itemDataId]);
      let cp = cacheLatestData[itemDataId].cp;
      let realVal = cacheLatestData[itemDataId].realVal;
      // 更新报警信息
      if(cp.func == "read") {
        await dealWarningResult(realVal, cp);
        // 更新到 redis 缓存
        await updateCache(cacheLatestData[itemDataId]);
      }
      helper.debug("dealWarningResult data success.", val, realVal, cp);
      // 推送消息
      helper.sendItemDataMsg(dbName, {
        id: itemDataId,
        val: realVal,
        updated: updated,
      });

    } else {
      if(!cacheLatestData[itemDataId]) {
        cacheLatestData[itemDataId] = {
          val: val,
          updated: updated,
        }
      } else {
        cacheLatestData[itemDataId].val = val;
        cacheLatestData[itemDataId].updated = updated;
      }
      cacheLatestData[itemDataId].hasUpdated = true;  // 标记更新过了
      cacheLatestData[itemDataId].first = updated;    // 更新变化起始时间
      cacheLatestData[itemDataId].start = true;
      // 第一次进入逻辑
      if(typeof cacheLatestData[itemDataId].cp == "undefined") {
        // 查找点位，有则入库
        let res = await _db.doSql(sql, [itemDataId]);
        helper.debug("sql res", itemDataId, res);
        if(res.length > 0) {
          let cp = {
            ...res[0],
            data_name: res[0].name,
            item_data_id: itemDataId
          };
          cacheLatestData[itemDataId].cp = cp;
          helper.debug("cp 0", cacheLatestData[itemDataId]);
          // 重新计算实际 val 值
          let realVal = dealWithFunclist(val, cp, data.calc);
          cacheLatestData[itemDataId].realVal = realVal;
          // 更新数据点位
          await dealResult(realVal, cp, updated);
          // 更新报警信息
          if(cp.func == "read") {
            await dealWarningResult(realVal, cp);
            // 更新到 redis 缓存
            await updateCache(cacheLatestData[itemDataId]);
          }
          helper.debug("sync data success.", val, realVal, cp);
        } else {
          // 不用关注的点
          // pass
          helper.debug("ignore val", data);
        }
      } else {
        let cp = cacheLatestData[itemDataId].cp;
        helper.debug("cp 1", cacheLatestData[itemDataId]);
        // 重新计算实际 val 值
        let realVal = dealWithFunclist(val, cp, data.calc);
        cacheLatestData[itemDataId].realVal = realVal;
        // 更新数据点位
        await dealResult(realVal, cp, updated);
        // 更新报警信息
        if(cp.func == "read") {
            await dealWarningResult(realVal, cp);
            // 更新到 redis 缓存
            await updateCache(cacheLatestData[itemDataId]);
        }
        helper.debug("sync data success.", val, realVal, cp);
      }
    }
  } catch(e) {
    console.trace(e);
    helper.debug("dealPostData error: ", JSON.stringify(e.message), " data: ", data);
    throw new Error(e.message);
  }
}

// 根据情况处理实际值
function dealWithFunclist(val, curPoint, calc) {
    let realVal = val;
    try {
        if(calc == "1" && curPoint.func_list != "") {
            let funcList = JSON.parse(curPoint.func_list);
            realVal = decUtil.batch(val, funcList);
        }
    } catch (e) {
        helper.log("dealWithFunclist error", e.message, val, curPoint.func_list, calc);
    }
    return realVal;
}

// 更新db中点位数据
// 只更新已知数据点信息，自动扫描的不做插入操作
// 读点且 val 变化的 + 写点
async function dealResult(val, curPoint, updated) {
  // 推送消息
  let cp = curPoint;
  var id = parseInt(cp.item_data_id);
  var msg = {id: id, val: val};
  if(updated) {
    msg.updated = updated;
  }
  msg.keepSecond = 0;
  helper.sendItemDataMsg(dbName, msg);
  // 处理逻辑
  if((curPoint.max_val == "" || (curPoint.max_val != "" && parseFloat(val) < parseFloat(curPoint.max_val)))
    && (curPoint.min_val == "" || (curPoint.min_val != "" && parseFloat(val) > parseFloat(curPoint.min_val))) ) {
    if(curPoint.func == "read") {
      // 更新数据点位
      await _db.updateData(msg);
      helper.debug("[update db]", msg);
      if(updateWritePoint) {
        let res = await _db.doSql(sql, [(id+1)]);
        helper.debug((id+1), "write", res);
        if(res.length > 0 && res[0].func == "write" && res[0].name == curPoint.data_name + "更新" && res[0].has_sync == 'Y') {
          await _db.updateData({id: (res[0].id), val: val, keepSecond: msg.keepSecond});
          // 更新到 redis 缓存
          await updateCache({
            cp: {
              item_data_id: res[0].id,
            },
            realVal: val,
            updated: msg.updated
          });
          helper.debug("[update db] w", {id: (res[0].id), val: val});
          // 实时推送变更数据信息
          helper.sendItemDataMsg(dbName, {id: (res[0].id), val: val, type: "write"});
        }
      }
      // 实时更新 influxdb 数据
      if(["int", "float"].includes(curPoint.data_type)) {
        msg.itemId = curPoint.item_id;
        msg.dataName = curPoint.data_name;
        msg.collectorId = curPoint.collector_id;
        helper.writeData2Influxdb(msg);
      }
    } else if(curPoint.func == "write") {
      // 如果是锁定状态，不更新同步状态，继续轮询
      if(curPoint.locked > 0) {
        // 更新数据点位
        await _db.updateDataWithoutHasSync(msg);
      } else {
        await _db.updateData(msg);
      }
      helper.debug("[update db]", msg);
      if(updateReadPoint) {
        let res = await _db.doSql(sql, [(id-1)]);
        helper.debug((id-1), "read", res);
        if(res.length > 0 && res[0].func == "read" && res[0].name + "更新" == curPoint.data_name) {
          let keepSecond = (res[0].val == val && res[0].updated_at && moment().diff(moment(res[0].updated_at, "second") <= offLineSecond))
              ? moment().diff(moment(res[0].updated_at), "second") : 0;
          await _db.updateData({id: (res[0].id), val: val, keepSecond: keepSecond});
          // 更新到 redis 缓存
          await updateCache({
            cp: {
              item_data_id: res[0].id,
            },
            realVal: val,
            updated: msg.updated
          });
          helper.debug("[update db ] r", {id: (res[0].id), val: val, keepSecond: keepSecond});
          // 实时推送变更数据信息
          helper.sendItemDataMsg(dbName, {id: (res[0].id), val: val, keepSecond: keepSecond});
        }
      }
    }
  } else {
    helper.debug("[update db] error value", val, curPoint.max_val, curPoint.min_val);
  }
}

// 更新到 redis 缓存中，默认 1 小时
async function updateCache(data, ttl = 60*60) {
  if(redisClient) {
    let key = cacheKey + data.cp.item_data_id;
    let d = {
      val: data.realVal || data.val,
      updated: data.updated,
    };
    try {
      let result = await redisClient.hSet(key, d); // 存入数据
      result = await redisClient.expire(key, ttl); // 加时间限制
      helper.debug("[redis update cache] success -> ", key, d, ttl);
    } catch (err) {
      helper.log('[redis update cache] failed -> ', err.message);
    }
  }
}

// 更新db中点位触发的报警数据
async function dealWarningResult(val, curPoint, updated) {
  // 更新报警信息
  try {
    var now = moment(updated).format("YYYY-MM-DD HH:mm:ss") || moment().format("YYYY-MM-DD HH:mm:ss");
    var itemId = curPoint.item_id;
    var itemDataId = curPoint.item_data_id;
    var rules = itemWarningMap[itemId];
    if(Array.isArray(rules) && rules.length > 0) {
        // 遍历rule, 检查是否需要报警
        for(let i=0; i<rules.length; i++) {
            let rule = rules[i];
            var wKey = rule.wKey;
            var wVal = rule.wVal;
            var wTimeSpan = rule.wTimeSpan;
            var wFunc = rule.wFunc;
            var wCompare = rule.wCompare;

            // 判定对比的字符串
            var compareStr = "";
            var compareArr = "";
            // 判定时间达到标记
            let compareTimeFlag = true;  // 默认按单次判断

            if(wKey == curPoint.data_name) {
                // 多次数据判定
                if(wTimeSpan > 0 && wFunc != "") {
                    val = parseFloat(val);  // 多次判断基于数字，先转成 float
                    compareTimeFlag = false; // 默认时间不达标
                    // 更是设备数据缓存
                    if(typeof cacheData[itemDataId] != "undefined") {
                        // 对一个设备有多个区间判断, 保留最长的区间数据
                        if(wTimeSpan > cacheData[itemDataId].timeSpan) {
                            cacheData[itemDataId].timeSpan = wTimeSpan;
                        }
                        // 添加最新缓存数据, 最少间隔1分钟的数据才会保存到列表(采集频率太高)
                        let latestData = cacheData[itemDataId].data[cacheData[itemDataId].data.length - 1];
                        if(val != latestData.val || moment(now).diff(latestData.time, "s") >= 60) {
                            cacheData[itemDataId].data.push({
                                time: now,
                                val: val,
                            });
                        }
                        // 判断持续时间是否达标
                        let firstData = cacheData[itemDataId].data[0];
                        compareTimeFlag = moment(now).diff(firstData.time, "s") >= cacheData[itemDataId].timeSpan;
                        helper.debug("dealWarningResult compareTimeFlag", compareTimeFlag, "diff s",moment(now).diff(firstData.time, "s"), "timeSpan", cacheData[itemDataId].timeSpan);
                        
                        // 删除超时的缓存数据，会保留超时时间最近的一条记录
                        cacheData[itemDataId].data = filterOlderData(cacheData[itemDataId].data, now, cacheData[itemDataId].timeSpan);
                    } else {
                        cacheData[itemDataId] = {
                            timeSpan: wTimeSpan,
                            data: [
                                {
                                    time: now,
                                    val: val,
                                }
                            ]
                        }
                    }
                    // helper.debug("dealWarningResult cacheData", cacheData[itemDataId]);
                    // 拿出符合时间区间的数据
                    let dataList = cacheData[itemDataId].data.filter(d=> {
                        return moment(now).diff(moment(d.time), "s") <= wTimeSpan;
                    });

                    // 获取区间数据
                    let maxVal = -99999999999999;
                    let minVal = 99999999999999;
                    let sumVal = 0;
                    let count = 0;
                    let realTimeSpan = 0;
                    dataList.map(d => {
                        maxVal = d.val > maxVal ? d.val : maxVal;
                        minVal = d.val < minVal ? d.val : minVal;
                        sumVal += d.val;
                        count += 1;
                        realTimeSpan = moment(d.time).diff(moment(dataList[0].time), "s");
                    });

                    helper.debug("dealWarningResult maxVal", maxVal, "minVal", minVal, "sumVal", sumVal, "count", count, "realTimeSpan", realTimeSpan, "compareTimeFlag", compareTimeFlag);
                    // 时间区间内数据变化率, 针对只会增长的数据. eg: 用电度数
                    if(wFunc == "diffValByTimeSpan") {
                        // 默认值
                        let v = 0;
                        if(dataList.length > 1) {
                            // 计算当前时间段的变化率
                            v = (maxVal - minVal) / moment(dataList[dataList.length - 1].time).diff(dataList[0].time, "s");
                        }
                        // 计算总平均值
                        compareStr = v+wCompare+wVal;
                        compareArr = JSON.stringify([v,wCompare,wVal]);
                    }
                    // 平均值
                    if(wFunc == "avg") {
                        // 默认值
                        let v = 0;
                        if(dataList.length > 1) {
                          // 平均值
                          v = sumVal / count;
                        }
                        // 计算总平均值
                        compareStr = v+wCompare+wVal;
                        compareArr = JSON.stringify([v,wCompare,wVal]);
                    }
                    // 最小值
                    if(wFunc == "min") {
                        // 取最小值
                        let v = minVal;
                        // 计算最小值
                        compareStr = v+wCompare+wVal;
                        compareArr = JSON.stringify([v,wCompare,wVal]);
                    }
                    // 最大值
                    if(wFunc == "max") {
                        // 取最大值
                        let v = maxVal;
                        // 计算最大值
                        compareStr = v+wCompare+wVal;
                        compareArr = JSON.stringify([v,wCompare,wVal]);
                    }
                // 单次数据判定
                } else {
                    compareStr = val+wCompare+wVal;
                    compareArr = JSON.stringify([val,wCompare,wVal]);
                }
                helper.debug("dealWarningResult ------- compareStr ", compareStr, "compareTimeFlag", compareTimeFlag, rule);
                if(eval(compareStr) && compareTimeFlag) {
                    if(typeof cacheWarning[itemId+wKey] == "undefined") {
                        helper.info(itemId, compareStr, curPoint.data_name, rule);
                        cacheWarning[itemId+wKey] = 1;
                    }
                    // 尝试报警
                    await saveWarning({
                        item_id: itemId,
                        item_data_id: itemDataId,
                        compare: compareArr,
                        warning_category: rule.wName,
                        severity: rule.wSeverity,
                        err_msg: rule.wErrMsg,
                        solution_ref: rule.wSolutionRef,
                        reported_at: now,
                        rule_id: rule.ruleId
                    });
                } else {
                    // 尝试修复
                    await saveWarning({
                        item_id: itemId,
                        item_data_id: itemDataId,
                        warning_category: rule.wName,
                        has_fixed: "Y",
                        reported_at: now,
                        compare: compareArr,
                        severity: rule.wSeverity,
                        rule_id: rule.ruleId
                    });
                }
            }
        }
    }
  } catch(e) {
    console.trace(e);
  }
}

// 删除超时的缓存数据，会保留超时时间最近的一条记录
function filterOlderData(dataList, now, timeSpan) {
  let tmp = [];
  let hasNearestRecord = false;
  for(let i=dataList.length-1; i>=0; i--) {
    let d = dataList[i];
    // 时间范围内记录
    if(moment(now).diff(moment(d.time), "s") <= timeSpan ) {
      tmp.unshift(d);
    }
    // 超时最近一条记录
    if(moment(now).diff(moment(d.time), "s") > timeSpan && !hasNearestRecord) {
      tmp.unshift(d);
      hasNearestRecord = true;
    }
  }
  return tmp;
}

// 保存报警数据到db
async function saveWarning(data) {
    helper.debug(data.has_fixed == "Y" ? "[fix" : "[add", "warning]", data.rule_id, data);
    let w = await _db.doSql(warningSql["check"], [data.item_id, data.warning_category]);
    let curRow = null;
    if(w && w.length > 0) {
        curRow = w[0];
    }
    if (data.has_fixed == "Y") {
        // 修复报警记录
        if(curRow) {
            // 修复报警 并将修复的比较 补充在后面
            await _db.doSql(warningSql["updateFix"], [data.reported_at, curRow.compare+';'+data.compare, curRow.id]);
            // 实时推送报警发生信息
            helper.sendWarningMsg(dbName, {
                type: "fixed",
                itemId: data.item_id,
                itemDataId: data.item_data_id,
                warningCategory: data.warning_category,
                severity: data.severity,
            });
        }
    } else {
        // 记录报警记录
        if (curRow) {
            // 更新最近一次的 compare 内容
            await _db.doSql(warningSql["updateLastData"], [data.reported_at, data.compare, curRow.id]);
            // 实时推送报警发生信息
            helper.sendWarningMsg(dbName, {
                type: "update",
                itemId: data.item_id,
                itemDataId: data.item_data_id,
                warningCategory: data.warning_category,
                severity: data.severity,
            });
        } else {
            await _db.doSql(warningSql["insert"], [data.item_id, data.item_data_id, data.compare, data.warning_category,
                                                   data.severity, data.err_msg, data.solution_ref,
                                                   data.reported_at]);
            // 实时推送报警发生信息
            helper.sendWarningMsg(dbName, {
                type: "new",
                itemId: data.item_id,
                itemDataId: data.item_data_id,
                warningCategory: data.warning_category,
                severity: data.severity,
            });
        }
    }
}

// 定时更新数据
// 当前时间比较 updated > 15*60 ? first 置为 updated ： 保持原 keep_second
async function syncLastData() {
  // let batchNum = 300;
  let batchList = [];
  for(let id in cacheLatestData) {
    let d = cacheLatestData[id];
    let val = cacheLatestData[id].hasOwnProperty("realVal") ? cacheLatestData[id].realVal : cacheLatestData[id].val;
    let cp = cacheLatestData[id].cp;
    let updated = cacheLatestData[id].updated;
    let hasUpdated = cacheLatestData[id].hasUpdated;
    let first = cacheLatestData[id].first;
    let keepSecond = 0;
    let realSeconds = moment().diff(moment(d.updated), "second");
    if(realSeconds > offLineSecond) {
      cacheLatestData[id].first = updated;
    } else {
      keepSecond = moment(d.updated).diff(moment(first), "second");
    }
    // helper.debug("syncLastData", cp, val, updated, first, realSeconds, keepSecond);
    // 防止多开的情况，多脚本会把旧点覆盖回去
    if(cp && cp.func == "read" && realSeconds <= 60) {
      // 批量更新数据点位
      if((cp.max_val == "" || (cp.max_val != "" && parseFloat(val) < parseFloat(cp.max_val)))
        && (cp.min_val == "" || (cp.min_val != "" && parseFloat(val) > parseFloat(cp.min_val))) ) {
        if(batchList.length < batchNum) {
          batchList.push({id: cp.item_data_id, val: val, updated: updated, keepSecond: keepSecond});
        } else {
          // 批量更新数据点位
          await updateDataBatch(batchList);
          helper.debug("syncLastData [update db batch]", batchList.map(d => d.id));
          batchList = [];
        }
      }
    }
  }
  // 更新最后一批数据
  if(batchList.length > 0) {
    // 批量更新数据点位
    await updateDataBatch(batchList);
    helper.debug("syncLastData [update db batch]", batchList.map(d => d.id));
  }
  helper.flushInfluxdb();
}

// 批量更新数据
async function updateDataBatch(list) {
  const caseValues = list.map(d => `WHEN id = ${d.id} THEN '${d.val}'`).join(' ');
  const caseUpdatedAts = list.map(d => `WHEN id = ${d.id} THEN '${d.updated}'`).join(' ');
  const caseKeepSecondAts = list.map(d => `WHEN id = ${d.id} THEN '${d.keepSecond}'`).join(' ');
  const sql = `
    UPDATE a_item_data
    SET val = CASE
      ${caseValues}
    END,
    updated_at = CASE
      ${caseUpdatedAts}
    END,
    keep_second = CASE
      ${caseKeepSecondAts}
    END
    WHERE id IN (${list.map(update => update.id).join(', ')});
  `;
  helper.debug("updateDataBatch sql", sql);
  try {
    let res = await _db.doSql(sql);
    helper.debug("updateDataBatch sql res", res);
    return res;
  } catch(e) {
    helper.debug("updateDataBatch sql error", e.message);
    helper.debug("updateDataBatch sql error", sql);
  }
}


async function start() {
    // 30秒执行一次
    schedule.scheduleJob('*/30 * * * * *',()=>{
        syncLastData();
        //helper.log('syncLightDataToBeacool success');
    });
    // 5分钟执行一次
    schedule.scheduleJob('1 */5 * * * *',()=>{
        updateWarningDatas();
        // helper.log('updateWarningDatas success');
    });
    // 1小时重启一次
    schedule.scheduleJob('0 1 * * * *',()=>{
        helper.log('Auto restart server');
        process.exit(1);
    });

    // 启动监听服务
    app = new AppServer(sysConfig);

    await connectRedis(sysConfig);
    helper.info("redis connect success", sysConfig.redis);

    // mqtt连接，系统互通各种消息通道
    helper.connectMqtt("mqtt");
    helper.info("connectMqtt success");

    // 日志服务连接至远程logstash
    helper.connectLogstash(sysConfig);
    helper.info("connectLogstash success");

    // influxdb初始化，记录实时数据点
    helper.initInfluxdb("influxdb");
    helper.info("init Influxdb success");

    await updateCaches(); // 初始化的时候从db缓存所有数据
    await updateWarningDatas();

    helper.info("start success");
};

if(dbName) {
  start();
}

module.exports.dealPostData = dealPostData;
module.exports.dealWarningResult = dealWarningResult;
