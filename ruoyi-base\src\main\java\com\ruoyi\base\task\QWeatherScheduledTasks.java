package com.ruoyi.base.task;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.base.domain.Building;
import com.ruoyi.base.domain.WeatherData;
import com.ruoyi.base.domain.po.MoJiWeatherPO;
import com.ruoyi.base.domain.po.QWeatherPO;
import com.ruoyi.base.mapper.BuildingMapper;
import com.ruoyi.base.mapper.UtilMapper;
import com.ruoyi.base.mapper.WeatherDataMapper;
import com.ruoyi.base.service.IQWeatherService;
import com.ruoyi.base.service.IUtilService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.Format;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
public class QWeatherScheduledTasks {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    private final Format FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Resource
    BuildingMapper buildingMapper;

    @Resource
    private WeatherDataMapper weatherDataMapper;

    @Resource
    private IUtilService utilService;

    @Resource
    private IQWeatherService iqWeatherService;

    @Value("${quartz.switch:true}")
    private boolean taskScheduling;
    // 报警定时任务开关
    @Value("${qweather.enabled:false}")
    private boolean weatherScheduling;

    //    @Scheduled(cron = "0 30 02,08,14,20 * * ?")
    @Scheduled(cron = "0 0 * * * ?")
    @Async("myThreadPoolTaskExecutor")
    public void updateWeatherTask(){
        if(taskScheduling && weatherScheduling) {
            Date current = new Date();
            logger.debug(MessageFormat.format("开始执行天气定时更新任务，Date：{0}", FORMAT.format(current)));
            List<Building> buildingList = buildingMapper.selectBuildingList(null);
            if (!buildingList.isEmpty()) {
                buildingList.forEach(building -> {
                    String cityId = building.getCityId();
                    updateWeatherData(cityId, "zh");
                });
            }
        }
    }

    //  24 小时 天气预报数据，更新到字典去， 每天6点更新一次
//    @Scheduled(cron = "0 0 06,18 * * ?")
    @Scheduled(cron = "0 0 * * * ?")
    @Async("myThreadPoolTaskExecutor")
    public void updateWeather72hTask(){
        if(taskScheduling && weatherScheduling) {
            Date current = new Date();
            logger.debug(MessageFormat.format("开始执行天气定时更新任务，Date：{0}", FORMAT.format(current)));
            List<Building> buildingList = buildingMapper.selectBuildingList(null);
            if (!buildingList.isEmpty()) {
                buildingList.forEach(building -> {
                    String cityId = building.getCityId();
                    updateWeather24hData(cityId, "zh", String.valueOf(building.getId()));
                });
            }
        }
    }

    public void updateWeatherData(String weatherId, String lang) {
        JSONObject weatherData = iqWeatherService.getNowWeather(weatherId, lang);
        handlerHourWeather(weatherId, weatherData);
    }

    private void handlerHourWeather(String weatherId, JSONObject weatherData) {
        QWeatherPO po = JSONUtil.toBean(weatherData, QWeatherPO.class);
        WeatherData wd = new WeatherData();
        wd.setTemp(po.getTemp());
        wd.setHumidity(po.getHumidity());
        wd.setCondition(po.getText());
        wd.setIcon(po.getIcon());
        wd.setRealFeel(po.getFeelsLike());  // 体感温度
        wd.setPressure(po.getPressure());
        wd.setWind360(po.getWind360());
        wd.setWindDirection(po.getWindDir());
        wd.setWindSpeed(po.getWindSpeed());
        wd.setWindScale(po.getWindScale());
        wd.setPrecip(po.getPrecip());
        wd.setCloud(po.getCloud());
        wd.setVis(po.getVis());
        wd.setDew(po.getDew());
        wd.setWeatherId(weatherId);
        wd.setUpdateTime(new Date());
        wd.setCreateTime(new Date());
        weatherDataMapper.insertWeatherData(wd);
    }

    // 24 小时预报数据
    public void updateWeather24hData(String weatherId, String lang, String buildingId) {
        JSONObject weatherData = iqWeatherService.getWeather24h(weatherId, lang);
        utilService.updateWeather24hData(buildingId, JSONUtil.toJsonStr(weatherData));
    }

}
