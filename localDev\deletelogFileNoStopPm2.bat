@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion
set "folder=D:\soft\logs"

set count=0

echo 正在扫描 %folder% 下的 .txt 和 .log 文件...

:: 使用空文件替换然后删除的方法处理大文件
for /r "%folder%" %%f in (*.txt *.log) do (
    echo 正在处理: "%%f"
    :: 获取文件大小
    for %%i in ("%%f") do set "size=%%~zi"

    :: 如果文件大于2GB(2147483648字节)，使用特殊方法
    if !size! gtr 2147483648 (
        echo 检测到大文件^!大小: !size! 字节^!，使用特殊方法删除...
        :: 创建空文件并替换原文件
        type nul > "%%f"
        :: 等待文件系统更新
        timeout /t 1 > nul
    )

    :: 删除文件
    del /f /q "%%f" && (
        set /a count+=1
        echo 已删除: "%%f"
    ) || (
        echo 删除失败: "%%f" - 尝试强制删除...
        :: 使用cacls获取所有权再尝试删除
        takeown /f "%%f" > nul
        cacls "%%f" /g administrators:f > nul
        del /f /q "%%f" && (
            set /a count+=1
            echo 已强制删除: "%%f"
        ) || (
            echo 仍然无法删除: "%%f" - 请手动检查
        )
    )
)

if %count% equ 0 (
    echo 没有找到 .txt 或 .log 文件可删除。
) else (
    echo 已删除 %count% 个 .txt 或 .log 文件。
)

echo 检查 pm2 服务状态...
start /B cmd /c "cd/d D:/soft &&pm2 status"

echo 日志清理和 pm2 重启完成！

endlocal

exit
