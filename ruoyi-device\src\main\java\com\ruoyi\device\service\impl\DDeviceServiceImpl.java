package com.ruoyi.device.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.SQLIntegrityConstraintViolationException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.druid.support.json.JSONUtils;
import com.alibaba.fastjson.JSON;
import com.influxdb.query.FluxRecord;
import com.ruoyi.base.service.IDingTalkService;
import com.ruoyi.base.service.IEmailService;
import com.ruoyi.base.service.IInfluxDbService;
import com.ruoyi.base.service.ISmsService;
import com.ruoyi.base.utils.EnergyConstant;
import com.ruoyi.base.mapper.UtilMapper;
import com.ruoyi.common.config.InfluxDBConfig;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.device.domain.DDeviceMaintenance;
import com.ruoyi.device.domain.vo.DeviceOperLog;
import com.ruoyi.device.domain.vo.DeviceWarning;
import com.ruoyi.device.mapper.DDeviceMaintenanceMapper;
import com.ruoyi.device.service.DeviceStatusUpdateService;
import com.ruoyi.device.service.IDDeviceItemDataMapService;
import io.jsonwebtoken.Header;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.ruoyi.device.mapper.DDeviceMapper;
import com.ruoyi.device.domain.DDevice;
import com.ruoyi.device.service.IDDeviceService;

/**
 * 设备Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-11-11
 */
@Service
public class DDeviceServiceImpl implements IDDeviceService
{
    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private DDeviceMapper dDeviceMapper;

    @Autowired
    private DDeviceMaintenanceMapper dDeviceMaintenanceMapper;

    @Autowired
    private DeviceStatusUpdateService deviceStatusUpdateService;

    @Autowired
    IDingTalkService dingTalkService;

    @Autowired
    ISmsService smsService;

    @Autowired
    IEmailService emailService;

    @Autowired
    UtilMapper utilMapper;

    // 当前dbName
    @Value("${spring.datasource.druid.master.db:ibms}")
    private String dbName;
    // 需要分表的表前缀
    String preTableName = "d_device_data_history_";

    @Autowired
    private InfluxDBConfig influxDBConfig;
    @Autowired
    private IInfluxDbService influxDbService;
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询设备
     *
     * @param id 设备ID
     * @return 设备
     */
    @Override
    public DDevice selectDDeviceById(Long id)
    {
        return dDeviceMapper.selectDDeviceById(id);
    }

    /**
     * 查询设备列表
     *
     * @param dDevice 设备
     * @return 设备
     */
    @Override
    public List<DDevice> selectDDeviceList(DDevice dDevice)
    {
        return dDeviceMapper.selectDDeviceList(dDevice);
    }

    /**
     * 新增设备
     *
     * @param dDevice 设备
     * @return 结果
     */
    @Override
    public int insertDDevice(DDevice dDevice)
    {
        return dDeviceMapper.insertDDevice(dDevice);
    }

    /**
     * 修改设备
     *
     * @param dDevice 设备
     * @return 结果
     */
    @Override
    public int updateDDevice(DDevice dDevice)
    {
        return dDeviceMapper.updateDDevice(dDevice);
    }

    /**
     * 批量删除设备
     *
     * @param ids 需要删除的设备ID
     * @return 结果
     */
    @Override
    public int deleteDDeviceByIds(Long[] ids)
    {
        return dDeviceMapper.deleteDDeviceByIds(ids);
    }

    @Override
    public List<Map> getResourceList(List<String> idList) {
        return dDeviceMapper.getResourceList(idList);
    }

    /**
     * 删除设备信息
     *
     * @param id 设备ID
     * @return 结果
     */
    @Override
    public int deleteDDeviceById(Long id)
    {
        return dDeviceMapper.deleteDDeviceById(id);
    }

    // 资源下所有设备列表
    @Override
    public List<Map> deviceList(String resourceId, List<String> deviceTypeList, String orderBy, String fromCache) {
        List<Map> list = dDeviceMapper.resourceDeviceIdList(resourceId, deviceTypeList, orderBy);
        List res = new ArrayList();
        logger.debug("res resourceId " + resourceId + " s -- " + new Date().getTime());
        if(list.size() > 0) {
            for(Map itemId : list) {
                Long deviceId = Long.valueOf(itemId.get("id").toString());
                logger.debug("resourceId " + resourceId + " deviceId " + deviceId + " s -- " + new Date().getTime());
                Map item = deviceDetail(resourceId, deviceId, fromCache);
                res.add(item);
                logger.debug("resourceId " + resourceId + " deviceId " + deviceId + " e -- " + new Date().getTime());
            }
        }
        logger.debug("res resourceId " + resourceId + " e -- " + new Date().getTime());
        return res;
    }

    // 建筑下所有设备列表
    @Override
    public List<Map> buildingDeviceList(Integer buildingId, String category, String type,
                                        String deviceType, String deviceDataName, String deviceStatus,
                                        List<String> deviceTags, String fromCache) {
        List<Map> list = dDeviceMapper.buildingDeviceIdList(buildingId, category, type, deviceType,
                deviceDataName, deviceStatus, deviceTags);
        if(list.size() > 0) {
            List res = new ArrayList();
            if(list.size() > 0) {
                for(Map itemId : list) {
                    Long deviceId = Long.valueOf(itemId.get("id").toString());
                    String resourceId = itemId.get("resourceId").toString();
                    Map item = deviceDetail(resourceId, deviceId, fromCache);
                    res.add(item);
                }
            }
            return res;
        } else {
            return new ArrayList<>();
        }
    }

    // 建筑下所由设备（不带设备详情信息，包含未绑定资源设备）
    @Override
    public List<Map> buildingDeviceFullList(Integer buildingId, List<String> types, String name,
                                            String workStatus, String warningStatus, String communicateStatus,
                                            String status, List<String> tags, String shelfTime) {
        List<Map> list = dDeviceMapper.buildingDeviceList(buildingId, types, name, workStatus,
                warningStatus, communicateStatus, status, tags, shelfTime);
        if(list.size() > 0) {
            return list;
        } else {
            return new ArrayList<>();
        }
    }

    // 建筑下所有绑定资源的设备列表(基础数据)
    @Override
    public List<Map> buildingResourceDeviceFullList(Integer buildingId, String category, String type,
                                                    String deviceType, String deviceStatus,
                                                    List<String> deviceTags) {
        List<Map> list = dDeviceMapper.buildingResourceDeviceFullList(buildingId, category,
                type, deviceType, deviceStatus, deviceTags);
        if(list.size() > 0) {
            return list;
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    // 批量根据设备Id，获取设备详情
    public List<Map> getDeviceIdByIdList(List<String> idList, String fromCache) {
        List res = new ArrayList();
        if(null != idList && idList.size() > 0) {
            for(String id : idList) {
                Map device = deviceDetail(null, Long.valueOf(id), fromCache);
                res.add(device);
            }
        }
        return res;
    }

    // 更新设备资源绑定关系
    @Override
    public void updateResourceDevice(String resourceId, String deviceType, List<String>deviceIdList) {
        if(StringUtils.isNotEmpty(resourceId)) {
            List<Map> list = dDeviceMapper.resourceDeviceIdList(resourceId, new ArrayList<>(Arrays.asList(deviceType.split(","))), null);
            if( null != list && list.size() > 0) {
                List<String> existIdList = list.stream()
                        .map(map -> map.get("id").toString())
                        .collect(Collectors.toList());
                if(null != deviceIdList && deviceIdList.size() > 0) {
                    // 获取存在的，需要删除的 id 列表
                    List<String> removeList = existIdList.stream().filter(id -> !deviceIdList.contains(id)).collect(Collectors.toList());
                    // 获取需要添加的 id 列表
                    List<String> addList = deviceIdList.stream().filter(id -> !existIdList.contains(id)).collect(Collectors.toList());
                    if(null != removeList && removeList.size() > 0) {
                        dDeviceMapper.deleteResourceDeviceMap(resourceId, removeList);
                    }
                    if(null != addList && addList.size() > 0 ) {
                        dDeviceMapper.insertRescueDeviceMap(resourceId, addList);
                    }
                } else {
                    dDeviceMapper.deleteResourceDeviceMap(resourceId, existIdList);
                }
            } else {
                if(null != deviceIdList && deviceIdList.size() > 0 ) {
                    dDeviceMapper.insertRescueDeviceMap(resourceId, deviceIdList);
                }
            }
        }
    }

    // 建筑下所有设备
    @Override
    public List<Map> buildingDeviceSummary(Integer buildingId, List<String> deviceTypeList,
                                           List<String> severityList, String hasFixed) {
        List<Map> list = dDeviceMapper.buildingDeviceSummary(buildingId, deviceTypeList, severityList, hasFixed);
        if(list.size() > 0) {
            return list;
        } else {
            return new ArrayList<>();
        }
    }


    // 建筑下设备种类和个数
    @Override
    public List<Map> buildingDeviceTypeList(Integer buildingId, String category, String type,
                                         String deviceType, String deviceStatus, List<String> deviceTags){
        List<Map> list = dDeviceMapper.buildingDeviceTypeList(buildingId, category, type, deviceType, deviceStatus, deviceTags);
        if(list.size() > 0) {
            return list;
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map> getDeviceIdByCode(List<String> deviceCodeList) {
        List<Map> list = new ArrayList<>();
        if(deviceCodeList != null && deviceCodeList.size() > 0) {
            list = dDeviceMapper.getDeviceIdByCode(deviceCodeList);
        }
        return list;
    }

    // 从缓存获取更新的数据并更新数据信息
    protected void updateDeviceDataFromCache(List<Map> deviceData) {
        // 获取缓存内的实时数据
        List<String> ids = deviceData.stream()
                .filter(map -> map.get("dId") != null)
                .map(map -> String.valueOf(map.get("dId")))
                .collect(Collectors.toList());
        List<String> idrs = deviceData.stream()
                .filter(map -> map.get("drId") != null)
                .map(map -> String.valueOf(map.get("drId")))
                .collect(Collectors.toList());
        // 合并所有id
        ids.addAll(idrs);
        List<String> keys = Arrays.asList("val,updated".split(","));
        Map<String, Map<String, String>> dataCache = redisCache.batchGetHashFields(ids, keys);

        if (null != deviceData && deviceData.size() > 0) {
            for (Map dd : deviceData) {
                // 用 Cache 信息替换 db 信息
                String dataId = dd.get("dId").toString();
                if (null != dataCache.get(dataId)
                        && null != dataCache.get(dataId).get("val")
                        && null != dataCache.get(dataId).get("updated")
                        && dataCache.get(dataId).get("updated").compareTo(dd.get("dUpdatedAt").toString()) >= 0) {
                    dd.put("dVal", dataCache.get(dataId).get("val"));
                    dd.put("dUpdatedAt", dataCache.get(dataId).get("updated"));
                }
                String dataRId = dd.get("drId").toString();
                if (null != dataCache.get(dataRId)
                        && null != dataCache.get(dataRId).get("val")
                        && null != dataCache.get(dataRId).get("updated")
                        && dataCache.get(dataRId).get("updated").compareTo(dd.get("drUpdatedAt").toString()) >= 0) {
                    dd.put("drVal", dataCache.get(dataRId).get("val"));
                    dd.put("drUpdatedAt", dataCache.get(dataRId).get("updated"));
                }
            }
        }
    }

    // 设备详情
    @Override
    public Map deviceDetail(String resourceId, Long deviceId, String fromCache) {
        Map item = dDeviceMapper.deviceBase(deviceId, resourceId);
        // 数据信息
        List<Map> deviceData = dDeviceMapper.deviceDataList(deviceId);

        if("1".equals(fromCache)) {
            // 从缓存获取更新的数据并更新数据信息
            updateDeviceDataFromCache(deviceData);
        }
        List<Map> deviceDataBase = new ArrayList<>();
        List<Map> deviceDataEnv = new ArrayList<>();
        List<Map> deviceDataWarn = new ArrayList<>();

        // 当前设备所关联的所有底层设备
        List<Integer> itemIds = new ArrayList<>();

        try {
            // 区分设备数据和环境数据

            // 健康度计算
            Integer runCount = null;
            Integer errorCount = null;
            Integer warnCount = null;
            Integer runHour = null;
            Integer maintanHour = 0;

            if (null != deviceData && deviceData.size() > 0) {
                for (Map dd : deviceData) {
                    String tag = dd.get("dmTag").toString();
                    if ("env".equals(tag)) {
                        deviceDataEnv.add(dd);
                    } else if ("warn".equals(tag)) {
                        deviceDataWarn.add(dd);
                    } else {
                        deviceDataBase.add(dd);
                        if (null != dd.get("dItemId") && StringUtils.isNotEmpty(dd.get("dItemId").toString())) {
                            itemIds.add(Integer.valueOf(dd.get("dItemId").toString()));
                        }
                    }

                    if(!StringUtils.isEmpty(tag) && tag.contains("runCount")) {
                        try {
                            runCount = Integer.valueOf(dd.get("dVal").toString());
                        } catch (Exception ex) {
                            // pass
                        }
                    }
                    if(!StringUtils.isEmpty(tag) && tag.contains("errorCount")) {
                        try {
                            errorCount = Integer.valueOf(dd.get("dVal").toString());
                        } catch (Exception ex) {
                            // pass
                        }
                    }
                    if(!StringUtils.isEmpty(tag) && tag.contains("runHour")) {
                        try {
                            runHour = Integer.valueOf(dd.get("dVal").toString());
                            // 只统计维修时间
                            List<String> typeList = new ArrayList<>();
                            typeList.add("紧急维修");
                            maintanHour = deviceMaintanHour(deviceId, typeList);
                        } catch (Exception ex) {
                            // pass
                        }
                    }

                }
            }
            item.put("deviceDataBase", deviceDataBase);
            item.put("deviceDataEnv", deviceDataEnv);
            item.put("deviceDataWarn", deviceDataWarn);

            // 计算设备健康度
            // 故障率=故障次数/运行次数
            if(null != runCount && null != errorCount) {
                item.put("runCount", runCount);
                item.put("errorCount", errorCount);
                if(runCount > 0) {
                    BigDecimal bd = new BigDecimal(100*errorCount/runCount);
                    bd = bd.setScale(2, RoundingMode.HALF_UP);
                    item.put("errorPercent", bd.toString());
                } else {
                    item.put("errorPercent", 0);
                }
            }
            // 维修率 = 维修时间/运行时间
            if(null != runHour) {
                item.put("runHour", runHour);
                item.put("maintanHour", maintanHour);
                if(runHour > 0) {
                    BigDecimal bd = new BigDecimal(100*maintanHour/runHour);
                    bd = bd.setScale(2, RoundingMode.HALF_UP);
                    item.put("maintanPercent", bd.toString());
                } else {
                    item.put("maintanPercent", 0);
                }
            }
            // 残值率=累计运行时间 / 正常使用寿命
            String serviceLife = item.get("serviceLife").toString();
            if(StringUtils.isNotEmpty(serviceLife)) {
                try {
                    Float serviceLifeHour = Float.valueOf(serviceLife) * 365 * 24;
                    item.put("serviceLifeHour", serviceLifeHour);
                    if(serviceLifeHour > 0) {
                        BigDecimal bd = new BigDecimal(100*runHour/serviceLifeHour);
                        bd = bd.setScale(2, RoundingMode.HALF_UP);
                        item.put("residualPercent", bd.toString());
                    } else {
                        item.put("residualPercent", 0);
                    }
                } catch (Exception ex) {
                    // pass
                }
            }
            // 健康度=1-（故障率*0.5+维修时间比率*0.3+残值率*0.2）
            if(null != item.get("errorPercent") && null != item.get("maintanPercent") && null != item.get("residualPercent")) {
                Float healthPercent = 100 - 0.5f * Float.valueOf(item.get("errorPercent").toString())
                                          - 0.3f * Float.valueOf(item.get("maintanPercent").toString())
                                          - 0.2f * Float.valueOf(item.get("residualPercent").toString());
                BigDecimal bd = new BigDecimal(healthPercent);
                bd = bd.setScale(2, RoundingMode.HALF_UP);
                item.put("healthPercent", bd.toString());
            } else {
                item.put("healthPercent", 100);
            }

            // 设备报警列表 从 item_rule 中获取
            List<Map> warningRuleList = new ArrayList<>();
            if (itemIds.size() > 0) {
                warningRuleList = dDeviceMapper.deviceWarningRuleList(itemIds);
            }
            item.put("warningRuleList", warningRuleList);

            // 设备故障信息
            // 默认显示未处理
//            Integer len = 5;
//            List<Map> warningList = new ArrayList<>();
//            if (itemIds.size() > 0) {
//                warningList = dDeviceMapper.deviceWarningList(itemIds, "N", "unread", len);
//            }
//            item.put("warningList", warningList);

            // 设备故障历史
            // 最近5条
//            List<Map> warningHistoryList = new ArrayList<>();
//            if (itemIds.size() > 0) {
//                warningHistoryList = dDeviceMapper.deviceWarningList(itemIds, null, null, len);
//            }
//            item.put("warningHistoryList", warningHistoryList);

            // 维保信息
//            List<Map> maintenanceList = new ArrayList<>();
//            if (itemIds.size() > 0) {
//                maintenanceList = dDeviceMapper.deviceMaintenanceList(deviceId, len);
//            }
//            item.put("maintenanceList", maintenanceList);

            // 设备绑定的策略信息
            List<Map> deviceStrategyList = dDeviceMapper.deviceStrategyList(deviceId.toString());
            item.put("strategyList", deviceStrategyList);
        } catch(Exception ex) {
            logger.warn("deviceDetail resourceId: " + resourceId + " deviceId: " + deviceId + " error: " + ex.getMessage());
        }
        return item;
    }

    public int deviceMaintanHour(Long deviceId, List typeList) {
        Map res = dDeviceMapper.deviceMaintanHour(deviceId, typeList);
        if(null == res) {
            return 0;
        }
        return Integer.valueOf(res.get("diffHours").toString());
    }

    @Override
    public Map deviceItemDataList(Long deviceId, Long itemDataId, String from, String to, String limit,
                                  String displayType, String agg) {
        Map dataInfo = dDeviceMapper.deviceItemDataInfo(deviceId, itemDataId);
        Map dataColInfo = dDeviceMapper.deviceItemDataInfoWithCol(itemDataId);
        List<Map> list = new ArrayList<>();
        if(null != dataInfo ) {
            Object collectId = dataColInfo.get("collectorId");
            if(influxDBConfig.isEnable()) {
                if(null != dataColInfo && ObjectUtil.isNotEmpty(collectId)) {
                    list = deviceItemDataHistoryByInfluxdb(Long.valueOf(collectId.toString()), itemDataId, from, to, limit, displayType, agg);
                }
            } else {
                list = deviceItemDataHistoryBySql(deviceId, itemDataId, from, to, limit, displayType, agg);
            }
        }
        if(null != dataInfo) {
            if(null != list && list.size() > 0) {
                dataInfo.put("data", list);
            } else {
                dataInfo.put("data", new ArrayList<>());
            }
        }
        return dataInfo;
    }

    // 设备点位历史数据
    @Override
    public List deviceItemDataHistoryBySql(Long deviceId, Long itemDataId, String from, String to, String limit,
                                           String displayType, String agg) {
        List<Map> list = new ArrayList<>();
        if(StringUtils.isEmpty(from) && StringUtils.isEmpty(to) && StringUtils.isEmpty(limit)) {
            limit = "200";
        }
        if (null != displayType && null != agg) {
            if(displayType.equals(EnergyConstant.BY_5)
                || displayType.equals(EnergyConstant.BY_15)
                || displayType.equals(EnergyConstant.BY_30)) {
                list = dDeviceMapper.deviceItemDataListAgg(deviceId, itemDataId, from, to, limit, displayType, agg);
            } else if(displayType.equals(EnergyConstant.BY_HOUR)) {
                list = dDeviceMapper.deviceItemDataListAggByHour(deviceId, itemDataId, from, to, limit, displayType, agg);
            } else if(displayType.equals(EnergyConstant.BY_DAY)) {
                list = dDeviceMapper.deviceItemDataListAggByDay(deviceId, itemDataId, from, to, limit, displayType, agg);
            } else if(displayType.equals(EnergyConstant.BY_MONTH)) {
                list = dDeviceMapper.deviceItemDataListAggByMonth(deviceId, itemDataId, from, to, limit, displayType, agg);
            } else if(displayType.equals(EnergyConstant.BY_YEAR)) {
                list = dDeviceMapper.deviceItemDataListAggByYear(deviceId, itemDataId, from, to, limit, displayType, agg);
            }
        } else {
            // 没有时间范围， 只看 limit 的情况
            if(StringUtils.isEmpty(from) && StringUtils.isEmpty(to)) {
                list = dDeviceMapper.deviceItemDataList(deviceId, itemDataId, from, to, limit);
            } else {
                //得到当前表的所有分表
                String tableNameFrom = getDeviceHistoryTableNameByDate(preTableName, from);
                String tableNameTo = getDeviceHistoryTableNameByDate(preTableName, to);
                if(null != tableNameFrom && tableNameFrom.equals(tableNameTo)) {
                    list = dDeviceMapper.deviceItemDataListShard(deviceId, itemDataId, from, to, limit, tableNameFrom, null);
                } else {
                    list = dDeviceMapper.deviceItemDataListShard(deviceId, itemDataId, from, to, limit, tableNameFrom, tableNameTo);
                }
            }
        }
        return list;
    }

    @Override
    public List deviceItemDataHistoryByInfluxdb(Long collectId, Long itemDataId, String from, String to, String limit,
                                                String displayType, String agg) {
        // 1. 查询 from to 时间格式需要转换，且需要转本地时间
        // 2。 返回的列表，需要把时间转换成本地时间
        List<Map> res = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSX");
        DateTimeFormatter formatterRet = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        try {
            // 格式化查询条件
            StringBuilder sb = new StringBuilder();
            sb.append("from(bucket: \"" + influxDBConfig.getBucket() + "\") ");
            if(StringUtils.isNotEmpty(from)) {
                LocalDateTime fromTime = LocalDateTime.parse(from, formatterRet);
                ZonedDateTime zonedDateTime = fromTime.atZone(ZoneId.systemDefault());
                ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneOffset.UTC);
                String fromUtc = utcDateTime.format(formatter);
                sb.append("|> range(start: " + fromUtc);
            } else {
                sb.append("|> range(start: -24h ");
            }
            if(StringUtils.isNotEmpty(to)) {
                LocalDateTime toTime = LocalDateTime.parse(to, formatterRet);
                ZonedDateTime zonedDateTime = toTime.atZone(ZoneId.systemDefault());
                ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneOffset.UTC);
                String toUtc = utcDateTime.format(formatter);
                sb.append(", stop: " + toUtc + ")");
            } else {
                sb.append(")");
            }
            sb.append("|> filter(fn: (r) => r._measurement == \"" + collectId + "\")");
            sb.append("|> filter(fn: (r) => r._field == \"" + itemDataId + "\")");
            if(StringUtils.isNotEmpty(displayType) && StringUtils.isNotEmpty(agg)) {
                String duration = "";
                String fn = "";
                if(displayType.equals(EnergyConstant.BY_5)
                        || displayType.equals(EnergyConstant.BY_15)
                        || displayType.equals(EnergyConstant.BY_30)) {
                    duration = displayType+"m";
                } else if(displayType.equals(EnergyConstant.BY_HOUR)) {
                    duration = "1h";
                } else if(displayType.equals(EnergyConstant.BY_DAY)) {
                    duration = "1d";
                } else if(displayType.equals(EnergyConstant.BY_MONTH)) {
                    duration = "1m";
                } else if(displayType.equals(EnergyConstant.BY_YEAR)) {
                    duration = "1y";
                }
                if("avg".equals(agg)) {
                    fn = "mean";
                } else if("max".equals(agg)) {
                    fn = "max";
                } else if("min".equals(agg)) {
                    fn = "min";
                }
                if(StringUtils.isNotEmpty(duration)) {
                    sb.append("|> aggregateWindow(every: " + duration + ", ");
                    if(StringUtils.isNotEmpty(fn)) {
                        sb.append(" fn: " + fn + ", createEmpty: false)");
                    } else {
                        sb.append(", createEmpty: false)");
                    }
                }
            }
            if(StringUtils.isNotEmpty(limit)) {
                sb.append("|> limit(n: " + limit + ", offset: 0)");
            }
            sb.append("|> yield( name: \"dataList\" )");
            // sb.append("|> aggregateWindow(every: v.windowPeriod, fn: mean, createEmpty: false)");
            String query = sb.toString();
            List<FluxRecord> resList = influxDbService.readRecordList(query, influxDBConfig.getOrg());
            // 格式转换
            Map<String, List<FluxRecord>> mergedData = resList.stream()
                    .collect(Collectors.groupingBy(
                            m -> m.getMeasurement(),
                            Collectors.toList()
                    ));
            for (Map.Entry<String, List<FluxRecord>> entry : mergedData.entrySet()) {
                String measurement = entry.getKey();
                List<FluxRecord> records = entry.getValue();
                for (FluxRecord record : records) {
                    Instant timeInst = record.getTime();
                    LocalDateTime localTime = LocalDateTime.ofInstant(timeInst, ZoneId.systemDefault());
                    String timeLocal = localTime.format(formatterRet);
                    Map point = new HashMap<>();
                    point.put("indication", record.getValue());
                    point.put("recordedAt", timeLocal);
                    res.add(point);
                }
            }
        } catch (Exception e) {
            logger.warn("deviceItemDataHistoryByInfluxdb error."
                    + " collectId:" + collectId
                    + " itemDataId:" + itemDataId
                    + " from:" + from
                    + " to:" + to
                    + " limit:" + limit
                    + " displayType:" + displayType
                    + " agg:" + agg
                    + " error:" + e.getMessage());
        }
        return res;
    }

    // 批量查询设备数据
    @Override
    public List<Map> getDeviceDataList(List<String> deviceIdList, String dataName, String fromCache) {
        List<Map> deviceData = dDeviceMapper.devicesDataList(deviceIdList, dataName);
        if("1".equals(fromCache)) {
            updateDeviceDataFromCache(deviceData);
        }
        return deviceData;
    }

    // 通过数据Id 批量查询数据
    @Override
    public List<Map> getDeviceDataListByIdList(List<String> deviceDataIdList, String fromCache) {
        List<Map> deviceData = dDeviceMapper.deviceDataListByIdList(deviceDataIdList);
        if("1".equals(fromCache)) {
            updateDeviceDataFromCache(deviceData);
        }
        return deviceData;
    }

    // 更新设备数据值
    @Override
    public void updateDeviceDataList(List<Map> dataList) {
        for(Map item : dataList) {
            String id = item.get("id").toString();
            String deviceId = item.get("deviceId").toString();
            String dataName = item.get("dataName").toString();
            String val = item.get("val").toString();
            Map oldItem = null;
            if(StringUtils.isNotEmpty(id)) {
                oldItem = dDeviceMapper.getItemDataById(Long.valueOf(id));
            } else if(StringUtils.isNotEmpty(deviceId) && StringUtils.isNotEmpty(dataName)) {
                oldItem = dDeviceMapper.getItemDataByDeviceIdAndDataName(deviceId, dataName);
            }
            try {
                if(null != oldItem) {
                    id = oldItem.get("id").toString(); // 容错，方式只有 deviceId 和 dataName 的情况
                    dDeviceMapper.updateDeviceData(id, val);
                    // 插入操作记录
                    Map res = new HashMap();
                    res.put("deviceId", oldItem.get("deviceId"));
                    res.put("deviceName", oldItem.get("deviceName"));
                    res.put("dataId", id);
                    res.put("dataName", oldItem.get("dataName"));
                    res.put("dataValueBefore", oldItem.get("val"));
                    res.put("dataValueAfter", val);
                    res.put("dataNote", oldItem.get("dataNote"));
                    dDeviceMapper.insertDeviceOperLog(oldItem.get("deviceId").toString(), oldItem.get("deviceType").toString(),
                            "设备操作", "2", "com.ruoyi.device.service.updateDeviceDataList()",
                            "USER_OPERATE", "1", SecurityUtils.getLoginUser().getUser().getNickName(),
                            JSON.toJSONString(item), JSON.toJSONString(res), "0");
                    // 添加一个更新设备状态的任务
                    deviceStatusUpdateService.setDevice(Long.valueOf(oldItem.get("deviceId").toString()));
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                logger.warn("updateDeviceDataList " + oldItem.get("deviceId") + " error: " + ex.getMessage());
            }
        }
    }

    // 批量更新设备数据点数据
    public void updateDeviceDataBatch(List<String> deviceIdList, String dataName, String dataValue, String forceUpdate, String logTag) {
        Map params = new HashMap();
        params.put("deviceIdList", deviceIdList);
        params.put("dataName", dataName);
        params.put("dataValue", dataValue);
        params.put("forceUpdate", forceUpdate);

        List<Map> dataList = new ArrayList<>();
        if(StringUtils.isNotEmpty(forceUpdate) && "1".equals(forceUpdate)) {
            dataList = dDeviceMapper.deviceEffectItemDataList(deviceIdList, dataName, null);
        } else {
            dataList = dDeviceMapper.deviceEffectItemDataList(deviceIdList, dataName, dataValue);
        }

        String userName = SecurityUtils.getLoginUser().getUser().getNickName();
        int i = 0;
        if(null != dataList && dataList.size() > 0) {
            for(Map item : dataList) {
                try {
                    dDeviceMapper.updateDeviceData(item.get("dataItemId").toString(), dataValue);
                    // 插入操作记录
                    Map res = new HashMap();
                    res.put("id", item.get("dataItemId").toString());
                    res.put("val", dataValue);
                    res.put("deviceId", item.get("deviceId"));
                    res.put("deviceName", item.get("deviceName"));
                    res.put("dataId", item.get("dataItemId"));
                    res.put("dataName", item.get("dataName"));
                    res.put("dataValueBefore", item.get("dataItemVal"));
                    res.put("dataValueAfter", dataValue);
                    res.put("dataNote", item.get("dataNote"));
                    dDeviceMapper.insertDeviceOperLog(item.get("deviceId").toString(), item.get("deviceType").toString(),
                            logTag, "2", "com.ruoyi.device.service.updateDeviceDataBatch()",
                            "USER_OPERATE", "1", userName,
                            JSON.toJSONString(params), JSON.toJSONString(res), "0");
                    i += 1;
                    logger.debug("updateDeviceDataBatch success. ind: " + i + " deviceId:" + item.get("deviceId"));
                    // 添加一个更新设备状态的任务
                    // deviceStatusUpdateService.setDevice(Long.valueOf(item.get("deviceId").toString()));
                } catch (Exception ex) {
                    Throwable cause = ex.getCause();
                    if (cause != null) { // The type of this exception is determined at runtime
                        cause = cause.getCause();
                        if (cause instanceof SQLIntegrityConstraintViolationException) {
                            // pass
                        }
                    }
                    logger.warn("updateDeviceDataBatch " + item.get("deviceId") + " error: " + ex.getMessage());
                }
            }
        }
    }

    // 所有设备故障
    @Override
    public List<DeviceWarning> deviceWarningList(Integer buildingId, List<String>deviceIds, List<String>deviceTypes, List<String>severityList,
                                       String hasFixed, String flowStatus, String orderBy) {
        List<DeviceWarning> list = dDeviceMapper.buildingDeviceWarningList(buildingId, deviceIds, deviceTypes, severityList, hasFixed, flowStatus, orderBy);
        return list;
    }

    // 单个设备故障历史
    @Override
    public List<Map> deviceWarningHistory(Integer deviceId, String hasFixed, String flowStatus, String orderBy) {
        // 设备故障历史
        List<Map> warningHistoryList = dDeviceMapper.deviceWarningList2(deviceId, hasFixed, flowStatus, null, orderBy, null);
        return null != warningHistoryList ? warningHistoryList : new ArrayList<>();
//        List<Map> warningHistoryList = new ArrayList<>();
//        try {
//            List<Map> deviceData = dDeviceMapper.deviceDataList(deviceId);
//            // 当前设备所关联的所有底层设备
//            List<Integer> itemIds = new ArrayList<>();
//            // 区分设备数据和环境数据
//            if (null != deviceData && deviceData.size() > 0) {
//                for (Map dd : deviceData) {
//                    if (null != dd.get("dItemId") && StringUtils.isNotEmpty(dd.get("dItemId").toString())) {
//                        itemIds.add(Integer.valueOf(dd.get("dItemId").toString()));
//                    }
//                }
//            }
//            if (itemIds.size() > 0) {
//                warningHistoryList = dDeviceMapper.deviceWarningList(itemIds, hasFixed, flowStatus, null, orderBy, null);
//            }
//        } catch (Exception ex) {
//            logger.error("deviceWarningHistory error ", deviceId, hasFixed, flowStatus, orderBy,ex.getMessage());
//        }
//            return warningHistoryList;
    }

    // 设备报警数据详情
    @Override
    public Map deviceWarningDetail(Integer deviceId, Integer itemWarningId) {
        return dDeviceMapper.deviceWarningDetail(deviceId, itemWarningId);
    }

    // 更新设备故障
    @Override
    public void updateDeviceWarning(String id, String hasFixed, String flowStatus, String operator, String note) {
        dDeviceMapper.updateDeviceWarning(id, hasFixed, flowStatus, operator, note);
    }

    // 删除设备报警信息
    @Override
    public void deleteDeviceWarning(Integer id) {
        dDeviceMapper.deleteDeviceWarning(id);
    }

    // 更新设备维保信息
    @Override
    public void updateDeviceMaintenance(String id, String buildingId, String deviceId, String operator, String record,
                                        String type, String note, String status, String pictures,
                                        String finishedAt, String warningId, String repairOrderId) {
        dDeviceMapper.updateDeviceMaintenance(id, buildingId, deviceId, operator, record, type, note, status,
                pictures, finishedAt, warningId);
        // 绑定 报修单 和 维保单关系
        if(StringUtils.isNotEmpty(id) && StringUtils.isNotEmpty(repairOrderId)) {
            List mapList = dDeviceMapper.getMaintenanceRepairOrderMap(id, repairOrderId);
            if(null != mapList || mapList.size() <= 0) {
                dDeviceMapper.addMaintenanceRepairOrderMap(id, repairOrderId);
            }
        }
    }

    // 删除设备维保信息
    @Override
    public void deleteDeviceMaintenance(Long id) {
        // 删除运维信息
        dDeviceMapper.deleteDeviceMaintenance(id);
        // 删除运维和报修单关系信息
        dDeviceMapper.deleteMaintenanceRepairOrderMap(id);
    }

    // 所有设备维保信息汇总
    @Override
    public List<Map> deviceMaintenanceList(Integer buildingId, String deviceId, String status) {
        List<Map> list = dDeviceMapper.buildingDeviceMaintenanceList(buildingId, deviceId, status);
        return list;
    }

    // 设备维保信息详情
    @Override
    public Map deviceMaintenanceDetail(Long id) {
        Map deviceMaintenance = dDeviceMapper.deviceMaintenanceDetail(id);
        if(null != deviceMaintenance) {
            List<Map> deviceMaintenanceConsumable = dDeviceMapper.deviceMaintenanceConsumableList(id);
            deviceMaintenance.put("consumable", deviceMaintenanceConsumable);
        }
        return deviceMaintenance;
    }

    ////////////////  运维报修接口 ///////////////
    // 报修单列表
    @Override
    public List<Map> repairOrderList(String buildingId, String orderId, String deviceId, String userId, String sUserId,
                                     String repairType, String status, String priority,
                                     String from, String to, String searchParam) {
        return dDeviceMapper.repairOrderList(buildingId, orderId, deviceId, userId, sUserId, repairType,
                status, priority, from, to, searchParam);
    }
    // 报修单详情
    @Override
    public Map repairOrderDetail(String id) {
        Map order = dDeviceMapper.repairOrderDetail(id);
        if(null != order) {
            List<Map> maintList = dDeviceMapper.repairOrderMaintenanceList(id);
            order.put("maintList", maintList);
        }
        return order;
    }
    // 报修单更新
    @Override
    public Integer updateRepairOrder(Map params) {
        return dDeviceMapper.updateRepairOrder(params);
    }
    // 报修单删除
    @Override
    public Integer deleteRepairOrder(String id) {
        if(StringUtils.isNotEmpty(id)) {
            return dDeviceMapper.deleteRepairOrder(id);
        }
        return null;
    }

    /////////////// 设备高级策略接口 ////////////////
    // 策略列表
    public List<Map> strategyList(String id, String buildingId, String deviceType, String name, String userId, String status, String tag) {
        return dDeviceMapper.strategyList(id, buildingId, deviceType, name, userId, status, tag);
    }
    // 更新策略
    public void updateStrategy(String id, String buildingId, String type, String deviceType,String oid, String userId,
                                     String name, String note, String tag, String status) {
        dDeviceMapper.updateStrategy(id, buildingId, type, deviceType, oid, userId, name, note, tag, status);
    }
    // 删除策略
    public void removeStrategy(String id) {
        dDeviceMapper.removeStrategy(id);
        dDeviceMapper.removeTask(null, id);
        dDeviceMapper.removeDeviceStrategyMap(null, null, id);
    }
    // 策略绑定的设备列表
    public List<Map> strategyDeviceList(String deviceId, String taskGroupId) {
        return dDeviceMapper.strategyDeviceList(deviceId, taskGroupId);
    }
    // 策略绑定设备更新
    public void updateStrategyDevice(List<String> taskGroups, List<String> deviceIds, String method) {
        if("card".equals(method)) {
            // 设备卡片来源情况, 只影响 deviceIds 设备, taskGroupId有则增加, 无则删除
            // 1. deviceIds 有  taskGroupList 无
            // 1. deviceIds 有  taskGroupList 有
            if(null != taskGroups && taskGroups.size() > 0) {
                // 先删除设备绑定的策略
                dDeviceMapper.removeDeviceStrategyMap(null, deviceIds, null);
                // 再绑定设备和策略
                if((null != deviceIds && !deviceIds.isEmpty())) {
                    for(String taskGroupId: taskGroups) {
                        dDeviceMapper.insertStrategyDeviceMap(taskGroupId, deviceIds);
                    }
                }
            } else {
                dDeviceMapper.removeDeviceStrategyMap(null, deviceIds, null);
            }
        } else {
            if(null != taskGroups && taskGroups.size() > 0) {
                // 批量操作情况 taskGroupId 一定有值， 只保留 deviceIds 对应
                for(String taskGroupId: taskGroups) {
                    dDeviceMapper.removeDeviceStrategyMap(null, null, taskGroupId);
                }
                // 再绑定设备和策略
                if((null != deviceIds && !deviceIds.isEmpty())) {
                    for(String taskGroupId: taskGroups) {
                        dDeviceMapper.insertStrategyDeviceMap(taskGroupId, deviceIds);
                    }
                }
            }
        }
//        // 1. taskGroupId not null,  deviceIds not null
//        if( (null != taskGroupId && StringUtils.isNotEmpty(taskGroupId))
//                && (null != deviceIds && !deviceIds.isEmpty() && !("".equals(deviceIds.get(0)))) ) {
//            if("card".equals(method)) {
//                dDeviceMapper.removeDeviceStrategyMap(null, null, taskGroupId);
//            }
//            dDeviceMapper.insertStrategyDeviceMap(taskGroupId, deviceIds);
//        }
//        // 2. taskGroupId null,  deviceIds not null
//        if((null == taskGroupId || StringUtils.isEmpty(taskGroupId) )
//            && (null != deviceIds && !deviceIds.isEmpty() && !("".equals(deviceIds.get(0)))) ) {
//            dDeviceMapper.removeDeviceStrategyMap(null, deviceIds, null);
//        }
//        // 3. taskGroupId not null, deviceIds null
//        if( (null != taskGroupId && StringUtils.isNotEmpty(taskGroupId))
//            && (null == deviceIds || deviceIds.isEmpty()) ) {
//            dDeviceMapper.removeDeviceStrategyMap(null, null, taskGroupId);
//        }
    }
    // 设备策略任务列表
    @Override
    public List<Map> strategyTaskList(String groupId, String userId, String status) {
        return dDeviceMapper.strategyTaskList(groupId, userId, status);
    }
    // 设备任务更新
    @Override
    public void updateTask(String id, String groupId, String deviceId, String oid,
                                  String userId, String name, String note, String otherData, String status) {
        dDeviceMapper.updateTask(id, groupId, deviceId, oid, userId, name, note,
                otherData, status);
    }
    // 删除策略
    public void removeTask(String id) {
        dDeviceMapper.removeTask(id, null);
    }

    // 设备历史数据
    @Override
    public List<Map> deviceDataHistoryList(Long deviceId, Long itemDataId, String from, String to, String limit) {
        List<Map> list = dDeviceMapper.deviceItemDataList(deviceId, itemDataId, from, to, limit);
        if(null != list) {
            return list;
        } else {
            return new ArrayList<>();
        }
    }

    // 更新设备状态
    @Override
    public void updateDeviceStatus(String deviceType,Integer offlineTime, String fromCache) {
        int offlineAdjust = offlineTime; // 单位分钟
        DDevice deviceQuery = new DDevice();
        if (deviceType != null) {
            deviceQuery.setType(deviceType);
        }
        List<DDevice> list = dDeviceMapper.selectDDeviceList(deviceQuery);
        for(DDevice device : list) {
            try {
                Long deviceId = (long) device.getId();
                updateSingleDeviceStatus(deviceId, offlineAdjust, device, fromCache);
            } catch (Exception ex) {
                logger.warn("updateDeviceStatus error. ", deviceType, " deviceId: " + device.getId(), " msg: " + ex.getMessage());
            }
        }
    }

    // 更新单个设备状态
    @Override
    public void updateSingleDeviceStatus(Long deviceId, Integer offlineAdjust, DDevice device, String fromCache) {
        try {
            // 补充设备数据
            if(null == device) {
                device = dDeviceMapper.selectDDeviceById(deviceId);
            }
            // 数据信息
            List<Map> deviceData = dDeviceMapper.deviceDataListByUpdatedAt(deviceId);
            if("1".equals(fromCache)) {
                // 从缓存获取更新的数据并更新数据信息
                updateDeviceDataFromCache(deviceData);
            }

            List<Map> deviceDataBase = new ArrayList<>();
            List<Map> deviceDataEnv = new ArrayList<>();
            Map statusData = null; // 运行状态
            String statusStr = "";
            Map commStatusData = null; // 在线状态
            String commStatusStr = "";

            // 当前设备所关联的所有底层设备
            List<Integer> itemIds = new ArrayList<>();

            // 区分设备数据和环境数据
            if (null != deviceData && deviceData.size() > 0) {
                // 默认第一个为 statusData
                statusData = deviceData.get(0);
                commStatusData = deviceData.get(0);
                for (Map dd : deviceData) {
                    if ("env".equals(dd.get("dmTag").toString())) {
                        deviceDataEnv.add(dd);
                    } else {
                        String tag = dd.get("dmTag").toString();
                        if(!StringUtils.isEmpty(tag) && tag.contains("status")) {
                            statusData = dd;
                            statusStr = dd.get("dVal").toString();
                            try {
                                if(!StringUtils.isEmpty(dd.get("dOtherData").toString())) {
                                    String od = dd.get("dOtherData").toString();
                                    String[] mp = od.split(";");
                                    Map<String, String> km = new HashMap();
                                    for(int i = 0; i< mp.length; i++) {
                                        String[] ms = mp[i].split(":");
                                        km.put(ms[0], ms[1]);
                                    }
                                    String v = dd.get("dVal").toString();
                                    statusStr = km.get(v);
                                }
                            } catch (Exception e) {
                                // pass
                            }
                        } else if(!StringUtils.isEmpty(tag) && tag.contains("online")) {
                            commStatusData = dd;
                            commStatusStr = dd.get("dVal").toString();
                            try {
                                if(!StringUtils.isEmpty(dd.get("dOtherData").toString())) {
                                    String od = dd.get("dOtherData").toString();
                                    String[] mp = od.split(";");
                                    Map<String, String> km = new HashMap();
                                    for(int i = 0; i< mp.length; i++) {
                                        String[] ms = mp[i].split(":");
                                        km.put(ms[0], ms[1]);
                                    }
                                    String v = dd.get("dVal").toString();
                                    commStatusStr = km.get(v);
                                }
                            } catch (Exception e) {
                                // pass
                            }
                        }
                        deviceDataBase.add(dd);
                        if (null != dd.get("dItemId") && StringUtils.isNotEmpty(dd.get("dItemId").toString())) {
                            itemIds.add(Integer.valueOf(dd.get("dItemId").toString()));
                        }
                    }
                }
            }

            // 设备故障信息
            // 默认显示未处理
            Integer len = 5;
            List<Map> warningList = new ArrayList<>();
            List<Map> faultList = new ArrayList<>();
            if (itemIds.size() > 0) {
                List<String> warnStr = Arrays.asList("一级,二级,三级".split(","));
                warningList = dDeviceMapper.deviceWarningList(itemIds, "N", "unread", warnStr, null, len);
                List<String> faultStr = Arrays.asList("故障".split(","));
                faultList = dDeviceMapper.deviceWarningList(itemIds, "N", "unread", faultStr, null, len);
            }

            // 判断
            String workStatus = statusStr;
            // 告警 > 故障 > 健康
            String warningStatus = "健康";
            if(null != faultList && faultList.size() > 0) {
                warningStatus = "故障";
            }
            if(null != warningList && warningList.size() > 0) {
                warningStatus = "告警";
            }

            // 默认在线，有点，且有时间再做离线判断
            String communicateStatus = "在线";
            // 根据数据时间初步判断是否在线
            if(commStatusData != null && StringUtils.isNotEmpty(commStatusData.get("dUpdatedAt").toString())) {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date date = simpleDateFormat.parse(commStatusData.get("dUpdatedAt").toString());
                long diffTime = (new Date()).getTime() - date.getTime();
                communicateStatus = diffTime > offlineAdjust * 60 * 1000 ? "离线" : "在线";
                // String workStatus = "在线".equals(communicateStatus) ?statusStr : "未知";
            }
            // 进一步判断在离线数据点的反馈值，根据实际值反馈
            if(StringUtils.isNotEmpty(commStatusStr) && (commStatusStr.contains("在线") || commStatusStr.contains("离线")) ) {
                communicateStatus = commStatusStr.contains("在线") ? "在线" : "离线";
            }
            dDeviceMapper.updateStatus(deviceId, workStatus, warningStatus, communicateStatus);
            // 更新离线报警记录
            dDeviceMapper.updateCommunicateWarning(device.getBuildingId(), device.getId(), device.getType(), communicateStatus);
        } catch (Exception ex) {
            logger.warn("updateDeviceStatus error. "
                    + " deviceId: " + deviceId
                    + " msg: " + ex.getMessage());
        }
    }

    // 获取要监控的设备数据
    @Override
    public List<Map> deviceMonitorDataList(String deviceType, String deviceId) { return dDeviceMapper.deviceMonitorDataList(deviceType, deviceId); }

    // 根据itemData生成大功率设备数据
    @Override
    public void generaterDeviceHistoryData(String deviceType, String deviceId) {
        List<Map> dataList = deviceMonitorDataList(deviceType, deviceId);
        for(Map data : dataList) {
            String tableName = getDeviceHistoryTableNameByDate(preTableName, null);
            try {
                dDeviceMapper.insertDeviceDataHistoryShard(data.get("id").toString(),
                        data.get("itemDataId").toString(),
                        data.get("val").toString(),
                        data.get("updatedAt").toString(),
                        tableName);
            } catch (Exception ex) {
                logger.debug("generaterDeviceHistoryData error ", tableName, deviceType, data.get("itemDataId").toString(), data.get("val").toString(), ex.getMessage());
            }
            try {
                dDeviceMapper.insertDeviceDataHistory(data.get("id").toString(),
                        data.get("itemDataId").toString(),
                        data.get("val").toString(),
                        data.get("updatedAt").toString());
            } catch (Exception ex) {
                logger.debug("generaterDeviceHistoryData error ", deviceType, data.get("itemDataId").toString(), data.get("val").toString(), ex.getMessage());
            }
        }
    }

    // 将持续报警的记录转换成工单  timeSpan 单位秒
    @Override
    public void generaterDeviceMaintenanceData(Integer timeSpan) {
        List<Map> list = dDeviceMapper.getNeedMaintenanceList(timeSpan);
        if(null != list && list.size() > 0) {
            for(Map data : list) {
                DDeviceMaintenance dDeviceMaintenance = new DDeviceMaintenance();
                dDeviceMaintenance.setBuildingId(Long.valueOf(data.get("buildingId").toString()));
                dDeviceMaintenance.setDeviceId(Long.valueOf(data.get("deviceId").toString()));
                dDeviceMaintenance.setType("紧急维修");
                dDeviceMaintenance.setStatus("new");
                dDeviceMaintenance.setWarningId(Long.valueOf(data.get("id").toString()));
                dDeviceMaintenanceMapper.insertDDeviceMaintenance(dDeviceMaintenance);
            }
        }
    }

    // 设备历史数据，创建分表
    @Override
    public void createDeviceHistoryTable() {
        //得到当前表的所有分表
        List<String> tableNameList = utilMapper.getHistoryTableNameList(dbName, preTableName);
        String tableName = getDeviceHistoryTableNameByDate(preTableName, null);
        //如果所有分表中不包含当前月份的分表，则新建表
        if (!tableNameList.contains(tableName)) {
            utilMapper.createDeviceHistoryTable(tableName);
            logger.info("创建设备历史分表:" + tableName);
        } else {
            logger.info("设备历史分表:" + tableName + "已经存在");
        }
    }

    // 定时生成 【日常维护】 维保单
    @Override
    public void createDeviceMaintenance(String type) {
        List<Map> needMaintenanceDeviceList = dDeviceMapper.getNeedMaintenanceDeviceList(type);
        if(needMaintenanceDeviceList != null && needMaintenanceDeviceList.size() > 0) {
            for(Map data : needMaintenanceDeviceList) {
                DDeviceMaintenance maintenance = new DDeviceMaintenance();
                maintenance.setType(type);
                maintenance.setPlanId(Long.valueOf(data.get("maintenancePlanId").toString()));
                maintenance.setBuildingId(Long.valueOf(data.get("buildingId").toString()));
                maintenance.setDeviceId(Long.valueOf(data.get("deviceId").toString()));
                maintenance.setNote(data.get("content").toString());
                maintenance.setOperator(data.get("operator").toString());
                maintenance.setStatus("new");
                dDeviceMaintenanceMapper.insertDDeviceMaintenance(maintenance);
            }
        }
    }

    // 根据日期返回表名
    private String getDeviceHistoryTableNameByDate(String preTableName, String dateStr) {
        String dateFmt = "YYYY_MM";
        String dateString = null;
        if(null != dateStr && StringUtils.isNotEmpty(dateStr)) {
            dateString = DateUtils.parseDateToStr(dateFmt, DateUtils.parseDate(dateStr));
        } else {
            dateStr = DateUtils.getTime();
            dateString = DateUtils.dateTimeNow(dateFmt);
        }
        // 修正日期函数bug  2024-12-31 00:00:00 会变成 2025-12
        if(!dateString.substring(0, 4).equals(dateStr.substring(0,4))) {
            dateString = dateStr.substring(0,4) + dateString.substring(4);
        }
        return preTableName + dateString;
    }

    // 清理设备历史数据
    @Override
    public void removeDeviceDataHistory(Integer days) {
        if(null != days) {
            dDeviceMapper.removeDeviceDataHistory(days);
        }
    }

    // 建筑下所有监控的测点信息列表
    @Override
    public List<Map> buildingMonitorItemDataList(Integer buildingId, Integer days) {
        List<Map> dataList = dDeviceMapper.buildingMonitorItemDataList(buildingId, days);
        if(null != dataList ) {
            return dataList;
        } else {
            return new ArrayList<>();
        }
    }

    // 设备数据 锁定
    public void updateDeviceDataLock(List<String> deviceIdList, String deviceType, String itemDataName, String value) {
        if(deviceIdList.size() > 0) {
            for(String id : deviceIdList) {
                try {
                    dDeviceMapper.updateDeviceDataLock(id, itemDataName, value);
                    // 插入操作记录
                    Map res = new HashMap();
                    res.put("deviceId", id);
                    res.put("dataName", itemDataName);
                    res.put("dataValueBefore", "");
                    res.put("dataValueAfter", value);
                    dDeviceMapper.insertDeviceOperLog(id, deviceType,
                            "设备锁定", "2", "com.ruoyi.device.service.updateDeviceDataUnlock()",
                            "USER_OPERATE", "1", SecurityUtils.getLoginUser().getUser().getNickName(),
                            "{}", JSON.toJSONString(res), "0");
                } catch (Exception ex) {
                    logger.warn("updateDeviceDataLock " + id + " " + itemDataName + " error:" + ex.getMessage());
                }
            }
        }
    }

    // 设备数据 解锁
    public void updateDeviceDataUnlock(List<String> deviceIdList, String deviceType) {
        if(deviceIdList.size() > 0) {
            for(String id : deviceIdList) {
                try {
                    dDeviceMapper.updateDeviceDataUnlock(id);
                    // 插入操作记录
                    dDeviceMapper.insertDeviceOperLog(id, deviceType,
                            "设备解锁", "2", "com.ruoyi.device.service.updateDeviceDataUnlock()",
                            "USER_OPERATE", "1", SecurityUtils.getLoginUser().getUser().getNickName(),
                            "{}", "{}", "0");
                } catch (Exception ex) {
                    logger.warn("updateDeviceDataUnlock " + id + " " + deviceType + " error:" + ex.getMessage());
                }
            }
        }
    }

    // 设备数据状态汇总
    @Override
    public List<Map> deviceWarningSummary(Integer buildingId, String deviceName, List<String> deviceTypeList) {
        List<Map> dataList = dDeviceMapper.deviceWarningSummary(buildingId, deviceName, deviceTypeList);
        if(null != dataList ) {
            return dataList;
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<Map> deviceWarningSummaryBySeverity(Integer buildingId, List<String> deviceIdList, List<String> deviceTypeList,
                                                    List<String> severityList, String hasFixed, String flowStatus) {
        List<Map> dataList = new ArrayList<>();
        // 只查离线的情况
        if(null != severityList && severityList.size() == 1 && severityList.contains("离线")) {
            dataList = dDeviceMapper.deviceOfflineSummary(buildingId, deviceIdList, deviceTypeList, hasFixed);
        } else {
            // 报警 + 离线 情况
            if(null != severityList && severityList.size() > 0 && severityList.contains("离线")) {
                List<Map> dataListOffline = dDeviceMapper.deviceOfflineSummary(buildingId, deviceIdList, deviceTypeList, hasFixed);
                List<Map> dataListWarning = dDeviceMapper.deviceWarningSummaryBySeverity(buildingId, deviceIdList, deviceTypeList,
                        severityList, hasFixed, flowStatus);
                dataList = Stream.concat(dataListOffline.stream(), dataListWarning.stream()).collect(Collectors.toList());;
            } else {
                // 只有报警情况
                dataList = dDeviceMapper.deviceWarningSummaryBySeverity(buildingId, deviceIdList, deviceTypeList,
                        severityList, hasFixed, flowStatus);
            }
        }
        // 离线数据
        if(null != dataList ) {
            return dataList;
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public Map warningSummary(Integer buildingId,List<String> deviceTypes,String from,String to) {
        return dDeviceMapper.warningSummary(buildingId,deviceTypes,from,to);
    }


    @Override
    public List<Map> deviceWarningSummaryList(Integer buildingId,List<String> deviceTypes,String severity,String displayType,String from,String to) {

        return dDeviceMapper.deviceWarningSummaryList(buildingId,deviceTypes,severity,displayType,from,to);
    }

    @Override
    public List<Map<String, Object>> communicateWarningList(Integer buildingId, String deviceName, List<String> deviceIdList,
                                            List<String> deviceTypeList, String hasFixed, String from, String to) {
        if(StringUtils.isNotEmpty(to) && to.length() == 10) {
            to = to + " 23:59:59";
        }
        List<Map<String, Object>> dataList = dDeviceMapper.communicateWarningList(buildingId,
                deviceName, deviceIdList, deviceTypeList, hasFixed, from, to);
        if(null != dataList ) {
            return dataList;
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public void removeCommunicateWarning(List<String> ids) {
        if(null != ids && ids.size() > 0) {
            dDeviceMapper.removeCommunicateWarning(ids);
        }
    }

    @Override
    public Map deviceMaintenanceSummary(Integer buildingId,List<String> deviceTypes,
                                        List<String> types,String status,String from, String to) {

        return dDeviceMapper.deviceMaintenanceSummary(buildingId,deviceTypes,types,status,from,to);
    }

    @Override
    public List<Map> deviceMaintenanceSummaryByTime(Integer buildingId,List<String> deviceTypes,List<String> types,String status,String displayType,String from, String to) {

        return dDeviceMapper.deviceMaintenanceSummaryByTime(buildingId,deviceTypes,types,status,displayType,from,to);
    }

    @Override
    public List<Map> deviceMaintenanceSummaryByDeviceType(Integer buildingId,List<String> deviceTypes,List<String> types,String status,String from, String to) {

        return dDeviceMapper.deviceMaintenanceSummaryByDeviceType(buildingId,deviceTypes,types,status,from,to);
    }

    @Override
    public Map deviceDataSummary(List<String> deviceIds,String function, String dataName) {

        return dDeviceMapper.deviceDataSummary(deviceIds,function,dataName);
    }

    // 设备数据操作日志
    @Override
    public List<Map> deviceOperateLogList(String deviceId, List deviceTypeList,
                                          String requestMethod, String operName,
                                          String beginTime, String endTime, String limit) {
        return dDeviceMapper.deviceOperateLogList(deviceId, deviceTypeList, requestMethod, operName, beginTime, endTime, limit);
    }

    // 设备数据操作日志
    public List<DeviceOperLog> deviceOperateLogListVo(String deviceId, List deviceTypeList,
                                                      String requestMethod, String operName,
                                                      String beginTime, String endTime) {
        List<Map> list = dDeviceMapper.deviceOperateLogList(deviceId, deviceTypeList, requestMethod, operName, beginTime, endTime, null);
        if(null != list && list.size() > 0) {
            List<DeviceOperLog> res = list.stream().map(d -> {
                DeviceOperLog row = new DeviceOperLog();
                row.setId(d.get("id").toString());
                try {
                    JSONObject resJson = JSONUtil.parseObj(d.get("jsonResult"));
                    row.setDeviceType(d.get("deviceType").toString());
                    row.setRequestMethod(d.get("requestMethod").toString());
                    row.setOperName(d.get("operName").toString());
                    row.setOperTime(d.get("operTime").toString());
                    row.setStatus(d.get("status").toString());
                    row.setDeviceName(resJson.get("deviceName").toString());
                    row.setDataName(resJson.get("dataName").toString());
                    row.setDataValueBefore(resJson.get("dataValueBefore").toString());
                    row.setDataValueAfter(resJson.get("dataValueAfter").toString());
                    row.setDataNote(resJson.get("dataNote").toString());
                } catch (Exception ex) {
                    // pass
                }
                return row;
            }).collect(Collectors.toList());
            return res;
        }
        return new ArrayList<>();
    }

    // 设备数据操作日志删除
    @Override
    public void deleteDeviceOperateLog(List ids) {
        if(null != ids && ids.size() > 0) {
            dDeviceMapper.deleteDeviceOperateLog(ids);
        }
    }

    // 设备数据操作日志清空
    @Override
    public void cleanDeviceOperateLog() {
        dDeviceMapper.cleanDeviceOperateLog();
    }


    // 更新设备寿命
    @Override
    public void updateDeviceLife(String deviceType) {
        DDevice deviceQuery = new DDevice();
        if (deviceType != null) {
            deviceQuery.setType(deviceType);
        }
        List<DDevice> list = dDeviceMapper.selectDDeviceList(deviceQuery);
        for(DDevice device : list) {
            try {
                String life = null;
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date dateStart = null;
                Float diffDay = null;
                if (StringUtils.isNotEmpty(device.getActivationTime())) {
                    dateStart = simpleDateFormat.parse(device.getActivationTime());
                }
                if (StringUtils.isNotEmpty(device.getServiceLife())) {
                    // 1. 有启用日期和设备寿命，可以计算 life
                    diffDay = ((new Date()).getTime() - dateStart.getTime()) / 24 / 60 / 60 / 1000f;
                    Float leftPercent = 100 * (1 - (diffDay / (Float.valueOf(device.getServiceLife()) * 365)));
                    life = String.valueOf(Math.max(0, Math.round(leftPercent))) + "%";
                } else if (StringUtils.isNotEmpty(device.getScrapTime())) {
                    // 2. 有启用日期和作废日期，可以计算 life
                    diffDay = ((simpleDateFormat.parse(device.getScrapTime())).getTime() - dateStart.getTime()) / 24 / 60 / 60 / 1000f;;
                    Float leftPercent = 100 * (1 - (diffDay / (Float.valueOf(device.getServiceLife()) * 365)));
                    life = String.valueOf(Math.max(0, Math.round(leftPercent))) + "%";
                }
                if(null != life) {
                    dDeviceMapper.updateLife(device.getId(), life);
                }
            } catch (Exception ex) {
                logger.warn("updateDeviceLife " + device.getId()  + " error:" + ex.getMessage());
            }
        }
    }

    // 报警详情信息
    public Map getItemWarning(String itenId, String warningCategory) {
        return dDeviceMapper.getItemWarning(itenId, warningCategory);
    }

    // 根据 itemId 找到绑定的 device
    @Override
    public List<Map> getDeviceByItemWarning(String itemId, String itemDataName) {
        List<Map> res = new ArrayList<>();
        if (StringUtils.isNotEmpty(itemId) && StringUtils.isNotEmpty(itemDataName)) {
            List<Map> list = dDeviceMapper.getDeviceByItemWarning(itemId, itemDataName);
            if(null != list) {
                return list;
            }
        }
        return res;
    }

    // 数采自建查询
    @Override
    public List<Map> deviceDateCheckList(String deviceType, String deviceId) {
        return dDeviceMapper.getDeviceDateCheckList(deviceType, deviceId);
    }

    // 监测点历史数据-表格
    @Override
    public List<Map> getMultdeviceItemDataList(String buildingId, List<String> deviceIdList, List<String> deviceDataNameList
                                                ,String from, String to, String limit) {
        return dDeviceMapper.getMultdeviceItemDataList(buildingId, deviceIdList, deviceDataNameList, from, to, limit);
    }

    // 运维知识库列表
    @Override
    public List<Map> getDeviceDevOpsList(String deviceType, String type, String queryStr) {
        return dDeviceMapper.getDeviceDevOpsList(deviceType, type, queryStr);
    }

    @Override
    public int insertDeviceDevOps(Map deviceDevOps) {
        return dDeviceMapper.insertDeviceDevOps(deviceDevOps);
    }

    @Override
    public int updateDeviceDevOps(Map deviceDevOps) {
        return dDeviceMapper.updateDeviceDevOps(deviceDevOps);
    }

    @Override
    public int deleteDeviceDevOpsById(Long id) {
        return dDeviceMapper.deleteDeviceDevOpsById(id);
    }

    // 运维知识库故障类型
    @Override
    public List<Map> deviceDevOpsTypeList(String deviceType) {
        return dDeviceMapper.deviceDevOpsTypeList(deviceType);
    }

    // 要发送的告警信息列表
    @Override
    public List<Map> alertWarningList(String warningId) {
        return dDeviceMapper.alertWarningList(warningId);
    }

    // 发送 钉钉消息推送
    @Override
    public void sendAlertWarningByDingTalk(List<Map> warningList) {
        // 已发送消息记录，防止多个消息重复发送
        List<String> sended = new ArrayList<>();
        for(Map itemWarning : warningList) {
            String nowStr = DateUtils.dateTimeNow("HH:mm:ss");
            String warnStart = itemWarning.get("warnStart").toString();
            String warnEnd = itemWarning.get("warnEnd").toString();
            // 发送通知
            dingTalkService.sendWarning("智慧建筑", "设备报警", "发生", null, null);
        }
    }

    // 发送告警信息
    @Override
    public void sendAlertWarningBySms(List<Map> warningList) {
        // 已发送消息记录，防止多个消息重复发送
        List<String> sended = new ArrayList<>();
        for(Map itemWarning : warningList) {
            String smsTo = itemWarning.get("smsTo").toString();
            String template = itemWarning.get("smsTemplate").toString();
            String nowStr = DateUtils.dateTimeNow("HH:mm:ss");
            String warnStart = itemWarning.get("warnStart").toString();
            String warnEnd = itemWarning.get("warnEnd").toString();
            // 发送短信
            if (StringUtils.isNotEmpty(smsTo)
                    && StringUtils.isNotEmpty(template)
                    && (StringUtils.isEmpty(warnStart) || (StringUtils.isNotEmpty(warnStart) && nowStr.compareTo(warnStart) >= 0))
                    && (StringUtils.isEmpty(warnEnd) || (StringUtils.isNotEmpty(warnEnd) && nowStr.compareTo(warnEnd) <= 0))
            ) {
                List<String> mobileList = Arrays.asList(smsTo.split(","));
                for (String mobile : mobileList) {
                    if (sended.indexOf(mobile) < 0) {
                        sended.add(mobile);
                        smsService.sendWarning("智慧建筑", "设备报警", "发生", mobile, template);
                    }
                }
            }
        }
    }

    // 发送告警信息
    @Override
    public void sendAlertWarningByEmail(List<Map> warningList) {
        // 已发送消息记录，防止多个消息重复发送
        List<String> sended = new ArrayList<>();
        for(Map itemWarning : warningList) {
            String mailTo = itemWarning.get("mailTo").toString();
            String template = itemWarning.get("smsTemplate").toString();
            String nowStr = DateUtils.dateTimeNow("HH:mm:ss");
            String warnStart = itemWarning.get("warnStart").toString();
            String warnEnd = itemWarning.get("warnEnd").toString();
            // 发送短信
            if (StringUtils.isNotEmpty(mailTo)
                    && StringUtils.isNotEmpty(template)
                    && (StringUtils.isEmpty(warnStart) || (StringUtils.isNotEmpty(warnStart) && nowStr.compareTo(warnStart) >= 0))
                    && (StringUtils.isEmpty(warnEnd) || (StringUtils.isNotEmpty(warnEnd) && nowStr.compareTo(warnEnd) <= 0))
            ) {
                List<String> mailList = Arrays.asList(mailTo.split(","));
                for (String email : mailList) {
                    if (sended.indexOf(email) < 0) {
                        sended.add(email);
                        emailService.sendWarning("智慧建筑", "设备报警", "发生", mailTo, template);
                    }
                }
            }
        }
    }

    // 需求侧响应列表
    public List<Map>demandList(String id, String buildingId, String searchKey, String status, String actDate, String limit) {
        List<Map> dataList = dDeviceMapper.demandList(id, buildingId, searchKey, status, actDate, limit);
        if(null != dataList ) {
            return dataList;
        } else {
            return new ArrayList<>();
        }
    }

    // 需求侧响应更新
    public void demandUpdate(String id, String buildingId, String operator, String title, String actDate,
                             String actHour, String note, String deviceList, String reduceEnergy,
                             String reduceFee, String status) {
        try {
            if(StringUtils.isNotEmpty(id)) {
                dDeviceMapper.updateDemand(id, buildingId, operator, title, actDate, actHour, note,
                        deviceList, reduceEnergy, reduceFee, status);
            } else {
                dDeviceMapper.insertDemand(buildingId, operator, title, actDate, actHour, note,
                        deviceList, reduceEnergy, reduceFee, status);
            }
        } catch (Exception ex) {
            logger.error("demandUpdate error id: " + id + " msg: " + ex.getMessage());
        }
    }

    public void demandDelete(List<String> ids) {
        if(null != ids && ids.size() > 0) {
            dDeviceMapper.demandDelete(ids);
        }
    }

    @Override
    public Map deviceDetailYuKon(Integer resourceId, Long deviceId, String fromCache) {
        Map item = dDeviceMapper.deviceBaseYuKon(deviceId, resourceId);
        // 数据信息
        List<Map> deviceData = dDeviceMapper.deviceDataListYuKon(deviceId);
        if("1".equals(fromCache)) {
            // 从缓存更新数据点
            updateDeviceDataFromCache(deviceData);
        }

        List<Map> deviceDataBase = new ArrayList<>();
        List<Map> deviceDataEnv = new ArrayList<>();
        List<Map> deviceDataWarn = new ArrayList<>();
        Map<String, String> resultHashMap = new HashMap<>();

        // 当前设备所关联的所有底层设备
        List<Integer> itemIds = new ArrayList<>();

        try {
            // 区分设备数据和环境数据
            // 健康度计算
            Integer runCount = null;
            Integer errorCount = null;
            Integer warnCount = null;
            Integer runHour = null;
            Integer maintanHour = 0;

            if (null != deviceData && deviceData.size() > 0) {
                for (Map dd : deviceData) {
                    String tag = dd.get("dmTag").toString();
                    if ("env".equals(tag)) {
                        deviceDataEnv.add(dd);
                    } else if ("warn".equals(tag)) {
                        deviceDataWarn.add(dd);
                    } else {
                        deviceDataBase.add(dd);
                        for (Map<String, String> map : deviceDataBase) {
                            String dmName = map.get("dmName");
                            String dVal = map.get("dVal");
                            resultHashMap.put(dmName, dVal);
                        }
                        if (null != dd.get("dItemId") && StringUtils.isNotEmpty(dd.get("dItemId").toString())) {
                            itemIds.add(Integer.valueOf(dd.get("dItemId").toString()));
                        }
                    }

                    if(!StringUtils.isEmpty(tag) && tag.contains("runCount")) {
                        try {
                            runCount = Integer.valueOf(dd.get("dVal").toString());
                        } catch (Exception ex) {
                            // pass
                        }
                    }
                    if(!StringUtils.isEmpty(tag) && tag.contains("errorCount")) {
                        try {
                            errorCount = Integer.valueOf(dd.get("dVal").toString());
                        } catch (Exception ex) {
                            // pass
                        }
                    }
                    if(!StringUtils.isEmpty(tag) && tag.contains("runHour")) {
                        try {
                            runHour = Integer.valueOf(dd.get("dVal").toString());
                            // 只统计维修时间
                            List<String> typeList = new ArrayList<>();
                            typeList.add("紧急维修");
                            maintanHour = deviceMaintanHour(deviceId, typeList);
                        } catch (Exception ex) {
                            // pass
                        }
                    }

                }
            }
            item.put("resultHashMap",resultHashMap);

            // 计算设备健康度
            // 故障率=故障次数/运行次数
            if(null != runCount && null != errorCount) {
                item.put("runCount", runCount);
                item.put("errorCount", errorCount);
                if(runCount > 0) {
                    BigDecimal bd = new BigDecimal(100*errorCount/runCount);
                    bd = bd.setScale(2, RoundingMode.HALF_UP);
                    item.put("errorPercent", bd.toString());
                } else {
                    item.put("errorPercent", 0);
                }
            }
            // 维修率 = 维修时间/运行时间
            if(null != runHour) {
                item.put("runHour", runHour);
                item.put("maintanHour", maintanHour);
                if(runHour > 0) {
                    BigDecimal bd = new BigDecimal(100*maintanHour/runHour);
                    bd = bd.setScale(2, RoundingMode.HALF_UP);
                    item.put("maintanPercent", bd.toString());
                } else {
                    item.put("maintanPercent", 0);
                }
            }
            // 残值率=累计运行时间 / 正常使用寿命
            String serviceLife = item.get("serviceLife").toString();
            if(StringUtils.isNotEmpty(serviceLife)) {
                try {
                    Float serviceLifeHour = Float.valueOf(serviceLife) * 365 * 24;
                    item.put("serviceLifeHour", serviceLifeHour);
                    if(serviceLifeHour > 0) {
                        BigDecimal bd = new BigDecimal(100*runHour/serviceLifeHour);
                        bd = bd.setScale(2, RoundingMode.HALF_UP);
                        item.put("residualPercent", bd.toString());
                    } else {
                        item.put("residualPercent", 0);
                    }
                } catch (Exception ex) {
                    // pass
                }
            }
            // 健康度=1-（故障率*0.5+维修时间比率*0.3+残值率*0.2）
            if(null != item.get("errorPercent") && null != item.get("maintanPercent") && null != item.get("residualPercent")) {
                Float healthPercent = 100 - 0.5f * Float.valueOf(item.get("errorPercent").toString())
                        - 0.3f * Float.valueOf(item.get("maintanPercent").toString())
                        - 0.2f * Float.valueOf(item.get("residualPercent").toString());
                BigDecimal bd = new BigDecimal(healthPercent);
                bd = bd.setScale(2, RoundingMode.HALF_UP);
                item.put("healthPercent", bd.toString());
            } else {
                item.put("healthPercent", 100);
            }

            // 设备报警列表 从 item_rule 中获取
            List<Map> warningRuleList = new ArrayList<>();
            if (itemIds.size() > 0) {
                warningRuleList = dDeviceMapper.deviceWarningRuleList(itemIds);
            }
            item.put("warningRuleList", warningRuleList);

            // 设备故障信息
            // 默认显示未处理
//            Integer len = 5;
//            List<Map> warningList = new ArrayList<>();
//            if (itemIds.size() > 0) {
//                warningList = dDeviceMapper.deviceWarningList(itemIds, "N", "unread", len);
//            }
//            item.put("warningList", warningList);

            // 设备故障历史
            // 最近5条
//            List<Map> warningHistoryList = new ArrayList<>();
//            if (itemIds.size() > 0) {
//                warningHistoryList = dDeviceMapper.deviceWarningList(itemIds, null, null, len);
//            }
//            item.put("warningHistoryList", warningHistoryList);

            // 维保信息
//            List<Map> maintenanceList = new ArrayList<>();
//            if (itemIds.size() > 0) {
//                maintenanceList = dDeviceMapper.deviceMaintenanceList(deviceId, len);
//            }
//            item.put("maintenanceList", maintenanceList);

            // 设备绑定的策略信息
            List<Map> deviceStrategyList = dDeviceMapper.deviceStrategyList(deviceId.toString());
            item.put("strategyList", deviceStrategyList);
        } catch(Exception ex) {
            logger.warn("deviceDetail resourceId: " + resourceId + " deviceId: " + deviceId + " error: " + ex.getMessage());
        }
        return item;
    }

    @Override
    public void updateDeviceDataListYuKon(String deviceId,String key,String values,List<Map> reqList) {
        for (Map item : reqList) {
            try {
                String id = item.get("id").toString();
                String val = item.get("val").toString();
                dDeviceMapper.updateDeviceData(id, val);
                // 插入操作记录
                Map res = new HashMap();
                res.put("deviceId", item.get("deviceId"));
                res.put("deviceName", item.get("deviceName"));
                res.put("dataId", id);
                res.put("dataName", item.get("dataName"));
                res.put("dataValueBefore", item.get("dataValueBefore"));
                res.put("dataValueAfter", item.get("dataValueAfter"));
                res.put("dataValueMapper", item.get("dataValueMapper"));

                dDeviceMapper.insertDeviceOperLog(item.get("deviceId").toString(), item.get("deviceType").toString(),
                        "组态操作", "2", "com.ruoyi.device.service.updateDeviceDataListYuKon()",
                        "USER_OPERATE", "1", SecurityUtils.getLoginUser().getUser().getNickName(),
                        JSON.toJSONString(item), JSON.toJSONString(res), "0");
                // 添加一个更新设备状态的任务
                deviceStatusUpdateService.setDevice(Long.valueOf(item.get("deviceId").toString()));

            }catch (Exception ex) {
                logger.warn("updateDeviceDataList " + item.get("deviceId") + " error: " + ex.getMessage());
            }

        }
        dDeviceMapper.updateDeviceDataYuKon(deviceId, key, values);

    }

    @Override
    public Map selectByDeviceIdConfigurationDiagram(String deviceId, String name) {
        return dDeviceMapper.selectByDeviceIdConfigurationDiagram(deviceId, name);
    }

    @Override
    public void updateDeviceDataYuKonWZ(String deviceId, String key, String values) {
        dDeviceMapper.updateDeviceDataYuKonWZ(deviceId, key, values);
    }
}
