package com.ruoyi.base.domain;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 模板设备点位数据表对象 a_tpl_device_data
 *
 * <AUTHOR>
 * @date 2024-09-09
 */
public class ATplDeviceData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 模板关联ID */
    @Excel(name = "模板关联ID")
    private Long templateId;

    /** 数据点名 */
    @Excel(name = "数据点名")
    private String name;

    /** 数据点别名 */
    @Excel(name = "数据点别名")
    private String alias;

    /** 数据类型 */
    @Excel(name = "数据类型")
    private String dataType;

    /** 数据单位 */
    @Excel(name = "数据单位")
    private String dataUnit;

    /** 备注 */
    @Excel(name = "备注")
    private String note;

    /** 备注 */
    @Excel(name = "map备注")
    private String note2;

    /** 读写标识 */
    @Excel(name = "读写标识")
    private String func;

    /** 读r;写w;计算c; */
    @Excel(name = "读r;写w;计算c;")
    private String type;

    /** 排序 */
    @Excel(name = "排序")
    private Long oid;

    /** 数据标签, 包含status值的记录可以反馈设备d当前的运行状态 */
    @Excel(name = "数据标签, 包含status值的记录可以反馈设备d当前的运行状态")
    private String tag;

    /** 是否为主要能耗 */
    @Excel(name = "是否为主要能耗")
    private String isPrimaryEnergy;

    @Excel(name = "pDeviceId")
    private Integer pDeviceId;
    @Excel(name = "pAddr")
    private Integer pAddr;
    @Excel(name = "pLength")
    private Integer pLength;
    @Excel(name = "pDataGroup")
    private String pDataGroup;
    @Excel(name = "pFunc")
    private String pFunc;
    @Excel(name = "pFuncList")
    private String pFuncList;

    public void setId(Long id) {
        this.id = id;
    }
    public Long getId() {
        return id;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }
    public Long getTemplateId() {
        return templateId;
    }

    public void setName(String name) {
        this.name = name;
    }
    public String getName() {
        return name;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }
    public String getAlias() {
        return alias;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    public String getDataType() {
        return dataType;
    }

    public void setDataUnit(String dataUnit) {
        this.dataUnit = dataUnit;
    }
    public String getDataUnit() {
        return dataUnit;
    }

    public void setNote(String note) {
        this.note = note;
    }
    public String getNote() {
        return note;
    }
    public void setNote2(String note2) {
        this.note2 = note2;
    }
    public String getNote2() {
        return note2;
    }

    public void setFunc(String func) {
        this.func = func;
    }
    public String getFunc() {
        return func;
    }

    public void setType(String type) {
        this.type = type;
    }
    public String getType() {
        return type;
    }

    public void setOid(Long oid) {
        this.oid = oid;
    }
    public Long getOid() {
        return oid;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }
    public String getTag() {
        return tag;
    }

    public void setIsPrimaryEnergy(String isPrimaryEnergy) {
        this.isPrimaryEnergy = isPrimaryEnergy;
    }
    public String getIsPrimaryEnergy() {
        return isPrimaryEnergy;
    }


    public void setPDeviceId(Integer pDeviceId) {
        this.pDeviceId = pDeviceId;
    }
    public Integer getPDeviceId() {
        return pDeviceId;
    }

    public void setPAddr(Integer pAddr) {
        this.pAddr = pAddr;
    }
    public Integer getPAddr() {
        return pAddr;
    }

    public void setPLength(Integer pLength) {
        this.pLength = pLength;
    }
    public Integer getPLength() {
        return pLength;
    }

    public void setPDataGroup(String pDataGroup) {
        this.pDataGroup = pDataGroup;
    }
    public String getPDataGroup() {
        return pDataGroup;
    }

    public void setPFunc(String pFunc) {
        this.pFunc = pFunc;
    }
    public String getPFunc() {
        return pFunc;
    }

    public void setPFuncList(String pFuncList) {
        this.pFuncList = pFuncList;
    }
    public String getPFuncList() {
        return pFuncList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("templateId", getTemplateId())
                .append("name", getName())
                .append("alias", getAlias())
                .append("dataType", getDataType())
                .append("dataUnit", getDataUnit())
                .append("note", getNote())
                .append("func", getFunc())
                .append("type", getType())
                .append("oid", getOid())
                .append("tag", getTag())
                .append("isPrimaryEnergy", getIsPrimaryEnergy())
                .toString();
    }
}