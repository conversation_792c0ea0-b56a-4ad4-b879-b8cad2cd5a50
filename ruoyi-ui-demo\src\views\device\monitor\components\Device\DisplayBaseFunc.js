
import {
  resourceList,
  listResourceDevice,
  resourceDeviceFullList,
  updateDeviceDatas,
  updateDeviceDataBatch,    // 批量更新设备点数据
  updateDeviceDataLock,     // 锁定面板
  updateDeviceDataUnlock,   // 解锁面板
  deviceFullList,
  resourceDevice,
  updateStrategyDevice,   // 批量更新设备控制模式
  strategyList,
} from "@/api/device/apis";
import { listResource, getResource, updateResource } from "@/api/base/resource";
import { updateDevice } from "@/api/device/device";

export default {
  destroyed() {
    clearInterval(this.autoRefresh);
    this.autoRefresh = null;
  },
  methods: {
    // 初始化数据
    async initPage() {
      await this.getStrategyList(); // 加载设备控制策略
      await this.getResourceList();
      await this.getResourceDeviceFullList(); // 获取所有绑定资源的设备
      this.getDeviceDataModel(); // 获取设备数据模型
      this.initPageFinished();
    },

    // 提供初始化结束后回调
    initPageFinished() {
    },

    // 更新自动更新逻辑
    updateAutoRefresh() {
      clearInterval(this.autoRefresh);
      this.autoRefresh = null;
      if(this.refreshSpan) {
        this.autoRefresh = setInterval(() => {
          this.getResourceDeviceList();
        }, this.refreshSpan*1000);
      }
    },
    // 切换分页触发
    changePage(val) {
      this.pageNum = val;
      console.log(this.pageNum);
    },
    // 分页大小改变
    sizeChange(val) {
      this.pageSize = val;
      console.log(this.pageSize);
    },
    // 点击顶部 tab 标签触发
    handleDeviceFilter(query) {
      if(query) {
        this.curSummaryTabActive = query;
      }
    },
    showLoad() {
        this.loadNum += 1;
        this.loading = this.loadNum > 0 ? true : false;
    },
    hideLoad() {
        this.loadNum -= 1;
        this.loading = this.loadNum > 0 ? true : false;
    },

    // 读取所有设备
    async getResourceDeviceFullList() {
      console.log("getResourceDeviceFullList");
      this.showLoad();
      let list = [];
      const {data = []} = await resourceDeviceFullList({
        buildingId: this.gf.getBuildingId(),
        deviceType: this.deviceMainType,
        orderBy: this.orderBy,
      });
      list = [...data]
      this.resourceDeviceList = list;
      this.hideLoad();
    },
    // 切换楼层分组
    async handleResourceGroupChange(tab) {
      console.log("handleResourceGroupChange", tab);
      if(this.activeResourceGroupBak != this.activeResourceGroup) {
        this.activeResourceGroup = this.resourceGroupList[tab.index];
        this.activeResourceGroupBak = this.activeResourceGroup;
        this.resourceList = this.resourceFullList.filter( r => {
            return r.groupName == this.activeResourceGroup;
        });
        this.tableTitlesCopy = null;
        // 默认选中第一个
        await this.handleResourceChange({index: 0});
      }
    },
    // 切换楼层
    async handleResourceChange(tab) {
      console.log("handleResourceChange", tab);
      if(this.resourceList.length > 0) {
        this.activeResource = this.resourceList[tab.index];
        this.activeResourceInd = parseInt(tab.index);
        this.activeResourceId = this.activeResource.id+'';
        await this.getResourceDeviceList();
      } else {
        this.activeResourceInd = -1;
        this.activeResourceId = -1;
        this.activeResource = { id: -1 };
        this.deviceList = [];
        this.deviceSummary = this.summaryDevice();
      }
      this.resourceChangeAfter();
    },
    // 切换tab后执行函数
    resourceChangeAfter() {
      // 刷新某些组件
      this.showFlag = false;
      this.$nextTick(() => {
        this.showFlag = true;
      });
    },

    // 查看设备 (覆盖mixins)
    handleDeviceShow(item, ind) {
      console.log("handleDeviceShow =====> ", ind, item);
      if(this.viewDetail == "prototype" && item.resourceId) {
        const { href } = this.$router.resolve({
          path: `/deviceMgt/device/prototype`,
          query: {
            deviceId:item.id,
            deviceName: item.name,
            deviceType: item.type,
            resourceId: item.resourceId,
            displayType: item.protoTypeDisplay || this.protoTypeDisplay,
          }
        })
        window.open(href, 'prototypePage');
      } else {
        this.activeDeviceInd = ind;
        this.activeDevice = item;
        this.activeDeviceSummaryShow = true;
      }
      //this.getDeviceChartDatas();  // 重新获取总用时能耗
    },
    handleDeviceViewClose(noReset) {
      if(!noReset){
        this.activeDeviceInd = -1;
        this.activeDevice = { id: -1 };
      }
      this.activeDeviceSummaryShow = false;
      //this.getDeviceChartDatas();  // 重新获取总用时能耗
    },

    // 跳转原理图
    handleDevicePrototypeShow(device, ind) {
      // const { href } = this.$router.resolve({
      //   path: `/deviceMgt/device/prototype`,
      //   query: {
      //     deviceId:device.id,
      //     deviceName: device.name,
      //     deviceType: device.type,
      //     resourceId: device.resourceId,
      //     displayType: device.protoTypeDisplay || this.protoTypeDisplay,
      //   }
      // })
      // window.open(href, 'prototypePage');
      this.activeDeviceInd = ind;
      this.activeDevice = device;
      this.activeDevicePrototypeShow = true;
      console.log("handleDevicePrototypeShow", this.activeDevicePrototypeShow);
      // this.$router.push({path:'/deviceMgt/device/prototype', query:{deviceId:device.id, resourceId:device.resourceId}});
    },
    handleDevicePrototypeClose() {
      this.activeDevicePrototypeShow = false;
    },

    // 楼层列表
    async getResourceList(ind) {
      console.log(ind);
      this.showLoad();
      let {data} = await resourceList({buildingId: this.gf.getBuildingId(), category: this.category, type: this.type})
      this.resourceFullList = data;
      // 生成资源分组信息
      this.resourceGroupInit();
      // 默认选中第一个
      await this.handleResourceGroupChange({index: 0});
      this.hideLoad();

    },
    // 初始化资源分组信息
    resourceGroupInit() {
      let gkey = "未分组";
      let map = {};
      this.resourceFullList.map( r => {
        r.groupName = r.groupName || gkey;
        if(map[r.groupName]) {
          map[r.groupName].push(r);
        } else {
           map[r.groupName] = [r];
        }
        // 生成资源组选项
        this.buildingChecked[r.groupName] = {
          isIndeterminate: false,
          checkAll: false,
          checkedFloors: [],
        }
        // 生成设备组选项
        this.deviceChecked[r.id+''] = {
          isIndeterminate: false,
          checkAll: false,
          checkedDevices: [],
        }
      });
      let keys = Object.keys(map);
      // 按名字排序
      keys = keys.sort((a,b) => {
        return a > b ? 1 : -1;
      });
      this.resourceGroupList = keys;
      this.resourceFullMap = map;
      console.log("resourceGroupInit",
        this.resourceGroupList,
        this.resourceFullMap,
        this.buildingChecked,
        this.deviceChecked
      );
    },

    // 楼层的设备列表
    // !!!! 只有主设备 !!!!
    async getResourceMainDeviceList(resourceId, callback) {
      console.log("getResourceMainDeviceList", resourceId);
      let pageNum = this.pageNum;
      this.refreshFlag = false;
      if (typeof resourceId == "undefined" ) {
        resourceId = this.activeResourceId;
      }
      this.showLoad();
      // 拉取数据
      let response = await listResourceDevice({
        resourceId: resourceId,
        deviceTypes: this.deviceMainType,
        orderBy: this.orderBy,
      })
      this.deviceList = response.data || [];
      this.deviceSummary = this.summaryDevice();
      this.deviceList = this.deviceSummary["total"];
      if(this.activeDevice && this.activeDevice.id > 0) {
        // 找到当前选中设备
        let id = this.activeDevice.id;
        this.deviceList.map( d => {
          if(d.id == id) {
            this.activeDevice = d;
          }
        });
        console.log("------->", this.activeDevice);
      }
      // if(this.activeDeviceInd >= 0) {
      //   this.pageNum = pageNum;
      //   let ind = this.activeDeviceInd + (this.pageSize * (this.pageNum -1));
      //   console.log("------->", pageNum, ind, this.deviceList.length);
      //   if(ind < this.deviceList.length) {
      //     this.activeDevice = this.deviceList[ind];
      //   }
      // }
      this.hideLoad();
      this.$nextTick(function() {
        this.refreshFlag = true;
      });
    },
    // 楼层的设备列表
    // !!!! 所有种类设备 !!!!
    async getResourceFullDeviceList(resourceId, callback) {
      console.log("DisplayByMap getResourceDeviceList", resourceId);
      let pageNum = this.pageNum;
      this.refreshFlag = false;
      if (typeof resourceId == "undefined" ) {
        resourceId = this.activeResourceId;
      }
      // this.scale = 1; // 还原比例
      this.showLoad();
      // 拉取数据
      let response = await listResourceDevice({
        resourceId: resourceId,
        deviceTypes: this.deviceType,
        orderBy: this.orderBy,
      });
      this.deviceList = response.data || [];
      this.deviceSummary = this.summaryDevice();
      this.deviceList = this.deviceSummary["total"];
      if(this.activeDevice && this.activeDevice.id > 0) {
        // 找到当前选中设备
        let id = this.activeDevice.id;
        this.deviceList.map( d => {
          if(d.id == id) {
            this.activeDevice = d;
          }
        });
        console.log("------->", this.activeDevice);
      }
      // if(this.activeDeviceInd >= 0) {
      //   this.pageNum = pageNum;
      //   let ind = this.activeDeviceInd + (this.pageSize * (this.pageNum -1));
      //   if(ind < this.deviceList.length) {
      //     this.activeDevice = this.deviceList[ind];
      //   }
      // }
      this.hideLoad();
      this.$nextTick( () => {
        this.$forceUpdate();
      });
      console.log("getResourceDeviceList", this.deviceList);
      // 获取用时，用能数据
      //this.getDeviceChartDatas();
    },

    // 加载设备数据模型
    getDeviceDataModel() {
      // 获取设备
      if(this.deviceList && this.deviceList.length > 0) {
        let data = this.deviceList[0];
        if(data) {
          (data.deviceDataBase || []).map( (dd,_i) => {
            // 容错，被其他组件 mixins 后，没有 this.globalVal 变量
            if(typeof this.globalVal == "object") {
              // 翻译所有状态成文本
              if(dd.drDataType == "float") {
                dd.editType = "inputNum";
                dd.valStr = dd.dVal + ' ' + dd.dDataUnit;
              } else {
                if(dd.dOtherData.trim() != "") {
                  var ddMap = {};
                  var kl = dd.dOtherData.trim().split(";");
                  for(var i in kl) {
                    var s = kl[i].trim().split(":");
                    if(s.length == 2) {
                      ddMap[s[0]] = s[1];
                    }
                  }
                  dd.otherDataMap = ddMap;
                  dd.valStr = ddMap[dd.dVal];
                  if(kl.length < 1) {
                    dd.editType = "input";
                  } else if(kl.length === 1) {
                    dd.editType = "button";
                  } else if(kl.length == 2) {
                    dd.editType = "switch";
                  } else if(kl.length > 2) {
                    dd.editType = "select";
                  }
                } else {
                  dd.editType = "input";
                  dd.valStr = dd.dVal + ' ' + dd.dDataUnit;
                }
              }
              let hasKey = Object.keys(this.globalVal).indexOf(dd.dmName) >= 0;
              if(hasKey && !this.globalSwichTypes.hasOwnProperty(dd.dmName)) {
                this.$set(this.globalSwichTypes, dd.dmName, {
                  dmName: dd.dmName,
                  drDataUnit: dd.drDataUnit,
                  editType: dd.editType,
                  otherDataMap: dd.otherDataMap,
                });
              }
            }
          });
        }
      }
      /*
      this.showLoad();
      deviceFullList({buildingId: this.curBuilding.id, types: this.deviceMainType})
        .then(({data}) => {
          this.hideLoad();
          if(data.length > 0) {
            this.showLoad();
            resourceDevice({deviceId: data[0].id})
              .then(({data}) => {
                this.hideLoad();
                if(data) {
                  data.deviceDataBase.map( (dd,_i) => {
                    // 容错，被其他组件 mixins 后，没有 this.globalVal 变量
                    if(typeof this.globalVal == "object") {
                      // 翻译所有状态成文本
                      if(dd.drDataType == "float") {
                        dd.editType = "inputNum";
                        dd.valStr = dd.dVal + ' ' + dd.dDataUnit;
                      } else {
                        if(dd.dOtherData.trim() != "") {
                          var ddMap = {};
                          var kl = dd.dOtherData.trim().split(";");
                          for(var i in kl) {
                            var s = kl[i].trim().split(":");
                            if(s.length == 2) {
                              ddMap[s[0]] = s[1];
                            }
                          }
                          dd.otherDataMap = ddMap;
                          dd.valStr = ddMap[dd.dVal];
                          if(kl.length < 2) {
                            dd.editType = "input";
                          } else if(kl.length == 2) {
                            dd.editType = "switch";
                          } else if(kl.length > 2) {
                            dd.editType = "select";
                          }
                        } else {
                          dd.editType = "input";
                          dd.valStr = dd.dVal + ' ' + dd.dDataUnit;
                        }
                      }
                      let hasKey = Object.keys(this.globalVal).indexOf(dd.dmName) >= 0;
                      if(hasKey && !this.globalSwichTypes.hasOwnProperty(dd.dmName)) {
                        this.$set(this.globalSwichTypes, dd.dmName, {
                          dmName: dd.dmName,
                          drDataUnit: dd.drDataUnit,
                          editType: dd.editType,
                          otherDataMap: dd.otherDataMap,
                        });
                      }
                    }
                  });
                }
              }).catch(e => {
                console.log("resourceDevice error", e.message);
                throw Error("resourceDevice", e.message);
              });
          }
        }).catch(e => {
          console.log("getDeviceList error", e.message);
          this.$message({ showClose: true, message: "数据加载失败，请刷新页面重试。getDeviceList", type: "warning" });
          this.hideLoad();
        });
       */
    },

    // 格式化设备特殊信息
    initItemData(item) {
      return item;
    },
    // 格式化设备信息
    initItemBaseData(item, opts) {
      let that = this;
      let d = item;
      if(!d) {
        return false;
      }

      // 设备有效时长
      d.activePercentage = 100;
      d.activeColor = "#67C23A";
      if(d.scrapTime != "" && d.activationTime != "") {
        d.activePercentage = parseFloat((100 * this.$moment(d.scrapTime).diff(this.$moment(), 'day') / this.$moment(d.scrapTime).diff(this.$moment(d.activationTime), 'day')).toFixed(2));
        if(d.activePercentage > 50) {
          d.activeColor = "#67C23A";
        } else if(d.activePercentage > 30) {
          d.activeColor = "#E6A23C";
        } else {
          d.activeColor = "#F56C6C";
        }
      }

      // 原逻辑，优先判定在线，再判定告警，再判断开关
      // d.status = "healthy";
      // // 在线 离线
      // if(d.communicateStatus == '离线') {
      //   d.status = "offline";
      // } else {
      //   if(d.workStatus.indexOf('停') >= 0 || d.workStatus.indexOf('关') >= 0) {
      //     d.status = "stop";
      //   } else if(d.workStatus.indexOf('运') >= 0 || d.workStatus.indexOf('开') >= 0 ) {
      //     d.status = "running";
      //   }
      //   if(d.warningStatus != "健康") {
      //     d.status = "alert";
      //   }
      // }

      d.showImages = opts ? opts.showImages : this.showImages || "0"; // 绑定设备原理图是否显示
      d.hasEnergy = opts ? opts.hasEnergy : this.hasEnergy || "0"; // 绑定设备用能是否显示
      d.hasWarning = opts ? opts.hasWarning : this.hasWarning || "0"; // 绑定设备报警管理是否显示
      d.hasDeviceInfo = opts ? opts.hasDeviceInfo : this.hasDeviceInfo || "0"; // 绑定设备是否显示设备信息
      d.hasMaintenance = opts ? opts.hasMaintenance : this.hasMaintenance || "0"; // 绑定设备是否显示运维相关tab
      d.hasEleProspection = opts ? opts.hasEleProspection : this.hasEleProspection || "0"; // 电气设备是否显示
      d.hasCOLink = opts ? opts.hasCOLink : this.hasCOLink || "0"; // CO联动是否显示
      d.hasLock = opts ? opts.hasLock : this.hasLock || "0";     // 绑定设备是否有锁定控制
      d.protoTypeDisplay = opts  ? opts.protoTypeDisplay : this.protoTypeDisplay || "";
      d.forceUpdate = opts ? opts.forceUpdate : this.forceUpdate || "0";
      d.strategyOpts = this.strategyOpts; // 绑定设备控制策略模型
      d.hasEditPosition = opts ? opts.hasEditPosition : this.hasEditPosition || "0"; // 位置编辑是否显示

      d.mode = '';
      // 默认未加锁
      d.isLock = false;
      // 是否有点位被锁定
      d.hasLocked = false;
      // 锁定点位列表
      d.lockedList = [];
      // 默认不可控制
      d.hasControl = false;
      // 默认无监测点
      d.hasMonitor = false;
      // 功率趋势控制显示 tag中包含monitor,power
      d.hasMonitorAndPower = false;
      // 电流趋势控制显示 tag中包含monitor,current
      d.hasMonitorAndCurrent = false;
      // // 电压趋势控制显示 tag中包含monitor,voltage
      d.hasMonitorAndVoltage = false;
      // 输出全局设置 globalSwichTypes
      if(d.deviceDataBase.length > 0) {
        // 支持过滤掉需要隐藏的点，tag 包含 hidden 的点
        d.deviceDataBase = d.deviceDataBase.filter( p => {
          return p.dmTag.indexOf("hidden") == -1;
        });
        d.deviceDataBase.map((dd,_i) => {
          d.hasLocked = d.hasLocked || dd.drLocked == 1;
          d.hasControl = d.hasControl || dd.drId > 0;
          d.hasMonitor = d.hasMonitor || dd.dmTag.indexOf("monitor") >= 0;
          d.hasMonitorAndPower = d.hasMonitorAndPower || ['monitor','power'].every(it => dd.dmTag.indexOf(it) >= 0);
          d.hasMonitorAndCurrent = d.hasMonitorAndCurrent || ['monitor','current'].every(it => dd.dmTag.indexOf(it) >= 0);
          d.hasMonitorAndVoltage = d.hasMonitorAndVoltage || ['monitor','voltage'].every(it => dd.dmTag.indexOf(it) >= 0);
          d.isLock = d.isLock || dd.drLocked > 0;

          if(dd.dVal && !isNaN(Number(dd.dVal)) && (dd.dVal.toString().includes('.') && (dd.dVal.toString()).split('.')[1].length > 6)) {
            dd.dVal = Number(dd.dVal).toFixed(2)
          }
          if(dd.drVal && !isNaN(Number(dd.drVal)) && (dd.drVal.toString().includes('.') && (dd.drVal.toString()).split('.')[1].length > 6)) {
            dd.drVal = Number(dd.drVal).toFixed(2)
          }

          // 翻译所有状态成文本
          // if(dd.dDataType == "float") {
          //   dd.dVal = parseFloat(dd.dVal).toFixed(2);
          // }
          if(dd.drDataType == "float") {
            dd.editType = "inputNum";
            dd.valStr = dd.dVal + ' ' + dd.dDataUnit;
            dd.rValStr = dd.drVal + ' ' + dd.drDataUnit;
          } else {
            if(dd.dOtherData.trim() != "") {
              // 针对读写 note 不一样的情况，需要分开
              var ddMap = {}; // 读点
              var ddrMap = {}; // 写点
              var kl = dd.dOtherData.trim().split(";");
              var klr = dd.drOtherData.trim().split(";");
              for(var i in kl) {
                var s = kl[i].trim().split(":");
                if(s.length == 2) {
                  ddMap[s[0]] = s[1];
                }
              }
              for(var ir in klr) {
                var sr = klr[ir].trim().split(":");
                if(sr.length == 2) {
                  ddrMap[sr[0]] = sr[1];
                }
              }
              dd.otherDataMap = ddMap;   // 读点
              dd.otherRDataMap = ddrMap; // 写点
              dd.valStr = ddMap[dd.dVal] || "";
              dd.rValStr = ddrMap[dd.drVal] || "";

              if(kl.length < 1) {
                dd.editType = "input";
              } else if(kl.length === 1) {
                dd.editType = "button";
              } else if(kl.length == 2) {
                dd.editType = "switch";
              } else if(kl.length > 2) {
                dd.editType = "select";
              }

              // 地图上的设备快速开关
              if(dd.dmTag.indexOf("status") >= 0) {
                d.swichObj = {
                  drId: dd.drId,
                  drVal: dd.drVal,
                  drDataType: dd.drDataType,
                  drRealVal: ''
                }
              }

              // 针对空调 统计空调运行模式个数
              // if(dd.dmName.indexOf("模式") >= 0) {
              //   if(dd.valStr.indexOf("制冷") >= 0) {
              //     d.mode = "cool";
              //   } else if(dd.valStr.indexOf("制热") >= 0) {
              //     d.mode = "hot";
              //   } else if(dd.valStr.indexOf("除湿") >= 0) {
              //     d.mode = "dehumidification";
              //   }
              // }

            } else {
              dd.editType = "input";
              dd.valStr = dd.dVal + ' ' + dd.dDataUnit;
              dd.rValStr = dd.drVal + ' ' + dd.drDataUnit;
            }
          }

          // 更新判定设备当前状态 (拿到最新开关状态)
          if(dd.dmTag.indexOf("status") >= 0) {
            if(dd.valStr.indexOf("开") >= 0 || dd.valStr.indexOf("启") >= 0 || dd.valStr.indexOf("运") >= 0) {
              d.status = "running";
              d.workStatus = "运行";
              d.swichObj.drRealVal = 1;
            } else if(dd.valStr.indexOf("关") >= 0 || dd.valStr.indexOf("停") >= 0) {
              d.status = "stop";
              d.workStatus = "停止";
              d.swichObj.drRealVal = 0;
            }
          }

          // 补充锁定的值
          if(dd.drLocked == 1) {
            d.lockedList.push(dd.dmName + ": " + dd.rValStr);
          }
          // 处理摄像头的url
          if(dd.dmName && dd.dmName.toLowerCase() == "fullpath" && dd.dVal != "") {
            d.fullPath = dd.dVal;
          }
          if(dd.dmName && dd.dmName.toLowerCase() == "rtsp" && dd.dVal != "") {
            d.rtsp = dd.dVal;
          }
          // 容错，被其他组件 mixins 后，没有 this.globalVal 变量
          // if(typeof this.globalVal == "object") {
          //   let hasKey = Object.keys(this.globalVal).indexOf(dd.dmName) >= 0;
          //   if(hasKey) {
          //     this.globalSwichTypes[dd.dmName] = {
          //       dmName: dd.dmName,
          //       drDataUnit: dd.drDataUnit,
          //       editType: dd.editType,
          //       otherDataMap: dd.otherDataMap,
          //     }
          //   }
          // }
        })
      }

      // 输出全局设置 globalSwichTypes
      if(d.deviceDataWarn && d.deviceDataWarn.length > 0) {
        d.deviceDataWarn.map((dd,_i) => {
          if(dd.dOtherData.trim() != "") {
            // 针对读写 note 不一样的情况，需要分开
            var ddMap = {}; // 读点
            var ddrMap = {}; // 写点
            var kl = dd.dOtherData.trim().split(";");
            var klr = dd.drOtherData.trim().split(";");
            for(var i in kl) {
              var s = kl[i].trim().split(":");
              if(s.length == 2) {
                ddMap[s[0]] = s[1];
              }
            }
            for(var ir in klr) {
              var sr = klr[ir].trim().split(":");
              if(sr.length == 2) {
                ddrMap[sr[0]] = sr[1];
              }
            }
            dd.otherDataMap = ddMap;   // 读点
            dd.otherRDataMap = ddrMap; // 写点
            dd.valStr = ddMap[dd.dVal] || "";
          } else {
            dd.editType = "input";
            dd.valStr = dd.dVal + ' ' + dd.dDataUnit;
          }
        })
      }

      if(d.deviceDataEnv.length > 0) {
        d.envStr = d.deviceDataEnv[0].dVal + d.deviceDataEnv[0].dDataUnit;
        d.deviceDataEnv.map(dd => {
          dd.valStr = dd.dVal + ' ' + dd.dDataUnit;
        })
      }

      // 判定设备当前状态，影响卡片样式
      // d.status = "default";
      d.dRuning = "default";
      d.dStatus = "default";
      d.dOnline = "default";
      // 在线 离线
      if(d.communicateStatus.indexOf('离线') >= 0) {
        // d.status = "offline";
        d.dOnline = "offline";
      } else if(d.communicateStatus.indexOf('在线') >= 0) {
        // d.status = "online";
        d.dOnline = "online";
      }
      if(d.workStatus.indexOf('停') >= 0 || d.workStatus.indexOf('关') >= 0) {
        // d.status = "stop";
        d.dRuning = "stop";
      } else if(d.workStatus.indexOf('开') >= 0 || d.workStatus.indexOf('启') >= 0 || d.workStatus.indexOf('运') >= 0) {
        // d.status = "running";
        d.dRuning = "running";
      }
      if(d.warningStatus == "故障") {
        // d.status = "fault";
        d.dStatus = "fault";
      } else if(d.warningStatus == "告警") {
        // d.status = "warning";
        d.dStatus = "warning";
      } else if(d.warningStatus == "健康") {
        // d.status = "healthy";
        // d.dStatus = "healthy";
      }

      // 优先判定在线，再判定告警，再判断开关
      d.status = "healthy";
      // 在线 离线
      if(d.communicateStatus == '离线') {
        d.status = "offline";
      } else {
        if(d.workStatus.indexOf('停') >= 0 || d.workStatus.indexOf('关') >= 0) {
          d.status = "stop";
        } else if(d.workStatus.indexOf('运') >= 0 || d.workStatus.indexOf('开') >= 0 || d.workStatus.indexOf('启') >= 0 ) {
          d.status = "running";
        }
        if(d.warningStatus == "故障") {
          d.status = "fault";
        } else if(d.warningStatus == "告警") {
          d.status = "warning";
        }
      }

      // 设备2D地图信息
      // 2.5D 位置大小在 otherData 里面
      let _od = {
        left: d.xAxis,
        top: d.yAxis,
        width: 40,     // 图标尺寸
        height: 40,
        padding: 2,
        imgWidth: 36,   // 图片尺寸
        imgHeight: 36,
      }
      let _od2 = {}
      try {
        _od2 = JSON.parse(d.otherData);
      } catch(e) {
        // pass
      }
      d.pos = {
        ..._od,
        ..._od2,
      };
      d.icon = d.icon.replace("default", d.status);

      // 设备预警状态
      // if(d.warningRuleList.length > 0) {
      //   d.warningRuleList.map(r => {
      //     r.isWarning = false;
      //     r.warningColor = "#67C23A";
      //     if(d.warningList.length > 0) {
      //       d.warningList.map(w => {
      //         if(w.warningCategory == r.name) {
      //           r.isWarning = true;
      //           r.warningColor = "#F56C6C";
      //           //r.warningColor = r.severity == "严重" ? "#F56C6C" : "#E6A23C";
      //         }
      //       });
      //     }
      //   });
      // }

      // 设备预警记录
      // if(d.warningHistoryList.length > 0) {
      //   d.warningHistoryList.map(w => {
      //     w.hasFixedStr = that.gf.getWarningStr(w.hasFixed);
      //     w.flowStatusStr = that.gf.getWarningStr(w.flowStatus);
      //     return w;
      //   });
      // }

      // 维保记录
      // if(d.maintenanceList.length > 0) {
      //   d.maintenanceStr = d.maintenanceList[0].operator + '(' + d.maintenanceList[0].updatedAt.split(" ")[0] + ')';
      //   d.maintenanceList.map(dd => {
      //     if(dd.pictures.trim() != "") {
      //       try {
      //         dd.pictureList = JSON.parse(dd.pictures);
      //         dd.pictureUrlList = dd.pictureList.map( p => p.url );
      //       } catch(e) {
      //         dd.pictureList = [];
      //       }
      //     }
      //   });
      // }

      // 维保信息
      d.brand = d.brand || "";
      d.model = d.model || "";
      d.contact = d.contact || "";
      d.mobile = d.mobile || "";
      try {
        if(typeof d.maintenance == 'string'){
          d.maintenance = JSON.parse(d.maintenance);
        }
      } catch(e) {
        d.maintenance = {
          contact: "",
          mobile: "",
          company: "",
        }
      }
      d.maintenanceTime = "";
      try {
        if(d.activationTime) {
          d.maintenanceTime = this.$moment(d.activationTime).add(Math.max(0, d.maintenanceSpan) , "day").format("YYYY-MM-DD HH:mm:ss");
        }
      } catch(e) {
        // pass
      }
      // 基础信息
      try {
        d.noteObj = JSON.parse(d.note);
      } catch(e) {
        d.noteObj = null;
      }
      // 铭牌信息
      try {
        d.nameplateObj = JSON.parse(d.nameplate);
      } catch(e) {
        d.nameplateObj = null;
      }
      // 电路信息
      try {
        d.circuitObj = JSON.parse(d.circuit);
      } catch(e) {
        d.circuitObj = null;
      }
      // 设备图片
      try {
        d.pictureList = JSON.parse(d.pictures);
        d.pictureUrlList = d.pictureList.map(p => p.url);
      } catch(e) {
        d.pictureList = [];
      }
      // 设备文件
      try {
        d.fileList = JSON.parse(d.files);
        //d.fileUrlList = d.fileList.map(p => p.url);
      } catch(e) {
        d.fileList = [];
      }
      d.fileListFlat = [];
      try{
        for(let tl in d.fileList) {
          if(Array.isArray(d.fileList[tl])) {
            for(let i = 0; i < d.fileList[tl].length; i++) {
              d.fileListFlat.push(d.fileList[tl][i]);
            }
          } else {
            d.fileListFlat.push(d.fileList[tl]);
          }
        }
      } catch(e) {
        // pass
      }

      d.activationTime = d.activationTime || "";
      d.scrapTime = d.scrapTime || "";

      // 设备控制策略
      d.strategy = d.strategyList.length > 0 ? d.strategyList.map(s => s.id) : [];
      return d;
    },

    // 设备统计逻辑
    summaryDevice(deviceList) {
      // 统计设备总数
      var res = {
        "total": [],

        "running": [],
        "stop": [],

        "offline": [],
        "online": [],

        "healthy": [],
        "warning": [],  // 报警
        "fault": [],  // 故障

        "error": [],
        "unknow": [],

        "cool": [],
        "hot": [],
        "dehumidification": [],
      };
      let list = deviceList || this.deviceList;

      list.map(d => {
        // 格式化单个设备数据
        d = this.initItemData(d);
        console.log(this.showImages, d.showImages);
        if(d) {
          d = this.initItemBaseData(d, {
            showImages: this.showImages || d.showImages || "0",
            hasLock: this.hasLock || d.hasLock || "0",
            hasEnergy: this.hasEnergy || d.hasEnergy || "0",
            hasWarning: this.hasWarning || d.hasWarning || "0",
            hasDeviceInfo: this.hasDeviceInfo || d.hasDeviceInfo || "0",
            hasMaintenance: this.hasMaintenance || d.hasMaintenance || "0",
            hasEleProspection: this.hasEleProspection || d.hasEleProspection || "0",
            hasCOLink: this.hasCOLink || d.hasCOLink || "0",
            protoTypeDisplay: this.protoTypeDisplay || d.protoTypeDisplay || "",
            forceUpdate: this.forceUpdate || d.forceUpdate || "0",
            hasEditPosition: this.hasEditPosition || d.hasEditPosition || "0",
          });
          // 统计个数
          res.total.push(d);
          // 开启  关闭  无状态(传感器)
          if(d.workStatus.indexOf("停") >= 0 || d.workStatus.indexOf('关') >= 0 ) {
            res.stop.push(d);
          } else if(d.workStatus.indexOf('运') >= 0 || d.workStatus.indexOf('开') >= 0 || d.workStatus.indexOf('启') >= 0) {
            res.running.push(d);
          }
          // 告警 故障 健康
          if(d.warningStatus == '告警') {
            res.warning.push(d);
          } else if(d.warningStatus == '故障') {
            res.fault.push(d);
          } else {
            res.healthy.push(d);
          }
          // 在线 离线
          if(d.communicateStatus == '离线') {
            res.offline.push(d);
          } else {
            res.online.push(d);
          }
        }
      });
      console.log("summaryDevice ==>", list, res);
      // 增加模式计数
      // this.deviceList.map(d => {
      //   if(d.mode == "cool") {
      //     res.cool.push(d);
      //   } else if(d.mode == "hot") {
      //     res.hot.push(d);
      //   } else if(d.mode == "dehumidification") {
      //     res.dehumidification.push(d);
      //   }
      // });
      return res;
    },

    // 禁止拖拽
    mousedownDisabled(e) {
      e.stopPropagation(); //阻止点击事件向上冒泡
    },
    mousemoveDisabled(e) {
      e.stopPropagation(); //阻止点击事件向上冒泡
    },

    deviceComp(type, suffix) {
      let res = "base" + suffix;
      let tp = type;
      if(type.indexOf('SXT') > -1){//摄像头类型编码，包含SXT的归为camera组件展示  eg: SXT01,SXT02
        tp = 'camera'
      }
      if(this.hasComponent(tp + suffix)) {
        res = tp + suffix;
      }
      console.log("deviceComp", res);
      return res;
    },
    // 判断组件是否存在
    hasComponent (type) {
      return this.$options.components[type];
    },

    ///////////////////////////////////

    // 重新加载 StrategyList (适配 原型图 多种设备类型情况)
    async updateDeviceStrategyList(item) {
      let user = await this.gf.getUserProfile();
      // 读取设备的策略模型
      let {data} = await strategyList({deviceType: item.type, userId: user.userId, buildingId: this.gf.getBuildingId()});
      let list = [];
      let optMap = {};
      data.map( d => {
        if(optMap.hasOwnProperty(d.type)) {
          optMap[d.type].push({
            value: d.id,
            label: d.name,
          });
        } else {
          optMap[d.type] = [{
            value: d.id,
            label: d.name,
          }];
        }
      });
      for(let o in optMap) {
        list.push({
          label: o,
          options: optMap[o],
        })
      }
      // 补充自定义按钮
      // list.push({
      //   label: "编辑",
      //   options: [
      //     {
      //       label: "移除绑定策略",
      //       value: '',
      //     },
      //     {
      //       label: "编辑自定义策略",
      //       value: 'edit',
      //     },
      //     {
      //       label: "刷新策略列表",
      //       value: 'refresh',
      //     }
      //   ],
      // })
      console.log("getStrategyList", optMap, list);
      item.strategyOpts = list;
      item.strategy = item.strategyList.length > 0 ? item.strategyList.map(d=>d.id) : [];
      console.log("updateDeviceStrategyList", item);

      // 刷新组件
      // this.refreshFlag = false;
      // this.$nextTick(() => {
      //   this.refreshFlag = true;
      // })
      // this.$forceUpdate();
      return item;
    },

    // 重新加载 设备菜单配置 (适配 原型图 多种设备类型情况)
    updateDeviceTypeMenuConfig() {
      this.getDicts("d_device_list_display")
        .then(resp => {
          resp.data.map(d => {
            // 这里只需要更新 hasLock 一个字段
            if(d.dictLabel == this.item.type) {
              try {
                let conf = JSON.parse(d.dictValue);
                for(let o in conf) {
                  if(this.dialogConfigKeys.indexOf(o) >= 0) {
                    this.item[o] = conf[o];
                  }
                }
              } catch(e) {
                //pass
                console.log("d_device_list_display parse error", e, "val", d.dictValue);
              }
            }
          });
          console.log("updateDeviceTypeMenuConfig", this.item);
          // 刷新组件
          this.refreshFlag = false;
          this.$nextTick(() => {
            this.refreshFlag = true;
          })
          this.$forceUpdate();
        });
    },

    // 一键报修
    handleDeviceMaintenance2(device) {
      console.log(device)
      this.$router.push({
        path:'/maintananceMgt/ops/repair',
        query:{
          deviceId: device.id,
          method:"add"
        }
      });
    },
    // 修改设备名称
    handleChangeName(device) {
      this.$prompt('', '请输入设备名称', {
        inputValue: device.name,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      }).then(({ value }) => {
        console.log(value);
        if(value != "") {
          updateDevice({
            id: device.id,
            name: value,
          }).then(response => {
            this.msgSuccess("修改成功");
            this.open = false;
            this.$emit('updateData'); // 通知父组件
            this.$emit('refreshList'); // 通知父组件
          });
        }
      });
    },

    // 下发指令，更新设置, 保存设备修改
    handleSaveDevice(data) {
      // 拿到修改的内容
      var ch = [];
      if(data){
        // 按钮数据
        ch = [{
          id: data.drId,
          val: data.val,
        }]
      }else {
        this.item.deviceDataBase.map((d, i) => {
          console.log(d.dmName, this.item.forceUpdate, d.drVal, this.device.deviceDataBase[i].drVal)
          if(d.drVal != null && d.drVal != "" && (this.item.forceUpdate != "0" || d.drVal != this.device.deviceDataBase[i].drVal)) {
            var checked = false;
            // 校验数据
            if(d.drDataType == "float" && (/^-?\d*\.\d+$/.test(d.drVal) || parseFloat(d.drVal) == d.drVal)) {
              checked = true;
            }
            if(d.drDataType == "int" && d.drVal%1 === 0) {
              checked = true;
            }
            if(d.drDataType == "boolean" && (d.drVal == "true" || d.drVal == "false")) {
              checked = true;
            }
            if(d.drDataType == "string") {
              checked = true;
            }
            if(checked) {
              ch.push({
                id: d.drId,
                val: this.item.deviceDataBase[i].drVal,
                // type: d.dDataType,
                // deviceId: this.item.id,
                // deviceName: this.item.name,
                // deviceType: this.item.type,
                // dataId: d.drId,
                // dataName: d.dmName,
                // dataValueBefore: this.device.deviceDataBase[i].drVal,
                // dataValueAfter: this.item.deviceDataBase[i].drVal,
                // dataValueMapper: this.device.deviceDataBase[i].otherRDataMap,
              });
            } else {
              this.$message({
                message: "数据格式不正确, 请检查",
                type: "warning",
              });
            }
          }
        });
      }
      console.log(ch);
      if(ch.length > 0) {
        this.$confirm("确定保存更改?", {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.loading = true;
          updateDeviceDatas({dataList: JSON.stringify(ch)}).then(responses => {
            //this.activeDeviceInd = -1;
            this.isEdit = false;
            this.loading = false;
            this.$message.success("修改已保存");
          }).then(() => {
            //this.getResourceDeviceList(); // 父组件刷新列表
            this.$emit('updateData'); // 通知父组件
            this.$emit('refreshList'); // 通知父组件
          });
        }).catch((e) => {
          // pass
          this.loading = false;
          this.$emit('updateData'); // 通知父组件
          this.$emit('refreshList'); // 通知父组件
        });
      }

      // 检查策略
      if(this.item && this.item.strategy){
        if(Array.from(new Set(this.item.strategy)).sort() != Array.from(new Set(this.device.strategy)).sort()) {
          this.$emit('handleBatchStrategySend', this.item.id, this.strategyKey, this.item.strategy.join(",")); // 通知父组件
        }
      }
    },
    // 取消设备修改
    handleCancelDevice() {
      this.isEdit = false;
      this.$emit('updateData'); // 通知父组件
    },

    async getStrategyList(type) {
      // 只取主设备的策略模型
      this.showLoad();
      console.log(type,'type')
      let deviceType = type ? type : this.deviceMainType;
      if(deviceType) {
        let {data} = await strategyList({deviceType: deviceType, buildingId: this.gf.getBuildingId()});
        this.hideLoad();
        this.strategyList = data;
        let list = [];
        let optMap = {};
        data.map( d => {
          if(optMap.hasOwnProperty(d.type)) {
            optMap[d.type].push({
              value: d.id,
              label: d.name,
            });
          } else {
            optMap[d.type] = [{
              value: d.id,
              label: d.name,
            }];
          }
        });
        for(let o in optMap) {
          list.push({
            label: o,
            options: optMap[o],
          })
        }
        // 补充自定义按钮
        // list.push({
        //   label: "编辑",
        //   options: [
        //     {
        //       label: "移除绑定策略",
        //       value: '',
        //     },
        //     {
        //       label: "编辑自定义策略",
        //       value: 'edit',
        //     },
        //     {
        //       label: "刷新策略列表",
        //       value: 'refresh',
        //     }
        //   ],
        // })
        console.log("getStrategyList", optMap, list);
        this.strategyOpts = list;
        return list;
      } else {
        this.hideLoad();
      }
    },

    // 编辑设备列表关闭
    handleResoureEditClose() {
      console.log("handleResoureEditClose")
      this.getResourceDeviceList();
    },

    // 批量设备控制模式更新
    handleBatchStrategySend(deviceIds, key, val) {
      console.log(deviceIds, key, val);
      this.showLoad();
      updateStrategyDevice({
        strategyIds: val,
        deviceIds: deviceIds.join(","),
        method: "card",
      }).then(({data}) => {
        this.$message({
          message: "操作成功，请稍后查看更新状态",
          type: "info",
        });
        this.hideLoad();
      }).catch(e => {
        this.$message({
          message: "操作失败，请重试。",
          type: "warning",
        });
        this.hideLoad();
      });
    },

    // 批量控制指令下发
    handleBatchSwitchSend(deviceIds, key, val) {
      console.log(deviceIds, key, val);
      this.showLoad();
      updateDeviceDataBatch({
        deviceIds: deviceIds.join(","),
        dataName: key,
        dataValue: val,
        forceUpdate: this.forceUpdate,
      }).then(({data}) => {
        this.$message({
          message: "操作成功，请稍后查看更新状态。",
          type: "info",
        });
        this.hideLoad();
      }).catch(e => {
        this.$message({
          message: "操作失败，请重试。",
          type: "warning",
        });
        this.hideLoad();
      });
    },

    // 锁定设备面板
    handleItemDataLock(deviceIds, deviceType, key, val) {
      console.log("handleItemDataLock", deviceIds, key, val);
      updateDeviceDataLock({
        deviceIds: deviceIds.join(","),
        deviceType: deviceType,
        dataName: key,
        dataValue: val,
      }).then(({data}) => {
        this.$message({
          message: "操作成功，请稍后查看更新状态。",
          type: "info",
        });
      }).catch(e => {
        this.$message({
          message: "操作失败，请重试。",
          type: "warning",
        });
      });
    },
    // 解锁设备面板
    handleItemDataUnlock(deviceIds, deviceType) {
      console.log("handleItemDataUnlock", deviceIds);
      updateDeviceDataUnlock({
        deviceIds: deviceIds.join(","),
        deviceType: deviceType,
      }).then(({data}) => {
        this.$message({
          message: "操作成功，请稍后查看更新状态。",
          type: "info",
        });
      }).catch(e => {
        this.$message({
          message: "操作失败，请重试。",
          type: "warning",
        });
      });
    },

    // 作废
    handleBatchSwitchSendBak(deviceIds, key, val) {
      console.log(deviceIds, key, val);
      // 遍历所有设备, 执行相同操作
      var ch = [];
      this.deviceList.map( device => {
        device.deviceDataBase.map( d => {
          if(d.dmName == key && d.drVal != val) {
            ch.push({
              id: d.drId,
              val: val,
              type: d.drDataType,
            });
          }
        });
      });
      if(ch.length > 0) {
        this.showLoad();
        console.log(ch);
        updateDeviceDatas({dataList: JSON.stringify(ch)}).then(responses => {
          //this.activeDeviceInd = -1;
          //this.activeDevice = {};
          this.hideLoad();
        }).then(() => {
          this.getResourceDeviceList();
        }).catch(e=>{
          this.hideLoad();
        });
      } else {
        this.$message({
          message: "所有设备已经是当前状态",
          type: "info",
        });
      }
    },

    // 找出两次值的变化
    checkDiff(arrBefore, arrAfter) {
      // 找出增加的
      let add = null;
      let remove = null;
      if(arrAfter.length > arrBefore.length) {
        arrAfter.map( d => {
          if(arrBefore.indexOf(d) < 0) {
            add = d;
          }
        });
      } else if(arrAfter.length < arrBefore.length) {
        arrBefore.map( d => {
          if(arrAfter.indexOf(d) < 0) {
            remove = d;
          }
        });
      }
      return {
        add: add,
        remove: remove,
      }
    },
    // 设备选择控制
    // 0 => 根; 1 => 大楼; 2 => 楼层
    updateCheckboxDisplay(level) {
      if(level == 0) {
        // 当前子状态
        let childIndeterminate = Object.keys(this.buildingChecked).reduce((prev, cur, index, arr) => {
          return prev || this.buildingChecked[arr[index]].isIndeterminate;
        }, false);
        this.isAllIndeterminate = this.isAllIndeterminate || childIndeterminate;
        this.checkAll = this.checkedBuildings.length == this.resourceGroupList.length ? true : false;
      } else if(level == 1) {
        let b = this.activeResourceGroup;
        this.buildingChecked[b].isIndeterminate = this.buildingChecked[b].checkedFloors.length > 0 && this.buildingChecked[b].checkedFloors.length < this.resourceFullMap[b].length;
        this.buildingChecked[b].checkAll = this.buildingChecked[b].checkedFloors.length == this.resourceFullMap[b].length ? true : false;
        this.$nextTick( () => {
          this.$forceUpdate();
          this.updateCheckboxDisplay(0);
        });
      } else if(level == 2) {
        let r = this.activeResource;
        this.deviceChecked[r.id+''].isIndeterminate = this.deviceChecked[r.id+''].checkedDevices.length > 0
                                                    && this.deviceChecked[r.id+''].checkedDevices.length < this.deviceList.length;
        this.deviceChecked[r.id+''].checkAll = this.buildingChecked[this.activeResourceGroup].checkAll || this.deviceChecked[r.id+''].checkedDevices.length == this.deviceList.length;
        this.$nextTick( () => {
          this.$forceUpdate();
          this.updateCheckboxDisplay(1);
        });
      }
    },


    ///////////////// 设备选择逻辑 /////////////////
    // 全部 checkbox 选中切换
    handleCheckAllChange(val) {
      // checkbox ui 更新
      console.log("handleCheckAllChange", this.checkAll, val);
      this.checkedBuildings = this.checkAll ? this.resourceGroupList : [];
      this.isAllIndeterminate = false;
      // 所有建筑 checkbox UI 更新
      if(this.checkAll) {
        for(let b in this.buildingChecked) {
          this.buildingChecked[b].checkAll = true;
          this.buildingChecked[b].isIndeterminate = false;
          this.buildingChecked[b].checkedFloors = this.resourceFullMap[b].map( r => r.id+'' );
        }
      } else {
        for(let b in this.buildingChecked) {
          this.buildingChecked[b].checkAll = false;
          this.buildingChecked[b].isIndeterminate = false;
          this.buildingChecked[b].checkedFloors = [];
        }
      }
      // 选中建筑 所有楼层 UI 更新
      if(this.checkAll) {
        for(let f in this.deviceChecked) {
          this.deviceChecked[f+''].checkAll = true;
          this.deviceChecked[f+''].isIndeterminate = false;
          this.deviceChecked[f+''].checkedDevices = this.resourceDeviceList.filter( (d) => {
            return d.resourceId == f;
          }).map( d => d.id );
        }
      } else {
        for(let f in this.deviceChecked) {
          this.deviceChecked[f+''].checkAll = false;
          this.deviceChecked[f+''].isIndeterminate = false;
          this.deviceChecked[f+''].checkedDevices = [];
        }
      }
      // 选中楼层 所有设备 UI 更新 checkbox-group 解决
    },
    // 全部 group 选中切换
    // handleCheckAllGroupChange() {
    //   console.log("handleCheckAllGroupChange", this.checkedBuildings);
    //   // UP 全部 checkbox 选中切换
    //   this.checkAll = this.checkedBuildings.length === this.resourceGroupList.length;
    //   this.isAllIndeterminate = this.checkedBuildings.length > 0 && this.checkedBuildings.length < this.resourceGroupList.length;
    //   // 判断楼层是否有选中
    //   let childIndeterminate = Object.keys(this.buildingChecked).reduce((prev, cur, index, arr) => {
    //     return prev || this.buildingChecked[arr[index]].isIndeterminate || this.buildingChecked[arr[index]].checkAll;
    //   }, false);
    //   this.isAllIndeterminate = this.isAllIndeterminate || childIndeterminate;
    //   // TODO DOWN activeResource 选中楼层对应的设备 UI 更新
    // },
    // 建筑 checkbox 选中切换
    handleCheckBuildingChange(val) {
      console.log("handleCheckBuildingChange", this.checkedBuildings, this.buildingChecked, arguments, arguments[1].target._value);
      let b = arguments[1].target._value;
      // 所有 checkbox UI 更新
      if(val) {
        if(this.checkedBuildings.indexOf(b) < 0) {
          this.checkedBuildings.push(b);
        }
        this.isAllIndeterminate = this.checkedBuildings.length > 0  && this.checkedBuildings.length < this.resourceGroupList.length;
        this.checkAll = this.checkedBuildings.length == this.resourceGroupList.length;
      } else {
        this.checkedBuildings.splice(this.checkedBuildings.indexOf(b), 1);
        this.isAllIndeterminate = this.checkedBuildings.length > 0  && this.checkedBuildings.length < this.resourceGroupList.length;
        this.checkAll = this.checkedBuildings.length == this.resourceGroupList.length;
      }
      // 选中建筑 所有楼层 UI 更新
      if(val) {
        this.buildingChecked[b].checkedFloors = this.resourceFullMap[b].map( f => f.id+'' );
        this.buildingChecked[b].isIndeterminate = false;
        for(let f in this.deviceChecked) {
          if(this.buildingChecked[b].checkedFloors.indexOf(f) >= 0) {
            this.deviceChecked[f+''].checkAll = true;
            this.deviceChecked[f+''].isIndeterminate = false;
            this.deviceChecked[f+''].checkedDevices = this.resourceDeviceList.filter( (d) => {
              return d.resourceId == f;
            }).map( d => d.id );
          }
        }
      } else {
        this.buildingChecked[b].checkedFloors = [];
        this.buildingChecked[b].isIndeterminate = false;
        for(let f in this.deviceChecked) {
          this.deviceChecked[f+''].checkAll = false;
          this.deviceChecked[f+''].isIndeterminate = false;
          this.deviceChecked[f+''].checkedDevices = [];
        }
      }
      // 选中楼层 所有设备 UI 更新 checkbox-group 解决
      this.$forceUpdate();
    },
    // 建筑 group 选中切换
    // handleCheckBuildingGroupChange() {
    //   console.log("handleCheckBuildingGroupChange", this.checkedBuildings);
    //   let g = this.activeResourceGroup;
    //   if(this.buildingChecked[g].checkAll) {
    //     this.checkedBuildings.push(g);
    //   } else {
    //     this.checkedBuildings.splice(this.checkedBuildings.indexOf(g), 1);
    //   }
    // },
    // 楼层 checkbox 选中切换
    handleCheckFloorChange(val) {
      console.log("handleCheckFloorChange",
        this.activeResourceGroup,
        this.buildingChecked[this.activeResourceGroup],
        val, arguments);
      let f = arguments[1].target._value+'';
      let b = this.activeResourceGroup;
      let r = this.activeResource;
      // 建筑 checkbox 更新
      if(val) {
        if(this.buildingChecked[b].checkedFloors.indexOf(f) < 0) {
          this.buildingChecked[b].checkedFloors.push(f);
        }
        this.buildingChecked[b].isIndeterminate = this.buildingChecked[b].checkedFloors.length > 0 && this.buildingChecked[b].checkedFloors.length < this.resourceFullMap[b].length;
        this.buildingChecked[b].checkAll = this.buildingChecked[b].checkedFloors.length == this.resourceFullMap[b].length;
      } else {
        this.buildingChecked[b].checkedFloors.splice(this.buildingChecked[b].checkedFloors.indexOf(f), 1);
        this.buildingChecked[b].isIndeterminate = this.buildingChecked[b].checkedFloors.length > 0 && this.buildingChecked[b].checkedFloors.length < this.resourceFullMap[b].length;
        this.buildingChecked[b].checkAll = this.buildingChecked[b].checkedFloors.length == this.resourceFullMap[b].length;
      }
      // 全部 checkbox 更新
      if(val) {
        if(this.buildingChecked[b].checkAll) {
          this.checkedBuildings.push(b);
        }
        this.isAllIndeterminate = (this.checkedBuildings.length > 0  && this.checkedBuildings.length < this.resourceGroupList.length)
                              || (this.buildingChecked[b].checkedFloors.length > 0);
        this.checkAll = this.checkedBuildings.length == this.resourceGroupList.length;
      } else {
        if(!this.buildingChecked[b].checkAll) {
          this.checkedBuildings.splice(this.checkedBuildings.indexOf(b), 1);
        }
        this.isAllIndeterminate = (this.checkedBuildings.length > 0  && this.checkedBuildings.length < this.resourceGroupList.length)
                              || (this.buildingChecked[b].checkedFloors.length > 0);
        this.checkAll = false;
      }
      // 选中楼层 UI 更新
      if(val) {
        this.deviceChecked[f+''].checkAll = true;
        this.deviceChecked[f+''].isIndeterminate = false;
        this.deviceChecked[f+''].checkedDevices = this.resourceDeviceList.filter( (d) => {
          return d.resourceId == f;
        }).map( d => d.id );
      } else {
        this.deviceChecked[f+''].checkAll = false;
        this.deviceChecked[f+''].isIndeterminate = false;
        this.deviceChecked[f+''].checkedDevices = [];
      }
      // 选中楼层 所有设备 UI 更新 checkbox-group 解决
      this.$forceUpdate();

      // this.handleCheckBuildingGroupChange();
      // // UP 全部 checkbox 更新
      // this.handleCheckAllGroupChange();
      // // DOWN activeResource 选中楼层对应的设备 UI 更新
      // let g = this.activeResourceGroup;
      // let r = this.activeResource;
      // let floorChecked = true;
      // if(this.buildingChecked[g].checkedFloors.indexOf(r.id) >= 0) {
      //   // 选中单个
      //   floorChecked = true;
      // } else {
      //   // 取消选中
      //   floorChecked = false;
      // }
      // if(floorChecked) {
      //   this.deviceChecked[r.id].checkAll = true;
      //   this.deviceChecked[r.id].isIndeterminate = false;
      //   this.deviceChecked[r.id].checkedDevices = this.deviceList.map( d => d.id );
      // } else {
      //   this.deviceChecked[r.id].checkAll = false;
      //   this.deviceChecked[r.id].isIndeterminate = false;
      //   this.deviceChecked[r.id].checkedDevices = [];
      // }
    },
    // 楼层 group 选中切换
    handleCheckFloorGroupChange() {
      console.log("handleCheckFloorGroupChange",
        this.activeResourceGroup,
        this.buildingChecked);
      let g = this.activeResourceGroup;
      let r = this.activeResource;
      let floorChecked = true;
      if(this.buildingChecked[g].checkedFloors.indexOf(r.id+'') >= 0) {
        // 选中单个
        floorChecked = true;
        this.buildingChecked[g].isIndeterminate = this.buildingChecked[g].checkedFloors.length > 0 && this.buildingChecked[g].checkedFloors.length < this.resourceFullMap[g].length;
        this.buildingChecked[g].checkAll = this.buildingChecked[g].checkedFloors.length == this.resourceFullMap[g].length;
      } else {
        // 取消选中
        floorChecked = false;
        this.buildingChecked[g].isIndeterminate = this.buildingChecked[g].checkedFloors.length > 0 && this.buildingChecked[g].checkedFloors.length < this.resourceFullMap[g].length;
        this.buildingChecked[g].checkAll = false;
      }
    },
    // 设备 group 选中切换
    handleCheckDeviceGroupChange() {
      console.log("handleCheckDeviceGroupChange",
        this.activeResourceGroup,
        this.buildingChecked,
        this.deviceChecked[this.activeResource.id].checkedDevices);
      // this.handleCheckFloorGroupChange();
      // UP 楼层 checkbox 更新
      let b = this.activeResourceGroup;
      let f = this.activeResource;
      this.deviceChecked[f.id+''].checkAll = this.deviceChecked[f.id+''].checkedDevices.length == this.deviceList.length;
      this.deviceChecked[f.id+''].isIndeterminate = this.deviceChecked[f.id+''].checkedDevices.length > 0 && this.deviceChecked[f.id+''].checkedDevices.length < this.deviceList.length;
      // UP 建筑 checkbox 更新
      if(this.deviceChecked[f.id+''].checkAll) {
        this.buildingChecked[b].checkedFloors.push(f.id);
        this.buildingChecked[b].isIndeterminate = this.buildingChecked[b].checkedFloors.length > 0 && this.buildingChecked[b].checkedFloors.length < this.resourceFullMap[b].length;
        this.buildingChecked[b].checkAll = this.buildingChecked[b].checkedFloors.length == this.resourceFullMap[b].length;
      } else {
        if(this.deviceChecked[f.id+''].isIndeterminate) {
          this.buildingChecked[b].isIndeterminate = true;
          this.buildingChecked[b].checkAll = false;
        } else {
          this.buildingChecked[b].checkedFloors.splice(this.buildingChecked[b].checkedFloors.indexOf(f.id),1);
          this.buildingChecked[b].isIndeterminate = this.buildingChecked[b].checkedFloors.length > 0 && this.buildingChecked[b].checkedFloors.length < this.resourceFullMap[b].length;;
          this.buildingChecked[b].checkAll = false;
        }
      }
      // UP 全部 checkbox 更新
      if(this.deviceChecked[f.id+''].checkAll) {
        if(this.buildingChecked[b].checkAll) {
          this.checkedBuildings.push(b);
          this.isAllIndeterminate = this.checkedBuildings.length > 0  && this.checkedBuildings.length < this.resourceGroupList.length;
          this.checkAll = this.checkedBuildings.length == this.resourceGroupList.length;
        } else {
          this.checkAll = false;
          this.isAllIndeterminate = true;
        }
      } else {
        if(this.deviceChecked[f.id+''].isIndeterminate) {
          this.checkAll = false;
          this.isAllIndeterminate = true;
        } else {
          this.isAllIndeterminate = this.checkedBuildings.length > 0  && this.checkedBuildings.length < this.resourceGroupList.length;
          this.checkAll = this.checkedBuildings.length == this.resourceGroupList.length;
        }
      }
      // this.handleCheckAllGroupChange();
      // 必须，否则设备 checkbox UI 不更新
      this.$forceUpdate();
    },
    // 设备选中改变
    handleCheckDeviceChange(deviceId, val) {
      // pass 只需要监听 group 即可
    },

    // 获取所有选中设备
    getAllCheckedDevice() {
      let resourceIds = [];
      let deviceIds = [];
      // 检查建筑选中
      for(let b in this.buildingChecked) {
        resourceIds = resourceIds.concat(this.buildingChecked[b].checkedFloors);
      }
      if(resourceIds.length > 0) {
        this.resourceDeviceList.map( d => {
          if(resourceIds.indexOf(d.resourceId) >= 0) {
            deviceIds.push(d.id);
          }
        });
      }
      for(let o in this.deviceChecked) {
        deviceIds = deviceIds.concat(this.deviceChecked[o+''].checkedDevices);
      }
      deviceIds = Array.from(new Set(deviceIds));
      console.log(deviceIds);
      return deviceIds;
    },


    /////////////// 系统图，组态图编辑逻辑  ///////////////////
    // 添加json项
    addJsonItem(){
      this.addJsonItemShow = true
      this.addJsonType = 'new'
      this.jsonItemData.name = new Date().getTime();
    },
    // 取消保存
    cancelJsonItem(){
      this.addJsonItemShow = false
      this.jsonItemData = {
        name: '',
        type: '',
        position: '{"top": "0px", "left": "0px"}',
        zIndex: 100,
        size: '14px',
        weight: 'bold',
        color: '#fff',
        data:'{"dVal":"","dDataUnit":""}',
        iconSize:'{"width":"80px","height":"80px"}',
        url: '',
        deviceId: null,
        dataId: null,
        dataName: '',
        val: null,
        textName: '',
        btnType: 'primary',
        isPlain: false,
        deviceName: '',
        textStyle: '{"fontSize":"12px","weight":"bold","color":"#fff","width":"60px","text-align": "center"}',
        urls: '{"0":"/images/IdcsGif/fan/fanLeftFalse.gif","1":"/images/IdcsGif/fan/fanLeftTrue.gif"}',
        chartsOptions: '',
        isFullScreen: false,
        textWidth: '160px',
        marginBottom: '20px',
      }
      this.$refs.jsonItemForm.resetField()
    },
    saveJsonItem(){
      let jsonItem = {
        name: this.jsonItemData.name,
        type: this.addType.split('-')[0],
        zIndex: this.jsonItemData.zIndex,
        position: this.jsonItemData.position,
      }
      jsonItem.position = JSON.parse(this.jsonItemData.position);
      switch (this.addType){
        case 'vtext-1':
          jsonItem.size = this.jsonItemData.size;
          jsonItem.weight = this.jsonItemData.weight;
          jsonItem.color = this.jsonItemData.color;
          jsonItem.data = JSON.parse(this.jsonItemData.data);
          break;
        case 'vtext-2':
        case 'vtext-3':
          jsonItem.size = this.jsonItemData.size;
          jsonItem.weight = this.jsonItemData.weight;
          jsonItem.color = this.jsonItemData.color;
          jsonItem.dataId = this.jsonItemData.dataId;
          jsonItem.dataName = this.jsonItemData.dataName;
          break;
        case 'vdecoration-1':
        case 'viframe-1':
          jsonItem.iconSize = JSON.parse(this.jsonItemData.iconSize);
          jsonItem.url = this.jsonItemData.url;
          break;
        case 'vdecoration-2':
          jsonItem.iconSize = JSON.parse(this.jsonItemData.iconSize);
          jsonItem.url = this.jsonItemData.url;
          jsonItem.deviceName = this.jsonItemData.deviceName;
          jsonItem.textStyle = JSON.parse(this.jsonItemData.textStyle);
          break;
        case 'vdecoration-3':
        case 'vdecoration-4':
          jsonItem.iconSize = JSON.parse(this.jsonItemData.iconSize);
          jsonItem.url = this.jsonItemData.url;
          jsonItem.deviceId = this.jsonItemData.deviceId;
          jsonItem.deviceName = this.jsonItemData.deviceName;
          jsonItem.textStyle = JSON.parse(this.jsonItemData.textStyle);
          break;
        case 'vicon-1':
          jsonItem.iconSize = JSON.parse(this.jsonItemData.iconSize);
          jsonItem.url = this.jsonItemData.url;
          jsonItem.dataId = this.jsonItemData.dataId;
          jsonItem.dataName = this.jsonItemData.dataName;
          jsonItem.urls = JSON.parse(this.jsonItemData.urls);
          break;
        case 'vselect-1':
        case 'vinput-1':
          jsonItem.dataId = this.jsonItemData.dataId;
          jsonItem.dataName = this.jsonItemData.dataName;
          break;
        case 'vbutton-1':
          jsonItem.dataId = this.jsonItemData.dataId;
          jsonItem.dataName = this.jsonItemData.dataName;
          jsonItem.val = this.jsonItemData.val;
          jsonItem.textName = this.jsonItemData.textName;
          jsonItem.btnType = this.jsonItemData.btnType;
          jsonItem.isPlain = this.jsonItemData.isPlain;
          jsonItem.textStyle = JSON.parse(this.jsonItemData.textStyle);
          break;
        case 'vswitch-1':
          jsonItem.iconSize = JSON.parse(this.jsonItemData.iconSize);
          jsonItem.dataId = this.jsonItemData.dataId;
          jsonItem.dataName = this.jsonItemData.dataName;
          jsonItem.urls = JSON.parse(this.jsonItemData.urls);
          break;
        case 'vtext-4':
          jsonItem.size = this.jsonItemData.size;
          jsonItem.weight = this.jsonItemData.weight;
          jsonItem.color = this.jsonItemData.color;
          jsonItem.eDeviceIds = this.jsonItemData.eDeviceIds;
          jsonItem.eDisplayType = this.jsonItemData.eDisplayType;
          jsonItem.unit = this.jsonItemData.unit;
          break;
        case 'vbutton-2':
          jsonItem.url = this.jsonItemData.url;
          jsonItem.textName = this.jsonItemData.textName;
          jsonItem.btnType = this.jsonItemData.btnType;
          jsonItem.isPlain = this.jsonItemData.isPlain;
          jsonItem.viewWay = this.jsonItemData.viewWay;
          jsonItem.iconSize = JSON.parse(this.jsonItemData.iconSize);
          jsonItem.textStyle = JSON.parse(this.jsonItemData.textStyle);
          jsonItem.isFullScreen = this.jsonItemData.isFullScreen;
          break;
        case 'vsqlCharts-1':
          jsonItem.chartsOptions = JSON.parse(this.jsonItemData.chartsOptions);
          jsonItem.iconSize = JSON.parse(this.jsonItemData.iconSize);
          break;
        case 'venergyCharts-1':
          jsonItem.iconSize = JSON.parse(this.jsonItemData.iconSize);
          jsonItem.eDeviceIds = this.jsonItemData.eDeviceIds;
          break;
        case 'vmulti-1':
          jsonItem.dataId = this.jsonItemData.dataId;
          jsonItem.dataName = this.jsonItemData.dataName;
          jsonItem.textWidth = this.jsonItemData.textWidth;
          jsonItem.marginBottom = this.jsonItemData.marginBottom;
          break;
      }
      if(this.addJsonType == 'edit'){
        this.scence.otherData.objects.splice(this.jsonItemData.ind,1,jsonItem)
      }else {
        this.scence.otherData.objects.push(jsonItem)
      }
      this.drawScence()
      this.cancelJsonItem()
    },
    // 编辑json单项
    editJsonItem(item,ind){
      this.addJsonType = 'edit';
      this.jsonItemData = JSON.parse(JSON.stringify({...item,ind}));
      if(item.device) delete this.jsonItemData.device;
      if(item.deviceDetail) delete this.jsonItemData.deviceDetail;
      console.log('edit..........',this.jsonItemData)
      if(item.position) this.jsonItemData.position = JSON.stringify(item.position);
      if(item.data) this.jsonItemData.data = JSON.stringify(item.data);
      if(item.iconSize) this.jsonItemData.iconSize = JSON.stringify(item.iconSize);
      if(item.textStyle) this.jsonItemData.textStyle = JSON.stringify(item.textStyle);
      if(item.urls) this.jsonItemData.urls = JSON.stringify(item.urls);
      if(item.chartsOptions) this.jsonItemData.chartsOptions = JSON.stringify(item.chartsOptions);
      if(item.type == 'vinput') this.addType = 'vinput-1';
      if(item.type == 'vselect') this.addType = 'vselect-1';
      if(item.type == 'vicon') this.addType = 'vicon-1';
      if(item.type == 'viframe') this.addType = 'viframe-1';
      if(item.type == 'vswitch') this.addType = 'vswitch-1';
      if(item.type == 'vsqlCharts') this.addType = 'vsqlCharts-1';
      if(item.type == 'vmulti') this.addType = 'vmulti-1';
      if(item.type == 'venergyCharts') this.addType = 'venergyCharts-1';
      if(item.type == 'vdecoration') {
        if(item.deviceName){
          if(item.deviceId){
            this.addType = 'vdecoration-3';
          }else {
            this.addType = 'vdecoration-2';
          }
        }else {
          this.addType = 'vdecoration-1';
        }
      };
      if(item.type == 'vtext') {
        if(item.dataName){
          this.addType = 'vtext-2';
        } else if (item.eDeviceIds){
          this.addType = 'vtext-4';
        } else {
          this.addType = 'vtext-1';
        }
      };
      if(item.type == 'vbutton') {
        if(item.viewWay || item.url){
          this.addType = 'vbutton-2';
        } else {
          this.addType = 'vbutton-1';
        }
      };
      this.addJsonItemShow = true
    },
    // 复制json单项
    copyJsonItem(){
      if(this.jsonItemData.device) delete this.jsonItemData.device;
      if(this.jsonItemData.deviceDetail) delete this.jsonItemData.deviceDetail;
      let newItem= JSON.parse(JSON.stringify(this.jsonItemData))
      if(newItem.data) newItem.data = JSON.parse(newItem.data);
      if(newItem.iconSize) newItem.iconSize = JSON.parse(newItem.iconSize);
      if(newItem.textStyle) newItem.textStyle = JSON.parse(newItem.textStyle);
      if(newItem.urls) newItem.urls = JSON.parse(newItem.urls);
      if(newItem.position){
        newItem.position = JSON.parse(newItem.position);
        newItem.position.top = parseInt(newItem.position.top) + 5 + 'px';
        newItem.position.left = parseInt(newItem.position.left) + 10 + 'px';
      }
      newItem.ind = this.scence.otherData.objects.length;
      this.scence.otherData.objects.push(newItem);
      this.drawScence()
    },
    // 删除json单项
    deleteJsonItem(){
      this.addJsonItemShow = false;
      this.scence.otherData.objects.splice(this.jsonItemData.ind,1);
      this.drawScence()
    },
    // 保存json
    saveJson(){
      let param = JSON.parse(JSON.stringify(this.scence))
      param.otherData = JSON.stringify(this.scence.otherData)
      console.log("saveJson", param);
      updateResource(param).then(res => {
        if(res.code == 200){
          this.$message({ showClose: false, message: "保存成功", type: "success" });
          this.refreshScence()
        }else {
          this.$message({ showClose: false, message: "保存失败", type: "error" });
        }
      })
    },
    // 退出编辑
    quitEditJson(){
      this.getDatas()
      this.$bus.$emit('changeEdit');
    },
    updatePosition(pos) {
      this.scence.otherData.objects[pos.ind]["position"].top = pos.top;
      this.scence.otherData.objects[pos.ind]["position"].left = pos.left;
      // console.log("updatePosition........", this.scence.otherData.objects[pos.ind].name, pos);
    },
    // 有iframe时，postMessage
    postIframeMessage(response){
      try {
        if(this.newScence.otherData.objects && this.newScence.otherData.objects.length){
          let iframeList = this.newScence.otherData.objects.filter( ii => ii.type == 'viframe')
          if(iframeList.length){
            const iframeDomList = document.getElementsByTagName('iframe');
            if(iframeDomList.length){
              for (let i=0;i<iframeDomList.length;i++){
                iframeDomList[i].contentWindow && iframeDomList[i].contentWindow.postMessage(
                  {
                    type: "freshBimDeviceData",
                    param: response,  //item为对应的系统名称 或者缩写
                  },
                  "*"
                );
              }
            }
          }
        }
      }catch (e) {
        console.trace(e)
      }
    },
    // 监听iframe点击事件
    iframeListener(){
      try {
        let that = this
        window.addEventListener('message', function (e) {
          // console.log('...................1111',e,that.devices,that.activeDeviceSummaryShow,that.activeDeviceInd, that.activeDevice)
          if (e.data.type === 'deviceId' && !that.activeDeviceSummaryShow) {
            if (e.data.param && e.data.param.deviceid) {
              const deviceId = e.data.param.deviceid;
              that.devices.map( (d, ind) => {
                if(d.id == deviceId) {
                  that.activeDeviceInd = ind;
                  that.activeDevice = d;
                  that.activeDeviceSummaryShow = true;
                }
              });
            }
          }
        }, false);
      }catch (e) {
        console.trace(e)
      }
    },
    tableRowClassName({row, rowIndex}) {
      let rowClass = "";
      // 开启  关闭  无状态(传感器)
      if(row.workStatus.indexOf('停') >= 0 || row.workStatus.indexOf('关') >= 0) {
        rowClass = "stop-row";
      } else if(row.workStatus.indexOf('启') >= 0 || row.workStatus.indexOf('开') >= 0 || row.workStatus.indexOf('运') >= 0 || row.workStatus.indexOf('行') >= 0) {
        rowClass = "running-row";
      } else {
        // pass
      }

      // 告警 健康
      if(row.warningStatus == "故障") {
        rowClass = "fault-row";
      } else if(row.warningStatus == "告警") {
        rowClass = "warning-row";
      } else if(row.warningStatus == "健康") {
        // pass
      }

      // 在线 离线
      if(row.communicateStatus == '离线') {
        rowClass = "offline-row";
      } else {
        // pass
      }
      return rowClass;
    },
    chartsTypeMapHandle (command) {
      console.log(command,"command");
      let chartsOptions = [];
      try {
        chartsOptions = JSON.parse(this.jsonItemData.chartsOptions);
      } catch (error) {
        chartsOptions = []
      }
      chartsOptions = [...chartsOptions, {
        ...command
      }];
      this.jsonItemData.chartsOptions = JSON.stringify(chartsOptions);
    }
  }
}
