<template>
  <div class="deviceMapDialog deviceDetailSmall2">
    <!-- <DeviceBase v-if="refreshFlag" :device="item" :protoTypeDisplay="protoTypeDisplay"
                @updateData="updateData"
                @addDeviceMaintenance="addDeviceMaintenance"
                @addDeviceMaintenance2="addDeviceMaintenance2"
                @handleDevicePrototype="handleDevicePrototype" /> -->
    <div class="lt" v-if="item">
      <el-image :src="(item && item.thumb) || ''" fit="contain"></el-image>
      <div class="lt_fields">
        <div v-for="it in deviceBaseFields" :key="it.code">
          {{ it.name }}：<span>{{ item[it.code] || "" }}</span>
        </div>
      </div>
      <div class="btns">
        <el-button
          class="btn"
          size="mini"
          plain
          @click="showDeviceVideo"
          >查看视频</el-button
        >
        <el-button
          class="btn"
          type="warning"
          size="mini"
          plain
          @click="addDeviceMaintenance2(item)"
          >一键报修</el-button
        >
        <el-button
          v-if="item.resourceId > 0 && !noResourceBtn"
          class="btn mr10"
          size="mini"
          type="primary"
          plain
          @click="handleDeviceSummaryShow(item, deviceInd)"
          >原理图</el-button
        >
      </div>
    </div>
    <div class="rt">
      <el-tabs
        tab-position="top"
        :lazy="true"
        v-if="item"
        v-model="tab"
        @tab-click="handleTabClick"
      >
        <el-tab-pane
          :label="tabItem.name"
          :name="tabItem.name"
          v-for="(tabItem, index) in getTabList"
          :key="index"
        />
      </el-tabs>
      <component
        :class="['detailBody', currentTab.class]"
        :is="currentTab.code"
        :device="item"
        :deviceInd="deviceInd"
        :type="currentTab.type"
        :key="`${currentTab.code}${currentTab.name}`"
        :height="currentTab.params && currentTab.params.height"
        :dataGroup="dataGroup"
        @updateData="updateData"
        @handleBatchStrategySend="handleBatchStrategySend"
        @handleItemDataLock="handleItemDataLock"
        @handleItemDataUnlock="handleItemDataUnlock"
      />
    </div>
    <!-- 使用v-if控制视频组件的显示隐藏 -->
    <Devicevideo 
      v-if="showVideoDialog" 
      ref="deviceVideoRef"
   
      @close="closeDeviceVideo"
    ></Devicevideo>
  </div>
</template>

<script type="text/javascript">
import DisplayBaseFunc from "@/views/device/monitor/components/Device/DisplayBaseFunc";
import DeviceBase from "./components/deviceBase.vue";
import DeviceImages from "@/views/device/monitor/components/Device/components/dialog/components/deviceImages.vue";
import DeviceItemData from "@/views/device/monitor/components/Device/components/dialog/components/deviceItemData.vue";
import RunningStatusFields from "@/views/device/monitor/components/Device/components/dialog/components/runningStatusFields.vue";
import DeviceControl from "@/views/device/monitor/components/Device/components/dialog/components/deviceControl.vue";
import DeviceDataChart from "@/views/device/monitor/components/Device/components/dialog/components/deviceDataChart.vue";
import DeviceEnergy from "@/views/device/monitor/components/Device/components/dialog/components/deviceEnergy.vue";
import DeviceWarning from "@/views/device/monitor/components/Device/components/dialog/components/deviceWarning.vue";
import DeviceMaintenance from "@/views/device/monitor/components/Device/components/dialog/components/deviceMaintenance.vue";
import DeviceInfo from "@/views/device/monitor/components/Device/components/dialog/components/deviceInfo.vue";
import DeviceOperatorLog from "@/views/device/monitor/components/Device/components/dialog/components/deviceOperatorLog.vue";
import DeviceFullBase from "@/views/device/monitor/components/Device/components/dialog/components/deviceFullBase2.vue";
import Devicevideo from "@/views/device/monitor/components/Device/components/dialog/components/devicevideo.vue";
export default {
  name: "DeviceDialog",
  props: {
    device: {
      type: Object,
      default: () => {},
    },
    deviceInd: {
      type: [String, Number],
      default: -1,
    },
    protoTypeDisplay: {
      type: String,
      default: "map", // 打开组态图的类型
    },
    _tab: {
      type: String,
      default: "", // 默认打开的 tab 页
    },
    noResourceBtn: {
      type: Boolean,
      default: false
    }
  },
  mixins: [DisplayBaseFunc], // 继承父模块
  components: {
    Devicevideo,
    DeviceImages,
    DeviceOperatorLog,
    DeviceInfo,
    DeviceMaintenance,
    DeviceWarning,
    DeviceEnergy,
    DeviceDataChart,
    DeviceControl,
    DeviceItemData,
    DeviceBase,
    DeviceFullBase,
    RunningStatusFields,
  },
  computed: {
    getTabList() {
      if (!this.item) {
        return [];
      }
      let arr = [
        // {
        //   name: "设备图片",
        //   showFlag: this.refreshFlag && this.item.showImages == '1',
        //   code: 'DeviceImages'
        // },
        // {
        //   name: "实时监测",
        //   showFlag: this.refreshFlag,
        //   code: 'DeviceItemData'
        // },

        {
          name: "运行状态",
          showFlag: this.refreshFlag,
          code: "runningStatusFields",
        },
        {
          name: "远程控制",
          showFlag: this.refreshFlag && this.item.hasControl,
          code: "DeviceControl",
          class: "detailControl2",
        },

        {
          name: "能效管理",
          showFlag: this.refreshFlag && this.item.hasEnergy == "1",
          code: "DeviceEnergy",
        },
        {
          name: "运行记录",
          showFlag: this.refreshFlag && this.item.hasMonitor,
          code: "DeviceDataChart",
          params: {
            height: "280px",
          },
        },
        {
          name: "操作记录",
          showFlag: this.refreshFlag && this.item.hasControl,
          code: "DeviceOperatorLog",
        },
        {
          name: "报警管理",
          showFlag: this.refreshFlag && this.item.hasWarning == '1',
          code: "DeviceWarning",
        },
        {
          name: "设备信息",
          showFlag: this.refreshFlag && this.item.hasDeviceInfo == '1',
          code: "DeviceFullBase",
        },
        {
          name: "报修管理",
          showFlag: this.refreshFlag && this.item.hasMaintenance == '1',
          code: "DeviceMaintenance",
          type: "紧急维修",
        },
        {
          name: "巡检管理",
          showFlag: this.refreshFlag && this.item.hasMaintenance == '1',
          code: "DeviceMaintenance",
          type: "日常巡检",
        },
        {
          name: "保养管理",
          showFlag: this.refreshFlag && this.item.hasMaintenance == '1',
          code: "DeviceMaintenance",
          type: "保养计划,日常维护,预见性维修,计划性维修,设备改进性维修",
        },
        {
          name: "设备档案",
          showFlag: this.refreshFlag,
          code: "DeviceInfo",
        },
      ];
      const list = arr.filter((item) => item.showFlag);
      return list;
    },
  },
  data() {
    return {
      // 当前设备
      item: null,
      // 标签页
      tab: "运行状态",
      // 刷新标记
      refreshFlag: true,
      currentTab: "",
      deviceBaseFields: [
        {
          name: "设备名称",
          code: "name",
        },
        {
          name: "设备编码",
          code: "description",
        },
        {
          name: "安装位置",
          code: "position",
        },
        {
          name: "质保时间",
          code: "shelfTime",
        },
      ],
      // 控制视频组件显示隐藏
      showVideoDialog: false,
      dataGroup: []
    };
  },
  watch: {
    device: {
      handler(val, oldVal) {
        console.log("=======deviceDialog=======", val);
        if (val) {
          this.backUpData();
        }
      },
      immediate: true,
      deep: true,
    }
  },
  mounted() {
    this.tab = this._tab || this.tab;
  },
  methods: {
    async backUpData() {
      let item = JSON.parse(JSON.stringify(this.device));
      // item = this.initItemBaseData(item);
      console.log("DeviceDialog", this.getTabList, item);
      // 重新加载 StrategyList (适配 原型图 多种设备类型情况)
      this.item = await this.updateDeviceStrategyList(item);
      // 重新加载 设备菜单配置 (适配 原型图 多种设备类型情况)
      // this.updateDeviceTypeMenuConfig();
      this.getDictData();
      this.$nextTick(() => {
        this.currentTab = this.getTabList.find(
          (item) => item.name === this.tab
        );
      });
    },

    handleTabClick(dt) {
      const { index } = dt;
      this.currentTab = this.getTabList[index];
      console.log("handleTabClick", this.currentTab);
    },

    handleDevicePrototype(device) {
      // this.handleDevicePrototypeShow(device, this.deviceInd);
      this.$emit("handleDevicePrototypeShow", device, this.deviceInd);
    },

    // 触发维保新增
    addDeviceMaintenance(device) {
      this.$refs.deviceMaintenance.addDeviceMaintenance(device);
    },

    // 触发一键报修
    addDeviceMaintenance2() {
      window.open(
        this.gf.getFullPath() +
          "/maintananceMgt/ops/repair?method=add&deviceId=" +
          this.item.id +
          "&deviceType=" +
          this.item.type
      );
    },

    // 刷新数据
    updateData() {
      this.$emit("refreshList"); // 通知父组件
    },

    // 更新策略
    handleBatchStrategySend(deviceId, key, val) {
      this.$emit("handleBatchStrategySend", [deviceId], key, val); // 通知父组件
    },
    // 锁定 解锁设备
    handleItemDataLock(deviceId, deviceType, itemDataName, itemDataValue) {
      this.$emit(
        "handleItemDataLock",
        [deviceId],
        deviceType,
        itemDataName,
        itemDataValue
      ); // 通知父组件
    },
    handleItemDataUnlock(deviceId, deviceType) {
      this.$emit("handleItemDataUnlock", [deviceId], deviceType); // 通知父组件
    },

    // 显示设备视频
    showDeviceVideo() {
      if (this.item) {
        this.showVideoDialog = true;
        this.$nextTick(() => {
          if (this.$refs.deviceVideoRef) {
            this.$refs.deviceVideoRef.openDialog(this.item);
          }
        });
      } else {
        this.$message.warning("未获取到设备信息");
      }
    },
    
    // 关闭设备视频
    closeDeviceVideo() {
      this.showVideoDialog = false;
    },
    handleDeviceSummaryShow (item, deviceInd) {
      this.$emit("handleDevicePrototypeShowAfterClose", item, deviceInd)
    },
    getDictData () {
      this.getDicts("d_device_list_display").then(resp => {
        const dt = resp.data.find(item => item.dictLabel == this.item.type);
        try {
          let conf = JSON.parse(dt.dictValue);
          this.dataGroup = conf.dataGroup || []
        } catch(e) {
          //pass
        }
      })
    }
  },
};
</script>
