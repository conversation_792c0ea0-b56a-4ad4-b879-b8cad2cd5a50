
const axios = require('axios');
const request = require('request');
const Logstash = require('logstash-client');
const mqtt = require('mqtt');
const {InfluxDB, Point} = require('@influxdata/influxdb-client')
const sysConfig = require('./conf/sysConfig').sysConfig();
var moment = require('moment');
const crypto = require("crypto");
const xml2js = require('xml2js');
const path = require('path');
const os = require('os');
const fs = require('fs/promises');
const { exec, spawn } = require('child_process');

// syncItemData2Db 缓存
let syncItemDataCachePostData = [];
let syncItemDataMaxNumSend = 50; // 达到 50 包发送一次请求
let syncItemDataTimeoutId = null;
let syncItemDataTimeoutSpan = 3000; // 不到 50 包的情况，每隔 3 秒也同步一次
let isFlushing = false;  // 标记是否正在刷新缓存

// Logstash
var logstash = null;
var mqttClient = null;
var dataServer = null;
var influxClient = null;
var influxWriteClient = null;

let _arguments = process.argv.slice(2); // 记录脚本的所有参数
// 判断是否在 pm2 里面启动
let _fullPath = 'PM2_HOME' in process.env || 'PM2_USAGE' in process.env ? process.env.pm_exec_path : process.argv[1]; // 记录启动的脚本
let _dbName = null;
let serverHeatbeatSpan = sysConfig.serverHeatbeatSpan || 30*1000;  // 30秒一次服务心跳
let serverHeatbeat = false;

// 日志记录相关
function connectLogstash(options) {
    this.opts = options;
    if(options.hasOwnProperty("logstash")) {
        logstash = new Logstash({
            type: 'tcp',
            host: this.opts.logstash.host,
            port: this.opts.logstash.port,
            maxQueueSize: 1000
        });
    }
    if(options.hasOwnProperty("mysql")) {
        _dbName = options.mysql.database;
    }
}

function remoteLog(name, msg) {
    if(logstash) {
        let act = "RECEIVE";
        if(name.indexOf("下发指令") >= 0) {
            act = "SEND";
        } else if(name.indexOf("发送消息") >= 0) {
            act = "SENDMSG";
        }
        logstash.send({
            'type': "INFO",
            'action': act,
            'from': name,
            'message': msg
        });
    } else {
        debug(moment().format("YYYY-MM-DD HH:mm:ss") + " remoteLog failed " + name + " msg: " + msg);
    }
}

function log() {
    try {
        let logStr = "";
        if (arguments.length > 0) {
            for(let i = 0 ; i < arguments.length; i ++) {
                let logObj = arguments[i];
                logStr += " " + (typeof logObj != "string" ? JSON.stringify(logObj) : logObj);
            }
        }
        console.log(moment().format("YYYY-MM-DD HH:mm:ss") + " " + logStr);
    } catch (e) {
        // pass
    }
}

function info() {
    log.apply(log, arguments);
}

function debug() {
    if(global.isDebug) {
        log.apply(log, arguments);
    }
}

// mqtt消息通道建立
function connectMqtt(dbName) {
    let conf = sysConfig.hasOwnProperty(dbName) ? sysConfig[dbName] : sysConfig["mqtt"];
    if(conf) {
        mqttClient = mqtt.connect(conf.host, conf);
        mqttClient.on('connect', function () {
            console.log(moment().format("YYYY-MM-DD HH:mm:ss"), "[数据采集平台]:连接mqtt服务", conf.host);
            // 启动服务后，尝试绑定服务心跳
            // 移除每个服务单独启动，改成单独一个服务监控所有
            // bindServiceHeatbeat();
        });
    }
}
// 订阅消息
function subscribeMsg(topic) {
    if(mqttClient) {
        mqttClient.subscribe(topic, function(err, res){
            if(err){
                console.log("主题订阅出错,", topic);
            } else {
                console.log("订阅成功", topic);
            }
        });
    }
}
// 绑定回调事件
function bindMsgCallback(callback) {
    if(mqttClient) {
        mqttClient.on('message', function (topic, buffer) {
            if(typeof callback == "function") {
                callback(topic, buffer);
            }
        });
    }
}
// 发送报警消息
function sendWarningMsg(dbName, params) {
    if(mqttClient) {
        mqttClient.publish('bms/itemWarning/'+dbName, JSON.stringify(params));
        remoteLog(dbName+"发送消息", JSON.stringify({
            ...params,
            method: "sendWarningMsg",
        }));
    }
}
// 发送变化的数据消息
function sendItemDataMsg(dbName, params) {
    if(mqttClient) {
        mqttClient.publish('bms/itemData/'+dbName, JSON.stringify(params));
        remoteLog(dbName+"发送消息", JSON.stringify({
            ...params,
            method: "sendItemDataMsg",
        }));
    }
}

// 服务启动后，自动心跳
async function bindServiceHeatbeat() {
    if(!serverHeatbeat && mqttClient) {
        serverHeatbeat = setInterval(async () => {
            if(mqttClient) {
                let msg = await getServerInfos();
                mqttClient.publish('bms/collectService/'+_dbName, JSON.stringify(msg));
                remoteLog(_dbName+"发送消息", JSON.stringify({
                    ...msg,
                    method: "sendServerHeatbeat",
                }));
            }
        }, serverHeatbeatSpan);
    }
}

async function getServerInfos() {
    // 26个字母的盘符列表
    let fn = Array.from({ length: 26 }, (_, i) => String.fromCharCode(97 + i)+":");
    return {
        fullPath: _fullPath,
        fileName: path.basename(_fullPath),
        arguments: _arguments,
        dbName: _dbName,
        ips: getLocalIPs(),
        machineInfo: getMachineInfo(),
        cpuUsage: await monitorCpuUsage(),
        diskSpace: await getDiskSpaces(["/"].concat(fn)),
        diskIo: await monitorDiskIo(),
    };
}

function getLocalIPs() {
    const interfaces = os.networkInterfaces();
    let addresses = [];

    for (const interfaceName in interfaces) {
        const iface = interfaces[interfaceName];
        for (const alias of iface) {
            if (alias.family === 'IPv4' && !alias.internal) {
                addresses.push({
                    name: interfaceName,
                    ip: alias.address,
                    mac: alias.mac,
                    family: alias.family
                });
            }
        }
    }
    addresses.sort((a, b) => {
        return a.ip.localeCompare(b.ip, undefined, { numeric: true, sensitivity: 'base' });
    });
    return addresses;
}
function getMachineInfo() {
    const totalMem = os.totalmem(); // 总内存大小（字节）
    const freeMem = os.freemem(); // 空闲内存大小（字节）
    const uptime = os.uptime(); // 系统运行时间（秒）
    const cpus = os.cpus(); // CPU信息数组
    const platform = os.platform(); // 运行的操作系统平台
    const release = os.release(); // 操作系统的发行版本
    const type = os.type(); // 操作系统的名称
    const hostname = os.hostname(); // 主机名
    const arch = os.arch(); // CPU架构
    return {
        totalMemory: totalMem,
        totalMemoryStr: `${(totalMem / 1024 / 1024).toFixed(2)} MB`,
        freeMemory: freeMem,
        freeMemoryStr: `${(freeMem / 1024 / 1024).toFixed(2)} MB`,
        uptime: uptime,
        uptimeStr: `${Math.floor(uptime / 60)} min ${Math.floor(uptime % 60)} sec`,
        cpuModel: cpus[0].model,
        cpuSpeed: cpus[0].speed,
        cpuSpeedStr: `${cpus[0].speed} MHz`,
        platform,
        release,
        type,
        hostname,
        architecture: arch
    };
}
function getCpuTimes() {
    const cpus = os.cpus();
    return cpus.map(cpu => cpu.times);
}
function calculateCpuUsage(prevTimes, currentTimes) {
    const totalDiffs = {};
    let totalDiff = 0;
    for (let key in currentTimes) {
        if (prevTimes.hasOwnProperty(key)) {
            totalDiffs[key] = currentTimes[key] - prevTimes[key];
            totalDiff += totalDiffs[key];
        }
    }
    // 计算非空闲时间（即活动时间）
    const activeTime = totalDiffs.user + totalDiffs.nice + totalDiffs.sys + totalDiffs.irq;
    // 计算CPU利用率
    const usage = (activeTime / totalDiff) * 100;
    return usage;
}
async function monitorCpuUsage(intervalMs = 1000) {
    let prevTimes = getCpuTimes();
    await sleep(intervalMs);
    const currentTimes = getCpuTimes();
    let usages = [];
    for (let i = 0; i < prevTimes.length; i++) {
        const usage = calculateCpuUsage(prevTimes[i], currentTimes[i]);
        usages.push({
            core: i,
            usage: parseFloat(usage.toFixed(2)),
        });
    }
    return usages;
}
// 获取磁盘使用情况
async function getDiskSpace(path = '/') {
  try {
    const stats = await fs.statfs(path);
    const total = stats.blocks * stats.bsize; // 总字节数
    const free = stats.bfree * stats.bsize;  // 空闲字节数
    const used = total - free;

    // 转换为 GB
    const format = (bytes) => parseFloat((bytes / 1024 ** 3).toFixed(2));
    
    return {
      total: format(total),
      free: format(free),
      used: format(used),
      format: "GB",
    };
  } catch (error) {
    if(error.code != "ENOENT") {
        console.error('Error:', error);
    }
  }
  return null;
}
async function getDiskSpaces(pathList) {
    let res = {};
    for(let i = 0; i < pathList.length; i++) {
        let s = await getDiskSpace(pathList[i]);
        if(s) {
            res[pathList[i]] = s;
        }
    }
    return res;
}

// 获取磁盘读写IO进程列表
// 根据操作系统选择监控方法
async function monitorDiskIo() {
    if (os.platform() === 'linux') {
        return await monitorLinux();
    } else if (os.platform() === 'win32') {
        return await monitorWindows();
    } else {
        log('不支持的平台', os.platform());
        return null;
    }
}
// Linux 监控逻辑
async function monitorLinux() {
    const pidstatCommand = 'pidstat -d'; // 每秒采样一次，共2次（计算平均值）
    const stdout = await execAsync(pidstatCommand);
    const processMap = parseLinuxOutput(stdout);
    const sorted = Object.values(processMap)
        .sort((a, b) => (b.read + b.write) - (a.read + a.write));

    const processList = sorted.slice(0, 15);
    return processList;
}
// 解析Linux的pidstat输出
function parseLinuxOutput(output) {
    const lines = output.split('\n').slice(7); // 跳过标题行
    const processMap = {};

    lines.forEach(line => {
        const parts = line.trim().split(/\s+/);
        if (parts.length < 8) return;
        const pid = parseInt(parts[3]);
        const name = parts[8];
        const read = parseFloat(parts[4]) / 1024;
        const write = parseFloat(parts[5]) / 1024;

        if (!isNaN(pid) && pid) {
            processMap[pid] = {
                pid,
                name,
                read,
                write
            };
        }
    });
    return processMap;
}
// Windows 监控逻辑
async function monitorWindows() {
    // 使用PowerShell获取进程的磁盘I/O
    const powershellCommand = `wmic path Win32_PerfFormattedData_PerfProc_Process get IDProcess,Name,IOReadBytesPersec,IOWriteBytesPersec`;
    const stdout = await execAsync(powershellCommand);
    if(stdout) {
        const processes = stdout.split('\r\n')
          .slice(1)
          .map(line => line.trim().split(/\s+/))
          .filter(([pid]) => pid)
          .map(([pid, read, write, name]) => ({
            pid: parseInt(pid),
            name,
            read: parseInt(read) / 1024 / 1024, // 转换为MB/s
            write: parseInt(write) / 1024 / 1024
          }))
          .sort((a, b) => (b.read + b.write) - (a.read + a.write));
        return processes.slice(0, 15);
    }
    return [];
}

async function execAsync(cmd) {
    return new Promise((resolve, reject) => {
        exec(cmd, { shell: false }, (error, stdout) => {
            if(error) {
                reject(error);
            }
            return resolve(stdout);
        });
    });
}

// 初始化 influxdb Client
function initInfluxdb(dbName) {
    let conf = sysConfig.hasOwnProperty(dbName) ? sysConfig[dbName] : sysConfig["influxdb"];
    if(conf) {
        influxClient = new InfluxDB(conf);
        influxWriteClient = influxClient.getWriteApi(conf.org, conf.bucket, 'ns');
    }
}
// 写入数据
function writeData2Influxdb(data) {
    if(influxClient && influxWriteClient) {
        let point = new Point(data.collectorId.toString())
            .tag("itemId", data.itemId.toString())
            .tag("dataName", data.dataName)
            .floatField(data.id.toString(), data.val);
        debug("writeData2Influxdb", point);
        influxWriteClient.writePoint(point);
    }
}
// 写入文件
function flushInfluxdb() {
    if(influxClient && influxWriteClient) {
        influxWriteClient.flush();
    }
}


function deepCopy(obj) {
    return JSON.parse(JSON.stringify(obj));
}

// 合并两个object的属性
function extendObj(obj1, obj2, overWrite) {
    for(var key in obj2){
        if(obj1.hasOwnProperty(key) && !overWrite) {
            continue;
        }
        obj1[key]=obj2[key];
     }
     return obj1;
}

// 公共等待函数
async function sleep(second) {
    return new Promise((resolve, reject) => {
        setTimeout(function() {
            resolve();
        }, second*1000);
    });
}


function getHolidays(year) {
    let holidays = [];
    let weekend = [];
    let fullHolidays = [];
    Object.keys(sysConfig.holidays[year]).map(md => {
        if(sysConfig.holidays[year][md] == 1) {
            weekend.push(year+md);
        } else if(sysConfig.holidays[year][md] == 2) {
            holidays.push(year+md);
        } else if(sysConfig.holidays[year][md] == 3) {
            weekend.push(year+md);
            holidays.push(year+md);
        }

    });
    return {
        holidays: holidays,
        weekend: weekend,
        fullHolidays: holidays.concat(weekend),
    }
}

// 节假日检查
// date: moment()
// type: 1 => 只有节假日；0 => 非节假日
function checkHoliday(date, type) {
    let dateStr = date.format("YYYYMMDD");
    let year = date.format("YYYY");
    // 有配置过节假日
    if(typeof sysConfig.holidays == "object"
        && typeof sysConfig.holidays[year] == "object"
        && Object.keys(sysConfig.holidays[year]).length > 0) {
        let holidays = getHolidays(year);
        // debug("holidays.holidays", holidays.holidays+"***********************");
        // debug("dateStr", dateStr+"***********************");
        // debug("holidays.holidays.indexOf(dateStr)", holidays.holidays.indexOf(dateStr)+"***********************");
        if(type == 0) {
            return holidays.holidays.indexOf(dateStr) < 0;
        } else if(type == 1) {
            return holidays.holidays.indexOf(dateStr) >= 0;
        }
    }
    return true;
}

// 工作日检查
// date: moment()
// type: 1 => 只有工作日；0 => 非工作日
function checkWeekend(date, type) {
    let dateStr = date.format("YYYYMMDD");
    let year = date.format("YYYY");
    // 有配置过节假日
    if(typeof sysConfig.holidays == "object"
        && typeof sysConfig.holidays[year] == "object"
        && Object.keys(sysConfig.holidays[year]).length > 0) {
        let holidays = getHolidays(year);
        // debug("holidays.weekend", holidays.weekend+"***********************");
        // debug("dateStr", dateStr+"=========================");
        // debug("holidays.weekend.indexOf(dateStr)", holidays.weekend.indexOf(dateStr)+"~~~~~~~~~~~~~~~~~~~~");
        if(type == 0) {
            return holidays.weekend.indexOf(dateStr) >= 0;
        } else if(type == 1) {
            return holidays.weekend.indexOf(dateStr) < 0;
        }
    }
    return true;
}

// for ctwing 数据转换
function base64toHEX(base64) {
  var raw = atob(base64);
  var HEX = '';
  for ( i = 0; i < raw.length; i++ ) {
    var _hex = raw.charCodeAt(i).toString(16)
    HEX += (_hex.length==2?_hex:'0'+_hex);
  }
  return HEX;
}

function encodePwd(pwd) {
  // 加解密
  const key = Buffer.from('9vApxLk5G3PAsJrM', 'utf8');
  const iv = Buffer.from('FnJL7EDzjqWjcbY9', 'utf8');
  let sign = '';
  const cipher = crypto.createCipheriv('aes-128-cbc', key, iv);
  sign += cipher.update(pwd, 'utf8', 'hex');
  sign += cipher.final('hex');
  return sign;
}
function decodePwd(ePwd) {
  // 加解密
  const key = Buffer.from('9vApxLk5G3PAsJrM', 'utf8');
  const iv = Buffer.from('FnJL7EDzjqWjcbY9', 'utf8');
  let src = '';
  const cipher = crypto.createDecipheriv('aes-128-cbc', key, iv);
  src += cipher.update(ePwd, 'hex', 'utf8');
  src += cipher.final('utf8');
  return src;
}

function encodeUrlStr(str) {
    return str.replace(/-/g, "$2d");
}

// explicitArray (default: true): Always put child nodes in an array if true; otherwise an array is created only if there is more than one.
async function parseXml2js(msg, opt={explicitArray : false}) {
    return new Promise((resolve, reject) => {
        return xml2js.parseString(msg, opt, function(err, json) {
            debug("parseXml2js parseString", msg);
            if(err) {
                reject(err);
            } else {
                resolve(json);
            }
        });
    });
}

function stringToBase64(str) {
  return new Buffer.from(str).toString("base64");
}
/**
 * base64转字符串
 */
function base64ToString(b64) {
  return new Buffer.from(b64, "base64").toString();
}

// 从右边开始，替换一次某个字符串
function removeRightSubstring(input, substring) {
    const index = input.lastIndexOf(substring); // 获取要删除的子字符串在原字符串中最后出现的位置
    if (index !== -1) {
        return input.substring(0, index); // 返回保留删除子字符串右侧的部分
    } else {
        return input; // 若未找到要删除的子字符串，直接返回原字符串
    }
}

function initDataServer(options) {
    function Api(opts) {
        this.opts = {
            timeout:  10000,
            ... opts
        };
        this._init_ = function() {
        }
    }
    dataServer = new Api(options.localServer);
}

// 用 data 替换 oldData 重复 id 的项
function upsertData(oldData, data) {
    if (Array.isArray(data)) {
        // 如果 data 是数组，逐个处理
        data.forEach(newItem => {
            const index = oldData.findIndex(item => item.id === newItem.id);
            if (index > -1) {
                // 存在则替换
                oldData[index] = newItem;
            } else {
                // 不存在则添加
                oldData.push(newItem);
            }
        });
    } else {
        // data 是单个对象
        const index = oldData.findIndex(item => item.id === data.id);
        if (index > -1) {
            oldData[index] = data;
        } else {
            oldData.push(data);
        }
    }
    return oldData;
}

// 调用 localhost:8079/data api, 数据入mysql库
async function syncItemData2Db(data) {
    // 压入缓存, 如果是相同的 item_data_id, 则只留最后一个
    syncItemDataCachePostData = upsertData(syncItemDataCachePostData, data);
    
    // 如果正在刷新中，只更新缓存，不再处理 flush 逻辑
    if(isFlushing) {
        return ;
    }

    // 满足立即提交条件：缓存达到 20 条
    if (syncItemDataCachePostData.length >= syncItemDataMaxNumSend) {
        await flushItemData2DbCache();
        return;
    }

    // 设置/重置定时器（3秒延迟）
    if (!syncItemDataTimeoutId) {
        syncItemDataTimeoutId = setTimeout(async () => {
            await flushItemData2DbCache();
        }, syncItemDataTimeoutSpan);
    }
}

// 封装缓存刷新逻辑
async function flushItemData2DbCache() {
    if (syncItemDataCachePostData.length > 0 && dataServer) {
        isFlushing = true; // 开始刷新，锁定状态
        const path = constructPath();
        try {
            // 先处理当前包内数据
            let data = [...syncItemDataCachePostData];
            // 清空缓存和定时器
            syncItemDataCachePostData = [];
            clearTimeout(syncItemDataTimeoutId);
            syncItemDataTimeoutId = null;

            debug("flushItemData2DbCache start", path, data);
            const resp = await axios.post(path, data);
            debug("flushItemData2DbCache success", resp.data);
        } catch (e) {
            log("flushItemData2DbCache error", e.message, path, syncItemDataCachePostData);
        } finally {
            isFlushing = false; // 释放锁
        }
    }
}

// 复用原有的路径构造逻辑
function constructPath() {
    let path = dataServer.opts.host + ":" + dataServer.opts.port + dataServer.opts.path;
    return path.indexOf("http") < 0 ? "http://" + path : path;
}


// 公共 call api 函数
function callApi(sn, cmd, requestUrl, timeout) {
  return new Promise(function(resolve,reject){
    let query = {
      url: requestUrl,
      method: "get",
      headers: {
        "content-type": "application/json; charset=utf-8",
      },
      qs: {
        sn: sn,
        cmd: cmd,
      },
      timeout: timeout || 10*1000,
    };
    request(query, function(error, response, body) {
      if(error) {
          log("callApi error", error);
          //reject(error);
      }
      // 成功后执行
      resolve(body);
    });
  });
}

// 公共 send request 函数
function sendRequest(requestUrl, method="post", headers={}, params={}, timeout=10000) {
  return new Promise(function(resolve, reject){
    let query = {
      url: requestUrl,
      method: "post",
      headers: headers,
      body: JSON.stringify(params),
      timeout: timeout || 10*1000,
    };
    debug("sendRequest", query);
    request(query, function(error, response, body) {
      if(error) {
          log("callApi error", error);
          //reject(error);
      }
      // 成功后执行
      resolve(body);
    });
  });
}

// 强制停N秒钟
async function sleep(sleepTime) {
    return new Promise(resolve => setTimeout(resolve, sleepTime));
}

function shuffle(array) {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
};
function splitDataset(X, y, testSize) {
  const data = X.map((x, i) => ({ X: x, y: y[i] }));
  shuffle(data);

  const splitIndex = Math.floor(data.length * (1 - testSize));
  const trainingData = data.slice(0, splitIndex);
  const testData = data.slice(splitIndex);

  const trainingSet = {
    X: trainingData.map(d => d.X),
    y: trainingData.map(d => d.y)
  };

  const testSet = {
    X: testData.map(d => d.X),
    y: testData.map(d => d.y)
  };

  return { trainingSet, testSet };
};

// 签名排序，一般用于第三方api签名前用
function paramSort(params, ignoreKey=[]) {
  return Object.keys(params)
    .filter( k => !ignoreKey.includes(k))
    .sort()
    .map(key => `${key}=${params[key]}`)
    .join('&');
}
// hmac-sha1 加密算法
function hmacSha1(string, secretKey) {
    return crypto.createHmac('sha1', secretKey)
        .update(string)
        .digest('hex');
}

// 处理字符串，确保只匹配纯数字 如 {50101}
function Strformat(str, o) {
    return str.replace(/\{(\d+)\}/g,
       function(a, b) {
           var r = o[b];  // 如果不匹配，则不处理
           return r;
       }
    );
};


module.exports.log = log;
module.exports.info = info;
module.exports.debug = debug;
module.exports.remoteLog = remoteLog;
module.exports.connectLogstash = connectLogstash;
module.exports.connectMqtt = connectMqtt;
module.exports.subscribeMsg = subscribeMsg;
module.exports.bindMsgCallback = bindMsgCallback;
module.exports.sendWarningMsg = sendWarningMsg;
module.exports.sendItemDataMsg = sendItemDataMsg;
module.exports.deepCopy = deepCopy;
module.exports.extendObj = extendObj;
module.exports.checkHoliday = checkHoliday;
module.exports.checkWeekend = checkWeekend;
module.exports.base64toHEX = base64toHEX;
module.exports.encodePwd = encodePwd;
module.exports.decodePwd = decodePwd;
module.exports.encodeUrlStr = encodeUrlStr;
module.exports.stringToBase64 = stringToBase64;
module.exports.base64ToString = base64ToString;
module.exports.parseXml2js = parseXml2js;
module.exports.removeRightSubstring = removeRightSubstring;
module.exports.initDataServer = initDataServer;
module.exports.upsertData = upsertData;
module.exports.syncItemData2Db = syncItemData2Db;
module.exports.callApi = callApi;
module.exports.sendRequest = sendRequest;
module.exports.sleep = sleep;
module.exports.initInfluxdb = initInfluxdb;
module.exports.writeData2Influxdb = writeData2Influxdb;
module.exports.flushInfluxdb = flushInfluxdb;
module.exports.splitDataset = splitDataset;
module.exports.getServerInfos = getServerInfos;
module.exports.monitorDiskIo = monitorDiskIo;
module.exports.execAsync = execAsync;
module.exports.paramSort = paramSort;
module.exports.Strformat = Strformat;
module.exports.hmacSha1 = hmacSha1;

