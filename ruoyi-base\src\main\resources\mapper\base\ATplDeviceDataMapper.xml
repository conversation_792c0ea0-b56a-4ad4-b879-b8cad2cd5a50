<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.base.mapper.ATplDeviceDataMapper">

    <resultMap type="ATplDeviceData" id="ATplDeviceDataResult">
        <result property="id"    column="id"    />
        <result property="templateId"    column="template_id"    />
        <result property="name"    column="name"    />
        <result property="alias"    column="alias"    />
        <result property="dataType"    column="data_type"    />
        <result property="dataUnit"    column="data_unit"    />
        <result property="note"    column="note"    />
        <result property="note2"    column="note2"    />
        <result property="func"    column="func"    />
        <result property="type"    column="type"    />
        <result property="oid"    column="oid"    />
        <result property="tag"    column="tag"    />
        <result property="isPrimaryEnergy"    column="is_primary_energy"    />
        <result property="pDeviceId"    column="p_device_id"    />
        <result property="pAddr"    column="p_addr"    />
        <result property="pLength"    column="p_length"    />
        <result property="pDataGroup"    column="p_data_group"    />
        <result property="pFunc"    column="p_func"    />
        <result property="pFuncList"    column="p_func_list"    />
    </resultMap>

    <sql id="selectATplDeviceDataVo">
        select id, template_id, name, alias, data_type, data_unit, note, note2, func, type, oid, tag, is_primary_energy,
               p_device_id, p_addr, p_length, p_data_group, p_func, p_func_list from a_tpl_device_data
    </sql>

    <select id="selectATplDeviceDataList" parameterType="ATplDeviceData" resultMap="ATplDeviceDataResult">
        <include refid="selectATplDeviceDataVo"/>
        <where>
            <if test="templateId != null "> and template_id = #{templateId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="alias != null  and alias != ''"> and alias = #{alias}</if>
            <if test="dataType != null  and dataType != ''"> and data_type = #{dataType}</if>
            <if test="dataUnit != null  and dataUnit != ''"> and data_unit = #{dataUnit}</if>
            <if test="note != null  and note != ''"> and note = #{note}</if>
            <if test="note2 != null  and note2 != ''"> and note2 = #{note2}</if>
            <if test="func != null  and func != ''"> and func = #{func}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="oid != null "> and oid = #{oid}</if>
            <if test="tag != null  and tag != ''"> and tag = #{tag}</if>
            <if test="isPrimaryEnergy != null  and isPrimaryEnergy != ''"> and is_primary_energy = #{isPrimaryEnergy}</if>
            <if test="pDeviceId != null  and pDeviceId != ''"> and p_device_id = #{pDeviceId}</if>
            <if test="pAddr != null  and pAddr != ''"> and p_addr = #{pAddr}</if>
            <if test="pLength != null  and pLength != ''"> and p_length = #{pLength}</if>
            <if test="pDataGroup != null  and pDataGroup != ''"> and p_data_group = #{pDataGroup}</if>
            <if test="pFunc != null  and pFunc != ''"> and p_func = #{pFunc}</if>
            <if test="pFuncList != null  and pFuncList != ''"> and p_func_list = #{pFuncList}</if>
        </where>
        order by template_id, oid, name
    </select>

    <select id="selectATplDeviceDataById" parameterType="Long" resultMap="ATplDeviceDataResult">
        <include refid="selectATplDeviceDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertATplDeviceData" parameterType="ATplDeviceData" useGeneratedKeys="true" keyProperty="id">
        insert into a_tpl_device_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateId != null">template_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="alias != null">alias,</if>
            <if test="dataType != null">data_type,</if>
            <if test="dataUnit != null">data_unit,</if>
            <if test="note != null">note,</if>
            <if test="note2 != null">note2,</if>
            <if test="func != null">func,</if>
            <if test="type != null">type,</if>
            <if test="oid != null">oid,</if>
            <if test="tag != null">tag,</if>
            <if test="isPrimaryEnergy != null">is_primary_energy,</if>
            <if test="pDeviceId != null">p_device_id,</if>
            <if test="pAddr != null">p_addr,</if>
            <if test="pLength != null">p_length,</if>
            <if test="pDataGroup != null">p_data_group,</if>
            <if test="pFunc != null">p_func,</if>
            <if test="pFuncList != null">p_func_list,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateId != null">#{templateId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="alias != null">#{alias},</if>
            <if test="dataType != null">#{dataType},</if>
            <if test="dataUnit != null">#{dataUnit},</if>
            <if test="note != null">#{note},</if>
            <if test="note2 != null">#{note2},</if>
            <if test="func != null">#{func},</if>
            <if test="type != null">#{type},</if>
            <if test="oid != null">#{oid},</if>
            <if test="tag != null">#{tag},</if>
            <if test="isPrimaryEnergy != null">#{isPrimaryEnergy},</if>
            <if test="pDeviceId != null">#{pDeviceId},</if>
            <if test="pAddr != null">#{pAddr},</if>
            <if test="pLength != null">#{pLength},</if>
            <if test="pDataGroup != null">#{pDataGroup},</if>
            <if test="pFunc != null">#{pFunc},</if>
            <if test="pFuncList != null">#{pFuncList},</if>
        </trim>
    </insert>

    <update id="updateATplDeviceData" parameterType="ATplDeviceData">
        update a_tpl_device_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="alias != null">alias = #{alias},</if>
            <if test="dataType != null">data_type = #{dataType},</if>
            <if test="dataUnit != null">data_unit = #{dataUnit},</if>
            <if test="note != null">note = #{note},</if>
            <if test="note2 != null">note2 = #{note2},</if>
            <if test="func != null">func = #{func},</if>
            <if test="type != null">type = #{type},</if>
            <if test="oid != null">oid = #{oid},</if>
            <if test="tag != null">tag = #{tag},</if>
            <if test="isPrimaryEnergy != null">is_primary_energy = #{isPrimaryEnergy},</if>
            <if test="pDeviceId != null">p_device_id = #{pDeviceId},</if>
            <if test="pAddr != null">p_addr = #{pAddr},</if>
            <if test="pLength != null">p_length = #{pLength},</if>
            <if test="pDataGroup != null">p_data_group = #{pDataGroup},</if>
            <if test="pFunc != null">p_func = #{pFunc},</if>
            <if test="pFuncList != null">p_func_list = #{pFuncList},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteATplDeviceDataById" parameterType="Long">
        delete from a_tpl_device_data where id = #{id}
    </delete>

    <delete id="deleteATplDeviceDataByIds" parameterType="String">
        delete from a_tpl_device_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>