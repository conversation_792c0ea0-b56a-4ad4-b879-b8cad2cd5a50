"use strict";

const util = require("util");
const EventEmitter = require("events");
const mysql = require("mysql2/promise");
var moment = require('moment');
const helper = require("./helper");

function DbServer(options) {
    EventEmitter.call(this);
    this.options = options;
    this.started = false;
    // this.server = null;
    this.pool = null;

    // 创建server实例
    this._createServer();
}
util.inherits(DbServer, EventEmitter);

// 启动服务
DbServer.prototype._createServer = function() {
    this.pool = mysql.createPool({
        host: this.options.host,
        port: this.options.port || 3306,
        database: this.options.database,
        user: this.options.username,
        password: this.options.password || helper.decodePwd(this.options.encodePwd),
        timezone: this.options.timezone ? "+"+this.options.timezone : "local",
        waitForConnections: true,
        connectionLimit: this.options.connectionLimit || 50, // 最大连接数
        queueLimit: 0              // 请求队列最大长度（0 表示不限制）
    });
};

DbServer.prototype.createServer2 = function(opt) {
    if(this.pool2) {
        try {
           this.pool2.end();
        } catch(e) {
            // pass
        }
    }
    this.pool2 = mysql.createPool({
        host: opt.host,
        port: opt.port || 3306,
        database: opt.database,
        user: opt.username,
        password: opt.password || helper.decodePwd(opt.encodePwd),
        timezone: opt.timezone ? "+"+opt.timezone : "local",
        waitForConnections: true,
        connectionLimit: opt.connectionLimit || 50, // 最大连接数
        queueLimit: 0              // 请求队列最大长度（0 表示不限制）
    });
};

// 停止服务
DbServer.prototype._stopServer = function() {
    if(this.pool) {
        this.pool.end();
        this.pool = null;
    }
    if(this.pool2) {
        this.pool2.end();
        this.pool2 = null;
    }
};

// 拿到相关数据
DbServer.prototype.getCollectors = async function() {
    var sql = "select * from a_collector order by code asc";
    try {
        let [rows] = await this.pool.query(sql);
        var res = rows && rows.length > 0 ? rows : [];
        return JSON.parse(JSON.stringify(res));
    } catch (error) {
        helper.log("getCollectors error", sql, "\nerror:", error);
        return null;
    }
};

// 拿到相关数据
DbServer.prototype.getItems = async function(collector_id) {
    var sql = "select i.*, ir.rules from a_item i left join a_item_rule ir on i.id=ir.item_id where i.collector_id = "+collector_id+" order by i.collector_id asc, i.code asc";
    try {
        let [rows] = await this.pool.query(sql);
        var res = rows && rows.length > 0 ? rows : [];
        return JSON.parse(JSON.stringify(res));
    } catch (error) {
        helper.log("getItems error", sql, "\nerror:", error);
        return null;
    }
};

// 拿到相关数据
DbServer.prototype.updateData = async function(msg) {
    var that = this;
    let updateTime = msg.updated ? msg.updated : moment().format("YYYY-MM-DD HH:mm:ss");
    // 兼容旧版本
    let sql = "update a_item_data set val = ?, has_sync = 'Y', updated_at = ? where id= ? limit 1";
    let val = [msg.val, updateTime, msg.id];
    if(msg.hasOwnProperty("keepSecond")) {
        sql = "update a_item_data set val = ?, has_sync = 'Y', updated_at = ?, keep_second = ? where id= ? limit 1";
        val = [msg.val, updateTime, msg.keepSecond, msg.id];
    }
    try {
        await this.pool.query(sql, val);
        if(that.pool2) {
            await this.pool2.query(sql, val);
        }
    } catch (error) {
        helper.log("updateData error", msg, "\nerror:", error);
        return null;
    }
};

// 拿到相关数据，不更新同步状态
DbServer.prototype.updateDataWithoutHasSync = async function(msg) {
    var that = this;
    let updateTime = msg.updated ? msg.updated : moment().format("YYYY-MM-DD HH:mm:ss");
    // 兼容旧版本
    let sql = "update a_item_data set val = ?, updated_at = ? where id= ? limit 1";
    let val = [msg.val, updateTime, msg.id];
    if(msg.hasOwnProperty("keepSecond")) {
        sql = "update a_item_data set val = ?, updated_at = ?, keep_second = ? where id= ? limit 1";
        val = [msg.val, updateTime, (msg.hasOwnProperty("keepSecond") ? msg.keepSecond : 0), msg.id];
    }
    try {
        await this.pool.query(sql, val);
        if(that.pool2) {
            await this.pool2.query(sql, val);
        }
    } catch (error) {
        helper.log("updateDataWithoutHasSync error", msg, "\nerror:", error);
        return null;
    }
};

// 更新设备 a_item.updated_at
DbServer.prototype.updateItemTime = async function(msg) {
    var that = this;
    var sql = "select * from a_item where id = ?";
    var val = msg.id;
    try {
        let [rows] = await this.pool.query(sql, val);
        var curItem = null;
        if (rows && rows.length > 0) {
            curItem = rows[0];
        }
        if (curItem) {
            let updateTime = msg.updated ? msg.updated : moment().format("YYYY-MM-DD HH:mm:ss");
            sql =
                "update a_item set updated_at = ? where id= ?";
            val = [updateTime , curItem.id];
        } else {
            //sql =
            //    "insert into a_item_data (item_id, indication, other_data, updated_at) values (?,?,?,?)";
            //val = [msg.id, msg.ind, JSON.stringify(msg), new Date()];
        }
        await that.pool.query(sql, val);
        if(that.pool2) {
            await this.pool2.query(sql, val);
        }
    } catch (error) {
        helper.log("updateItemTime error", msg, "\nerror:", error);
        return null;
    }
};

// 检查网关在线状态，超过5分钟，判断离线  deleted
DbServer.prototype.checkCollectorState = async function(msg) {
    var that = this;
    var sql = "select * from a_collector where id = ?";
    var val = msg.id;
    try {
        let [rows] = await this.pool.query(sql, val);
        var curItem = null;
        if (rows && rows.length > 0) {
            curItem = rows[0];
        }
        if (curItem) {
            // 超过判定时间
            if( moment(moment().format("YYYY-MM-DD HH:mm:ss")).diff(moment(curItem.updated_at), "m") > msg.offLineCheck ) {
                let updateTime = msg.updated ? msg.updated : moment().format("YYYY-MM-DD HH:mm:ss");
                sql =
                    "update a_collector set state_conn = 'offline' where id= ?";
                val = [curItem.id];
            }
        } else {
            //sql =
            //    "insert into a_item_data (item_id, indication, other_data, updated_at) values (?,?,?,?)";
            //val = [msg.id, msg.ind, JSON.stringify(msg), new Date()];
        }
        await that.pool.query(sql, val);
        if(that.pool2) {
            await this.pool2.query(sql, val);
        }
    } catch (error) {
        helper.log("updateItemTime error", msg, "\nerror:", error);
        return null;
    }
};

// 更新网关a_collector.updated_at
DbServer.prototype.updateCollectorTime = async function(msg) {
    var that = this;
    var sql = "select * from a_collector where id = ?";
    var val = msg.id;
    try {
        let [rows] = await this.pool.query(sql, val);
        var curItem = null;
        if (rows && rows.length > 0) {
            curItem = rows[0];
        }
        if (curItem) {
            let updateTime = msg.updated ? msg.updated : moment().format("YYYY-MM-DD HH:mm:ss");
            sql =
                "update a_collector set updated_at = ?, state_conn = ? where id= ?";
            val = [updateTime, msg.stateConn, curItem.id];
        } else {
            //sql =
            //    "insert into a_item_data (item_id, indication, other_data, updated_at) values (?,?,?,?)";
            //val = [msg.id, msg.ind, JSON.stringify(msg), new Date()];
        }
        await that.pool.query(sql, val);
        if(that.pool2) {
            await this.pool2.query(sql, val);
        }
    } catch (error) {
        helper.log("updateItemTime error", msg, "\nerror:", error);
        return null;
    }
};

DbServer.prototype.updateWarning = async function(msg) {
    var that = this;
    var sql = "select * from a_item_warning where item_id = ? and warning_category = ? and has_fixed = 0 order by reported_at desc";
    let n = moment().format("YYYY-MM-DD HH:mm:ss");
    try {
        let [rows] = await this.pool.query(sql, [msg.item_id, msg.warning_category]);
        var curItem = null;
        if (rows && rows.length > 0) {
            curItem = rows[0];
        }
        helper.debug(curItem);

        // 修复预警
        var needSql = false;
        sql = "";
        var val = [];
        if (msg.has_fixed == 1) {
            if (curItem) {
                sql = "update a_item_warning set updated_at = ?, has_fixed = 1 where id= ?";
                val = [n, curItem.id];
                needSql = true;
            } else {
                // 没有错误, 忽略
            }
        } else {
            if (curItem) {
                sql = "update a_item_warning set updated_at = ?, compare = ? where id= ?";
                val = [n, msg.compare, curItem.id];
            } else {
                sql = "insert into a_item_warning (item_id, compare, warning_category, severity, err_msg, solution_ref, reported_at) values (?,?,?,?,?,?,?)";
                val = [msg.item_id, msg.compare, msg.warning_category, msg.severity, msg.err_msg, msg.solution_ref, n];
            }
            needSql = true;
        }
        helper.debug(sql, '\n', val);
        if(needSql) {
            that.pool.query(sql, val);
        }
    } catch (error) {
        helper.log("doSql error", sql, vals, "\nerror:", error);
        return null;
    }
}

DbServer.prototype.getItemData = async function(gatewayId) {
    // 因为采集时间取第一个时间, 所以用最近一个数据提供
    var sql = `
        select
            i.*,
            ia.item_id,
            ia.indication,
            ia.other_data,
            date_format(ia.updated_at, '%Y%m%d%H%i%s') as updated_at
        from a_item i
        LEFT JOIN a_item_data ia on ia.item_id=i.id
        where i.collector_code = ?
        order by ia.updated_at desc
    `;
    try {
        let [rows] = await this.pool.query(sql, [gatewayId]);
        var res = rows && rows.length > 0 ? rows : [];
        return JSON.parse(JSON.stringify(res));
    } catch (error) {
        helper.log("doSql error", sql, vals, "\nerror:", error);
        return null;
    }
}

// 拿到相关数据
DbServer.prototype.doSql = async function(sql, vals) {
    try {
        let [rows] = await this.pool.query(sql, vals);
        return rows;
    } catch (error) {
        helper.log("doSql error", sql, vals, "\nerror:", error);
        return null;
    }
};

// 插入或更新数据
DbServer.prototype.insertOrUpdate = async function(sql, vals) {
    var that = this;
    try {
        var exist = await that.doSql(sql["check"], vals["check"]);
        if(exist.length > 0) {
            if(sql["update"]) {
                await that.doSql(sql["update"], vals["update"]);
            }
        } else if(sql["insert"]) {
            await that.doSql(sql["insert"], vals["insert"]);
            exist = await that.doSql(sql["check"], vals["check"]);
        }
        if(exist.length > 0) {
            return exist;
        } else {
            helper.log("insertOrUpdate error: effect row 0");
            return [];
        }
    } catch(e) {
        helper.log("insertOrUpdate error:", e.message, sql, vals);
        return [];
    }
};

module.exports = DbServer;
