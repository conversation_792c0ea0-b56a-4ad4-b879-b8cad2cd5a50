package com.ruoyi.base.mapper;

import cn.hutool.json.JSONObject;
import com.ruoyi.base.domain.Resource;
import com.ruoyi.base.domain.vo.DeviceDataTpl;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.SelectProvider;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Component;


import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface UtilMapper {

    // 查询表是否存在
    public List<String> getHistoryTableNameList(@Param("dbName") String dbName,
                                                @Param("tableName") String tableName);

    public void createDeviceHistoryTable(@Param("tableName") String tableName);

    public void createEnergyHistoryTable(@Param("tableName") String tableName);

    public int addLicenseCode(@Param("md") String md,
                               @Param("code") String code,
                               @Param("note") String note,
                               @Param("expDate") String expDate);

    public int updateLicenseCode(@Param("md") String md,
                                 @Param("dongleInfo") String dongleInfo,
                                 @Param("dongleCode") String dongleCode,
                                 @Param("dbCode") String dbCode,
                                 @Param("expDate") String expDate);

    public List<Map> licenseCodeList(@Param("md") String md,
                                     @Param("note") String note);

    List<Map<String, Object>> executeSql(@Param("sql") String sql);
    int executeSqlImport(@Param("sql") String sql);
    int executeSqlClear(@Param("sql") String sql);
    int executeSqlUpdate(@Param("sql") String sql);

    // 从 d_device 表查询设备信息
    List<Map> getDDeviceList(@Param("idList") List<String> idList);

    // 自动生成模块，清除逻辑
    public int deleteItem(@Param("id") Long id);
    public int deletePointTableData(@Param("itemId") Long itemId);
    public int deleteItemData(@Param("itemId") Long itemId);
    public int deleteDevice(@Param("id") Long id);
    public int deleteDeviceItemDataMap(@Param("deviceId") Long deviceId);
    public int deleteDeviceResourceMap(@Param("deviceId") Long deviceId);
    public int deleteEDevice(@Param("id") Long id);
    public int deleteEDeviceItemDataMap(@Param("deviceId") Long deviceId);
    public int deleteEDeviceGroupMap(@Param("deviceId") Long deviceId);
    public int deleteWarningRule(@Param("itemId") Long itemId);
    public int deleteItemRuleMap(@Param("itemId") Long itemId);

    public int deleteDeviceDataHistory(@Param("deviceId") Long deviceId,
                                       @Param("from") String from,
                                       @Param("to") String to);
    public int deleteEDeviceDataHistory(@Param("deviceId") Long deviceId,
                                        @Param("from") String from,
                                        @Param("to") String to);
    public int deleteEDeviceDataHistoryDaily(@Param("deviceId") Long deviceId,
                                             @Param("from") String from,
                                             @Param("to") String to);
    public int deleteDeviceOperLog(@Param("deviceId") Long deviceId,
                                   @Param("from") String from,
                                   @Param("to") String to);
    public int deleteItemWarning(@Param("deviceId") Long deviceId,
                                   @Param("from") String from,
                                   @Param("to") String to);
    public int deleteEWarning(@Param("deviceId") Long deviceId,
                                 @Param("from") String from,
                                 @Param("to") String to);
    public int deleteDeviceMaintenance(@Param("deviceId") Long deviceId,
                              @Param("from") String from,
                              @Param("to") String to);

    public int insertItem(@Param("id") Long id,
                          @Param("collectorId") Long collectorId,
                          @Param("name") String name,
                          @Param("description") String description);
    public int insertDevice(@Param("id") Long id,
                            @Param("type") String type,
                            @Param("name") String name,
                            @Param("brand") String brand,
                            @Param("model") String model,
                            @Param("note") String note,
                            @Param("icon") String icon,
                            @Param("thumb") String thumb,
                            @Param("buildingId") Long buildingId,
                            @Param("resourceId") Long resourceId,
                            @Param("files") String files,
                            @Param("serviceLife") String serviceLife,
                            @Param("energyLever") Long energyLever,
                            @Param("ratedPower") BigDecimal ratedPower,
                            @Param("ajustablePower") String ajustablePower);
    public int insertDeviceResourceMap(@Param("deviceId") Long deviceId,
                                       @Param("resourceId") Long resourceId);

    public int insertWarningRule(@Param("id") Long id,
                                 @Param("name") String name,
                                 @Param("description") String description,
                                 @Param("key") String key,
                                 @Param("val") String val,
                                 @Param("compare") String compare,
                                 @Param("severity") String severity,
                                 @Param("errMsg") String errMsg,
                                 @Param("solutionRef") String solutionRef,
                                 @Param("tag") String tag,
                                 @Param("timeSpan") Long timeSpan,
                                 @Param("func") String func,
                                 @Param("mailTo") String mailTo,
                                 @Param("smsTo") String smsTo,
                                 @Param("smsTemplate") String smsTemplate,
                                 @Param("warnStart") String warnStart,
                                 @Param("warnEnd") String warnEnd);
    public int insertItemRuleMap(@Param("itemId") Long itemId,
                                 @Param("ruleId") Long ruleId);

    public int insertEDevice(@Param("id") Long id,
                             @Param("type") String type,
                             @Param("name") String name,
                             @Param("description") String description,
                             @Param("buildingId") Long buildingId,
                             @Param("remark") String remark);

    public int insertItemData(@Param("id") Long id,
                              @Param("itemId") Long itemId,
                              @Param("name") String name,
                              @Param("alias") String alias,
                              @Param("dataType") String dataType,
                              @Param("dataUnit") String dataUnit,
                              @Param("note") String note,
                              @Param("func") String func);
    public int insertPointTable(@Param("id") Long id,
                                @Param("collectorId") Long collectorId,
                                @Param("itemDataId") Long itemDataId,
                                @Param("dataName") String dataName,
                                @Param("alias") String alias,
                                @Param("type") String type);
    public int insertPointTable2(@Param("id") Long id,
                                 @Param("collectorId") Long collectorId,
                                 @Param("itemDataId") Long itemDataId,
                                 @Param("dataName") String dataName,
                                 @Param("alias") String alias,
                                 @Param("type") String type,
                                 @Param("pDeviceId") Integer pDeviceId,
                                 @Param("pAddr") Integer pAddr,
                                 @Param("pLength") Integer pLength,
                                 @Param("pDataGroup") String pDataGroup,
                                 @Param("pFunc") String pFunc,
                                 @Param("pFuncList") String pFuncList);
    public int insertDeviceItemDataMap(@Param("id") Long id,
                                       @Param("deviceId") Long deviceId,
                                       @Param("itemDataId") Long itemDataId,
                                       @Param("oid") Long oid,
                                       @Param("name") String name,
                                       @Param("alias") String alias,
                                       @Param("relateItemDataId") Long relateItemDataId,
                                       @Param("tag") String tag,
                                       @Param("note") String note);
    public int updateDeviceItemDataMap(@Param("id") Long id,
                                       @Param("relateItemDataId") Long relateItemDataId);

    public int insertEDeviceItemDataMap(@Param("id") Long id,
                                        @Param("deviceId") Long deviceId,
                                        @Param("itemDataId") Long itemDataId,
                                        @Param("name") String name);

    public List<DeviceDataTpl> getDeviceDataTemplate(@Param("deviceIdList") List<String> deviceIdList);
    public List<Map> getDeviceDataTemplateFull(@Param("deviceIdList") List<String> deviceIdList);

    public int updateItemCode(@Param("id") String id,
                              @Param("code") String code);

    // collectorId 不许用修改
    public int updatePointTable(@Param("itemDataId") String itemDataId,
                                @Param("collectorId") String collectorId,
                                @Param("addr") String addr,
                                @Param("deviceId") String deviceId,
                                @Param("length") String length,
                                @Param("dataGroup") String dataGroup,
                                @Param("func") String func,
                                @Param("funcList") String funcList);

    // collectorId 可以修改
    public int updatePointTable2(@Param("itemDataId") String itemDataId,
                                 @Param("collectorId") String collectorId,
                                 @Param("addr") String addr,
                                 @Param("deviceId") String deviceId,
                                 @Param("length") String length,
                                 @Param("dataGroup") String dataGroup,
                                 @Param("func") String func,
                                 @Param("funcList") String funcList);

    public List<Map> getIotMonitorList(@Param("name") String name);
    public Map getIotMonitorLast(@Param("name") String name);
    public void clearIotMonitorHistory(@Param("dateStr") String dateStr);

    public List<Map> getResouce(@Param("buildingId") String buildingId,
                                @Param("category") String category,
                                @Param("type") String type,
                                @Param("groupName") String groupName,
                                @Param("name") String name,
                                @Param("note") String note);

    public int updateWriteVal();

}
