
# 这只是样例，不需要复制全部文件，复制下面的 /apibc/ 的配置到nginx.conf 里面相应的位置即可
worker_processes  1;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;
    client_header_buffer_size 512k;
    large_client_header_buffers 4 512k;

    sendfile        on;
    keepalive_timeout  65;

    #gzip  on;
	  #gzip_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript;


    server {
        listen 80;
        listen 443 ssl;
        server_name  hail.yuankong.org.cn;

        ssl_certificate cert/yuankong.org.cn.pem;
        ssl_certificate_key cert/yuankong.org.cn.key;
     
        ssl_session_cache shared:SSL:1m;
        ssl_session_timeout  5m;
        ssl_ciphers HIGH:!aNULL:!MD5;
        ssl_prefer_server_ciphers on;

        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        location / {
            root  D:/soft/www/dist;
						try_files $uri $uri/  /index.html;
            index  index.html index.htm;
        }
		
				location /api/{
		        proxy_set_header Host localhost;
            proxy_pass  http://localhost:8044/;
						proxy_http_version 1.1;
						proxy_send_timeout 300;
            proxy_read_timeout 300;
            proxy_connect_timeout 300;
            client_max_body_size 500M;
        }
				location /dev-api/{
		    		proxy_set_header Host localhost;
            proxy_pass  http://localhost:8044/;
						proxy_http_version 1.1;
						proxy_send_timeout 300;
            proxy_read_timeout 300;
            proxy_connect_timeout 300;
            client_max_body_size 500M;
        }

				# 新加配置项  开始
        location /apibc/{
            proxy_set_header Host localhost;
            proxy_pass  http://localhost:8049/;
            proxy_http_version 1.1;
            proxy_send_timeout 300;
            proxy_read_timeout 300;
            proxy_connect_timeout 300;
            client_max_body_size 500M;
        }
				# 新加配置项  结束
        location /profile/{
           alias D:/soft/www/uploadPath/;
        }
        location /miniStatic/{
           alias D:/soft/www/uploadPath/miniStatic/;
        }
        location /pcStatic/{
           alias D:/soft/www/dist/;
        }
        error_page  404              /;
        error_page 400 500 502 503 504  /50x.html;
        location = /50x.html {
            root html;
			internal;
        }
    }

}


 