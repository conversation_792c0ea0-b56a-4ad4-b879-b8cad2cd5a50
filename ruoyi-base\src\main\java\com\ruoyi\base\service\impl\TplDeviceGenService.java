package com.ruoyi.base.service.impl;

import com.ruoyi.base.domain.ATplDevice;
import com.ruoyi.base.domain.ATplDeviceData;
import com.ruoyi.base.domain.ATplDeviceDataWarning;
import com.ruoyi.base.domain.Item;
import com.ruoyi.base.domain.vo.DeviceDataTpl;
import com.ruoyi.base.mapper.ItemMapper;
import com.ruoyi.base.mapper.UtilMapper;
import com.ruoyi.base.service.IATplDeviceDataService;
import com.ruoyi.base.service.IATplDeviceDataWarningService;
import com.ruoyi.base.service.IATplDeviceService;
import com.ruoyi.base.service.ITplDeviceGenService;
import com.ruoyi.common.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class TplDeviceGenService implements ITplDeviceGenService {

    private final TransactionTemplate transactionTemplate;
    @Autowired
    public TplDeviceGenService(PlatformTransactionManager transactionManager) {
        this.transactionTemplate = new TransactionTemplate(transactionManager);
    }

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IATplDeviceService tplDeviceService;
    @Autowired
    private IATplDeviceDataService tplDeviceDataService;
    @Autowired
    private IATplDeviceDataWarningService tplDeviceDataWarningService;
    @Autowired
    private UtilMapper utilMapper;

    // 通过数据模板。生成设备数据
    // forceUpdate = true, 则会更新冲突的 id
    @Override
    public Map genTplDeviceData(Long templateId, Long deviceIdFrom, Long deviceIdTo, Boolean forceUpdate,
                                Boolean forceUpdatePointTable) {
        Map res = new HashMap<>();
        List success = new ArrayList<>();
        List failed = new ArrayList<>();
        res.put("success", success);
        res.put("failed", failed);

        ATplDevice template = tplDeviceService.selectATplDeviceById(templateId);
        ATplDeviceData dataQuery = new ATplDeviceData();
        dataQuery.setTemplateId(templateId);
        List<ATplDeviceData> dataList = tplDeviceDataService.selectATplDeviceDataList(dataQuery);

        if(null != template && null != template.getStatus() && template.getStatus() == 1L
                && null != dataList && dataList.size() > 0) {
            int ind = 0;
            for(long i = deviceIdFrom; i <= deviceIdTo; i++) {
                ind += 1;
                try {
                    if(GenDeviceData(i, ind, forceUpdate, forceUpdatePointTable, template, dataList)) {
                        success.add(i);
                    }
                } catch (Exception e) {
                    logger.warn("GenDeviceData failed. error: " + e.getMessage());
                    Map f = new HashMap();
                    f.put("id", i);
                    f.put("error", e.getCause() + " " + e.getMessage());
                    failed.add(f);
                }
            }
        } else {
            logger.warn("genTplDeviceData warn. no valid data.");
        }
        return res;
    }

    // 生成各个表数据，异常回滚
    // 1. 生成 a_item 数据
    // 2. 生成 a_point_table 数据, 注：需要在删除 a_item_data 之前
    // 3. 生成 a_item_data 数据
    // 4. 生成 d_device 数据
    // 5. 生成 d_device_item_data_map 数据
    // 6. 生成 d_device_resource_map 数据
    // 7. 生成 e_device 数据
    // 8. 生成 e_device_item_data_map 数据
    // 9. 生成 a_warning_rule 数据
    // 10.生成 a_item_rule_map 数据
    public boolean GenDeviceData(Long deviceId, Integer ind, Boolean forceUpdate, Boolean forceUpdatePointTable,
                                 ATplDevice tpl, List<ATplDeviceData> dataList) {
        transactionTemplate.execute(new TransactionCallback<Void>() {
            @Override
            public Void doInTransaction(TransactionStatus status) {
                try {
                    // List<Long> dataIdList = dataList.stream().map(d -> d.getId()).collect(Collectors.toList());
                    if (forceUpdate) {
                        // 尝试先删除相关表记录内容
                        utilMapper.deleteItem(deviceId);
                        // 原数采点保留，变更的只做增加动作
                        // utilMapper.deletePointTableData(deviceId);
                        utilMapper.deleteItemData(deviceId);
                        utilMapper.deleteWarningRule(deviceId);
                        utilMapper.deleteItemRuleMap(deviceId);
                        utilMapper.deleteDevice(deviceId);
                        utilMapper.deleteDeviceItemDataMap(deviceId);
                        // 保留原有设备和资源绑定关系，不做清除动作
                        // utilMapper.deleteDeviceResourceMap(deviceId);
                        utilMapper.deleteEDevice(deviceId);
                        utilMapper.deleteEDeviceItemDataMap(deviceId);
                    }
                    // 生成 a_item 数据
                    String deviceName = tpl.getName() + ind;
                    utilMapper.insertItem(deviceId, tpl.getCollectorId(), deviceName, tpl.getDescription());
                    // 生成 d_device 数据
                    utilMapper.insertDevice(deviceId, tpl.getType(), deviceName, tpl.getBrand(), tpl.getModel(),
                            tpl.getNote(), tpl.getIcon(), tpl.getThumb(), tpl.getBuildingId(),
                            tpl.getResourceId(), tpl.getFiles(), tpl.getServiceLife(), tpl.getEnergyLever(), tpl.getRatedPower(),
                            tpl.getAjustablePower());
                    // 生成 d_device_resource_map 数据
//                    utilMapper.insertDeviceResourceMap(deviceId, tpl.getResourceId());

                    // 生成设备 data 数据
                    int dataLen = String.valueOf(dataList.size()).length();
                    ATplDeviceData energyData = null;
                    for (int j = 1; j <= dataList.size(); j++) {
                        Long dataId = deviceId * (long)Math.pow(10, dataLen) + j;
                        ATplDeviceData d = dataList.get(j - 1);
                        utilMapper.insertItemData(dataId, deviceId, d.getName(), d.getAlias(), d.getDataType(), d.getDataUnit(), d.getNote(), d.getFunc());
                        // insert or update PointTable
                        if(forceUpdatePointTable) {
                            utilMapper.insertPointTable2(dataId, tpl.getCollectorId(), dataId, d.getName(), d.getAlias(), d.getType(),
                                    d.getPDeviceId(), d.getPAddr(), d.getPLength(), d.getPDataGroup(), d.getPFunc(), d.getPFuncList());
                        } else {
                            utilMapper.insertPointTable(dataId, tpl.getCollectorId(), dataId, d.getName(), d.getAlias(), d.getType());
                        }
                        // 生成 d_device_item_data_map 数据
                        if ("write".equals(d.getFunc()) && d.getName().contains("更新")) {
                            utilMapper.updateDeviceItemDataMap(dataId - 1, dataId);
                        } else {
                            utilMapper.insertDeviceItemDataMap(dataId, deviceId, dataId, d.getOid(), d.getName(), d.getAlias(), null, d.getTag(), d.getNote2());
                        }
                        if ("1".equals(d.getIsPrimaryEnergy())) {
                            energyData = d;
                            energyData.setId(dataId);
                        }

                        // 生成 warning_rule 数据
                        List<ATplDeviceDataWarning> warningList = tplDeviceDataWarningService.getATplDeviceDataWarningListByDeviceDataId(d.getId());
                        if(null != warningList && warningList.size() > 0) {
                            int warnLen = String.valueOf(warningList.size()).length();
                            for (int k = 1; k <= warningList.size(); k++) {
                                Long wrId = dataId * (long)Math.pow(10, warnLen) + k;
                                ATplDeviceDataWarning w = warningList.get(k - 1);
                                utilMapper.insertWarningRule(wrId, w.getName(), w.getDescription(), d.getName(),
                                        w.getVal(), w.getCompare(), w.getSeverity(), w.getErrMsg(), w.getSolutionRef(),
                                        w.getTag(), w.getTimeSpan(), w.getFunc(), w.getMailTo(), w.getSmsTo(), w.getSmsTemplate(),
                                        w.getWarnStart(), w.getWarnEnd());
                                utilMapper.insertItemRuleMap(deviceId, wrId);
                            }
                        }
                    }

                    // 生成 e_device 数据
                    if (null != tpl.getIsEnergy() && tpl.getIsEnergy() == 1L) {
                        utilMapper.insertEDevice(deviceId, tpl.getEnergyType(), deviceName, tpl.getDescription(),
                                tpl.getBuildingId(), tpl.getRemark());
                        if (null != energyData) {
                            utilMapper.insertEDeviceItemDataMap(energyData.getId(), deviceId, energyData.getId(), energyData.getName());
                        }
                    }
                } catch (Exception ex) {
                    // 出现异常时手动回滚
                    status.setRollbackOnly();
                    throw ex; // 重新抛出异常
                }
                return null; // 返回类型为 Void
            }
        });
        return true;
    }

    // 根据协议导出设备数据模板
    @Override
    public List<DeviceDataTpl> genDeviceDataTpl(List<String> deviceIds) {
        List res = utilMapper.getDeviceDataTemplate(deviceIds);
        return res;
    }

    // 根据协议导出设备数据模板
    @Override
    public List<Map> getDeviceDataTemplateFull(List<String> deviceIds) {
        List res = utilMapper.getDeviceDataTemplateFull(deviceIds);
        return res;
    }

    // 导入设备数据
    @Override
    public Map importDeviceData(List<DeviceDataTpl> list) {
        Map res = new HashMap<>();
        List successItem = new ArrayList<>();
        List failedItem = new ArrayList<>();
        List successPointTable = new ArrayList<>();
        List failedPointTable = new ArrayList<>();
        res.put("successItem", successItem);
        res.put("failedItem", failedItem);
        res.put("successPointTable", successPointTable);
        res.put("failedPointTable", failedPointTable);

        if(null != list && list.size() > 0) {
            Map<String, List<DeviceDataTpl>> itemDataMap = list.stream()
                    .collect(Collectors.groupingBy(DeviceDataTpl::getItemId)); // 按 name 分组
            // 方法 1: 使用 forEach 遍历 Map
            itemDataMap.forEach((itemId, itemDataList) -> {
                if(null != itemDataList && itemDataList.size() > 0) {
                    // 更新 aItem.code
                    DeviceDataTpl fItemData = itemDataList.get(0);
                    try {
                        if(StringUtils.isNotEmpty(fItemData.getItemCode())) {
                            utilMapper.updateItemCode(itemId, fItemData.getItemCode());
                        }
                        successItem.add(itemId);
                    } catch (Exception ex) {
                        logger.warn("importDeviceData warn (update aItem.code). itemId: " + itemId);
                        Map f = new HashMap();
                        f.put("itemId", itemId);
                        f.put("error", ex.getCause() + " " + ex.getMessage());
                        failedItem.add(f);
                    }
                    // 更新 pointTable
                    itemDataList.forEach(itemData -> {
                        String itemDataId = itemData.getItemDataId();
                        try {
                            // 导入不允许修改 collectorId
                            utilMapper.updatePointTable(itemDataId, itemData.getCollectorId(), itemData.getPointTableAddr(), itemData.getPointTableDeviceId(),
                                    itemData.getPointTableLength(), itemData.getPointTableDataGroup(), itemData.getPointTableFunc(),
                                    itemData.getPointTableFuncList());
                            successPointTable.add(itemDataId);
                        } catch (Exception ex) {
                            logger.warn("importDeviceData warn (update pointTable). itemId: " + itemId + " itemDataId: " + itemDataId);
                            Map f = new HashMap();
                            f.put("itemDataId", itemDataId);
                            f.put("error", ex.getCause() + " " + ex.getMessage());
                            failedPointTable.add(f);
                        }
                    });
                }
            });
        }
        return res;
    }

    // 指定清理设备数据, 有多少删多少，不做异常回滚
    public List<Map> clearDeviceData(List<String> deviceIds, String range, String from, String to) {
        List<Map> res = new ArrayList<>();
        for(String id : deviceIds) {
            Map idRes = new HashMap<>();
            Long deviceId = Long.valueOf(id);
            if(range.contains("base")) {
                // 尝试先删除相关表记录内容
                idRes.put("aItem", utilMapper.deleteItem(deviceId));
                idRes.put("aPointTable", utilMapper.deletePointTableData(deviceId));
                idRes.put("aItemData", utilMapper.deleteItemData(deviceId));
                idRes.put("aWarningRule", utilMapper.deleteWarningRule(deviceId));
                idRes.put("aItemRuleMap", utilMapper.deleteItemRuleMap(deviceId));
                idRes.put("dDevice", utilMapper.deleteDevice(deviceId));
                idRes.put("dDeviceItemDataMap", utilMapper.deleteDeviceItemDataMap(deviceId));
                idRes.put("eDevice", utilMapper.deleteEDevice(deviceId));
                idRes.put("eDeviceItemDataMap", utilMapper.deleteEDeviceItemDataMap(deviceId));
            }
            // 清除设备资源关系
            if(range.contains("resourceMap")) {
                idRes.put("dDeviceResourceMap", utilMapper.deleteDeviceResourceMap(deviceId));
            }
            // 清除能耗设备分组关系
            if(range.contains("energyGroupMap")) {
                idRes.put("eDeviceGroupMap", utilMapper.deleteEDeviceGroupMap(deviceId));
            }
            // 清除历史记录
            if(range.contains("history")) {
                idRes.put("dDeviceDataHistory", utilMapper.deleteDeviceDataHistory(deviceId, from, to));
                idRes.put("eEnergyDataHistory", utilMapper.deleteEDeviceDataHistory(deviceId, from, to));
                idRes.put("eEnergyDataHistory", utilMapper.deleteEDeviceDataHistoryDaily(deviceId, from, to));
            }
            // 清除 log 日志
            if(range.contains("log")) {
                idRes.put("dDeviceOperLog", utilMapper.deleteDeviceOperLog(deviceId, from, to));
            }
            // 清除报警记录
            if(range.contains("warning")) {
                idRes.put("aItemWarning", utilMapper.deleteItemWarning(deviceId, from, to));
                idRes.put("eWarning", utilMapper.deleteEWarning(deviceId, from, to));
            }
            // 清除运维记录
            if(range.contains("maintenance")) {
                idRes.put("dDeviceMaintenance", utilMapper.deleteDeviceMaintenance(deviceId, from, to));
            }
            res.add(idRes);
        }
        return res;
    }

    // 更新数采配置，允许修改 collectorId
    public int updatePointTable(String itemDataId, String collectorId, String deviceId, String addr,
                                String length, String dataGroup, String func, String funcList) {
        if(StringUtils.isNotEmpty(itemDataId)) {
            // 可以连 collectorId 一起修改
            return utilMapper.updatePointTable2(itemDataId, collectorId, addr, deviceId, length, dataGroup, func, funcList);
        }
        return 0;
    }

    public int updateWriteVal() {
        return utilMapper.updateWriteVal();
    }
}
