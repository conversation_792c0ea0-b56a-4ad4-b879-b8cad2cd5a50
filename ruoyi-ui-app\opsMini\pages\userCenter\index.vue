<template>
	<view class="usercenter">
		<image class="bg" :src="`${api}/image/miniStatic/usercenter_bg.png`" mode="aspectFill"></image>
		<view class="main">
			<view class="infocard card">
				<view class="photo">
					<image :src="userInfo.avatar ? `${VUE_APP_BASE_API}${userInfo.avatar}` : `../../static/avatar.png`"
						mode="aspectFill"></image>
				</view>
				<view class="info">
					<view class="name">
						{{userInfo.nickName || userInfo.userName}}
						<view class="role" v-if="roleGroupName">
							{{roleGroupName}}
						</view>
					</view>
					<view class="companyname" v-if="!isVisitor">
						<view>{{userInfo.deptName || ' '}}</view>
						<image @click="goDetail" class="right" src="@/static/right.png" mode="aspectFit"></image>
					</view>
				</view>
			</view>
			<view class="list card">
				<view class="list_item" v-for="(item,index) in list" :key="index" @click="doMethod(item)">
					<view class="left">
						<image class="icon" :src="item.icon" mode="aspectFit"></image>
						<view class="">{{item.name}}</view>
					</view>

					<image class="right" src="@/static/right.png" mode="aspectFit"></image>
				</view>
			</view>
			<view class="logout" @click="logoutHandler">退出</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, computed, onBeforeMount } from 'vue'
	import { logout } from '@/api/loginApi';
	import { getUserProfile } from '@/api/userApi';
	import { getUserIdentity, visitorAuthentication } from "@/common/common.js"
	import { clearStorage } from '@/utils/tools.js'
	const VUE_APP_BASE_API = uni.$g.VUE_APP_BASE_API;
	const api = uni.$g.VUE_APP_BASE_API;
	const list = [
		// {
		// 	name:"修改手机号",
		// 	page:"",
		// 	icon:"../../static/usercenter_phone.png"
		// },
		{
			name: "修改密码",
			page: "/pages/userCenter/detail/passwordChange",
			icon: "../../static/psw_modify.png"
		},
		{
			name: "修改区域",
			page: "/pages/userCenter/detail/buildingChange",
			icon: "../../static/usercenter_local.png"
		}
	];
	const isVisitor = ref(false);
	const userInfo = uni.getStorageSync('user_info') || {};
	const roleGroupName = ref(); //角色名
	
	const logoutHandler = () => {
		uni.showModal({
			title: '提示',
			content: '确定退出应用吗？',
			success: function (res) {
				if (res.confirm) {
					logout().then(res => {
						clearStorage();
						console.log(res, 'res')
						uni.reLaunch({
							url: '/pages/login/index'
						});
					})

				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});

	}


	const getUserProfileHandler = () => {
		getUserProfile().then(res => {
			console.log(res, 'getUserProfile')
			const { postGroup, roleGroup } = res;
			roleGroupName.value = roleGroup;
			uni.setStorageSync('user_profile', {
				postGroup,
				roleGroup
			})
		})
	}

	const doMethod = (data) => {
		if(isVisitor.value){
			visitorAuthentication();
			return;
		}
		uni.navigateTo({
			url: data.page
		});
	}

	const goDetail = () => {
		uni.navigateTo({
			url: '/pages/userCenter/detail/index'
		});
	}

	onBeforeMount(() => {
		let identity = getUserIdentity();
		isVisitor.value = identity === 'visitor';
		console.log(isVisitor.value)
		if(identity !== 'visitor'){
			getUserProfileHandler();
		}
	})
</script>

<style lang="scss" scoped>
	.usercenter {
		background-position: 0 0;
		height: 100vh;
		position: relative;
		.bg {
			width: 100%;
		}
		.main {
			position: absolute;
			top: 110rpx;
			left:32rpx;
			width: calc(100% - 64rpx);

			.card {
				background: #fff;
				border-radius: 20rpx;
			}

			.infocard {
				display: flex;
				justify-content: flex-start;

				height: 200rpx;

				.photo {
					width: 154rpx;
					height: 154rpx;
					margin: -40rpx 36rpx 0;
					flex-shrink: 0;
					border-radius: 100%;
					padding: 4rpx;
					background: #fff;
					overflow: hidden;

					image {
						width: 100%;
						height: 100%;
						border-radius: 100%;
					}
				}

				.info {
					width: 100%;
					padding-top: 20rpx;

					.name {
						display: flex;
						align-items: center;
						font-size: 40rpx;
						color: #2D3033;
						font-weight: 500;

						.role {
							background: rgba(243, 129, 72, 0.2);
							border-radius: 6rpx;
							font-size: 24rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							padding: 0 8rpx;
							color: #F38148;
							margin-left: 10rpx;
							height: 40rpx;
						}
					}

					.companyname {
						display: flex;
						font-size: 28rpx;
						color: #5A6066;
						justify-content: space-between;
						align-items: center;
						margin-top: 8rpx;

						.right {
							width: 34rpx;
							height: 34rpx;
							margin-right: 34rpx;
						}
					}

				}

			}

			.list {
				margin-top: 32rpx;

				&_item {
					border-bottom: 2rpx solid rgba(217, 217, 217, 0.24);
					padding: 32rpx 0 32rpx 32rpx;

					.icon {
						width: 52rpx;
						height: 52rpx;
						margin-right: 16rpx;
					}

					font-weight: 400;
					font-size: 28rpx;
					color: #2D3033;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.left {
						display: flex;
						align-items: center;
					}

					.right {
						width: 34rpx;
						height: 34rpx;
						margin-right: 34rpx;
					}
				}
			}

			.logout {
				margin-top: 100rpx;
				background: #FFFFFF;
				border-radius: 64rpx;
				border: unset;
				height: 90rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 32rpx;
				color: #2D3033;
				font-weight: 400;
			}
		}

	}
</style>