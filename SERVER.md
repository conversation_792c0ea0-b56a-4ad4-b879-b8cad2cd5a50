# 登陆密码加密
本地部署秘钥对 512 pkcs8
https://uutool.cn/rsa-generate/ 在线生成
公钥:
MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAOq+mmTOVehSEfikgCrB3DU79//Vp7E6
DvZdW18FUHWncL1xKhlxi0JthPwCc01NCpP8QIWFYZSfd9rT155GbLMCAwEAAQ==
私钥:
MIIBVgIBADANBgkqhkiG9w0BAQEFAASCAUAwggE8AgEAAkEA6r6aZM5V6FIR+KSA
KsHcNTv3/9WnsToO9l1bXwVQdadwvXEqGXGLQm2E/AJzTU0Kk/xAhYVhlJ932tPX
nkZsswIDAQABAkEA34Ib/1927H04H0aBwjRTN1yiBTWpNVXSaUD6fr7QnTO9aAaO
S6uLoAUQRZ8DUNLqWXLh4Nm7u6VkcCf728UoAQIhAPu3/EWbV3OT0ODDu3n9J0Cj
pe4qBCDVRtDeqkCoqAOjAiEA7ry1yRx0DnHkH5TEtJZscDjykQeAnEtrbN60Tja7
A7ECIQCQOXSztTPAJxtxDPToCmJpKNqzwSMoZMr0df/6mCfcHQIhALdq9sNHgAbl
C54T3zjbZP6nUf2I5Q+vhh5D0AVTWUVRAiA7rBNL54hazTs1EAFcUnrnLqhoZs0S
cEBxwN7dfVMehA==


# 数据库密码加密
~~~
密钥对

privateKey:MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmqlo3AitWaFH3j02J960vey79bpVaJ6fc61KV7FiuAR3JtQoURHyS3xKd6yfxofDY0TcSN1fHvrWpaMlu8ahKwIDAQABAkATHE/KsH/LDXS171ZbwtcOiMwI5wdasA7/2i/Peim6lxF4Tmu1zELZHngf3QhmxekxolA786igZJ2cjIbOfnUBAiEAzLZCBDTwfaf1PF1CzCIQ7oVar55sEeU2aBbltg/qnEECIQDBaQzsp3sGlTJrijkCHorwMdSeZmpUHZe7NplrBgrSawIgTnF0oJoMoYnvvh7ajtBq/otZMbyaGs1jaMWFZpt4wkECIQCkFOopD0D46bQ3tbD9kknO0oeNxlcoe16RLhXjzOF7dwIgTT7N6hz7cdHsaH3SCa2tM4+40XBIE5et9Yq3xr8FXso=

publicKey:MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJqpaNwIrVmhR949NifetL3su/W6VWien3OtSlexYrgEdybUKFER8kt8Snesn8aHw2NE3EjdXx761qWjJbvGoSsCAwEAAQ==


pwd1:alOAyGqf4ARhF/WEItpn+924qZcrf+rnYjOvuayla9tUr9tRXbdPTEd6mFmLDQ8uvpPYbB9H5LKoy3x5aGLfKg==
pwd2:WAIJ7/nhSqMjgYMh/VJvYem8laBxIgtXqSW5NaxWXMVZ2yFNZyCP3Mn4H1iePwVx5EHFAs0298SSuC/HjfZ+Gw==


String pwd1 = "root123";
String pwd2 = "lanxingtk121";
~~~




1. 湖州长兴泗安农业银行 (neoenergy)
  hzabc.lanxing.tech
  admin / lanxing121!

  npm run mq7

  dir: ruoyi-ui-hzabc
  api path: /prod-api-hzabc/
  upload path: /root/www/bms/uploadPath
  db: lanxing -> ly_hz_abc, username: ly_hz_abc
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com  db: 10
  api: 8041 (neoenergy)
  debug: 9041


2. 湖州泰伦
  数据库，api在云端，数采在本地机器
  hztl.lanxing.tech
  admin / hztllx!121

  网关对接 mqtt:
    // 电表，车棚光伏，储能车充电桩
    // 测试：
      // host: "mqtt://**************",
      // username: "public",
      // password: "shz76hg%rsdq#ws",
      // topic: "lanxinghztl",
    // 正式：
      host: "mqtt://**************",
      username: "lanxing",
      password: "naudcno9kaqlli",
      topic: "lanxinghztl",

  ecs:
    npm run ctw1t
    npm run bc1t
    npn run bs1t
    npm run mq8tl
    npm run dbtl   -- 批量发布控制指令

  本地: 向日葵 724613496 / 600100 bms
  cd d:/soft
  运行 start.bat
  会执行如下命令:
    npm run r1tl  -- 线路温度
    npm run r2tl  -- 电表
    npm run r3tl  -- 空调读
    npm run w3tl  -- 空调写
    。。。

  dir: ruoyi-ui-hztl
  api path: /prod-api-hztl/
  upload path: /root/www/bms/uploadPath
  db: lanxing -> ly_hz_tl, username: ly_hz_tl
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com  db: 15
  api: 8042
  debug: 9042

 【浙江电力】最新可用网址  http://elec.zhny3060.com/#/login
  p13511223312-密码xhza@6785#a

  http://************:10866/login
  账号：13511221512
  密码：13511221512


2.1 湖州泰伦2 -- 作为测试
  复制泰伦的数据作为测试版
  hztl2.lanxing.tech
  admin / hztllx121
  dir: ruoyi-ui-hztl2
  api path: /prod-api-hztl2/
  upload path: /root/www/bms/uploadPath
  db: lanxing -> ly_hz_tl2, username: llproj
  redis: localhost  db: 15
  api: 8082


3. 嘉兴电力大楼
  jxdl.lanxing.tech
  admin / lanxing123

  dir: ruoyi-ui-jxdl
  api path: /prod-api-jxdl/
  upload path: /root/www/bms/uploadPath
  db: lanxing -> ly_jx_dl, username: ly_jx_dl  pwd: z0fpw64yWpig
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com  db: 8
  api: 8043
  debug: 9043

    "bc1t": "forever start -l /home/<USER>/www/bms/ruoyi-backendServer/node_modbus/logs/bc1t.txt -a src/bcSpider.js 1 lp_ry_tl",
    "bs1t": "forever start -l /home/<USER>/www/bms/ruoyi-backendServer/node_modbus/logs/bs1t.txt -a src/bcSyncData.js 1 lp_ry_tl",


4. 嘉善养老院
  全本地, db: bms2
  toDesk: 854225208 / 600100



5. 宁波能源电力大楼
  nbny.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-nbny
  api path: /prod-api-nbny/
  upload path: /root/www/bms/uploadPath
  db: lanxing -> ly_hz_nbny, username: ly_hz_nbny
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com  db: 5
  api: 8045
  debug: 9045

  rds: ly_hz_nbny / ke0422loadPa51nd

  向日葵: 
  厕所  
    云 ecs
    http 106.15.0.215:5003

  照明  智向

  智能插座 ctwing.cn 249464296
    设备管理 -> 分组 宁波综合能源--智能插座     设备名字 cz1 ~ cz97
    订阅服务 http://bms.lanxing.tech:5004/data
  六合一传感器
    设备管理 -> 分组 宁波综合能源--智能插座     设备名字 1 ~ 51
    订阅服务 http://bms.lanxing.tech:5004/data


6. 嘉善图书馆 (neoenergy)
  jstsg.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-jstsg
  api path: /prod-api-jstsg/
  db: lanxing -> ly_js_tsg, username: 
  api: 8046 (neoenergy)
  debug: 9046
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com  db: 6
  
7. 兴业银行 (neoenergy)
  xyyh.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-xyyh
  db: lanxing -> ly_xy_yh, username: 
  api: 8047 (neoenergy)
  debug: 9047
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com  db: 7


8. 余姚电力局
  yydl.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-yydl
  db: lanxing -> ly_yy_dl, username: 
  api: 8051
  debug: 9051
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 11
  1、vrv空调2、电表3、照明4、光伏接口


9. 余姚舜能电气 -> 改成 nzakd 宁综 宁波综合能源 爱科迪工厂
  yysn.lanxing.tech ->  nzakd.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-yysn   ->  ruoyi-ui-nzakd
  db: lanxing -> ly_yy_sn, username: ly_nb_akd
  api: 8052
  debug: 9052
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 12
  1、风冷热泵温控器2、nb消防灭火器、一个消防水管压力3、同方的ddc箱子，只有2个风机 -> XXXX

10. 上海能率
  shnl.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-shnl
  db: lanxing -> ly_sh_nl, username: 
  api: 8053
  debug: 9053
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 13
  1、各种表


11. 温州文成 (neoenergy)
  java 部署在 neoenergy 主机
  wzwc.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-wzwc
  db: lanxing -> ly_wz_wc, username: 
  api: 8054 (neoenergy)
  debug: 9054
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 14
  1、集成平台、空调控制、能源管理、智慧消防、食堂监控

11. 杭州江晖地铁 -- 临时 (neoenergy)
  java 部署在 neoenergy 主机
  hzjh.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-hzjh
  db: lanxing -> ly_hz_jh, username: 
  api: 8055 (neoenergy)
  debug: 9055
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 20
  1、水泵，风机，照明箱控制，消防，监控，广播，电梯，多联机

12. 奉化党校  (neoenergy)
  fhdx.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-fhdx
  db: lanxing -> ly_fh_dx, username: 
  api: 8056 (neoenergy)
  debug: 9056
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 16
  1、水表 电表 传感器

13. 金华横店影视城
  jhhd.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-jhhd
  db: lanxing -> ly_jh_hd, username: 
  api: 8057
  debug: 9057
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 17
  1、电表 (光伏，储能，充电桩 接入电表 配底图展示数据)


14. 宁波北仑电器
  nbbl.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-nbbl
  db: lanxing -> ly_nb_bl, username: 
  api: 8058
  debug: 9058
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 18
  1、空调

15. 宁波默勒电气
  nbml.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-nbml
  db: lanxing -> ly_nb_ml, username: 
  api: 8059
  debug: 9059
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 19
  1、空调

16. 上海国际会议中心
  -- lanxing 部署
    shgh.lanxing.tech
    admin / lanxing123
    dir: ruoyi-ui-shgh
    db: lanxing -> ly_sh_gh, username: 
    api: 8060
    debug: 9060
    redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 21
  -- 项目实际部署
    ssh: **************
    用户名: root
    密码: jienengxiehui@2023
    mqtt远端数据接入:
      host: **************:1883 / topic: ibms_shgh --> 电表
      host: **************:1883 / topic: ibms_shgh_light --> 照明 ????
  1、空调

17. 20个简单项目 青岛地铁站
  ibms.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-ibms
  db: lanxing -> ly_ibms, username: 
  api: 8061
  debug: 9061
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 22
  1、组态图
  ------------------------------
  端口映射文件 /etc/supervisor/conf.d/
  本地工控机登录  xintun_a / ecs7000m
  机器端口映射， 登录方式: ssh <EMAIL> -p60001
    1号机    lanxing云
    22       60001
    3316     60002
    8080     60003
    2号机    lanxing云
    22       60006
    3316     60007
    8080     60008
    3号机    lanxing云
    22       60011
    3316     60012
    8080     60013
    5号机    lanxing云
    22       60031
    3316     60032
    8080     60033



18. 北京高领
  bjgl.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-bjgl
  db: lanxing -> ly_bj_gl, username:
  api: 8065
  debug: 9060
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 25
  1、空调

19. 嘉兴学院 
  jxxy.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-jxxy
  db: lanxing -> ly_jx_xy, username:
  api: 8066
  debug: 9066
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 26
  

20. 金华新能源汽车小镇
  jhxz.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-jhxz
  db: lanxing -> ly_jh_xz, username:
  api: 8067
  debug: 9067
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 27
  1、


21. 嘉兴秀洲新塍
  jxxc.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-jxxc
  db: lanxing -> ly_jh_xc, username:
  api: 8068
  debug: 9068
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 28
  1、

22. 温州乐清
  wzlq.lanxing.tech
  admin / lanxing123
  dir: ruoyi-ui-wzlq
  db: lanxing -> ly_wz_lq, username:
  api: 8069
  debug: 9069
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 29
  1、

22. 浙江丽水
  zjls.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-zjls
  db: lanxing -> ly_zj_ls, username:
  api: 8070
  debug: 9070
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 30
  1、

23. 杭州汽轮动力股份有限公司
  hzql.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-hzql
  db: lanxing -> ly_hz_ql, username:
  api: 8071
  debug: 9071
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 31

24. 杭州中国计量研究院
  hzzhjl.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-hzzhjl
  db: lanxing -> ly_hz_zhjl, username:
  api: 8072
  debug: 9072
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 32

25. 常州领航
  czlh.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-czlh
  db: lanxing -> ly_cz_lh, username:
  api: 8073
  debug: 9073
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 33

26. 平湖通用电器  可关闭
  phty.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-phty
  db: lanxing -> ly_ph_ty, username:
  api: 8074
  debug: 9074
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 34

27. 宁波青年港项目
  nbqng.lanxing.tech
  admin / lanxing123!
  dir: ruoyi-ui-nbqng
  db: lanxing -> ly_nb_qng, username:
  api: 8075
  debug: 9075
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 35

28. 无锡海归雪浪小镇项目
  wxhg.lanxing.tech
  admin / lanxing123!
  dir: ruoyi-ui-wxhg
  db: lanxing -> ly_wx_hg, username:
  api: 8076
  debug: 9076
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 36

29. 景德镇市鱼山码头建设工程 -- 良港  接口:BA，灯控，监控，停车场，报警，门禁
  jdys.lanxing.tech
  admin / lanxing123!
  dir: ruoyi-ui-jdys
  db: lanxing -> ly_jd_ys, username:
  api: 8077
  debug: 9077
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 37

30. 合肥新站工人文化宫  接口:能耗
  hfxz.lanxing.tech
  admin / lanxing123!
  dir: ruoyi-ui-hfxz
  db: lanxing -> ly_hf_xz, username:
  api: 8078
  debug: 9078
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 38

31. 合肥电信大楼  接口:
  hfdx.lanxing.tech
  admin / lanxing123!
  dir: ruoyi-ui-hfdx
  db: lanxing -> ly_hf_dx, username:
  api: 8079
  debug: 9079
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 39

32. 景德镇市鱼山码头建设工程 -- 义城 接口:
  jdysyc.lanxing.tech
  admin / lanxing123!
  dir: ruoyi-ui-jdysyc
  db: lanxing -> ly_jd_ysyc, username:
  api: 8080
  debug: 9080
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 40

33. 安徽怀远文化馆科技馆二馆合一 接口:
  ahhy.lanxing.tech
  admin / lanxing123!
  dir: ruoyi-ui-ahhy
  db: lanxing -> ly_ah_hy, username:
  api: 8081
  debug: 9081
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 41

34. 源控测试项目 ykdemo:
  ykdemo.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-ykdemo
  db: lanxing -> ly_ykdemo, username:
  api: 8083
  debug: 9083
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 42

35. 浙江衢州文体中心
  qzwt.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-qzwt
  db: lanxing -> ly_qz_wt, username:
  api: 8084
  debug: 9084
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 44

36. 浙江玉环人民医院
  yhyy.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-yhyy
  db: lanxing -> ly_yh_yy, username:
  api: 8085
  debug: 9085
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 45

37. 湖北武汉市委党校
  whdx.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-whdx
  db: lanxing -> ly_wh_dx, username:
  api: 8086
  debug: 9086
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 46

38. demo平台
  demo.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-demo
  db: lanxing -> ly_demo, username:
  api: 8087
  debug: 9087
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 47

39 中国(南昌)中医药科创城研发孵化中心项目
  nczy.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-nczy
  db: lanxing -> ly_nc_zy, username:
  api: 8088
  debug: 9088
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 48

40. 杭州绿城总部大楼
  hzlc.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-hzlc
  db: lanxing -> ly_hz_lc, username:
  api: 8089
  debug: 9089
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 49

41. 安徽亳州涡阳人民医院
  ahbz.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-ahbz
  db: lanxing -> ly_ah_bz, username:
  api: 8090
  debug: 9090
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 50

42. 临时演示空调等设备控制
  view.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-view
  db: lanxing -> ly_tp_demo, username:
  api: 8091
  debug: 9091
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 51

43. 苏州长三角启动区（苏州）二期
  szcsj.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-szcsj
  db: lanxing -> ly_sz_csj, username:
  api: 8092
  debug: 9092
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 52

44. 中国银行湖州双林支行
  hzzy.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-hzzy
  db: lanxing -> ly_hz_zy, username:
  api: 8093
  debug: 9093
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 53

45. 宁海行政服务中心
  nhxz.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-nhxz
  db: lanxing -> ly_nh_xz, username:
  api: 8094
  debug: 9094
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 54


46. 宁波明月湖安置房
  nbmy.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-nbmy
  db: lanxing -> ly_nb_my, username:
  api: 8095
  debug: 9095
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 55


47. 测试项目--王凯
  ttwk.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-ttwk
  db: lanxing -> test_wangkai, username:
  api: 8096
  debug: 9096
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 56

48. 测试项目--卓春生
  ttzcs.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-ttzcs
  db: lanxing -> test_zhuochunsheng, username:
  api: 8097
  debug: 9097
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 57

49. 测试项目--岳帆
  ttyf.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-ttyf
  db: lanxing -> test_yuefan, username:
  api: 8098
  debug: 9098
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 58

50. 测试项目--何艳
  tthy.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-tthy
  db: lanxing -> test_heyan, username:
  api: 8098
  debug: 9098
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 58

51. 杭州蓝橙国际科创中心的云平台
  hzlk.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-hzlk
  db: lanxing -> ly_hz_lk, username:
  api: 8099
  debug: 9099
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 59

52. 重庆唐家桥低碳环保科创示范基地
  cqkc.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-cqkc
  db: lanxing -> ly_cq_kc, username:
  api: 8100
  debug: 9100
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 60

53. 常州中以国际创新村二期项目
  czgc.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-czgc
  db: lanxing -> ly_cz_gc, username:
  api: 8101
  debug: 9101
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 61

54. 金华浦江行政服务中心
  jhpx.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-jhpx
  db: lanxing -> ly_jh_px, username:
  api: 8102
  debug: 9102
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 62
  云端 tcp 透传转发服务, npm run tpspj,  9014(http)  9015(tcp)
  
55. 金华兰溪总部大楼能源管理项目的云平台
  jhlx.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-jhlx
  db: lanxing -> ly_jh_lx, username:
  api: 8103
  debug: 9103
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 63
  
56. 长三角(湖州)产业合作区天子湖项目
  hztzh.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-hztzh
  db: lanxing -> ly_hz_tzh, username:
  api: 8104
  debug: 9104
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 64

57. 丽水松阳中医院项目
  lsyy.lanxing.tech
  admin / lanxing121!
  dir: ruoyi-ui-lsyy
  db: lanxing -> ly_ls_yy, username:
  api: 8105
  debug: 9105
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 65


58 宁海象山行政服务中心   注意和 nhxz 区分  访问地址 https://nhxs2.lanxing.tech/
  db: lanxing -> ly_nh_xs, username:
  admin / lanxing121!
  dir: ruoyi-ui-nhxs
  db: lanxing -> ly_nh_xs, username:
  api: 8106
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 66


59 杭州绿城升华大厦项目
  db: lanxing -> ly_hz_sh, username:
  admin / lanxing121!
  dir: ruoyi-ui-hzsh
  db: lanxing -> ly_hz_sh, username:
  api: 8107
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 67

60. 金华婺城区人民医院
  db: lanxing -> ly_jh_ry, username:
  admin / lanxing121!
  dir: ruoyi-ui-jhry
  db: lanxing -> ly_jh_ry, username:
  api: 8108
  redis: r-uf6ovhvudcno9kaq1qpd.redis.rds.aliyuncs.com / hzabc / 25rR1qfcrxl db: 68

--------------------------

yukon redis 使用

项目后端所在位置: /home/<USER>/XXX 
项目前端所在位置: /home/<USER>/yukon/XXX 

  华为云
    华住 aiqk / ly_ai_qk / 6001 / 101 / https://aiqk.yuankong.org.cn/
    正大天晴 zdtq / ly_nj_zdtq / 6002 / 100 / https://zdtq.yuankong.org.cn/
    上海仁济医院    ly_sh_rj / shrj / 8074 / 108 / https://shrj.yuankong.org.cn/  
    越南比亚迪 B2厂房 ly_yn_bydb2 / ynbydb2 / 7076 / 110 / https://ynbydb2.yuankong.org.cn/
    越南比亚迪 B3厂房 ly_yn_bydb3 / ynbydb3 / 7077 / 111 / https://ynbydb3.yuankong.org.cn/
    苏州国际会议酒店 ly_sz_ghj / szjd  / 8041 / 119 / https://szjd.yuankong.org.cn/
  

  阿里云
    浙江绍兴二院    ly_sx_ey sxey / 7078 / 112 / https://sxey.yuankong.org.cn/
    上海现代院展厅    ly_sh_xd / shxd / 8042 / 109 / https://shxd.yuankong.org.cn/
    南京鹏为大厦 ly_nj_pw / njpw  / 8043 / 125 / https://njpw.yuankong.org.cn/
    嘉定海螺 ly_jx_hl / /www/ibms  / 8044 / 113 / https://hailuo.yuankong.org.cn/
    dev测试 ly_yk_view / dev  / 8046 / 114 / https://dev.yuankong.org.cn/
    泾县四馆 ly_jx_sg / jxsg  / 8051 / 118 / https://jxsg.yuankong.org.cn/
    青岛虚拟产业园 ly_qd_xn / qdxn  / 8054 / 121 / https://qdxn.yuankong.org.cn/
    源控标准版 ly_yk_lyy / nybz  / 8055 / 117 / https://nybz.yuankong.org.cn/
    上海奉贤公共卫生中心实验室  wssy / ly_ws_sy / wssy / 8056 / 105 / https://wssy.yuankong.org.cn/
    嘉善经济技术开发区枫惠学校 fhxx / ly_fh_xx / fhxx / 8057 / 104 / https://fhxx.yuankong.org.cn
    李惠利医院 ly_lhl_yy / lhlyy  / 8058 / 115 / https://dev.yuankong.org.cn/
    上海建科院    jyy / ly_sh_jyy / 8070 / 120 / https://jyy.yuankong.org.cn/
    西安健康3D物联平台展厅  xajk / ly_xa_jk / xajk / 8071 / 106 / https://xajk.yuankong.org.cn/
    长兴海洋馆 ly_sh_cxhy / cxhy  / 8072 / 116 / https://cxhy.yuankong.org.cn/
    一汽锻造青岛工厂建设项目 qdyq / ly_qd_yq / 8073 / 102 / https://qdyq.yuankong.org.cn/
    西安绿色生态双创中心   xasc / ly_xa_sc / 8074 /103 / https://xasc.yuankong.org.cn/
    天津大学服务平台智慧实验室功能 tjdx / ly_tj_dx / tjdx / 8075 / 107 / https://tjdx.yuankong.org.cn/
    上海市第六人民医院临港院区二期扩建工程弱电智能化工程 shly / ly_sh_ly /shly / 8076 / 123 / https://shly.yuankong.org.cn/
    重庆第四人民医院  cysy / ly_cy_sy / cysy / 8077 / 124 / https://cysy.yuankong.org.cn/
    鹰潭市余江区中医院 ytyy / ly_yt_yy / ytyy / 8078 / 127 / https://ytyy.yuankong.org.cn/
    源控测试架服务 yktest / ly_yk_test / yktest / 8079 / 126 / https://yktest.yuankong.org.cn/

--------------------------


余姚电力
10      照明 厂家云接入       ecs需要能访问 http://marsiot.beacool.com/
41      插座 天翼云           ecs 开通 5024
42      水表 厂家云接入       ecs 开通 5025
301     电表 4G透传          ecs 开通 8003 8002
302     空调 4G透传          ecs 开通 8003 8002

余姚舜能
41      灭火器 天翼云        ecs 开通 5030
301     空调 4G透传          ecs 开通 8003 8002
302     空调 4G透传          ecs 开通 8003 8002
303     风机 4G透传          ecs 开通 8003 8002

--------------------------

公共服务
data:    [22] DwKp /usr/bin/node src/tcpServerLister.js 8003 8002                 471127  471134    /root/www/bms/ruoyi-backendServer/node_modbus/logs/nbtcp.txt     14:16:43:7.675000000046566  

package.json
    "-----泰伦本地数采 start -----": "start",
    "-----线路温度 -----": "start",
    "r01tl": "forever start -l d:/soft/logs/r01tl.txt -a src/modbusRead.js 0 ly_hz_tl",
    "r02tl": "forever start -l d:/soft/logs/r02tl.txt -a src/modbusRead.js 1 ly_hz_tl",
    "-----电表 -----": "start",
    "r11tl": "forever start -l d:/soft/logs/r11tl.txt -a src/modbusRead.js 2 ly_hz_tl",
    "r12tl": "forever start -l d:/soft/logs/r12tl.txt -a src/modbusRead.js 3 ly_hz_tl",
    "r16tl": "forever start -l d:/soft/logs/r16tl.txt -a src/modbusRead.js 6 ly_hz_tl",
    "r17tl": "forever start -l d:/soft/logs/r17tl.txt -a src/modbusRead.js 7 ly_hz_tl",
    "r18tl": "forever start -l d:/soft/logs/r18tl.txt -a src/modbusRead.js 8 ly_hz_tl",
    "r19tl": "forever start -l d:/soft/logs/r19tl.txt -a src/modbusRead.js 9 ly_hz_tl",
    "r110tl": "forever start -l d:/soft/logs/r110tl.txt -a src/modbusRead.js 10 ly_hz_tl",
    "r111tl": "forever start -l d:/soft/logs/r111tl.txt -a src/modbusRead.js 11 ly_hz_tl",
    "-----空调 -----": "start",
    "r21tl": "forever start -l d:/soft/logs/r21tl.txt -a src/modbusRead2.js 4 ly_hz_tl",
    "r22tl": "forever start -l d:/soft/logs/r22tl.txt -a src/modbusRead2.js 5 ly_hz_tl",
    "w21tl": "forever start -l d:/soft/logs/w21tl.txt -a src/modbusWrite2.js 4 ly_hz_tl",
    "w22tl": "forever start -l d:/soft/logs/w22tl.txt -a src/modbusWrite2.js 5 ly_hz_tl",
    "r212tl": "forever start -l d:/soft/logs/r212tl.txt -a src/modbusRead2.js 12 ly_hz_tl",
    "w212tl": "forever start -l d:/soft/logs/w212tl.txt -a src/modbusWrite2.js 12 ly_hz_tl",
    "r213tl": "forever start -l d:/soft/logs/r213tl.txt -a src/modbusRead2.js 13 ly_hz_tl",
    "w213tl": "forever start -l d:/soft/logs/w213tl.txt -a src/modbusWrite2.js 13 ly_hz_tl",
    "r214tl": "forever start -l d:/soft/logs/r214tl.txt -a src/modbusRead2.js 14 ly_hz_tl",
    "w214tl": "forever start -l d:/soft/logs/w214tl.txt -a src/modbusWrite2.js 14 ly_hz_tl",
    "r215tl": "forever start -l d:/soft/logs/r215tl.txt -a src/modbusRead2.js 15 ly_hz_tl",
    "w215tl": "forever start -l d:/soft/logs/w215tl.txt -a src/modbusWrite2.js 15 ly_hz_tl",
    "r216tl": "forever start -l d:/soft/logs/r216tl.txt -a src/modbusRead2.js 16 ly_hz_tl",
    "w216tl": "forever start -l d:/soft/logs/w216tl.txt -a src/modbusWrite2.js 16 ly_hz_tl",
    "r217tl": "forever start -l d:/soft/logs/r217tl.txt -a src/modbusRead2.js 17 ly_hz_tl",
    "w217tl": "forever start -l d:/soft/logs/w217tl.txt -a src/modbusWrite2.js 17 ly_hz_tl",
    "-----变配电 -----": "start",
    "r601tl": "forever start -l d:/soft/logs/r601tl.txt -a src/modbusReadRtu.js 18 ly_hz_tl",
    "r602tl": "forever start -l d:/soft/logs/r602tl.txt -a src/modbusReadRtu.js 19 ly_hz_tl",
    "-----视频-----": "start",
    "c101tl": "forever start -l d:/soft/logs/c101tl.txt -a src/ipDeviceCheck.js 20 ly_hz_tl",
    "-----泰伦本地数采 end -----": "start",
    "-----泰伦云端数采 start -----": "start",
    "bc1t": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/bc1t.txt -a src/bcSpider.js 1 ly_hz_tl",
    "bs1t": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/bs1t.txt -a src/bcSyncData.js 1 ly_hz_tl",
    "ctw1t": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/ctw1t.txt -a src/httpServerListener.js 41 5002 ly_hz_tl",
    "ctw1t2": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/ctw1t2.txt -a src/httpServerListener.js 42 5014 ly_hz_tl debug",
    "mq8tl": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/mq8tl.txt -a src/mqttClient.js 8 ly_hz_tl",
    "mq19tl": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/mq19tl.txt -a src/mqttClient2.js 19 ly_hz_tl",
    "dbuptl": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/dbuptl.txt -a src/dbUpdate.js ly_hz_tl",
    "-----泰伦云端数采 end -----": "start",

    "-----宁波能源云端数采 start -----": "start",
    "bc1n": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/bc1n.txt -a src/bcSpider.js 1 ly_hz_nbny",
    "bs1n": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/bs1n.txt -a src/bcSyncData.js 1 ly_hz_nbny",
    "ctw1n": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/ctw1n.txt -a src/httpServerListener.js 41 5004 ly_hz_nbny",
    "ctw1wc": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/ctw1wc.txt -a src/httpServerListener.js 42 5003 ly_hz_nbny",
    "-----宁波能源云端数采 end -----": "start",

    "-----嘉兴电力局本地数采 start -----": "start",
    "__ibsMysql": "node src/tfMysqlEms4Sync.js 19",
    "s0": "forever start -l c:/soft/logs/sydb0.txt -a src/tfMysqlEms4Sync.js 0",
    "s1": "forever start -l c:/soft/logs/sydb1.txt -a src/tfMysqlEms4Sync.js 1",
    "s2": "forever start -l c:/soft/logs/sydb2.txt -a src/tfMysqlEms4Sync.js 2",
    "s3": "forever start -l c:/soft/logs/sydb3.txt -a src/tfMysqlEms4Sync.js 3",
    "s4": "forever start -l c:/soft/logs/sydb4.txt -a src/tfMysqlEms4Sync.js 4",
    "s5": "forever start -l c:/soft/logs/sydb5.txt -a src/tfMysqlEms4Sync.js 5",
    "mr": "forever start -l c:/soft/logs/mr.txt -a src/modbusReadRtu.js 6 ly_jx_dl",
    "mw": "forever start -l c:/soft/logs/mw.txt -a src/modbusWriteRtu.js 7 ly_jx_dl",
    "-----嘉兴电力局本地数采 end -----": "start",
    "-----嘉兴电力局云端数采 start -----": "start",
    "nbc0jx": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/nbc0jx.txt -a src/tcpCollector.js 0 ly_jx_dl",
    "mq8jx": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/mq8tl.txt -a src/mqttClient.js 8 ly_jx_dl",
    "ctw0jx": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/ctw0jx.txt -a src/httpServerListener.js 40 8001 ly_jx_dl",
    "ctw1jx": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/ctw1jx.txt -a src/httpServerListener.js 41 5013 ly_jx_dl",
    "ctw2jx": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/ctw1jx.txt -a src/httpServerListener.js 42 5012 ly_jx_dl",
    "ctw3jx": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/ctw3jx.txt -a src/httpServerListener.js 43 5031 ly_jx_dl",
    "bc1jx": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/bc1jx.txt -a src/bcSpider.js 1 ly_jx_dl",
    "bs1jx": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/bs1jx.txt -a src/bcSyncData.js 1 ly_jx_dl",
    "dbupjxdl": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/dbupjxdl.txt -a src/dbUpdate.js ly_jx_dl",
    "-----嘉兴电力局云端数采 end -----": "start",

    "-----嘉善图书馆云端数采 start -----": "start",
    "mq7jt": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/mq7jt.txt -a src/mqttClient.js 7 ly_js_tsg",
    "mq9jt": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/mq9jt.txt -a src/mqttClient.js 9 ly_js_tsg",
    "-----嘉善图书馆云端数采 start -----": "end",

    "-----兴业银行云端数采 start -----": "start",
    "mq7xy": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/mq7xy.txt -a src/mqttClient.js 7 ly_xy_yh",
    "nbc6xy": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/nbc6xy.txt -a src/tcpCollector.js 6 ly_xy_yh",
    "-----兴业银行云端数采 start -----": "end",

    "湖州长兴泗安农业银行云端数采 start -----": "start",
    "mq7": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/mq7.txt -a src/mqttClient.js 7 ly_hz_abc",
    "湖州长兴泗安农业银行云端数采 start -----": "end",

    "-----余姚电力云端数采 start -----": "start",
    "bc1yydl": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/bc1yydl.txt -a src/bcSpider.js 1 ly_yy_dl",
    "bs1yydl": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/bs1yydl.txt -a src/bcSyncData.js 1 ly_yy_dl",
    "ctw1yydl": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/ctw1yydl.txt -a src/httpServerListener.js 41 5024 ly_yy_dl",
    "tc20yydl": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/tc20yydl.txt -a src/tcpCollector.js 21 ly_yy_dl",
    "tc22yydl": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/tc22yydl.txt -a src/tcpCollectorModbusBase.js 22 ly_yy_dl",
    "tc23yydl": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/tc23yydl.txt -a src/tcpCollectorModbusBase.js 23 ly_yy_dl",
    "tc23wyydl": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/tc23wyydl.txt -a src/tcpCollectorModbusBaseWrite.js 23 ly_yy_dl",
    "ctw2yydl": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/ctw2yydl.txt -a src/httpServerListener.js 42 5025 ly_yy_dl",
    "dbupyydl": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/dbupyydl.txt -a src/dbUpdate.js ly_yy_dl",
    "-----余姚电力云端数采 end -----": "end",

    "-----余姚舜能云端数采 start -----": "start",
    "ctw1yysn": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/ctw1yysn.txt -a src/httpServerListener.js 41 5030 ly_yy_sn",
    "tc25yysn": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/tc25yysn.txt -a src/tcpCollectorModbusBase.js 25 ly_yy_sn",
    "tc26yysn": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/tc26yysn.txt -a src/tcpCollectorModbusBase.js 26 ly_yy_sn",
    "tc25wyysn": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/tc25wyysn.txt -a src/tcpCollectorModbusBaseWrite.js 25 ly_yy_sn",
    "tc26wyysn": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/tc26wyysn.txt -a src/tcpCollectorModbusBaseWrite.js 26 ly_yy_sn",
    "dbupyysn": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/dbupyysn.txt -a src/dbUpdate.js ly_yy_sn",
    "-----余姚舜能云端数采 end -----": "end",

    "-----上海能率云端数采 start -----": "start",
    "ctw1nl": "forever start -l /root/www/bms/ruoyi-backendServer/node_modbus/logs/ctw1nl.txt -a src/httpServerListener.js 41 5040 ly_jx_dl",
    "-----上海能率云端数采 end -----": "end",

    "-----杭州江晖云端数采 start -----": "start",
    "-----杭州江晖云端数采 end -----": "end",





