<template>
    <div class="energy-summary new-system-container data-analysis">
        <div class="main-card">
            <h3>
                <div class="head">
                    <span>用能分析</span>
                    <el-button type="warning" icon="el-icon-download" size="mini" plain
                            @click="handleExport">导出数据</el-button>
                </div>
                <el-divider></el-divider>
            </h3>
        </div>
        <el-tabs v-model="energyType" @tab-click="energyTypeChange">
            <el-tab-pane v-for="item in energyTypes" :key="item.type" :label="item.typeName" :name="item.type" />
        </el-tabs>
        <el-row class="filters">
            <el-col :md="8" class="filters_left">
                <el-form ref="queryForm" :inline="true" label-width="130">
                    <el-form-item label="数据分组选择:">
                        <el-select v-model="queryParams.groupIds" placeholder="请选择分组" size="mini" multiple
                            collapse-tags>
                            <el-option v-for="item in groupList" :key="item.id" :label="item.groupName"
                                :value="item.id"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleAdd()">新增分组</el-button>
                    <el-button type="success" icon="el-icon-edit" size="mini" plain
                        @click="handleUpdate">编辑分组</el-button>
                </el-form>
            </el-col>
            <el-col :md="16" class="filters_right">
                <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="130">
                     <el-form-item prop="showType">
                        <el-select v-model="queryParams.showType" placeholder="请选择" size="mini" style="width: 100px">
                            <el-option v-for="item in dataShowTypes" :key="item.code" :label="item.label"
                                :value="item.code"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="dateArea">
                        <el-date-picker v-model="queryParams.dateArea" :type="getDatePickerType('type')"
                            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="mini"
                            style="width: 240px" unlink-panels :format="getDatePickerType('format')"
                            value-format="yyyy-MM-dd">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item prop="compareYear" label="同比:" v-if="queryParams.showType !== 'year'">
                        <el-date-picker v-model="queryParams.compareYear" type="year" placeholder="选择年" size="mini"
                            style="width: 100px" value-format="yyyy">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item class="mr0">
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                    </el-form-item>
                </el-form>
            </el-col>
        </el-row>
        <CustCard title="数据展示" autoHeight>
            <BaseChart ref="baseChart" :optionData="chartOption" :height="`280px`" v-loading="loading" />
            <el-table v-loading="loading" id="dataTable" ref="tableRef" v-if="tableData && tableData.length > 0"
                :data="tableData" style="width: 100%;margin-top: 10px;" height="320px" stripe>
                <el-table-column v-for="item in columns" :key="item.code" :prop="item.code" :label="item.name"
                    :width="item.width" :min-width="item.minWidth"></el-table-column>
            </el-table>
        </CustCard>
        <!-- 功能 -->
        <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body class="common-dialog"
            :before-close="() => open = false">
            <el-form ref="form" :model="form" :rules="rules" label-width="120px">
                <el-form-item label="设备分组名称：" prop="groupName">
                    <el-input v-model="form.groupName" type="text" placeholder="请输入分组名称" />
                </el-form-item>
                <el-form-item label="设备选择：" prop="deviceList">
                    <div class="device_list">
                        <el-tag class="mr10" v-for="item in form.deviceList" :key="item.id" closable type="success"
                            @close="handleClose(item)">
                            {{ item.name }}
                        </el-tag>
                    </div>
                    <el-button class="mt10" type="primary" size="mini" @click="showTransferSelect">{{
                        `${form.deviceList.length > 0 ? '修改' :
                            '前往'}` }}选择</el-button>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="() => open = false">取 消</el-button>
                <el-button type="primary" @click="submitForm">确 定</el-button>
            </div>
        </el-dialog>

        <el-dialog :visible.sync="groupListModify" width="1000px" class="common-dialog"
            :before-close="() => groupListModify = false">
            <div slot="title" class="dialog_header">
                {{ groupListModifyTitle }}
                <el-button size="mini" type="text" icon="el-icon-plus" @click="handleAdd()">新增</el-button>
            </div>
            <el-table :data="groupList" style="width: 100%">
                <el-table-column prop="groupName" label="分组名称" width="180">
                </el-table-column>
                <el-table-column prop="deviceList" label="设备" show-overflow-tooltip>
                    <template slot-scope="scope">
                        <span v-for="(item, index) in scope.row.deviceList" :key="item.id">
                            {{ item.name }}{{ index < scope.row.deviceList.length - 1 ? '、' : '' }} </span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" class-name="small-padding" width="140">
                    <template slot-scope="scope">
                        <el-button size="mini" type="text" icon="el-icon-edit"
                            @click="handleAdd(scope.row)">修改</el-button>
                        <el-button size="mini" type="text" icon="el-icon-delete"
                            @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>

        <DeviceTransferSelect v-if="transferFlag" :flag="transferFlag" :deviceType="energyType"
            :device-list-insert="fullDeviceList" :device-selected-insert="form.deviceList"
            @close-dialog="handleTransferClose" @update-dialog="handleTransferUpdate" />
    </div>
</template>

<script>
import { listData, updateData, addData } from "@/api/system/dict/data"
import DeviceTransferSelect from "@/views/device/monitor/components/Device/DeviceTransferSelect"
import { buildingDeviceList } from "@/api/energy/apis";
import { nanoid } from 'nanoid';
import CustCard from '@/views/components/cO2View/components/common/screen/components/custCard.vue'
import BaseChart from "@/views/ScreenCommBase/components/common/BaseChart.vue";
import { buildingEnergyDeviceSummaryList } from "@/api/energy/apis";
export default {
    components: {
        DeviceTransferSelect,
        CustCard,
        BaseChart
    },
    computed: {
        getGroupDictName() {
            return `dataAnalysis_groups_${this.energyType}`
        },
    },
    data() {
        return {
            loading: false,
            title: "数据分析",
            queryParams: {
                groupIds: [],
                showType: "day",
                compareYear: this.$moment().add(-1, "year").format("YYYY"),
                dateArea: [this.$moment().add(-1, "months").startOf("month").format("YYYY-MM-DD"), this.$moment().add(-1, "months").endOf("month").format("YYYY-MM-DD")]
            },
            energyType: "electricity",
            buildingId: this.gf.getBuildingId(),
            dataShowTypes: [
                {
                    code: "day",
                    type: "daterange",
                    label: "日",
                    format: "yyyy-MM-dd",
                    momentFormat: "YYYY-MM-DD"
                },
                {
                    code: "month",
                    type: "monthrange",
                    label: "月",
                    format: "yyyy-MM",
                    momentFormat: "YYYY-MM"
                },
                {
                    code: "year",
                    type: "monthrange",
                    label: "年",
                    format: "yyyy",
                    momentFormat: "YYYY"
                }
            ],
            energyTypes: [],
            currentTypeData: {},
            groupList: [],
            open: false,
            title: "设备分组新增",
            rules: {
                groupName: [{ required: true, message: '请输入名分组称', trigger: 'blur' }, { validator: this.validateGroupName, message: '命名重复，请修改', trigger: 'blur' }],
                deviceList: [{ required: true, message: '请选择设备', trigger: 'blur', validator: this.checkDeviceIds }]
            },
            form: {
                id: "",
                groupName: "",
                deviceList: [],
            },
            transferFlag: false,
            fullDeviceList: [],
            tabsActive: "",
            curBuilding: this.gf.getCurBuilding(),
            groupListModify: false,
            groupListModifyTitle: "设备分组查看",
            chartOption: {},
            defaultOptions: {
                "grid": {
                    "left": 10,
                    "top": "50",
                    "bottom": "10",
                    containLabel: true
                },
                "color": [
                    "#88CAF3",
                    "#FCD869",
                    "#2294FE",
                    "#E45757",
                    "#E45757",
                    "#37D9B2",
                    "#9380F7",
                    "#3CCCF9",
                    "#FF7D00",
                    "#165DFF",
                    "#D91AD9",
                    "#14C9C9",
                    "#722ED1"
                ],
                "legend": {
                    "left": "right",
                    "top": "top",
                    "itemHeight": 8,
                    "data": []
                },
                "xAxis": {
                    "data": [],
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: "#3489BF",
                            width: 1
                        }
                    }
                },
                "yAxis": [
                    {
                        "nameGap": 15,
                        "splitLine": {
                            "lineStyle": {
                                "color": "rgba(0, 83, 209, .4)",
                                "width": .5
                            }
                        },
                        name: '{a|单位：}{b|kWh}',
                        nameGap: 20,
                        nameTextStyle: {
                            rich: {
                                a: {
                                    color: '#fff',
                                    lineHeight: 10,
                                    fontSize: 14
                                },
                                b: {
                                    color: '#3CCCF9',
                                    lineHeight: 10,
                                    fontSize: 14
                                }
                            }
                        },
                    }
                ],
                "series": [],
                "dataZoom": [
                    {
                        "type": "inside",
                        "show": false
                    }
                ],
                "tooltip": {
                    "trigger": "axis",
                    confine: true
                }
            },
            columns: [],
            tableData: []
        };
    },
    methods: {
        async getEnergyTypes() {
            await this.gf.getBuildingEnergyTypes(this.buildingId).then(list => {
                this.energyTypes = [...(list || [])];
                this.getCurrentTypeData();
            })
        },
        getCurrentTypeData() {
            const dt = this.energyTypes.find(item => item.type === this.energyType)
            this.currentTypeData = { ...dt }
        },
        async getGroups() {
            await listData({
                dictType: `building_config_${this.buildingId}`,
                dictLabel: this.getGroupDictName,
            }).then(({ rows = [] }) => {
                let data = rows[0] || {};
                this.dictConfig = data;
                try {
                    this.groupList = JSON.parse(data.dictValue)
                } catch (error) {
                    this.groupList = [];
                }
                console.log(this.groupList, 'this.groupList')
            })
        },
        handleExport() {
            this.et('dataTable', "数据分析");
        },
        handleUpdate() {
            this.groupListModify = true;
        },
        handleAdd(data) {
            this.form = {
                id: "",
                groupName: "",
                deviceList: [],
            };
            if (data) {
                this.form = { ...data };
            }
            this.open = true;
        },
        handleDelete(data) {
            let that = this;
            this.$confirm('是否确认删除分组名称为"' + data.groupName + '"的数据项？').then(function () {
                that.groupList = that.groupList.filter(item => item.id !== data.id);
                that.queryParams.groupIds = that.queryParams.groupIds.filter(item => item !== data.id);
                that.updateDict(that.groupList);
            }).catch(() => { });
        },
        handleQuery() {
            this.initChartData();
        },
        resetQuery() {
            this.queryParams = {
                groupIds: [],
                showType: "day",
                compareYear: this.$moment().add(-1, "year").format("YYYY"),
                dateArea: [this.$moment().add(-1, "months").startOf("month").format("YYYY-MM-DD"), this.$moment().add(-1, "months").endOf("month").format("YYYY-MM-DD")]
            }
            this.getCurrentTypeData();
            this.defaultEchartSet();
            this.resetTableData();
        },
        showTransferSelect() {
            this.transferFlag = true
        },
        handleTransferClose() {
            this.transferFlag = false
        },
        handleTransferUpdate(ids) {
            this.handleTransferClose();
            const list = this.fullDeviceList.filter(item => ids.indexOf(item.id) > -1);
            this.form.deviceList = list.map(item => {
                return {
                    id: item.id,
                    name: item.name
                }
            })
            this.$refs["form"].validateField('deviceList');
        },
        submitForm() {
            const groupList = this.groupList;
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    const data = { ...this.form }
                    if (data.id) {
                        let idx = groupList.findIndex(item => item.id === this.form.id);
                        groupList[idx] = { ...data };
                    } else {
                        data.id = nanoid(10);
                        groupList.push(data)
                    }
                    this.updateDict(groupList);
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        },
        async updateDict(list) {
            if (!this.dictConfig.dictCode) {
                await addData({
                    dictSort: 0,
                    dictType: `building_config_${this.buildingId}`,
                    dictLabel: this.getGroupDictName,
                    dictValue: JSON.stringify([]),
                    status: "0",
                }).then(async res => {
                    await this.getGroups();
                })
            }
            const updatedConfig = {
                ...this.dictConfig,
                dictValue: JSON.stringify(list),
            };
            updateData(updatedConfig).then(res => {
                this.$message({
                    message: "分组已更新",
                    type: "success",
                });
                this.getGroups();
                this.open = false;
            })
        },
        getDevicdeFullList() {
            buildingDeviceList({ buildingId: this.curBuilding.id, deviceType: this.energyType }).then(({ data }) => {
                this.fullDeviceList = data
            })
        },
        handleClose(tag) {
            this.form.deviceList = this.form.deviceList.filter(item => item.id != tag.id);
            this.$refs["form"] && this.$refs["form"].validateField('deviceList');
        },
        checkDeviceIds(rule, value, callback) {
            const { message } = rule;
            if (this.form.deviceList && this.form.deviceList.length > 0) {
                callback();
            } else {
                callback(new Error(message));
            }
        },
        validateGroupName(rule, value, callback) {
            const { message } = rule;
            const list = this.groupList;
            const it = list.find(item => item.groupName === value);
            if (!it || this.form.id) {
                callback();
            } else {
                callback(new Error(message));
            }
        },
        getDatePickerType(key) {
            const it = this.dataShowTypes.find(item => item.code === this.queryParams.showType)
            return it[key]
        },
        initChartData() {
            let from, to, compareFrom, compareTo;
            if (!this.queryParams.dateArea || this.queryParams.dateArea.length < 2 || !this.queryParams.groupIds || this.queryParams.groupIds.length < 1) {
                this.resetQuery();
                return;
            }
            const format = this.getDatePickerType("momentFormat");
            from = this.$moment(this.queryParams.dateArea[0]).format(format);
            to = this.$moment(this.queryParams.dateArea[1]).format(format);
            compareFrom = this.queryParams.compareYear + from.substring(4);
            compareTo = this.queryParams.compareYear + to.substring(4);
            //补充时间格式
            if (this.queryParams.showType !== "day") {
                from = this.$moment(from).startOf(this.queryParams.showType).format("YYYY-MM-DD");
                compareFrom = this.$moment(compareFrom).startOf(this.queryParams.showType).format("YYYY-MM-DD");
                to = this.$moment(to).endOf(this.queryParams.showType).format("YYYY-MM-DD");
                compareTo = this.$moment(compareTo).endOf(this.queryParams.showType).format("YYYY-MM-DD");
            }
            const promiseList = [];
            this.queryParams.groupIds.forEach(item => {
                const groupData = (this.groupList.find(it => it.id === item) || {});
                //当期
                const promiseIt1 = buildingEnergyDeviceSummaryList({
                    buildingId: this.buildingId,
                    from,
                    to,
                    energyType: this.energyType,
                    displayType: this.queryParams.showType,
                    deviceIds: groupData.deviceList.map(it => it.id).join(","),
                }).then(res => {
                    return {
                        data: res.data,
                        groupName: groupData.groupName,
                        type: "bar"
                    }
                });
                promiseList.push(promiseIt1)
                //同比
                if (this.queryParams.showType !== 'year') {
                    const promiseIt2 = buildingEnergyDeviceSummaryList({
                        buildingId: this.buildingId,
                        from: compareFrom,
                        to: compareTo,
                        energyType: this.energyType,
                        displayType: this.queryParams.showType,
                        deviceIds: groupData.deviceList.map(it => it.id).join(","),
                    }).then(res => {
                        return {
                            data: res.data,
                            groupName: groupData.groupName + "同比",
                            type: "line"
                        }
                    });
                    promiseList.push(promiseIt2)
                }
            });
            this.loading = true;
            Promise.all(promiseList).then(resList => {
                this.setOptions(resList);
                this.loading = false;
            });
        },
        setxAxisData() {
            const xAxisData = [];
            const from = this.$moment(this.queryParams.dateArea[0]);
            const to = this.$moment(this.queryParams.dateArea[1]);
            let currentDate = from;
            let maxNum = 0;
            const format = this.getDatePickerType("momentFormat");
            const code = this.getDatePickerType("code");
            while (currentDate.isSameOrBefore(to) && maxNum < 9999) { // 包含结束日期
                xAxisData.push(currentDate.format(format)); // 格式化为字符串
                currentDate.add(1, code); // 逐日增加
                maxNum++;
            }
            this.chartOption.xAxis.data = xAxisData;
        },
        defaultEchartSet() {
            this.setxAxisData();
            const xAxisData = this.chartOption.xAxis.data;
            let list = [];
            const series = [];
            xAxisData.forEach(i => {
                list.push(0);
            })
            let seriesItem = {
                "name": "",
                type: "bar",
                "barMaxWidth": 20,
                "data": [...list]
            }
            series.push(seriesItem);
            this.chartOption.series = series;
        },
        setOptions(resList) {
            const series = [];
            const legendData = [];
            this.setxAxisData();
            const xAxisData = this.chartOption.xAxis.data;
            //每个时间节点可能会有多条代表数据  求和
            resList.forEach(item => {
                const { data, groupName, type } = item;
                const list = [];
                xAxisData.forEach(i => {
                    let sum = 0;
                    data.forEach(i2 => {
                        if (i2.recordedAt === i) {
                            sum += i2.totalVal
                        }
                    })
                    list.push(sum);
                })
                let seriesItem = {
                    "name": groupName,
                    type,
                    "barMaxWidth": 20,
                    "data": [...list]
                }
                series.push(seriesItem);
                legendData.push(groupName)
            })

            this.chartOption.series = series;
            this.chartOption.legend.data = legendData;
            let that = this;
            this.chartOption.tooltip.formatter = function (para) {
                let params = []
                if (Array.isArray(para)) {
                    params = [...para]
                } else {
                    params.push(para)
                }
                // params 是一个数组，数组中包含每个系列的数据信息
                const timeName = params[0] ? params[0].name : ''; // 系列名称
                let result = `<div style="margin-bottom: 10px;">${timeName}</div>`;
                params.forEach(function (item) {
                    // item 是每一个系列的数据
                    let seriesName = item.seriesName; // 系列名称
                    const value = parseFloat(item.value).toFixed(2); // 数据值
                    const marker = item.marker; // 标志图形
                    if (seriesName.indexOf('同比') < 0) {
                        seriesName = seriesName + "用能"
                    }
                    result += `${marker}<span style="width: 70px;display:inline-block;">${seriesName}</span><span style="margin-left: 10px;">${value} ${that.currentTypeData.typeUnit}</span><br/>`;
                });
                return result
            }
            this.setTableData(xAxisData, series, legendData);
        },
        setTableData(xAxisData, series, legendData) {
            console.log(xAxisData, series, legendData, 'setTableData');
            const tableData = [];
            const columns = [{
                name: "日期",
                code: "recordedAt",
                width: "120px"
            }];
            legendData.forEach((item, index) => {
                columns.push({
                    name: item.indexOf("同比") > -1 ? `${item}用${this.currentTypeData.typeName}(${this.currentTypeData.typeUnit})` : `${item}本期用${this.currentTypeData.typeName}(${this.currentTypeData.typeUnit})`,
                    code: `groupIdx_${index}`,
                    minWidth: "180px"
                })
                if (item.indexOf("同比") > -1) {
                    columns.push({
                        name: `${item}(${this.queryParams.compareYear})`,
                        code: `groupIdx_${index}_p`,
                        minWidth: "140px"
                    })
                }
            })
            //tabledata
            xAxisData.forEach((item2, index2) => {
                let dt = {
                    recordedAt: item2
                };
                legendData.forEach((item, index) => {
                    const it = series.find(item2 => item2.name === item);
                    dt[`groupIdx_${index}`] = it.data[index2].toFixed(2);
                    if (item.indexOf("同比") > -1) {
                        dt[`groupIdx_${index}_p`] = parseFloat(dt[`groupIdx_${index}`])  === 0 ? '--' : ((parseFloat(dt[`groupIdx_${index -1}`]) / parseFloat(dt[`groupIdx_${index}`])) * 100).toFixed(2) + "%"
                    }
                })
                tableData.push(dt)
            })
            this.columns = [...columns];
            this.tableData = [...tableData];
            this.$nextTick(() => {
                this.$refs.tableRef.doLayout(); // 确保表格重新布局
            });
        },
        resetTableData() {
            this.columns = [];
            this.tableData = [];
        },
        energyTypeChange(val) {
            const type = (this.energyTypes[val.index] || {}).type;
            this.resetQuery();
            this.queryParams = {
                energyType: type,
                groupIds: [],
                showType: "day",
                compareYear: this.$moment().add(-1, "year").format("YYYY"),
                dateArea: [this.$moment().add(-1, "months").startOf("month").format("YYYY-MM-DD"), this.$moment().add(-1, "months").endOf("month").format("YYYY-MM-DD")]
            }
            this.getGroups();
            this.getDevicdeFullList();



        }
    },
    async created() {
        this.chartOption = { ...this.defaultOptions };
        await this.getEnergyTypes();
        this.getGroups();
        this.getDevicdeFullList();
        this.defaultEchartSet();
    }
};
</script>

<style scoped lang='scss'>
.data-analysis {
    padding: 10px 20px;
    .head {
        display: flex;
        justify-content: space-between;
    }
    .filters {
        &.el-row {
            margin: 10px 0;

            .el-col {
                margin: 0;

                .el-form-item {
                    margin-bottom: 0;
                }
            }
        }

        .filters_left {
            .el-form {
                display: flex;
                align-items: center;
            }
        }

        .filters_right {
            line-height: 36px;
            .el-form {
                display: flex;
                align-items: center;
                justify-content: flex-end;
            }
        }
    }

    .dialog_header {
        width: calc(100% - 30px);
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 20px;
        line-height: 20px;
    }
}

.device_list {
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
}

</style>