<template>
  <div class="shift_summary">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
      <el-form-item label="值班人" prop="userId">
        <el-select v-model="queryParams.userId" placeholder="请选择值班人" clearable size="small" :multiple="true" collapse-tags>
          <el-option v-for="dict in userList" :key="dict.userId" :label="dict.nickName" :value="dict.userId" />
        </el-select>
      </el-form-item>
      <el-form-item label="日期" prop="date">
        <el-date-picker :clearable="false" size="small" style="width: 200px" v-model="queryParams.date" type="month"
          value-format="yyyy-MM" placeholder="请选择年月">
        </el-date-picker>
      </el-form-item>
      <el-form-item class="ml20">
        <el-button type="cyan" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button
          plain
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['inspection:piShiftSchedule:export']"
        >导出</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="tableData" max-height="400px" id="scheduleTable">
      <el-table-column label="值班人" align="center" prop="userId" :formatter="userIdFormat" />
      <el-table-column label="班次合计" align="center" prop="shiftSum" :formatter="shiftSumFormat" />
      <el-table-column label="排班总时长" align="center" prop="totalWorkTime" />
    </el-table>
    <!-- 排班表 -->
    <ScheduleView :shiftTypeOptions="shiftTypeOptions" :date="scheduleViewProps.date" :userList="userList"
      :shiftList="shiftList" :userId="scheduleViewProps.userId" :shiftNickName="shiftNickName" @showDialog="showDialog"></ScheduleView>
    <shiftAdd :title="title" v-if="open" :visible.sync="open" :shiftTypeOptions="shiftTypeOptions" :userList="userList"
      :modifyId="modifyId" :modifyDate="modifyDate" @freshList="freshList" fromSummary :userId="userId"></shiftAdd>
  </div>
</template>

<script>
import { listUser } from "@/api/system/user";
import { getShiftScheduleList } from "@/api/inspection/shiftSchedule.js";
import ScheduleView from "./scheduleView.vue";
import shiftAdd from "./shiftAdd.vue";

export default {
  data() {
    return {
      queryParams: {
        date: ""
      },
      userList: [],
      shiftList: [],
      tableData: [],
      loading: false,
      // 班次类型字典
      shiftTypeOptions: [],
      scheduleViewProps: {},

      // 是否显示弹出层
      open: false,
      title: '修改值班排班表',
      modifyId: '',
      modifyDate: '',
      userId: '',
      shiftNickName: [
        {
          nickName: '白',
          code: 0,
        },
        {
          nickName: '中',
          code: 1,
        },
        {
          nickName: '夜',
          code: 2,
        },
        {
          nickName: '大白',
          code: 3,
        },
        {
          nickName: '大夜',
          code: 4,
        }
      ],
      curBuilding: this.gf.getCurBuilding(),
    };
  },
  components: {
    ScheduleView,
    shiftAdd
  },
  async created() {
    this.queryParams.date = this.$moment().format("YYYY-MM");
    this.getUserList();
    await this.getDicts("shift_schedule_type").then(response => {
      this.shiftTypeOptions = response.data;
    });
    this.handleQuery();
  },
  methods: {
    // 所有用户
    getUserList() {
      listUser({ pageNum: 1, pageSize: 1000000 }).then(response => {
        this.userList = response.rows;
      });
    },
    /** 搜索按钮操作 */
    async handleQuery() {
      this.queryParams.pageNum = 1;
      this.queryParams.pageSize = 1000000;
      await this.getList();
      this.scheduleViewProps = { ...this.queryParams }
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    getList() {
      this.loading = true;
      const queryParams = {
        ...this.queryParams,
        userIds: (this.queryParams.userId || []).join(','),
        buildingId: this.curBuilding.id,
      }
      if(this.queryParams.date){
        queryParams.dateTime = this.queryParams.date
      }
      delete queryParams.date;
      delete queryParams.userId;

      getShiftScheduleList(queryParams).then(response => {
        this.shiftList = response.data || [];
        let arr = []
        this.userList.forEach(item => {
          const it = {
            userName: item.nickName,
            userId: item.userId,
            shiftType_0: 0,//白班
            shiftType_1: 0,//中班
            shiftType_2: 0,//夜班
            shiftType_3: 0,//大白班
            shiftType_4: 0,//大夜班
            totalWorkTime: 0,//排班总工作时长
          };
          (response.data || []).filter(item2 => item2.userId === item.userId).forEach(item2 => {
            it[`shiftType_${item2.shiftType}`]++;
            const noteStr = this.shiftTypeOptions.find(dict => dict.dictValue == item2.shiftType).remark;
            const note = noteStr && JSON.parse(noteStr);
            let length = note.endTime.split(':')[0] - note.startTime.split(':')[0];
            length = length >= 0 ? length : length + 24;
            it.totalWorkTime += length;
          });
          arr.push(it)
        })
        this.tableData = arr;
        if(this.queryParams.userId && this.queryParams.userId.length > 0){
          this.tableData = this.tableData.filter(item => this.queryParams.userId.indexOf(item.userId) > -1);
        }
        this.loading = false;
      });
    },
    // 值班人字典翻译
    userIdFormat(row, column) {
      const it = this.userList.find(item => item.userId === row.userId);
      return it.nickName;
    },
    // 班次整理
    shiftSumFormat(row, column) {
      let str = ''
      this.shiftNickName.forEach(item => {
        str = str + `${item.nickName}:${row['shiftType_'+item.code]}，`
      })
      return str
    },
    freshList() {
      this.open = false;
      this.getList()
    },
    showDialog({modifyId, userId, date}) {
      this.open = true;
      this.modifyId = modifyId;
      this.userId = userId;
      this.modifyDate = date
    },
    /** 导出按钮操作 */
    handleExport() {
      this.et("scheduleTable", "排班统计")
    },
  }
};
</script>

<style scoped lang="scss">
.shift_summary {
  padding: 16px;
}
</style>