<template>
  <div>
    <!-- 建筑分组 -->
    <el-tabs class="resourceTabsParent"
      tab-position="top"
      v-model="activeResourceGroup"
      @tab-click="handleResourceGroupChange">
      <el-tab-pane :label="g" :name="g" v-for="g in resourceGroupList">
        <span slot="label" class="tabTitle" v-show="showTabs">
          <i class="el-icon-office-building" size="mini"></i>
          {{ g }}
        </span>

        <!-- 分组内楼层 -->
        <el-tabs class="resourceTabs"
          tab-position="left"
          v-model="activeResourceId"
          @tab-click="handleResourceChange"
          >
          <el-tab-pane :key="f.id+''" :name="f.id+''" v-if="f.groupName == g" v-for="f in resourceList">
            <span slot="label" class="tabTitle" v-show="showTabs">
              {{f.name}}
            </span>

            <div >
              <DeviceSummary
                class="pull-left"
                v-if="f.id == activeResource.id"
                :data="deviceSummary"
                :summaryType="summaryType"
                :current="curSummaryTabActive"
                @handleDeviceFilter="handleDeviceFilter" />

              <DeviceResoureEdit
                class="pull-left"
                v-hasRole="['admin','operator']"
                :deviceList="deviceList"
                :deviceType="deviceMainType"
                :resourceId="activeResourceId"
                @handleResoureEditClose="handleResoureEditClose" />
              <div class="clearfix mb10" />

              <el-pagination
                background
                class="pull-left"
                layout="total, sizes, prev, pager, next"
                :current-page.sync="pageNum"
                :page-size="pageSize"
                :total="deviceTotal"
                @current-change="changePage"
                @size-change="sizeChange"/>

              <div class="pull-right">
                <el-button class="mr20" size="small" type="primary" @click="handleUpdateData">刷新数据</el-button>
                <el-button class="mr30" size="small" type="warning" plain @click="exportTable" >导出数据</el-button>
                <el-popover
                  placement="left"
                  title="表格选项"
                  width="200"
                  trigger="click">
                  <el-checkbox-group style="max-height: 480px; overflow: auto;" v-model="tableShowTitles" @change="tableTitlesChange">
                    <el-checkbox class="m10" v-for="t in tableFullTitles" :label="t" :value="t"></el-checkbox>
                  </el-checkbox-group>
                  <el-button
                    slot="reference"
                    type="text"
                    icon="el-icon-s-tools"
                     size="small"
                     class="mr10"
                    v-hasPermi="['device:*:*']"
                  >表格选项</el-button>
                </el-popover>
              </div>
              <div class="clearfix mb10"></div>

              <div class="p10" :key="`${g}-${f.id}`">
                <el-table
                  id="tb"
                  :data="filterDevice"
                  :default-expand-all="false"
                  :row-class-name="tableRowClassName"
                  :row-key="getRowKey"
                  :expand-row-keys="expandRowKeys"
                  @expand-change="handleExpandChange"
                  stripe>
                  <el-table-column type="expand">
                    <template slot-scope="props">
                      <div style="border:0.5px solid rgba(255,255,255,0.2); width:80%" >
                        <el-table :show-header="true" :data="props.row.deviceDataBase" >
                          <el-table-column label="参数名称" align="center" fixed="left" prop="dmName" />
                          <el-table-column label="数值" align="center" fixed="left" prop="valStr" />
                          <el-table-column label="数据说明" align="center" fixed="left" prop="dmNote" />
                          <el-table-column prop="dUpdatedAt" label="采集时间" width="180" />
                        </el-table>
                      </div>
                    </template>
                  </el-table-column>

                  <!-- <el-table-column label="编号" align="center" width="80" prop="id" sortable /> -->
                  <el-table-column label="设备名称" prop="name" sortable>
                    <template slot-scope="scope">
                      <el-tooltip
                        placement="top-start"
                        trigger="hover"
                        :content="`${scope.row.id}(${scope.row.description})`">
                        <div>{{scope.row.name}}</div>
                      </el-tooltip>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column label="设备编码" prop="description" sortable /> -->
                  <el-table-column label="设备位置" prop="position" sortable />
                  <el-table-column label="状态" sortable width="120">
                    <template slot-scope="scope">
                      <el-tooltip
                        placement="top-start"
                        trigger="hover">
                        <div slot="content">
                          <div v-if="summaryType == 'device' ">开关状态:  {{ scope.row.workStatus }} </div>
                          <div>告警状态:  {{ scope.row.warningStatus }} </div>
                          <div v-if="scope.row.communicateStatus">通讯状态:  {{ scope.row.communicateStatus }} </div>
                        </div>
                        <el-tag color="#2294fe2e" size="mini" type="success" style="margin:2px 5px 2px 0;">
                          <svg-icon v-if="summaryType == 'device' " icon-class="deviceRunning" class="titleIcon mr5" :class="scope.row.dRuning+'Txt'" />
                          <svg-icon icon-class="deviceWarning" class="titleIcon mr5" :class="scope.row.dStatus+'Txt'" />
                          <svg-icon icon-class="deviceOnline" class="titleIcon" :class="scope.row.dOnline+'Txt'" />
                        </el-tag>
                      </el-tooltip>
                      <el-tooltip
                        placement="top-start"
                        trigger="hover"
                        v-if="scope.row.strategyList && scope.row.strategyList.length > 0">
                        <div slot="content">
                          <div v-for="s in scope.row.strategyList" v-text="s.name" />
                        </div>
                        <el-tag color="#2294fe2e" size="mini" type="primary" style="margin:2px 2px 2px 0; font-size: 10px;">
                            <i style="color: #ffffffa6;">AI</i>
                        </el-tag>
                      </el-tooltip>
                    </template>
                  </el-table-column>

                  <el-table-column label="数据时间" width="100" prop="数据时间" sortable />
                  <el-table-column :key="`${t}-${timestamp}`" v-for="t in tableShowTitles" :prop="t" :label="t" width="100" sortable />
                  <el-table-column :key="t.dmId" v-for="t in shortcutFields" :label="t.dmName" width="100" sortable>
                    <template slot-scope="scope">
                        <span v-if="scope.row[`${t.dmName}_shortcut`]">
                          <span v-if="scope.row[`${t.dmName}_shortcut`].editType == 'input' || scope.row[`${t.dmName}_shortcut`].editType == 'inputNum'">
                            <el-input size="mini" placeholder="请输入" clearable
                              v-model="scope.row[`${t.dmName}_shortcut`].dVal" @change="statusChange(scope.row[`${t.dmName}_shortcut`])">
                              <template slot="append" v-text="scope.row[`${t.dmName}_shortcut`].unit" v-if="scope.row[`${t.dmName}_shortcut`].unit"></template>
                            </el-input>
                          </span>
                          <span v-if="scope.row[`${t.dmName}_shortcut`].editType == 'select' || scope.row[`${t.dmName}_shortcut`].editType == 'switch'">
                            <el-select size="mini" clearable v-model="scope.row[`${t.dmName}_shortcut`].dVal" placeholder="请选择" @change="statusChange(scope.row[`${t.dmName}_shortcut`])">
                              <el-option
                                v-for="(mv, mk) in scope.row[`${t.dmName}_shortcut`].otherDataMap"
                                :key="mk+''"
                                :label="mv"
                                :value="mk+''">
                              </el-option>
                            </el-select>
                          </span>
                        </span>
                    </template>
                  </el-table-column>

                  <el-table-column label="操作" align="center" width="120">
                    <template slot-scope="scope">
                      <el-button-group class="smBtn">
                        <el-button size="mini" type="primary" icon2="el-icon-view"
                                   @click="handleDeviceShow(scope.row, scope.$index)">详情</el-button>
                        <el-button size="mini" type="primary" icon2="el-icon-picture"
                                   v-if="scope.row.resourceId>0 && (!dialogType || dialogType.indexOf('mult') < 0)"
                                   @click="handleDevicePrototypeShow(scope.row, scope.$index)">组态</el-button>
                      </el-button-group>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>

          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
    </el-tabs>

    <!-- 弹框 设备详情  -->
    <DeviceDialogSmall
      v-if="activeDevice.id > 0 && activeDeviceSummaryShow && (!dialogType || dialogType.indexOf('mult') < 0)"
      :width="activeDevice.type.includes('camera') ? '980px' : '925px' "
      :activeDevice="activeDevice"
      :activeDeviceInd="activeDeviceInd"
      :protoTypeDisplay="protoTypeDisplay"
      @handleDeviceViewClose="handleDeviceViewClose"
      @handleDevicePrototypeShow="handleDevicePrototypeShow"
      @refreshList="getResourceDeviceList"
      @handleBatchStrategySend="handleBatchStrategySend"
      @handleItemDataLock="handleItemDataLock"
      @handleItemDataUnlock="handleItemDataUnlock"
    />

    <!-- 弹框 设备详情大弹框，包含复杂子组件  -->
    <DeviceDialogMult
      v-if="activeDevice.id > 0 && activeDeviceSummaryShow && dialogType == 'mult'"
      width="calc(100% - 60px)"
      :activeDevice="activeDevice"
      :activeDeviceInd="activeDeviceInd"
      :protoTypeDisplay="protoTypeDisplay"
      :refreshSpan="refreshSpan"
      @handleDeviceViewClose="handleDeviceViewClose"
      @refreshList="getResourceDeviceList"
      @handleBatchStrategySend="handleBatchStrategySend"
      @handleItemDataLock="handleItemDataLock"
      @handleItemDataUnlock="handleItemDataUnlock"
    />

    <!-- 弹框 设备详情大弹框，包含复杂子组件  -->
    <DeviceDialogMultBuildingControl
      v-if="activeDevice.id > 0 && activeDeviceSummaryShow && dialogType == 'multBc'"
      width="calc(100% - 60px)"
      :activeDevice="activeDevice"
      :activeDeviceInd="activeDeviceInd"
      :protoTypeDisplay="protoTypeDisplay"
      :refreshSpan="refreshSpan"
      @handleDeviceViewClose="handleDeviceViewClose"
      @refreshList="getResourceDeviceList"
      @handleBatchStrategySend="handleBatchStrategySend"
      @handleItemDataLock="handleItemDataLock"
      @handleItemDataUnlock="handleItemDataUnlock"
    />

    <!-- 弹框 设备组态图弹框  -->
    <DeviceDialogPrototype
      v-if="activeDevice.id > 0 && activeDevicePrototypeShow"
      width="calc(100% - 60px)"
      :activeDevice="activeDevice"
      :activeDeviceInd="activeDeviceInd"
      :protoTypeDisplay="protoTypeDisplay"
      :refreshSpan="refreshSpan"
      @refreshList="getResourceDeviceList"
      @handleDevicePrototypeClose="handleDevicePrototypeClose"
    />

  </div>
</template>

<script>
// import DeviceCardSmall from "./DeviceCardSmall";
// import DeviceDetail from "./DeviceDetail";
import DisplayBase from "./DisplayBase";
import DeviceDialogSmall from "@/views/device/monitor/components/Device/DeviceDialogSmall2.vue";
import DeviceDialogMult from "@/views/device/monitor/components/Device/DeviceDialogMult.vue";
import DeviceDialogPrototype from "@/views/device/monitor/components/Device/DeviceDialogPrototype.vue";
import DeviceDialogMultBuildingControl from "@/views/device/monitor/components/Device/DeviceDialogMultBuildingControl.vue";

export default {
  props: {

  },
  mixins: [ DisplayBase ], // 继承父模块
  components: {DeviceDialogPrototype, DeviceDialogMult, DeviceDialogSmall,DeviceDialogMultBuildingControl},
  data () {
    return {
      // loading: false,
      // 设备离线超时最大时间
      //offlineTimeOut: 60*60, // 1小时

      //buildingId: this.buildingId,
      //category: this.category,
      //deviceType: this.deviceType,

      // 建筑资源列表
      //resourceList: [],
      //activeResourceId: "",
      //activeResource: {},

      // 设备列表
      //deviceList: [],
      //deviceSummary: this.gf.deviceSummary(),
      //deviceStatusMap: this.gf.deviceStatusMap(),

      // 设备详情
      // activeDeviceInd: -1,
      // activeDevice: { id: -1 },
      // activeDeviceSummaryShow: false,
      // activeDeviceDetailShow: false,
      // acticeName: "总",

      // 设备开关
      //globalSwichTypes: this.gf.globalSwichTypes(),

      tableFullTitles: [],
      tableShowTitles: [],
      dataNum: 6,  // 默认显示 6 个数据
      showExtend: false,
      expandRowKeys: [],
      timestamp: this.$moment().valueOf(),
      tableTitlesCopy: null,
      shortcutFields: [],
      statusMap: [
        {
          name: "off",
          maybe: ['停', '关'],
        },
        {
          name: "on",
          maybe: ['开', '启', '运'],
        }
      ],
    };
  },
  created () {
  },
  mounted() {
    this.getDicts("d_device_type").then(response => {
       this.typeOptions = response.data;
    });
    this.pageSize = this.defaultPageSize;
  },
  destroyed() {
  },
  computed: {
  },
  methods: {
    // 根据点位数据生成表列
    addTableColum() {
      this.tableFullTitles = [];
      this.shortcutFields = [];
      let arr = []
      this.dataNum = this.listMoreFieldsNum;
      if(this.deviceList.length > 0) {
        if(this.displayKeys.length > 0) {
          // 通过第一个设备，创建表格 title
          let fd = this.deviceList[0];
          // 是否显示更多
          const deviceDataBases = fd.deviceDataBase.filter(item => item.dmTag.indexOf("shortcut") === -1);
          this.shortcutFields = fd.deviceDataBase.filter(item => item.dmTag.indexOf("shortcut") > -1);
          this.showExtend = deviceDataBases.length > this.dataNum;
          arr = this.displayKeys;
          deviceDataBases.map((dd, i) =>{
            this.tableFullTitles.push(dd.dmName);
          });
          this.deviceList.map(d => {
            d.deviceDataBase.map((dd, i) => {
              d[dd.dmName] = dd.valStr;
              if(dd.dmTag.indexOf("shortcut") > -1){
                d[`${dd.dmName}_shortcut`] = dd
              }
              if(i == 0) {
                this.$set(d, "数据时间", dd.dUpdatedAt)
              }
            });
            return d;
          });
        } else {
          // 通过第一个设备，创建表格 title
          let fd = this.deviceList[0];
          // 是否显示更多
          const deviceDataBases = fd.deviceDataBase.filter(item => item.dmTag.indexOf("shortcut") === -1);
          this.shortcutFields = fd.deviceDataBase.filter(item => item.dmTag.indexOf("shortcut") > -1);
          this.showExtend = deviceDataBases.length > this.dataNum;
          deviceDataBases.map((dd, i) =>{
            this.tableFullTitles.push(dd.dmName);
            if(i < this.dataNum) {
              arr.push(dd.dmName);
            }
          });
          this.deviceList.map((d,di) => {
            d.deviceDataBase.map((dd,i) => {
              d[dd.dmName] = dd.valStr;
              if(dd.dmTag.indexOf("shortcut") > -1){
                d[`${dd.dmName}_shortcut`] = dd
              }
              if(i == 0) {
                this.$set(d, "数据时间", dd.dUpdatedAt)
              }
            });
            return d;
          });
        }
        // if(this.deviceList[0].hasOwnProperty("数据时间") && this.deviceList[0]["数据时间"] != "") {
        //   this.tableTitles.unshift("数据时间");
        // }
      }
      console.log("DisplayByList tableTitles", this.tableTitles);
      console.log("DisplayByList deviceListDisplay", this.deviceList,this.shortcutFields);
      this.tableShowTitles = this.tableTitlesCopy || arr;
      this.timestamp = this.$moment().valueOf();
      return this.deviceList;
    },

    // 所有种类设备
    async getResourceDeviceList() {
      await this.getResourceMainDeviceList(undefined);
      this.addTableColum();
      console.debug("DisplayByList getResourceDeviceList", this.deviceList);
    },

    // 设备类型字典翻译
    typeFormat(row, column) {
      return this.selectDictLabel(this.typeOptions, row.type);
    },

    handleUpdateData() {
      this.getResourceDeviceList();
    },

    exportTable() {
      this.et('tb', "设备数据");
    },

    handleSortChange({ prop, order }) {
      this.sortColumn = prop;
      this.sortOrder = order === 'ascending' ? 'asc' : 'desc';
    },
    handleExpandChange (row, expandedRows) {
      if(expandedRows.length > 0){
        this.expandRowKeys = [row.id]
      } else {
        this.expandRowKeys = []
      }
    },
    getRowKey(row) {
      return row.id; // 每行的唯一标识是id字段
    },
    tableTitlesChange (list) {
      this.tableTitlesCopy = [...list];
    },
    getSwitchValue(theMap, status) {
      let type = status === 'active' ? 'on' : 'off';
      const it = this.statusMap.find(item => item.name === type);
      let val = "";
      for (const k in theMap) {
        if (it.maybe.some(item => theMap[k].indexOf(item) > -1)) {
          val = k;
          break; // 找到后退出循环
        }
      };
      return val;
    },
    statusChange (row) {
      this.handleSaveDevice({
        drId: row.drId,
        val: row.dVal,
      })
    }
  }
}
</script>
