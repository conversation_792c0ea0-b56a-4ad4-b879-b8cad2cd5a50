<template>
  <div :style="style"  @mouseup="updatePosition" class="multi-box">
    <template v-for="(data,index) in dataList">
      <template v-if="data.type == 'control'">
        <div v-if="data.editType == 'input'" class="item" :style="itemStyle">
          <div :style="textStyle">{{data.dmName}}</div>
          <el-input
            v-model="data.drVal"
            size="mini"
            class="control"
            @change="(value) => updateDataVal(value,data)">
            <template slot="append" v-if="data.drDataUnit!=''"><span v-text="data.drDataUnit"></span></template>
          </el-input>
        </div>
        <div v-if="data.editType == 'inputNum'" class="item" :style="itemStyle">
          <div :style="textStyle">{{data.dmName}}</div>
          <el-input
            v-model="data.drVal"
            size="mini"
            class="control"
            @change="(value) => updateDataVal(value,data)">
            <template slot="append" v-if="data.drDataUnit!=''"><span v-text="data.drDataUnit"></span></template>
          </el-input>
        </div>
        <div v-if="data.editType == 'select'" class="item" :style="itemStyle">
          <div :style="textStyle">{{data.dmName}}</div>
          <el-select
            v-model="data.drVal"
            size="mini"
            class="control"
            @change="(value) => updateDataVal(value,data)">
            <el-option
              v-for="(item,index) in data.opts"
              :key="index"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </div>
      </template>
      <div v-if="data.type == 'status'" class="item" :style="itemStyle">
        <div :style="textStyle">{{data.dmName}}</div>
        <div class="status-txt">{{data.valStr}}</div>
      </div>
    </template>

  </div>

</template>

<script>

import baseMinix from "./baseMinix";

export default {
  mixins: [baseMinix],
  props: {
    "data": {
      type: Object,
      default: () => {}
    },
    "activeDevice": {
      type: Object,
      default: () => {}
    },
  },
  data () {
    return {
      // style: {},
      // name: "",
      value: "",
      unit: "",
      dataList: [],
      itemStyle:{
        "width": "300px",
        "margin-bottom": "20px",
      },
      textStyle:{
        "width": "160px",
      },
    }
  },
  methods: {
    updateDataVal(val,data){
      this.$nextTick(()=> {
        console.log('........',val,data)
        let ch = [{
          id: data.drId,
          val: val,
        }];
        this.deviceDatasUpdate(ch)
      })

    },
    initElement() {
      console.log('................activeDevice',this.data,this.activeDevice)
      this.dataList = []
      this.style.top = this.data["position"].top;
      this.style.left = this.data["position"].left;
      this.style.transform = this.data["position"].transform || "";
      this.style.zIndex = this.data["zIndex"] || 100;
      this.textStyle['width'] = this.data["textWidth"] || '160px';
      this.itemStyle["margin-bottom"] = this.data["marginBottom"] || '20px';
      if(this.data.dataName && this.activeDevice.deviceDataBase && this.activeDevice.deviceDataBase.length){
        let list = this.data.dataName.split(' ')
        console.log('this.list...............',list)
        this.activeDevice.deviceDataBase.forEach(base => {
          if(list.includes(base.dmName)){
            if(base.dmRelateItemDataId != ''){
              // 控制点
              if(base.editType == 'select'){
                let opts = []
                if(typeof base.dValMap != "undefined") {
                  for(let o in base.dValMap){
                    opts.push({
                      label: base.dValMap[o],
                      value: o,
                    });
                  }
                }
                base.opts = opts
              }
              base.type = 'control'
            }else {
              // 状态点
              base.type = 'status'
            }
            this.dataList.push(base)
          }
        })
        console.log('this.dataList...............',JSON.stringify(this.dataList))
      }
      this.$forceUpdate()
    },
  }
}
</script>

<style scoped>
.el-input--medium .el-input__inner { height:100%; line-height:inherit; padding:0 5px; }
.el-input-group__append, .el-input-group__prepend { padding:0 5px; }

.item {
  display: flex;
}
.status-txt {
  width: 150px;
  font-size: 14px;
  color: #2294D6;
  font-weight: bold;
  text-align: left;
}
.control {
  width: 150px;
  height: 30px;
}
</style>
