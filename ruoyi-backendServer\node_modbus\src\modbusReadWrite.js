/**
功能: 读取/写入 modbus 点表数据
运行: node src/modbusReadWrite.js [CollectorConf Index] [dbName] [debug]

modbus 点表表 a_point_table
    collector_id  : collector.id -> 匹配 CollectorConf[Index].collectorId
    item_data_id  : a_item_data.id
    data_name     : 数据描述
    addr          : modbus address
    device_id     : modbus deviceId
    length        : 点位长度
    func          : modbus FunctionCode
    func_list     : 数据处理方式: []
    type          : 读r;写w

CollectorConf 采集配置
    [
        {
            host: "**************",
            port: 502,
            collectorId: 1,
        }
        ...
    ]
*/

const schedule = require('node-schedule');
// create an empty modbus client
var ModbusRTU = require("modbus-serial");
var moment = require('moment');

const Db = require("./mydb");
const helper = require("./helper");
const decUtil = require('./lib/_decUtil');

const CollectorConf = require("./conf/collectorConfig2").collectorConfig();
const sysConfig = require('./conf/sysConfig').sysConfig();

const collecotInd = typeof process.argv[2] != "undefined" ? process.argv[2] : 0 ;
const dbName = process.argv[3] && process.argv[3] != "debug" ? process.argv[3] : null;
global.isDebug = process.argv[4] == "debug" ? true : false;

const conf = CollectorConf[collecotInd];
let timeout = conf && conf.timeout || 5000;
let cmdRetryMax = conf && conf.cmdRetryMax || 5;
let isTailunCollector = conf && conf.isTailunCollector ? conf.isTailunCollector : false;

// 参数替换 覆盖之前的配置
if(dbName) {
  sysConfig.mysql.database = dbName;
}
var _db = new Db(sysConfig.mysql);
// 双数据库同步
if(typeof sysConfig.mysql2 != "undefined") {
    _db.createServer2(sysConfig.mysql2);
}

var client = null;

function initClient() {
    client = new ModbusRTU();
    client.connectTCP(conf.host, { port: conf.port });
    client.setTimeout(timeout);
}

var errorMaxNum = 10; // 最大错误次数后, 不记录日志
var errorCurNum = 0;  // 相同错误次数
var errorMsg = null;  // 当前错误

// 支持的指令类型，可以扩充
// | FC1 "Read Coil Status" | `readCoils(coil, len)` |
// | FC2 "Read Input Status" | `readDiscreteInputs(addr, arg)` |
// | FC3 "Read Holding Registers" | `readHoldingRegisters(addr, len) ` |
// | FC4 "Read Input Registers" | `readInputRegisters(addr, len) ` |
// | FC5 "Force Single Coil" | `writeCoil(coil, binary) //NOT setCoil` |
// | FC6 "Preset Single Register"
// | FC15 "Force Multiple Coil" | `writeRegister(addr, value)` |
// | FC16 "Preset Multiple Registers" | `writeRegisters(addr, valueAry)` |
// | FC43/14 "Read Device Identification" (supported ports: TCP, RTU) | `readDeviceIdentification(id, obj)` |

const FunctionCode = {
   "1" : "readCoils",      // 读取开关合/分闸状态
   "2" : "readDiscreteInputs",// 读取是否能被远程控制
   "3" : "readHoldingRegisters",   // 读从机实时数据
   "4" : "readInputRegisters",     // 读从机参数
   "5" : "writeCoil",
   "6" : "writeRegister",
   "01" : "readCoils",      // 读取开关合/分闸状态
   "02" : "readDiscreteInputs",// 读取是否能被远程控制
   "03" : "readHoldingRegisters",   // 读从机实时数据
   "04" : "readInputRegisters",     // 读从机参数
   "05" : "writeCoil",
   "06" : "writeRegister",
   "15" : "writeRegister",          // 写线圈
   "16" : "writeRegisters",         // 写从机参数
};

// open connection to a tcp line
//
var dataPoints = []; // 缓存设备数据
var itemWarningMap = {}; // 缓存设备报警数据
var curInd = 0;
var sql = `
    select
        p.*,
        it.item_id,
        it.val,
        it.has_sync,
        it.locked,
        ifnull(it.max_val, "") max_val,
        ifnull(it.min_val, "") min_val,
        ifnull(it.coefficient, 1) coefficient
    from a_point_table p
    LEFT JOIN a_item_data it on it.id=p.item_data_id
    where p.collector_id = ? and p.type = ? and it.has_sync = ?
    and it.item_id is not null
    order by p.device_id, p.addr, p.data_group
    `;
var rsql = `
    select
        p.*
    from a_point_table p
    where item_data_id = ?
`;
var wsql = `
    select
        *
    from a_item_data it
    where id = ? and func = ? and has_sync = ?
`;
var itemWarningRuleSql = `
    SELECT
        i.id as itemId,
        i.name as itemName,
        wr.name as wName,
        wr.description as wDescription,
        wr.key as wKey,
        wr.val as wVal,
        wr.time_span as wTimeSpan,
        wr.func as wFunc,
        wr.compare as wCompare,
        wr.severity as wSeverity,
        wr.err_msg as wErrMsg,
        wr.solution_ref as wSolutionRef,
        wr.tag as wTag
    from a_item_rule_map rm
    LEFT JOIN a_item i on rm.item_id = i.id
    LEFT JOIN a_warning_rule wr on wr.id = rm.rule_id
    where i.collector_id = ?
`;

var warningSql = {
    "check": `select * from a_item_warning where item_id = ? and warning_category = ? and has_fixed = 'N' order by reported_at desc`,
    "insert": `insert into a_item_warning (item_id, compare, warning_category, severity, err_msg, solution_ref, reported_at) values (?,?,?,?,?,?,?)`,
    "updateFix": `update a_item_warning set updated_at = ?, compare = ?, has_fixed = 'Y' where id= ?`,
    "updateLastData": `update a_item_warning set updated_at = ?, compare = ? where id= ?`,
}

// 提供 a_point_table.val 中 值包含 energyWeekDayHourlyAvg 的数据转换
var energyWeekDayHourlyAvgSql = `
    SELECT
        *
    from v_e_device_diff_indication_avg_hourly where deviceId in (
        select
            device_id
        FROM a_item_data d
        LEFT JOIN e_device_item_data_map dm on dm.item_data_id = d.id
        where d.item_id = ? and d.name= ?
    ) and weekDay = ? and dayHour = ?
`;

// 全局变量
var gtmp = {};
// 缓存报过的警, 如果一直未变化, 则不会重复报
var cacheWarning = {};
// 缓存设备最近几次数据
var cacheData = {};
// 记录日志标记
var logSave = true;
// 采集成功标记
var collectorConn = false;

// 更新采集相关数据
async function updateCollectDatas() {
    // 获取采集设备列表
    let dataPointRes = await _db.doSql(sql, [conf.collectorId, "r", "Y"]);
    dataPoints = fmtDataPoints(dataPointRes);
    helper.info("采集数据更新，共 ", dataPointRes.length, "个数据采集点。", Object.keys(dataPoints).length, "次采集命令");

    // 获取设备故障列表
    let itemWarningRes = await _db.doSql(itemWarningRuleSql, [conf.collectorId]);
    helper.info("采集数据更新，共 ", itemWarningRes.length, "个故障检测点。");

    if(itemWarningRes.length > 0) {
        for(let i =0; i<itemWarningRes.length; i++) {
            let iw = itemWarningRes[i];
            if(typeof itemWarningMap[iw["itemId"]] == "undefined") {
                itemWarningMap[iw["itemId"]] = [iw];
            } else {
                itemWarningMap[iw["itemId"]].push(iw);
            }
        }
    }
    logSave = true;

    // 检查采集器状态，如果更新时间超过 5 分钟，判定离线
    //await _db.checkCollectorState({id: conf.collectorId, offLineCheck: 5});
}

// 格式化数据, 一组的放在一次采集里面
function fmtDataPoints(dataPointList) {
    if(dataPointList.length <= 0) {
        return {};
    }
    let cachePoint = {};
    for(let i=0; i<dataPointList.length; i++) {
        let curPoint = dataPointList[i];
        // 如果data_group为空, 容错
        if(curPoint.data_group == null || curPoint.data_group == "") {
            curPoint.data_group = curPoint.addr;
        }
        let cacheKey = [curPoint.device_id, curPoint.func, curPoint.data_group].join("-");
        if(typeof cachePoint[cacheKey] == "undefined") {
            cachePoint[cacheKey] = {
                item_id: curPoint.item_id,
                device_id: curPoint.device_id,
                addr: curPoint.addr,
                length: curPoint.length,
                func: curPoint.func,
                points: [curPoint],
                coefficient: curPoint.coefficient,
            };
        } else {
            cachePoint[cacheKey].length = curPoint.addr+curPoint.length - cachePoint[cacheKey]["points"][0].addr;
            cachePoint[cacheKey]["points"].push(curPoint);
        }
    }
    return cachePoint;
}

// 为 ModbusRTU client 添加扩展方法
function expandClient() {
    // 添加 103
    client.airConditionReadHoldingRegister = function(addr, length) {
        return new Promise((resolve, reject) => {
            // eg: 01 03 00 06 00 06 25 C9
            let clientId = client.getID();
            let functionCode = 3;
            var bufferLength = 6;
            var buffer = Buffer.alloc(bufferLength);

            buffer.writeUInt8(clientId, 0);
            buffer.writeUInt8(functionCode, 1);
            buffer.writeUInt16BE(addr, 2);
            buffer.writeUInt16BE(length, 4);

            var crcValue = crc16(buffer);
            var crcBuffer = Buffer.alloc(2);
            crcBuffer.writeUInt16LE(crcValue, 0);
            let code = Buffer.concat([buffer, crcBuffer]);
            helper.debug("buffer指令地址:", clientId, "code:", JSON.stringify(code));
        });
    }
    // 添加 106
    client.airConditionWriteRegister = function(addr, length) {
        return new Promise((resolve, reject) => {
            let clientId = client.getID();
            console.log("expandClient airConditionWriteRegister", clientId);
        });
    }
}

// 执行采集
async function startCollect() {
    if(gtmp.startCollect) {
        // 任务未执行完成, 等待下次轮训
        return false;
    }
    gtmp.startCollect = true;
    collectorConn = false; // 重置采集器状态
    try {
        if(Object.keys(dataPoints).length > 0) {
            // 尝试重连
            if(!client.isOpen) {
                client.connectTCP(conf.host, { port: conf.port });
                client.setTimeout(conf.timeout);
            }
            // 开始采集
            for(let i in dataPoints) {
                var curPoint = dataPoints[i];
                helper.debug("curPoint", curPoint);
                client.setID(curPoint.device_id);
                var _f = FunctionCode[curPoint.func];
                helper.debug("_f", _f, curPoint.addr, curPoint.length);
                try {
                    //let data = await client[_f](curPoint.addr, curPoint.length);
                    let data = null;
                    let en = 0;
                    // 尝试多次
                    while (en < cmdRetryMax) {
                        try {
                            data = await client[_f](curPoint.addr, curPoint.length);
                            break;
                        } catch (e) {
                            if(e.errno == "ETIMEDOUT") {
                                // 再尝试一次
                                en += 1;
                            } else {
                                throw new Error("client " + _f + " error. " + e.errno + "; " + e.message);
                            }
                        }
                    }
                    errorCurNum = 0;
                    errorCur = null;

                    helper.debug("data", data);
                    
                    if(logSave) {
                        helper.remoteLog(conf.name, JSON.stringify({
                            db: dbName,
                            collectorId: conf.collectorId,
                            host: conf.host,
                            port: conf.port,
                            func: _f,
                            res: data,
                            curPoint: curPoint,
                        }));
                    }

                    // // 更新设备时间
                    // await _db.updateItemTime({id: curPoint.item_id});

                    // 针对 coils 需要解析 data.data, 其他解析 data.buffer
                    if(_f == "readCoils" || _f == "writeCoil" || _f == "readDiscreteInputs") {
                        await parseData(data.data, curPoint);
                    } else {
                        await parseBuffer(data.buffer, curPoint);
                    }
                } catch(e) {
                    console.trace(e);
                    if(errorMaxNum <= errorCurNum && errorMsg == e.message) {
                        // pass 不记录重复日志
                    } else {
                        helper.log("发生错误", _f, curPoint.addr, curPoint.length, JSON.stringify(e.message));
                    }
                    errorCurNum += 1;
                    errorMsg = e.message;
                    // throw new Error("client error: " + errorMsg);
                    continue; // 执行下一个
                }
            }
        }

        // 更新采集器在线状态, 更新设备时间
        await _db.updateCollectorTime({id: conf.collectorId, stateConn: "online"});
    } catch(e) {
        helper.info("startCollect error", e.message);
        gtmp.startCollect = false;
        // 尝试重启解决
        process.exit(1);
    }
    gtmp.startCollect = false;
    logSave = false;
};

// 解析数据，针对 Coils
async function parseData(list, curPoint) {
    if(curPoint.points.length > 0) {
        let fcp = curPoint.points[0];
        for(let i=0; i<curPoint.points.length; i++) {
            let cp = curPoint.points[i];
            let cbuff = list[(cp.addr - fcp.addr)]; // 取对应的点位信息，只可能一个长度
            let funcList = JSON.parse(cp.func_list);
            var val = decUtil.batch(cbuff, funcList);
            await dealResult(val, cp);
            // // 更新数据点位
            // await dealResult(val, cp);
            // // 更新报警信息
            // await dealWarningResult(val, cp);
        }
    }
}

// 解析数据，针对 Registers
async function parseBuffer(buff, curPoint) {
    // 针对泰伦空调，判断数据是否都是0
    if(isTailunCollector) {
        if(buff.length >= 6 && buff.every(function f(v){return v==0;})) {
            helper.info("buff is all 0", buff);
            return false;
        }
    }
    
    if(curPoint.points.length > 0) {
        let fcp = curPoint.points[0];
        for(let i=0; i<curPoint.points.length; i++) {
            let cp = curPoint.points[i];
            let cbuff = decUtil.slice(buff, (cp.addr - fcp.addr) * 2, (cp.addr - fcp.addr + cp.length) * 2);
            let funcList = JSON.parse(cp.func_list);
            var val = decUtil.batch(cbuff, funcList);
            await dealResult(val, cp);
            // // 更新数据点位
            // await dealResult(val, cp);
            // // 更新报警信息
            // await dealWarningResult(val, cp);
        }
    } else {
        helper.debug("[update db] error value", val, curPoint.max_val, curPoint.min_val);
    }
}

// 更新db中点位数据
// 只更新已知数据点信息，自动扫描的不做插入操作
async function dealResult(val, cp) {
    helper.debug("decUtil", val, cp);
    helper.debug("[sync to db] start", cp.item_data_id, val);
    // 统一数据入库
    let res = await helper.syncItemData2Db({
        id: cp.item_data_id,
        val: val,
        updatedAt: moment().format('YYYY-MM-DD HH:mm:ss'),
    });
    helper.debug("[sync to db] end", res);
    return res;
}

async function syncDbWriteToModbus() {
    if(gtmp.syncDbWriteToModbus) {
        // 任务未执行完成, 等待下次轮训
        return false;
    }
    gtmp.syncDbWriteToModbus = true;
    try {
        var points = await _db.doSql(sql, [conf.collectorId, "w", "N"]);
        helper.debug("syncDbWriteToModbus", points.length);
        if(points.length > 0) {
            for(let i=0; i<points.length; i++) {
                let d = points[i];
                if(d.hasOwnProperty("tag") && d.tag == "1") {
                    // 需要读点标识
                    let readPoints = await _db.doSql(rsql, [(d.item_data_id - 1)]);
                    let readPoint = readPoints[0];
                    helper.debug("readPoint", readPoint);
                    try {
                        client.setID(readPoint.device_id);
                        var _f = FunctionCode[readPoint.func];
                        helper.debug("_f", _f, readPoint.addr, readPoint.length);
                        let data = await client[_f](readPoint.addr, readPoint.length);
                        helper.debug("readPoint data", data);
                        // 针对 coils 需要解析 data.data, 其他解析 data.buffer
                        let buffer = null;
                        if(_f == "readCoils" || _f == "writeCoil" || _f == "readDiscreteInputs") {
                            buffer = data.data;
                        } else {
                            buffer = data.buffer;
                        }
                        helper.debug("buffer", buffer);
                        let funcList = JSON.parse(d.func_list);
                        var val = decUtil.batch([buffer, d.val], funcList);
                        helper.info("write val", val, d);
                        let sres = await syncToModbusData(d, val);
                        helper.remoteLog(conf.name+"下发指令", JSON.stringify({
                            db: dbName,
                            collectorId: conf.collectorId,
                            host: conf.host,
                            port: conf.port,
                            curPoint: d,
                            res: sres,
                        }));

                        if(typeof sres.address != "undefined" && sres.address == d.addr) {
                            var msg = {id: d.item_data_id, val: d.val};
                            dealResult(d.val, d);
                        }
                    } catch (e) {
                        console.trace(e);
                        // 读取失败，不做处理，下次再等待读取
                    }

                } else {
                    // 普通点标识
                    try {
                        let funcList = JSON.parse(d.func_list);
                        var val = decUtil.batch(d.val, funcList);
                        helper.info(val, d);
                        let sres = await syncToModbusData(d, val);

                        helper.remoteLog(conf.name+"下发指令", JSON.stringify({
                            db: dbName,
                            collectorId: conf.collectorId,
                            host: conf.host,
                            port: conf.port,
                            curPoint: d,
                            res: sres,
                        }));

                        if(typeof sres.address != "undefined" && sres.address == d.addr) {
                            var msg = {id: d.item_data_id, val: d.val};
                            dealResult(d.val, d);
                        }
                    } catch(e) {
                        helper.info("syncDbWriteToModbus error", e.message, "item_id", d.item_id, "item_data_id", d.item_data_id, "point_table_id", d.id, "val", d.val);
                        // throw new Error("client error: " + e.message);
                    }
                }
            }
        }
    } catch(e) {
        console.trace()
        helper.info("syncDbWriteToModbus error", e.message);
        gtmp.syncDbWriteToModbus = false;
        // 尝试重启解决
        process.exit(1);
    }
    gtmp.syncDbWriteToModbus = false;
}

async function syncToModbusData(curPoint, val) {
    client.setID(curPoint.device_id);
    var _f = FunctionCode[curPoint.func];
    let res = await client[_f](curPoint.addr, val);
    helper.info("_f", _f, curPoint.addr, val, res);
    return res;
}

async function start() {
    // 每1秒一次
    schedule.scheduleJob('*/1 * * * * *',()=>{
        syncDbWriteToModbus();
        //helper.log('syncLightDataToBeacool success');
    });
    // 每5秒一次
    schedule.scheduleJob('*/5 * * * * *',()=>{
        startCollect();
        //helper.log('syncLightDataToBeacool success');
    });
    // 5分钟执行一次
    schedule.scheduleJob('1 */5 * * * *',()=>{
        //updateCollectDatas();
        helper.log('updateCollectDatas success');
    });
    // 1小时重启一次
    schedule.scheduleJob('0 1 * * * *',()=>{
        helper.log('Auto restart server');
        process.exit(1);
    });

    // 启动采集 client
    initClient();
    helper.info("initClient success");

    // 数据服务初始化
    helper.initDataServer(sysConfig);
    helper.info("initDataServer success");

    // mqtt连接，系统互通各种消息通道
    helper.connectMqtt(dbName);
    helper.info("connectMqtt success");

    // 日志服务连接至远程logstash
    helper.connectLogstash(sysConfig);
    helper.info("connectLogstash success");

    await updateCollectDatas();
    helper.info("updateCollectDatas success");
    await startCollect();
    helper.info("startCollect success");
    helper.info("start success");

};

if(collecotInd >= 0) {
    start();
}

module.exports.dealResult = dealResult;



// async function test() {
//     await updateCollectDatas();

//     cacheData[1] = {"timeSpan":30,"data":[{"time":"2021-02-05 14:13:48","val":2.470}]}
//     cacheData[10] = {"timeSpan":30,"data":[{"time":"2021-02-05 14:13:48","val":2.470}]}

//     let val = 2.470;
//     let curPoint = {"id":3, "collector_id":1, item_data_id: 10, data_name: "73#用水",
//     addr: "18064", device_id:1, length:2, data_group:18064, func:"04", item_id:1};
//     await dealWarningResult(val, curPoint);
// }

// test();
