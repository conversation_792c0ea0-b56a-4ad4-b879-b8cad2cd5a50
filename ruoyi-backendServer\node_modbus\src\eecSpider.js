/**
 常州中以国际 对接 泰杰赛 ba，能耗 数据
 功能:
 运行: node src/eecSpider.js [buildingId] [dbName] [debug]

 字典 building_config_1 中增加配置
 eec_server：   服务器地址，
 eec_username： 用户名
 eec_password： 密码
 eec_collectorId： 采集器id


 // 1分钟执行一次
 syncDeviceData(); -> 同步所有数据

 // 1小时重启一次


 */

// create an empty modbus client
const schedule = require("node-schedule");
var moment = require("moment");

const Db = require("./mydb");
const SerApi = require("./lib/eecApi");
const helper = require("./helper");

const sysConfig = require("./conf/sysConfig").sysConfig();
const config = {};

const buildingId = process.argv[2] > 0 ? process.argv[2] : 1;
const dbName = process.argv[3] && process.argv[3] != "debug" ? process.argv[3] : null;
global.isDebug = process.argv[4] == "debug" ? true : false;


// 参数替换 覆盖之前的配置
if (dbName) {
    sysConfig.mysql.database = dbName;
}

var _db = new Db(sysConfig.mysql);
// 双数据库同步
if (typeof sysConfig.mysql2 != "undefined") {
    _db.createServer2(sysConfig.mysql2);
}

// i接口实例，启动加载配置后，初始化
let _pxServ = null;

var pxConfigSql = `
    select
        *
    from sys_dict_data
    where dict_type = ? and dict_label like "eec_%";
`;

var dbUpdateSql = `
    SELECT
        i.code,
        i.alias,
        i.description,
        i.name,
        d.id as item_data_id,
        d.name as item_data_name,
        d.val as item_data_val,
        ifnull(d.max_val, '') max_val,
        ifnull(d.min_val, '') min_val,
        p.func_list,
        p.func
    FROM a_collector c
    LEFT JOIN a_item i on c.id = i.collector_id
    LEFT JOIN a_item_data d on i.id = d.item_id
    LEFT JOIN a_point_table p on d.id = p.item_data_id
    where c.id = ?
`;

var deviceWSql = `
    select
        p.*,
        it.item_id,
        it.val,
        it.has_sync,
        it.locked,
        i.code
    from a_point_table p
    LEFT JOIN a_item_data it on it.id=p.item_data_id
    LEFT join a_item i on it.item_id = i.id
    where p.collector_id = ? and p.type = ?
    and it.has_sync = ?
    and it.item_id is not null
    order by p.addr
`;

// 全局变量
var gtmp = {};
var dbDataList = [];
var pontIds = [];

// 读取配置 获取对应配置
async function readApiConfig() {
    if (gtmp.readApiConfig) {
        // 任务未执行完成, 等待下次轮训
        return false;
    }
    // 初始化服务
    gtmp.readApiConfig = true;
    try {
        let list = await _db.doSql(pxConfigSql, ["building_config_" + buildingId]);
        if (list.length > 0) {
            for (let i = 0; i < list.length; i++) {
                let conf = list[i];
                if (conf.dict_label == "eec_server") {
                    config.server = conf.dict_value;
                }
                if (conf.dict_label == "eec_username") {
                    config.username = conf.dict_value;
                }
                if (conf.dict_label == "eec_password") {
                    config.password = conf.dict_value;
                }
                if (conf.dict_label == "eec_collectorId") {
                    config.collectorId = conf.dict_value;
                }
            }
        }
        helper.debug("read db config", config);
        // 实例化接口服务
        _pxServ = new SerApi.SerApi(config);

        await _pxServ.getToken();
    } catch (e) {
        console.trace(e);
        helper.info("readApiConfig error", e.message);
        gtmp.readApiConfig = false;
        process.exit(1);
    }
    gtmp.readApiConfig = false;
}
async function getData() {
    dbDataList = await _db.doSql(dbUpdateSql, [config.collectorId]);
    pontIds = dbDataList.map(ii => { return ii.func })
}

async function syncDeviceData() {
    if (gtmp.syncDeviceData) {
        // 任务未执行完成, 等待下次轮训
        return false;
    }
    // 初始化服务
    gtmp.syncDeviceData = true;
    try {
        if(pontIds.length){
            // 调用 api 获取数据
            let apiDataRes = await _pxServ.getDeviceData(pontIds);
            let apiData = (apiDataRes && apiDataRes.data) || [];
            for (let i = 0; i < apiData.length; i++) {
                let data = apiData[i]
                if (dbDataList.length) {
                    for (let k = 0; k < dbDataList.length; k++) {
                        let cp = dbDataList[k];
                        if (data.pointId == cp.func) {
                            let val = data.value;
                            // 更新数据点位
                            helper.debug("[sync to db]", cp.item_data_id, val);
                            // 统一数据入库
                            await helper.syncItemData2Db({
                                id: cp.item_data_id,
                                val: val,
                                updatedAt: data.time || moment().format("YYYY-MM-DD HH:mm:ss"),
                            });
                        }else {
                            // helper.debug("no.........",data);
                        }
                    }
                }
            }
        }
    } catch (e) {
        console.trace(e);
        helper.info("syncDeviceData error", e.message);
        gtmp.syncDeviceData = false;
    }
    gtmp.syncDeviceData = false;
}

// 数据下发
async function syncDbWriteToDevice() {
    if (gtmp.syncDbWriteToDevice) {
        // 任务未执行完成, 等待下次轮训
        return false;
    }
    gtmp.syncDbWriteToDevice = true;
    try {
        var points = await _db.doSql(deviceWSql, [config.collectorId, "w", "N"]);
        helper.debug("syncDbWriteToDevice", points.length);
        if (points.length > 0) {
            for (let i = 0; i < points.length; i++) {
                try {
                    let d = points[i];
                    let data = {
                        pointId: d.func,
                        value: d.val,
                    };
                    let sres = await _pxServ.setDeviceData(data);
                    if (sres.statusCode == 200) {
                        let msg = { id: d.item_data_id, val: d.val };
                        let msg2 = { id: d.item_data_id - 1, val: d.val };
                        if (d.locked > 0) {
                            await _db.updateDataWithoutHasSync(msg);
                            await _db.updateData(msg2);
                        } else {
                            await _db.updateData(msg);
                            await _db.updateData(msg2);
                        }
                    } else {
                        helper.debug("sres", sres);
                    }
                } catch (e) {
                    helper.info(e.message);
                }
            }
        }
    } catch (e) {
        console.trace(e);
        helper.info("syncDbWriteToDevice error", e.message);
        gtmp.syncDbWriteToDevice = false;
    }
    gtmp.syncDbWriteToDevice = false;
}

async function start() {
    // 每1秒一次
    schedule.scheduleJob("*/1 * * * * *", () => {
        syncDbWriteToDevice();
        //helper.log('syncDbWriteToDevice success');
    });
    // 1分钟执行一次    30 * * * * *
    schedule.scheduleJob("1 */1 * * * *", () => {
        syncDeviceData();
        helper.log("syncDeviceData success");
    });
    // 半小时执行一次
    schedule.scheduleJob("1 */30 * * * *", () => {
        getData();
        helper.log("generaterEnergyData success");
    });
    // 1小时执行一次
    schedule.scheduleJob("1 1 * * * *", () => {
        helper.log("Auto restart server");
        process.exit(1);
    });

    // 数据服务初始化
    helper.initDataServer(sysConfig);
    helper.info("initDataServer success");

    // 日志服务连接至远程logstash
    helper.connectLogstash(sysConfig);
    helper.info("connectLogstash success");

    await readApiConfig();
    await getData();
    await syncDeviceData();
    helper.info("syncDeviceData success");
    helper.info("start success");
}

start();
