<template>
    <div v-if="globalWarning.warningShow">
      <div class="flex-row" style="margin: 16px;" @click="handleJump" v-if="totalWarning > 0">
        <el-popover
          placement="top-start"
          trigger="hover">
          <div :class="['alart_tag', noticeClass]" slot="reference">
            <img v-if="alarmFlag" src="/image/alarm_icon.png" alt="报警">
            <img v-if="!alarmFlag && noticeFlag" src="/image/notice_icon.png" alt="预警">
            <img v-if="!alarmFlag && !noticeFlag && offlineFlag" src="/image/offline_icon.png" alt="离线">
            <span v-text="totalWarning > 99 ? '99+' : totalWarning" />
          </div>
          <div class="popover">
            <div class="item flex-row flex-align-v" v-for="item in [...warningSummaryData, ...offlineSummaryData]" :key="item.severity" @click="handleItemJump(item.severity)">
              <div>{{item.severity}}</div>
              <div class="num">{{item.num}}</div>
            </div>
          </div>
        </el-popover>

        <audio id="alarmVoice" controls="controls" loop autoplay="autoplay" style="display: none;" v-if="alarmVoiceOpen && totalWarning > 0">
          <source src="/image/sound_alarm.wav" type="audio/mpeg" v-if="alarmFlag" />
          <source src="/image/sound_notice.wav" type="audio/mpeg" v-if="!alarmFlag && noticeFlag" />
          <source src="/image/sound_offline.mp3" type="audio/mpeg" v-if="!alarmFlag && !noticeFlag && offlineFlag" />
        </audio>
      </div>
    </div>
</template>

<script>

import {
  deviceWarningSummaryBySeverity
} from "@/api/device/apis";
export default {
  props: {
    alarmVoiceOpen: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      settings: this.gf.projectSettings(),
      warningInterval: null,

      // 全局通知弹框
      globalNotice: [],
      globalNoticeInd: 0,

      warningDictDeviceTypeList: [],
      offlineDictDeviceTypeList: [],

      // 报警
      warningSummaryData: [],
      // 离线
      offlineSummaryData: [],
      // 离线 + 报警
      totalWarning: 0,

      globalWarningBak: {
        warningShow: true,    // 报警提醒开关
        alarmVoiceOpen: true, // 报警声音开关
        timeSpan: 10*1000,    // 报警提醒轮询时间， 10 秒检查一次
        severity: "一级,二级,三级,离线",  // 报警提醒包含所有的 severity 等级；只显示一个图标；显示优先级 alarm > notice > offline
        alarm: "一级",        // 按照 alarm 显示的 severity 等级，不配置不显示提醒
        notice: "二级,三级",  // 按照 notice 显示的 severity 等级，不配置不显示提醒
        offline: "离线",      // 按照 offline 显示的 severity 等级，不配置不显示提醒
      },
      globalWarning: {},
      alarmFlag: false,
      noticeFlag: false,
      offlineFlag: false,
    }
  },
  computed: {
    noticeClass() {
      this.alarmFlag = this.warningSummaryData.find(item => this.globalWarning.alarm.indexOf(item.severity) >= 0 );
      this.noticeFlag = this.warningSummaryData.find(item => this.globalWarning.notice.indexOf(item.severity) >= 0 );
      this.offlineFlag = this.offlineSummaryData.find(item => this.globalWarning.offline.indexOf(item.severity) >= 0 );
      console.log("noticeClass", this.alarmFlag, this.noticeFlag, this.offlineFlag, this.alarmFlag ? "alarm" : (this.noticeFlag ? "notice" : (this.offlineFlag ? 'offline' : '')));
      return this.alarmFlag ? "alarm" : (this.noticeFlag ? "notice" : (this.offlineFlag ? 'offline' : ''));
    },
  },
  created () {
    // 兼容旧配置项
    if(this.settings.alarm_voice_open && this.settings.alarm_voice_open == 1) {
      this.globalWarningBak.alarmVoiceOpen = true;
    }
    // 报警统一配置
    try {
      this.globalWarning = {
        ...this.globalWarningBak,
        ...JSON.parse(this.settings.global_warning),
      };
    } catch(e) {
      this.globalWarning = this.globalWarningBak;
    }
    try {
      this.globalNotice = JSON.parse(this.settings.global_notice);
    } catch(e) {
      // pass
    }
    // warningDeviceType + offlineDeviceType

    console.log("globalWarning", this.globalWarning, "globalNotice", this.globalNotice);
  },
  destroyed() {},
  watch: { },
  mounted() {
    // 立即执行一次
    this.initWarningNum();

    setInterval(() => {
      this.soundPlay();
    },1000);

    this.genGlobalNotice();

  },
  methods: {
    handleJump() {
      setTimeout(() => {
        if(this.alarmFlag  || this.noticeFlag) {
          this.$router.push({
            path: "/maintananceMgt/warning/warning",
          });
        } else {
          this.$router.push({
            path: "/maintananceMgt/warning/offline",
          });
        }
      }, 200);
    },
    handleItemJump (severity) {
        if(severity !== "离线") {
          this.$router.push({
            path: "/maintananceMgt/warning/warning",
          });
        } else {
          this.$router.push({
            path: "/maintananceMgt/warning/offline",
          });
        }
    },

    getWarningNum() {
      setTimeout(() => {
        this.initWarningNum()
      }, this.globalWarning.timeSpan);
    },

    soundPlay() {
      var music = document.getElementById("alarmVoice");//获取ID
      try {
        if (music && music.paused) { //判读是否播放
          music.paused=false;
          music.play(); //没有就播放
        }
      } catch(e) {
        // pass
      }
    },

    genGlobalNotice() {
      if(this.globalNotice) {
        this.showGlobalNotice();
      }
    },

    showGlobalNotice() {
      let n = this.globalNotice[this.globalNoticeInd];
      if(n && n.hasOwnProperty("condition")) {
        let now = this.$moment();
        let nMonth = now.format("MM");
        let nDay = now.format("DD");
        let nHour = now.format("HH");
        let nMinute = now.format("mm");
        let checkDate = (n.condition.month.length > 0 ? n.condition.month.indexOf(nMonth) >= 0 : true)
          && (n.condition.day.length > 0 ? n.condition.day.indexOf(nDay) >= 0 : true)
          && (n.condition.hour.length > 0 ? n.condition.hour.indexOf(nHour) >= 0 : true)
          && (n.condition.minute.length > 0 ? n.condition.minute.indexOf(nMinute) >= 0 : true)
        let ignoreNotice = this.gf.getLocalObject("globalNotice_" + n.title);
        if (checkDate && !ignoreNotice) {
          this.$confirm(n.content, n.title, {
            confirmButtonText: '关闭，今天不再提醒',
            cancelButtonText: '关闭',
            type: (n.type || 'warning')
          }).then(() => {
            this.gf.setLocalObject("globalNotice_" + n.title, 1, this.$moment().endOf('day').diff(this.$moment()));
            this.globalNoticeInd += 1;
            this.showGlobalNotice();
          }).catch(() => {
            this.globalNoticeInd += 1;
            this.showGlobalNotice();
          });
        }
      }
    },

    async initWarningNum() {
      let totalWarning = 0;
      let res = {data: []};
      let resOffline = {data: []};
      let warningDeviceType = await this.gf.getDictDeviceTypeList("warning");
      if(warningDeviceType.length > 0) {
        res = await deviceWarningSummaryBySeverity({
          buildingId: this.gf.getBuildingId(),
          severitys: this.globalWarning.severity.split(',').filter(item => item.trim() !== '离线').join(','),  // 移除离线
          deviceTypes: warningDeviceType.join(","),
          hasFixed: "N",
        });
      }
      this.warningSummaryData = res.data || [];
      this.warningSummaryData.forEach(item => {
        totalWarning += item.num
      });
      this.warningSummaryData = this.warningSummaryData.sort((a,b) => {
        const severityOrder = { "一级": 1, "二级": 2, "三级": 3 };
        return severityOrder[a.severity] - severityOrder[b.severity];
      });

      // this.totalWarning = totalWarning;

      let totalOffline = 0;
      let offlineDeviceType = await this.gf.getDictDeviceTypeList("offlineAlert");
      if(offlineDeviceType.length > 0) {
        resOffline = await deviceWarningSummaryBySeverity({
          buildingId: this.gf.getBuildingId(),
          severitys: "离线",
          deviceTypes: offlineDeviceType.join(","),
          hasFixed: "N",
        });
      }
      this.offlineSummaryData = resOffline.data || [];
      this.offlineSummaryData.forEach(item => {
        totalWarning += item.num
      });
      this.totalWarning = totalWarning;
      console.log("totalWarning", this.totalWarning);

      // 定时再次执行
      setTimeout(() => {
        this.initWarningNum();
      }, this.globalWarning.timeSpan);
    },
  }
}
</script>

<style lang="scss" scoped>
  .alart_tag {
    display: flex;
    width: 60px;
    height: 30px;
    display: flex;
    align-items: center;
    cursor: pointer;
    img {
      width: 24px;
      height: 24px;
      margin-left: 6px;
    }
    span {
      font-size: 16px;
      margin-top: 2px;
      margin-left: 4px;
      font-family: Source Han Sans CN, Source Han Sans CN;
    }
    &.alarm {
      background: url('/image/alarm_bg.png') no-repeat center;
      background-size: contain;
      span {
        color: #BD2D2D;
      }
    }
    &.notice {
      background: url('/image/notice_bg.png') no-repeat center;
      background-size: contain;
      span {
        color: #CA8F30;
      }
    }
    &.offline {
      background: url('/image/offline_bg.png') no-repeat center;
      background-size: contain;
      span {
        color: #b1c6d5;
      }
    }
  }
  .popover {
    .item {
      line-height: 20px;
      margin-top: 10px;
      padding: 4px;
      &:hover {
        color: #2294fe;
        cursor: pointer;
        background-color: #27374e;
      }
      &:first-child {
        margin-top: 0;
      }
      .num {
        font-size: 16px;
        margin-left: 10px;
        width: 30px;
        text-align: center;
      }
    }

  }
</style>
