/**
 常州中以国际创新村  对接  广播数据
 功能:
 运行: node src/paSpider.js [buildingId] [dbName] [debug]

 字典 building_config_1 中增加配置
 pa_server：   服务器地址，
 pa_username： 用户名
 pa_password： 密码
 pa_collectorId： 采集器id


 // 1分钟执行一次
 syncDeviceData(); -> 同步所有数据

 // 1小时重启一次


 */

// create an empty modbus client
const schedule = require("node-schedule");
var moment = require("moment");

const Db = require("./mydb");
const SerApi = require("./lib/paApi");
const helper = require("./helper");

const sysConfig = require("./conf/sysConfig").sysConfig();
const config = {};

const buildingId = process.argv[2] > 0 ? process.argv[2] : 1;
const dbName = process.argv[3] && process.argv[3] != "debug" ? process.argv[3] : null;
global.isDebug = process.argv[4] == "debug" ? true : false;

// 参数替换 覆盖之前的配置
if (dbName) {
	sysConfig.mysql.database = dbName;
}

var _db = new Db(sysConfig.mysql);
// 双数据库同步
if (typeof sysConfig.mysql2 != "undefined") {
	_db.createServer2(sysConfig.mysql2);
}

// i接口实例，启动加载配置后，初始化
let _pxServ = null;

var pxConfigSql = `
    select
        *
    from sys_dict_data
    where dict_type = ? and dict_label like "pa_%";
`;
var codeSql = `
    select
        code
    from a_item
    where collector_id = ?;
`;

var dbUpdateSql = `
    SELECT
        i.code,
        i.alias,
        i.description,
        i.name,
        d.id as item_data_id,
        d.name as item_data_name,
        d.val as item_data_val,
        ifnull(d.max_val, '') max_val,
        ifnull(d.min_val, '') min_val,
        p.func_list,
        p.func
    FROM a_collector c
    LEFT JOIN a_item i on c.id = i.collector_id
    LEFT JOIN a_item_data d on i.id = d.item_id
    LEFT JOIN a_point_table p on d.id = p.item_data_id
    where c.id = ?
`;

var deviceWSql = `
    select
        p.*,
        it.item_id,
        it.val,
        it.has_sync,
        it.locked,
        i.code
    from a_point_table p
    LEFT JOIN a_item_data it on it.id=p.item_data_id
    LEFT join a_item i on it.item_id = i.id
    where p.collector_id = ? and p.type = ?
    and it.has_sync = ?
    and it.item_id is not null
    order by p.addr
`;

// 实时任务
var realtimeTaskSql = {
	check: "select * from d_pa_real_time_task where taskId=?",
	insert: "INSERT INTO d_pa_real_time_task (taskId, taskGuid, taskName, tiggerDeviceno, medioOrg, taskStatus, createTime, updateTime, sourceId, ttsvolume, ttsrate, videodevice, jsonpath, synchronousControl, volumeSetup, taskLevel, wholeSong, noTimeLimit, custom, wholeHour, wholeMinute, wholeSecond, mediaId, speechContent, userName, userId, taskContent, presskeydown, executetaskafter, deletetaskafter, deletefileafter, voltage1, voltage2, voltage3, voltage4, outputport1, outputport2, outputport3, outputport4) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
	update: `UPDATE d_pa_real_time_task SET taskGuid = ?,taskName = ?,tiggerDeviceno = ?,medioOrg = ?,taskStatus = ?,createTime = ?,updateTime = ?,sourceId = ?,ttsvolume = ?,ttsrate = ?,videodevice = ?,jsonpath = ?,synchronousControl = ?,volumeSetup = ?,taskLevel = ?,wholeSong = ?,noTimeLimit = ?,custom = ?,wholeHour = ?,wholeMinute = ?,wholeSecond = ?,mediaId = ?,speechContent = ?,userName = ?,userId = ?,taskContent = ?,presskeydown = ?,executetaskafter = ?,deletetaskafter = ?,deletefileafter = ?,voltage1 = ?,voltage2 = ?,voltage3 = ?,voltage4 = ?,outputport1 = ?,outputport2 = ?,outputport3 = ?,outputport4 = ? WHERE taskId = ?`,
};
// 计划任务列表
var planTaskSql = {
	check: "select * from d_pa_plan_task where planId=?",
	insert: "INSERT INTO d_pa_plan_task (planId, planName, planStartTime, planEndTime, planLevel, planStatus, createTime, modifyTime, jsonpath, userId) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
	update: `UPDATE d_pa_plan_task SET planName = ?, planStartTime = ?, planEndTime = ?, planLevel = ?, planStatus = ?, createTime = ?, modifyTime = ?, jsonpath = ?, userId = ? where planId = ?`,
};

//联动任务列表
var linkageTaskSql = {
	check: "select * from d_pa_link_age_task where id=?",
	insert: "INSERT INTO d_pa_link_age_task (id, taskGuid,eventTaskName, tiggerDeviceno, medioOrg, implementType, createTime, updateTime, sourceId, ttsvolume, ttsrate, videodevice, jsonpath, synchronousControl, volumeSetup, taskLevel, wholeSong, noTimeLimit, custom, wholeHour, wholeMinute, wholeSecond, mediaId, speechContent, userName, userId, taskContent, presskeydown, executetaskafter, deletetaskafter, deletefileafter, voltage1, voltage2, voltage3, voltage4, outputport1, outputport2, outputport3, outputport4) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
	update: `UPDATE d_pa_link_age_task SET taskGuid = ?,eventTaskName = ?,tiggerDeviceno = ?,medioOrg = ?,implementType = ?,createTime = ?,updateTime = ?,sourceId = ?,ttsvolume = ?,ttsrate = ?,videodevice = ?,jsonpath = ?,synchronousControl = ?,volumeSetup = ?,taskLevel = ?,wholeSong = ?,noTimeLimit = ?,custom = ?,wholeHour = ?,wholeMinute = ?,wholeSecond = ?,mediaId = ?,speechContent = ?,userName = ?,userId = ?,taskContent = ?,presskeydown = ?,executetaskafter = ?,deletetaskafter = ?,deletefileafter = ?,voltage1 = ?,voltage2 = ?,voltage3 = ?,voltage4 = ?,outputport1 = ?,outputport2 = ?,outputport3 = ?,outputport4 = ? WHERE id = ?`,
};
// 终端列表;
var deviceListSql = {
	check: "select * from d_pa_device_list where id=?",
	insert: "INSERT INTO d_pa_device_list (id, deviceNo, deviceNoStr, name, ip, port, devicePwd, latitude, longitude,pid, delFlag, login, deviceLogin, isVideo, ultrasound, addressconflict,talkSponsor, devtalktype, callkeypresstype, keypressPriority, priority,broadcastsponsordatatype, tasktype, zcusNo, ids, meeting, monitor, broadcast,talking, taskcontent, volume, peerAddress, parent, drag, open, createTime, modifyTime) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
	update: `UPDATE d_pa_device_list SET deviceNo = ?,deviceNoStr = ?,name = ?,ip = ?,port = ?,devicePwd = ?,latitude = ?,longitude = ?,pid = ?,delFlag = ?,login = ?,deviceLogin = ?,isVideo = ?,ultrasound = ?,addressconflict = ?,talkSponsor = ?,devtalktype = ?,callkeypresstype = ?,keypressPriority = ?,priority = ?,broadcastsponsordatatype = ?,tasktype = ?,zcusNo = ?,ids = ?,meeting = ?,monitor = ?,broadcast = ?,talking = ?,taskcontent = ?,volume = ?,peerAddress = ?,parent = ?,drag = ?,open = ?,createTime = ?,modifyTime = ? WHERE id = ?`,
};
// 历史记录列表
var recordHistorySql = {
	maxTime: "select max(createTime) as maxTime from d_pa_record_history",
	check: "select * from d_pa_record_history where id=?",
	insert: "INSERT INTO d_pa_record_history (id, userId, type, sender, receiver, sdeviceNo, rdeviceNo, msg, createTime,jsonpath) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
	update: null,
};

// 全局变量
var gtmp = {};
var dbDataList = [];
var codeList = [];

// 读取配置 获取对应配置
async function readApiConfig() {
	if (gtmp.readApiConfig) {
		// 任务未执行完成, 等待下次轮训
		return false;
	}
	// 初始化服务
	gtmp.readApiConfig = true;
	try {
		let list = await _db.doSql(pxConfigSql, ["building_config_" + buildingId]);
		if (list.length > 0) {
			for (let i = 0; i < list.length; i++) {
				let conf = list[i];
				if (conf.dict_label == "pa_server") {
					config.server = conf.dict_value;
				}
				if (conf.dict_label == "pa_username") {
					config.username = conf.dict_value;
				}
				if (conf.dict_label == "pa_password") {
					config.password = conf.dict_value;
				}
				if (conf.dict_label == "pa_collectorId") {
					config.collectorId = conf.dict_value;
				}
			}
		}
		helper.debug("read db config", config);
		// 实例化接口服务
		_pxServ = new SerApi.SerApi(config);

		await _pxServ.getToken();
	} catch (e) {
		console.trace(e);
		helper.info("readApiConfig error", e.message);
		gtmp.readApiConfig = false;
		process.exit(1);
	}
	gtmp.readApiConfig = false;
}

async function getData() {
	dbDataList = await _db.doSql(dbUpdateSql, [config.collectorId]);
	codeList = await _db.doSql(codeSql, [config.collectorId]);
	syncRealtimeTask();
	syncPlanTask();
	syncLinkageTask();
	syncDeviceList();
	syncRecordHistory();
}
// 实时任务
async function syncRealtimeTask() {
	if (gtmp.syncRealtimeTask) {
		// 任务未执行完成, 等待下次轮训
		return false;
	}
	// 初始化服务
	gtmp.syncRealtimeTask = true;
	try {
		// 调用 api 获取数据
		let apiDataRes = await _pxServ.realtimeTask();
		let apiData = (apiDataRes.page && apiDataRes.page.list) || [];
		for (let i = 0; i < apiData.length; i++) {
			let d = apiData[i];
			let _res = await _db.insertOrUpdate(realtimeTaskSql, {
				check: [d.taskId],
				insert: dealVals([
					d.taskId,
					d.taskGuid,
					d.taskName,
					d.tiggerDeviceno,
					d.medioOrg,
					d.taskStatus,
					moment(d.createTime).format("YYYY-MM-DD HH:mm:ss"),
					moment(d.updateTime).format("YYYY-MM-DD HH:mm:ss"),
					d.sourceId,
					d.ttsvolume,
					d.ttsrate,
					d.videodevice,
					d.jsonpath,
					d.synchronousControl,
					d.volumeSetup,
					d.taskLevel,
					d.wholeSong,
					d.noTimeLimit,
					d.custom,
					d.wholeHour,
					d.wholeMinute,
					d.wholeSecond,
					d.mediaId,
					d.speechContent,
					d.userName,
					d.userId,
					d.taskContent,
					d.presskeydown,
					d.executetaskafter,
					d.deletetaskafter,
					d.deletefileafter,
					d.voltage1,
					d.voltage2,
					d.voltage3,
					d.voltage4,
					d.outputport1,
					d.outputport2,
					d.outputport3,
					d.outputport4,
				]),
				update: dealVals([
					d.taskGuid,
					d.taskName,
					d.tiggerDeviceno,
					d.medioOrg,
					d.taskStatus,
					moment(d.createTime).format("YYYY-MM-DD HH:mm:ss"),
					moment(d.updateTime).format("YYYY-MM-DD HH:mm:ss"),
					d.sourceId,
					d.ttsvolume,
					d.ttsrate,
					d.videodevice,
					d.jsonpath,
					d.synchronousControl,
					d.volumeSetup,
					d.taskLevel,
					d.wholeSong,
					d.noTimeLimit,
					d.custom,
					d.wholeHour,
					d.wholeMinute,
					d.wholeSecond,
					d.mediaId,
					d.speechContent,
					d.userName,
					d.userId,
					d.taskContent,
					d.presskeydown,
					d.executetaskafter,
					d.deletetaskafter,
					d.deletefileafter,
					d.voltage1,
					d.voltage2,
					d.voltage3,
					d.voltage4,
					d.outputport1,
					d.outputport2,
					d.outputport3,
					d.outputport4,
					d.taskId,
				]),
			});
			helper.debug(_res);
		}
	} catch (e) {
		helper.info("syncRealtimeTask error", e.message);
		gtmp.syncRealtimeTask = false;
	}
	gtmp.syncRealtimeTask = false;
}

// 计划任务列表
async function syncPlanTask() {
	if (gtmp.syncPlanTask) {
		// 任务未执行完成, 等待下次轮训
		return false;
	}
	// 初始化服务
	gtmp.syncPlanTask = true;
	try {
		// 调用 api 获取数据
		let apiDataRes = await _pxServ.planTask();
		let apiData = (apiDataRes.page && apiDataRes.page.list) || [];
		for (let i = 0; i < apiData.length; i++) {
			let d = apiData[i];
			let _res = await _db.insertOrUpdate(planTaskSql, {
				check: [d.planId],
				insert: dealVals([d.planId, d.planName, d.planStartTime, d.planEndTime, d.planLevel, d.planStatus, moment(d.createTime).format("YYYY-MM-DD HH:mm:ss"), moment(d.modifyTime).format("YYYY-MM-DD HH:mm:ss"), d.jsonpath, d.userId]),
				update: dealVals([d.planName, d.planStartTime, d.planEndTime, d.planLevel, d.planStatus, moment(d.createTime).format("YYYY-MM-DD HH:mm:ss"), moment(d.modifyTime).format("YYYY-MM-DD HH:mm:ss"), d.jsonpath, d.userId, d.planId]),
			});
			helper.debug(_res);
		}
	} catch (e) {
		helper.info("syncPlanTask error", e.message);
		gtmp.syncPlanTask = false;
	}
	gtmp.syncPlanTask = false;
}

// 联动任务列表
async function syncLinkageTask() {
	if (gtmp.syncLinkageTask) {
		// 任务未执行完成, 等待下次轮训
		return false;
	}
	// 初始化服务
	gtmp.syncLinkageTask = true;
	try {
		// 调用 api 获取数据
		let apiDataRes = await _pxServ.linkageTask();
		let apiData = (apiDataRes.page && apiDataRes.page.list) || [];
		for (let i = 0; i < apiData.length; i++) {
			let d = apiData[i];
			let _res = await _db.insertOrUpdate(linkageTaskSql, {
				check: [d.id],
				insert: dealVals([
					d.id,
					d.taskGuid,
					d.eventTaskName,
					d.tiggerDeviceno,
					d.medioOrg,
					d.implementType,
					moment(d.createTime).format("YYYY-MM-DD HH:mm:ss"),
					moment(d.updateTime).format("YYYY-MM-DD HH:mm:ss"),
					d.sourceId,
					d.ttsvolume,
					d.ttsrate,
					d.videodevice,
					d.jsonpath,
					d.synchronousControl,
					d.volumeSetup,
					d.taskLevel,
					d.wholeSong,
					d.noTimeLimit,
					d.custom,
					d.wholeHour,
					d.wholeMinute,
					d.wholeSecond,
					d.mediaId,
					d.speechContent,
					d.userName,
					d.userId,
					d.taskContent,
					d.presskeydown,
					d.executetaskafter,
					d.deletetaskafter,
					d.deletefileafter,
					d.voltage1,
					d.voltage2,
					d.voltage3,
					d.voltage4,
					d.outputport1,
					d.outputport2,
					d.outputport3,
					d.outputport4,
				]),
				update: dealVals([
					d.taskGuid,
					d.eventTaskName,
					d.tiggerDeviceno,
					d.medioOrg,
					d.implementType,
					moment(d.createTime).format("YYYY-MM-DD HH:mm:ss"),
					moment(d.updateTime).format("YYYY-MM-DD HH:mm:ss"),
					d.sourceId,
					d.ttsvolume,
					d.ttsrate,
					d.videodevice,
					d.jsonpath,
					d.synchronousControl,
					d.volumeSetup,
					d.taskLevel,
					d.wholeSong,
					d.noTimeLimit,
					d.custom,
					d.wholeHour,
					d.wholeMinute,
					d.wholeSecond,
					d.mediaId,
					d.speechContent,
					d.userName,
					d.userId,
					d.taskContent,
					d.presskeydown,
					d.executetaskafter,
					d.deletetaskafter,
					d.deletefileafter,
					d.voltage1,
					d.voltage2,
					d.voltage3,
					d.voltage4,
					d.outputport1,
					d.outputport2,
					d.outputport3,
					d.outputport4,
					d.id,
				]),
			});
			helper.debug(_res);
		}
	} catch (e) {
		helper.info("syncLinkageTask error", e.message);
		gtmp.syncLinkageTask = false;
	}
	gtmp.syncLinkageTask = false;
}

// 终端列表
async function syncDeviceList() {
	if (gtmp.syncDeviceList) {
		// 任务未执行完成, 等待下次轮训
		return false;
	}
	// 初始化服务
	gtmp.syncDeviceList = true;
	try {
		// 调用 api 获取数据
		let apiDataRes = await _pxServ.deviceList();
		let apiData = (apiDataRes.page && apiDataRes.page.list) || [];
		for (let i = 0; i < apiData.length; i++) {
			let d = apiData[i];
			let _res = await _db.insertOrUpdate(deviceListSql, {
				check: [d.id],
				insert: dealVals([
					d.id,
					d.deviceNo,
					d.deviceNoStr,
					d.name,
					d.ip,
					d.port,
					d.devicePwd,
					d.latitude,
					d.longitude,
					d.pid,
					d.delFlag,
					d.login,
					d.deviceLogin,
					d.isVideo,
					d.ultrasound,
					d.addressconflict,
					d.talkSponsor,
					d.devtalktype,
					d.callkeypresstype,
					d.keypressPriority,
					d.priority,
					d.broadcastsponsordatatype,
					d.tasktype,
					d.zcusNo,
					d.ids,
					JSON.stringify(d.meeting),
					JSON.stringify(d.monitor),
					JSON.stringify(d.broadcast),
					JSON.stringify(d.talking),
					d.taskcontent,
					JSON.stringify(d.volume),
					JSON.stringify(d.peerAddress),
					d.parent,
					d.drag,
					d.open,
					moment(d.createTime).format("YYYY-MM-DD HH:mm:ss"),
					moment(d.modifyTime).format("YYYY-MM-DD HH:mm:ss"),
				]),
				update: dealVals([
					d.deviceNo,
					d.deviceNoStr,
					d.name,
					d.ip,
					d.port,
					d.devicePwd,
					d.latitude,
					d.longitude,
					d.pid,
					d.delFlag,
					d.login,
					d.deviceLogin,
					d.isVideo,
					d.ultrasound,
					d.addressconflict,
					d.talkSponsor,
					d.devtalktype,
					d.callkeypresstype,
					d.keypressPriority,
					d.priority,
					d.broadcastsponsordatatype,
					d.tasktype,
					d.zcusNo,
					d.ids,
					JSON.stringify(d.meeting),
					JSON.stringify(d.monitor),
					JSON.stringify(d.broadcast),
					JSON.stringify(d.talking),
					d.taskcontent,
					JSON.stringify(d.volume),
					JSON.stringify(d.peerAddress),
					d.parent,
					d.drag,
					d.open,
					moment(d.createTime).format("YYYY-MM-DD HH:mm:ss"),
					moment(d.modifyTime).format("YYYY-MM-DD HH:mm:ss"),
					d.id,
				]),
			});
			helper.debug(_res);
		}
	} catch (e) {
		helper.info("syncDeviceList error", e.message);
		gtmp.syncDeviceList = false;
	}
	gtmp.syncDeviceList = false;
}

async function syncRecordHistory() {
	helper.debug("syncRecordHistory start");
	try {
		let dbLast = await _db.doSql(recordHistorySql.maxTime);
		let startTime = "2025-01-01 00:00:00"; // 默认只抓25年后的
		if (dbLast && dbLast.length && dbLast[0].maxTime) {
			startTime = moment(dbLast[0].maxTime).format("YYYY-MM-DD HH:mm:ss");
		}
		let endTime = moment().format("YYYY-MM-DD HH:mm:ss");
		let apiDataRes = await _pxServ.recordHistory(startTime, endTime);
		let apiData = (apiDataRes.page && apiDataRes.page.list) || [];
		for (let i = 0; i < apiData.length; i++) {
			let d = apiData[i];
			let _res = await _db.insertOrUpdate(recordHistorySql, {
				check: [d.id],
				insert: dealVals([d.id, d.userId, d.type, d.sender, d.receiver, d.sdeviceNo, d.rdeviceNo, d.msg, moment(d.createTime).format("YYYY-MM-DD HH:mm:ss"), d.jsonpath]),
				update: [],
			});
			helper.debug(_res);
		}
	} catch (e) {
		helper.debug("syncRecordHistory error ", e);
	}
	helper.debug("syncRecordHistory finished");
}

// 音量数据下发
async function syncDbWriteToDevice() {
	if (gtmp.syncDbWriteToDevice) {
		// 任务未执行完成, 等待下次轮训
		return false;
	}
	gtmp.syncDbWriteToDevice = true;
	try {
		var points = await _db.doSql(deviceWSql, [config.collectorId, "w", "N"]);
		helper.debug("syncDbWriteToDevice", points.length);
		if (points.length > 0) {
			for (let i = 0; i < points.length; i++) {
				try {
					let d = points[i];
					let data = {
						ids: d.code,
						volume: { setting: d.val },
					};
					let sres = await _pxServ.devicebatchUpdateFoOthers(data);
					if (sres.code == 0) {
						let msg = { id: d.item_data_id, val: d.val };
						let msg2 = { id: d.item_data_id - 1, val: d.val };
						if (d.locked > 0) {
							await _db.updateDataWithoutHasSync(msg);
							await _db.updateData(msg2);
						} else {
							await _db.updateData(msg);
							await _db.updateData(msg2);
						}
					} else {
						helper.debug("sres", sres);
					}
				} catch (e) {
					helper.info(e.message);
				}
			}
		}
	} catch (e) {
		console.trace(e);
		helper.info("syncDbWriteToDevice error", e.message);
		gtmp.syncDbWriteToDevice = false;
	}
	gtmp.syncDbWriteToDevice = false;
}

async function syncDeviceData() {
	if (gtmp.syncDeviceData) {
		// 任务未执行完成, 等待下次轮训
		return false;
	}
	// 初始化服务
	gtmp.syncDeviceData = true;
	try {
		if (codeList.length > 0) {
			for (let i = 0; i < codeList.length; i++) {
				let apiDataRes = await _pxServ.deviceInfo(codeList[i].code);
				let data = (apiDataRes && apiDataRes.device) || {};
				if (dbDataList.length) {
					for (let k = 0; k < dbDataList.length; k++) {
						let cp = dbDataList[k];
						if (data.deviceNo == cp.code && data.hasOwnProperty(cp.func)) {
							let val = data[cp.func];
							if (cp.func == "volume") {
								let volume = val && JSON.parse(val);
								let setting = volume.setting || '';
								let vals = setting.split("|");
								val = vals.length && vals[1];
							}
							// 更新数据点位
							helper.debug("[sync to db]", cp.item_data_id, val);
							// 统一数据入库
							await helper.syncItemData2Db({
								id: cp.item_data_id,
								val: val,
								updatedAt: moment().format("YYYY-MM-DD HH:mm:ss"),
							});
						} else {
							// helper.debug("no.........",listItem);
						}
					}
				}
			}
		}
	} catch (e) {
		console.trace(e);
		helper.info("syncDeviceData error", e.message);
		gtmp.syncDeviceData = false;
	}
	gtmp.syncDeviceData = false;
}

// 入库前数据处理
function dealVals(list) {
	return list.map(v => {
		if (typeof v == "undefined") {
			return null;
		}
		return v;
	});
}
async function start() {
	// 每1秒一次
	schedule.scheduleJob("*/1 * * * * *", () => {
		// syncDbWriteToDevice();
		//helper.log('syncDbWriteToDevice success');
	});
	// 1分钟执行一次    30 * * * * *
	schedule.scheduleJob("1 */1 * * * *", () => {
		syncDeviceData();
		helper.log("syncDeviceData success");
	});
	// 半小时执行一次
	schedule.scheduleJob("1 */30 * * * *", () => {
		getData();
		helper.log("generaterEnergyData success");
	});
	// 1小时执行一次
	schedule.scheduleJob("1 1 * * * *", () => {
		helper.log("Auto restart server");
		process.exit(1);
	});

	// 数据服务初始化
	helper.initDataServer(sysConfig);
	helper.info("initDataServer success");

	// 日志服务连接至远程logstash
	helper.connectLogstash(sysConfig);
	helper.info("connectLogstash success");

	await readApiConfig();
	await getData();
	await syncDeviceData();
	helper.info("syncDeviceData success");
	helper.info("start success");
}

start();
