
import DeviceSummary from "./DeviceSummary";
import DeviceModeSummary from "./DeviceModeSummary";
import DisplayGlobalControl from "./DisplayGlobalControl";
import DeviceResoureEdit from "./DeviceResoureEdit";

// import baseDialog from "./components/deviceDialog";
// import cameraDialog from "./components/dialog/cameraDialog.vue";          // 普通 iframe 视频
// import cameraWxhgDialog from "./components/dialog/cameraWxhgDialog.vue";  // 无锡海归雪浪 大华 h264 视频流

import baseCard from "./components/card/BaseMultNew";
// import lightCard from "./components/card/light";
// import sensorSmokeCard from "./components/card/sensorSmoke";
import lightCard from "./components/card/light2";
import fanCard from "./components/card/fan";
import fan2Card from "./components/card/fan2";
import elevatorCard from "./components/card/elevator2";
import airconditionCard from "./components/card/airCondition2";
import aircondition2Card from "./components/card/airCondition3";
import cameraCard from "./components/card/camera";
import cameraWxhgCard from "./components/card/cameraWxhg";
// import elevatorCard from "./components/card/elevator";

import baseIcon from "./components/mapIcon/BaseMult";
import cameraIcon from "./components/mapIcon/camera";
import bathRoomIcon from "./components/map/bathRoom";
import DT10Icon from "./components/mapIcon/dtIcon";   // 电梯
// import sensorSmokeIcon from "./components/map/sensorSmoke";

import DisplayBaseFunc from "./DisplayBaseFunc";

// 设备详情大弹框，包含复制内容
import MultDeviceDetail from "@/views/device/monitor/components/Device/components/dialog/deviceDetailMult.vue";

export default {
  props: {
    "buildingId": {
      type: [String, Number],
      default: 1,
    },
    "searchKey": {
      type: String,
      default: "",
    },
    "category": {
      type: String,
      default: "楼层",
    },
    "type": {
      type: String,
      default: null,
    },
    "deviceType": {
      type: String,
      default: "",
    },
    "deviceMainType": {
      type: String,
      default: "",
    },
    "cellHeight": {
      type: String,
      default: "",
      desc: "单个设备块高度"
    },
    "globalVal": {
      type: Object,
      default: () => {},
    },
    "showImages": {
      type: [String, Number],
      default: "0",
      desc: "设备是否启用原理图+现场图标签"
    },
    "hasLock": {
      type: [String, Number],
      default: "0",
      desc: "设备是否可锁定"
    },
    "hasEnergy": {
      type: [String, Number],
      default: "0",
      desc: "设备详情是否有用能模块"
    },
    "hasWarning": {
      type: [String, Number],
      default: "0",
      desc: "设备详情是否展示报警管理"
    },
    "hasDeviceInfo": {
      type: [String, Number],
      default: "0",
      desc: "设备详情是否展示设备信息"
    },
    "hasMaintenance": {
      type: [String, Number],
      default: "0",
      desc: "设备详情是否展示运维相关tab"
    },
    "hasEleProspection": {
      type: [String, Number],
      default: "0",
      desc: "电气设备是否显示"
    },
    "hasCOLink": {
      type: [String, Number],
      default: "0",
      desc: "CO联动是否显示"
    },
    "summaryType":{
      type: String,
      default: "device",
      desc: "设备统计模块显示内容, device/sensor"
    },
    "displayKeys": {
      type: Array,
      default: () => { return [] },
      desc: "卡片上显示的三个属性值，如果没有，则按oid取前3个"
    },
    "refreshSpan": {
      default: null,
      desc: "定时刷新数据间隔, 单位秒"
    },
    "orderBy": {
      default: "id",
      desc: "排序"
    },
    "showTabs": {
      type: Boolean,
      default: true,
      desc: "是否显示周边控制框"
    },
    "viewDetail": {
      type: String,
      default: "viewDetail",
      desc: "设备详情跳转模式, dialog(基本弹框), prototype(跳转组态)"
    },
    "protoTypeDisplay": {
      type: String,
      default: "map",
      desc: "设备弹框详情，跳转组态图类型, map, by, 3d, mult"
    },
    "dialogType": {
      type: String,
      default: "",
      desc: "设备弹框详情样式，复杂样式:mult, 复杂样式(楼控专用):multBc"
    },
    "cardDataNum": {
      type: Number,
      default: 3,
      desc: "卡片形式下数据展示个数"
    },
    "cardType": {
      type: String,
      default: '',
      desc: "设备卡片的展示形式"
    },
    "forceUpdate": {
      type: [String, Number],
      default: 0,
      desc: "是否强制下发指令(即使数据没变化，也会下发指令)"
    },
    "issueTimeoutSetInSecond": {
      type: Array,
      default: () => { return [] },
      desc: "设定多次下发指令的时间间隔，未设定则只执行一次"
    },
    "hasEditPosition": {
      type: [String, Number],
      default: 0,
      desc: "编辑位置是否显示"
    },
    hideMonitoringDetails: {
      type: Boolean,
      default: false,
      desc: "隐藏监控详情模块"
    },
    defaultPageSize: {
      type: Number | String,
      default: 10,
      desc: "默认分页页长"
    },
    defaultSortBy: {
      type: String,
      default: "",
      desc: "默认排序条件"
    },
    groupControlIssue: {
      type: String,
      default: "",
      desc: "群控下发提示语句"
    },
    listMoreFieldsNum: {
      type: Number,
      default: 6,
      desc: "列表展示时 表格选项默认展示的字段数"
    }
  },
  watch: {
    refreshSpan: {
      immediate: true,
      handler(newValue,oldValue){
        console.log("===========> refreshSpan ", this.refreshSpan);
        this.updateAutoRefresh();
      }
    }
  },
  mixins: [DisplayBaseFunc],
  components: {
    // 汇总统计
    DeviceSummary,
    DeviceModeSummary,
    DisplayGlobalControl,
    DeviceResoureEdit, // 编辑分组种的设备
    // 弹框
    // baseDialog,
    // cameraDialog,
    // cameraWxhgDialog,
    // 列表
    baseCard,
    airconditionCard,
    aircondition2Card,
    lightCard,
    fanCard,
    fan2Card,
    elevatorCard,
    // sensorSmokeCard,
    cameraCard,
    cameraWxhgCard,
    // elevatorCard,
    // 图标
    baseIcon,
    cameraIcon,
    bathRoomIcon,
    // sensorSmokeIcon,

    MultDeviceDetail,
    DT10Icon
  },
  data() {
    return {
      isShow: true,
      // 加载状态
      loading: false,
      loadNum: 0,
      // 加载文字
      loadingText: '',

      showFlag: true,  // 某些需要刷新的组件标记

      curBuilding: this.gf.getCurBuilding(),
      user: this.$store.state.user,

      // 所有资源
      resourceFullList: [],
      resourceFullMap: {},
      // 所有绑定资源的设备
      resourceDeviceList: [],
      // 资源分组
      activeResourceGroupBak: "", // 判断tab是否切换
      activeResourceGroup: "",
      resourceGroupList: [],
      // 某个分组下的资源
      resourceList: [],

      // 选中的资源
      activeResourceInd: -1,
      activeResourceId: "",
      activeResource: {},

      // 设备分类列表
      deviceTypeList: [],
      deviceMainTypeList: [],

      // 设备列表
      deviceList: [],
      deviceTotal: 0,
      deviceSummary: this.gf.deviceSummary(),
      deviceStatusMap: this.gf.deviceStatusMap(),
      pageNum: 1,
      pageSize: 10,
      // 被激活的 summary Tab 标签
      curSummaryTabActive: "total",

      // 设备详情
      activeDeviceInd: -1,
      activeDevice: { id: -1 },
      activeDeviceSummaryShow: false,  // 小弹框
      activeDeviceDetailShow: false,   // 大弹框
      activeDevicePrototypeShow: false, // 系统图弹框

      // 设备开关
      globalSwichTypes: {},

      // 设备控制模式列表
      strategyList: [],
      strategyOpts: [],

      // 是否编辑状态
      isEdit: false,
      // 暂存当前设备原始数据
      deviceCopy: {},

      // 设备类型字典
      typeOptions: [],

      // 用时，用能图表
      from: this.$moment().add(-15, "day").format("YYYY-MM-DD"),
      to: this.$moment().format("YYYY-MM-DD"),
      deviceData: {
        runHour: {
          legend: [],
          date: [],
          series: [],
        },
        useEnergy: {
          legend: [],
          date: [],
          series: [],
        }
      },

      // 所有全选
      ct: "", // 隐藏 checkbox 文字
      // 所有大楼全选
      checkAll: false,
      isAllIndeterminate: false,
      // 选中的楼层
      checkedBuildings: [],
      checkedBuildingsCache: [], // 缓存上一次的值

      // 单个楼层全选
      buildingChecked: {
        // "1号楼": {
        //   isIndeterminate: false,
        //   checkAll: true,
        //   checkedFloors: [],
        // },
      },
      // 资源选中设备chech, key 是 string 类型
      deviceChecked: {
        // "1": {
          //   isIndeterminate: false,
          //   checkAll: true,
          //   checkedDevices: [],
        // }
      },
      // 无用变量，仅用来刷新 components 中 checkbox 显示状态
      noUseValue: false,
      // 刷新列表flag
      refreshFlag: false,

      // 自动刷新对象
      autoRefresh: null,

      //排序
      sortColumn: "",
      sortOrder: "asc",
      sortOptions: [
        {
          name: "设备编号",
          code: "id"
        },
        {
          name: "设备名称",
          code: "name"
        },
        {
          name: "设备编码",
          code: "description"
        },
        {
          name: "设备位置",
          code: "position"
        }
      ]
    }
  },
  computed: {
    // 筛选设备
    filterDevice() {
      let sk = this.searchKey || "";  //获取搜索关键字
      const sortColumn = this.sortColumn; // 获取排序列
      console.log("filterDevice", sortColumn, this.deviceList);
      let list = this.deviceList;
      if(this.curSummaryTabActive) {
        list = this.deviceSummary[this.curSummaryTabActive];
      }
      let deviceList = list.filter( d => {
        let s = d.name || "";
        return s.toLowerCase().indexOf(sk.toLowerCase()) >= 0;
      });
      // 前端处理排序
      if (sortColumn) {
        deviceList.sort((a, b) => {
          if (this.sortOrder === 'asc') {
            return a[sortColumn] > b[sortColumn] ? 1 : -1;
          } else {
            return a[sortColumn] < b[sortColumn] ? 1 : -1;
          }
        });
      }
      this.deviceTotal = deviceList.length;
      return deviceList.slice(this.pageSize*(this.pageNum-1), this.pageSize*(this.pageNum));
    }
  },
  mounted() {
    // 检查 deviceType 是否包含多个
    this.deviceTypeList = this.deviceType.split(",");
    this.deviceMainTypeList = this.deviceMainType.split(","); // 主设备类型
    this.initPage();
    window._this = this;
    //字典默认设置
    this.sortColumn = this.defaultSortBy;
  },
}
