<template>
	<view class="list">
		<view class="item" v-for="item in videoArr" :key="item.channel" v-if="accessToken">
			<view class="title">{{ item.name }}</view>
			<view class="cont" @click="goDetail(item.channel)" v-if="mode === 'single'">
				<image class="cont_bg" :src="`${api}/image/miniStatic/video_bg.png`" mode="aspectFill"></image>
				<image class="cont_icon" src="@/static/video_play.png" mode="aspectFit"></image>
			</view>

			<!-- 插件播放  多窗口同时播放  吃带宽 
			 带宽不够只个别通道可播放 
			 可实现为跳转播放页面单个播放-->
			<ezplayer
				v-if="mode === 'multi'"
				:id="`ezplayer_${item.channel}`"
				:key="item.channel"
				:accessToken="accessToken"
				:url="`rtmp://open.ys7.com/${deviceSerial}/${item.channel}/live`"
				recPlayTime=""
				width="360"
				height="300"
				:themeData="themeData"
			/>
		</view>
	</view>

</template>

<script setup>
import { ref, onBeforeMount } from 'vue';
const api = uni.$g.VUE_APP_BASE_API;
const accessToken = ref("");
const AppKey = "142ff3f7627e4b9586767c3b1327c936";
const Secret = "98daaded69abe4b75401a668032f368e";
const deviceSerial = "FH1040114";

const mode = "single";//single、multi单窗口播放还是多窗口播放  吃带宽 带宽不够最好单通道播放
const themeData = { "poster": "", "controls": { "muteBtn": false, "fullScreenBtn": false, "hdBtn": true } }

const goDetail = (no = 1) => {
	wx.navigateToMiniProgram({
		appId: 'wxf2b3a0262975d8c2',
		path: `pages/live/live?accessToken=${accessToken.value}&deviceSerial=${deviceSerial}&channelNo=${no}`,
		success(res) {
			// 打开成功
		}
	})
}
const videoArr = [
	{
		name: "双丁路车行出入口西侧1(C042)",
		channel: 1
	},
	{
		name: "B1B2中间通道人行入口(C014)",
		channel: 2
	},
	{
		name: "A3办公楼东侧消防通道(C018)",
		channel: 5
	},
	{
		name: "智拓路车行出入口东侧2(C029)",
		channel: 6
	},
	{
		name: "A3办公楼西侧消防通道1(C020)",
		channel: 31
	}
];


const getAccessToken = () => {
	uni.request({
		url: 'https://open.ys7.com/api/lapp/token/get',
		method: 'POST',
		header: {
			"Content-Type": "application/x-www-form-urlencoded"
		},
		data: {
			appKey: AppKey,
			appSecret: Secret
		},
		success({ data = {} }) {
			accessToken.value = data.data.accessToken;
		},
		fail(err) {
			reject(err);
		},
	});
}

onBeforeMount(() => {
	getAccessToken()
})
</script>

<style lang="scss" scoped>
.list {
	background: #F5F8FA;

	.item {
		background: #fff;
		margin-top: 24rpx;
		padding: 24rpx 32rpx;

		.title {
			font-weight: 400;
			font-size: 28rpx;
			color: #2D3033;
			padding-bottom: 20rpx;
		}

		.cont {
			position: relative;
			width: 100%;
			height: 340rpx;
			border-radius: 16rpx;
			overflow: hidden;

			.cont_bg {
				width: 100%;
				height: 340rpx;
				border-radius: 16rpx;
				overflow: hidden;
			}

			.cont_icon {
				width: 90rpx;
				height: 90rpx;
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
				z-index: 1;
			}
		}

		// .ezplayer {
		// 	overflow: hidden;
		// 	display: block;
		// }
	}
}

::v-deep .ezplayer--page-live {
	width: 100% !important;
	height: 236px !important;

	.ezplayer--video-container {
		width: 100% !important;
		height: 236px !important;
		border-radius: 16rpx;
	}
}
</style>