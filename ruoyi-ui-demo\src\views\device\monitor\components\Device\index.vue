<template>
  <div class="device-summary">
    <div class="main-card">
      <h3 v-if="!hideHeader && showTabs">
        <span class="page-title"
          >{{ pageTitle ? pageTitle : "空调机组" }}（<i>{{
            deviceStatusSum[0].num
          }}</i>）</span>
        <el-tooltip v-hasPermi="['device:setting:*']" class="tools_tip" effect="dark" content="页面配置" placement="top-start">
          <i class="el-icon-s-tools" size="18" @click="showPageSettings"/>
        </el-tooltip>
        
        <span class="search-form" style="width: 200px">
          <el-input v-model="searchKey" placeholder="快速查找" clearable>
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>
        </span>
        <ResourceModelSelect :deviceType="deviceMainType" />
        <!-- <div class="status-sum">
        <div v-for="(item,index) in deviceStatusSum" :key="index" :class="{'devide' : item.devide}" :style="{color: item.color}">
          <div class="name">{{item.name}}：</div>
          <div class="num">{{item.num}}</div>
        </div>
      </div> -->
        <span class="pull-right">
          <em>
            <!-- displayTypes 支持 ["card", "list", "map", "pt", "3d", "iframe"] -->
            <el-tooltip
              effect="dark"
              placement="top"
              :content="displayTypeMap[tp].description"
              v-for="tp in displayTypes"
            >
              <el-button
                :type="displayType == tp ? 'primary' : ''"
                size="small"
                v-text="displayTypeMap[tp].name"
                @click="handleDisplayType(tp)"
              />
            </el-tooltip>
          </em>
          <el-dropdown class="dropdown_fresh" trigger="click" @command="handleCommand">
            <span class="el-dropdown-link">
              {{ getRefreshSpanName }}<i class="el-icon-refresh"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in refreshSpanOpts" :key="item.value" :command="item.value" :class="{'active' : refreshSpan == item.value}">{{item.label}}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </span>
        <el-divider></el-divider>
      </h3>

      <!-- 设备卡片展示 -->
      <DisplayByCard
        v-if="inited && displayType == 'card'"
        :buildingId="buildingId"
        :searchKey="searchKey"
        :cellHeight="cellHeight"
        :category="category"
        :type="type"
        :deviceType="deviceType"
        :deviceMainType="deviceMainType"
        :globalVal="globalVal"
        :showImages="showImages"
        :hasEnergy="hasEnergy"
        :hasWarning="hasWarning"
        :hasDeviceInfo="hasDeviceInfo"
        :hasMaintenance="hasMaintenance"
        :hasEleProspection="hasEleProspection"
        :hasCOLink="hasCOLink"
        :hasEditPosition="hasEditPosition"
        :hasLock="hasLock"
        :summaryType="summaryType"
        :displayKeys="displayKeys"
        :refreshSpan="refreshSpan"
        :orderBy="orderBy"
        :showTabs="showTabs"
        :viewDetail="viewDetail"
        :protoTypeDisplay="protoTypeDisplay"
        :dialogType="dialogType"
        :cardType="cardType"
        :cardDataNum="cardDataNum"
        :forceUpdate="forceUpdate"
        :issueTimeoutSetInSecond="issueTimeoutSetInSecond"
        :defaultPageSize="defaultPageSize"
        :defaultSortBy="defaultSortBy"
        :groupControlIssue="groupControlIssue"
      />

      <!-- 设备列表展示 -->
      <DisplayByList
        v-if="inited && displayType == 'list'"
        :buildingId="buildingId"
        :searchKey="searchKey"
        :category="category"
        :type="type"
        :deviceType="deviceType"
        :deviceMainType="deviceMainType"
        :globalVal="globalVal"
        :showImages="showImages"
        :hasEnergy="hasEnergy"
        :hasWarning="hasWarning"
        :hasDeviceInfo="hasDeviceInfo"
        :hasMaintenance="hasMaintenance"
        :hasEleProspection="hasEleProspection"
        :hasCOLink="hasCOLink"
        :hasEditPosition="hasEditPosition"
        :hasLock="hasLock"
        :summaryType="summaryType"
        :displayKeys="displayKeys"
        :refreshSpan="refreshSpan"
        :orderBy="orderBy"
        :showTabs="showTabs"
        :viewDetail="viewDetail"
        :protoTypeDisplay="protoTypeDisplay"
        :dialogType="dialogType"
        :forceUpdate="forceUpdate"
        :defaultPageSize="defaultPageSize"
        :listMoreFieldsNum="listMoreFieldsNum"
      />

      <!-- 设备 three 3D 展示 -->
      <DisplayBy3D
        v-if="inited && displayType == '3d'"
        :buildingId="buildingId"
        :searchKey="searchKey"
        :category="category"
        :type="type"
        :deviceType="deviceType"
        :deviceMainType="deviceMainType"
        :iconDisplay="iconDisplay"
        :globalVal="globalVal"
        :showImages="showImages"
        :hasEnergy="hasEnergy"
        :hasWarning="hasWarning"
        :hasDeviceInfo="hasDeviceInfo"
        :hasMaintenance="hasMaintenance"
        :hasEleProspection="hasEleProspection"
        :hasCOLink="hasCOLink"
        :hasEditPosition="hasEditPosition"
        :hasLock="hasLock"
        :summaryType="summaryType"
        :displayKeys="displayKeys"
        :refreshSpan="refreshSpan"
        :orderBy="orderBy"
        :showTabs="showTabs"
        :viewDetail="viewDetail"
        :protoTypeDisplay="protoTypeDisplay"
        :dialogType="dialogType"
        :forceUpdate="forceUpdate"
      />

      <!-- 设备 钱总 3D 展示 -->
      <DisplayByIframe
        v-if="inited && displayType == 'iframe'"
        :buildingId="buildingId"
        :searchKey="searchKey"
        :category="category"
        :type="type"
        :deviceType="deviceType"
        :deviceMainType="deviceMainType"
        :iconDisplay="iconDisplay"
        :globalVal="globalVal"
        :showImages="showImages"
        :hasEnergy="hasEnergy"
        :hasWarning="hasWarning"
        :hasDeviceInfo="hasDeviceInfo"
        :hasMaintenance="hasMaintenance"
        :hasEleProspection="hasEleProspection"
        :hasCOLink="hasCOLink"
        :hasEditPosition="hasEditPosition"
        :hasLock="hasLock"
        :summaryType="summaryType"
        :displayKeys="displayKeys"
        :refreshSpan="refreshSpan"
        :orderBy="orderBy"
        :showTabs="showTabs"
        :viewDetail="viewDetail"
        :protoTypeDisplay="protoTypeDisplay"
        :dialogType="dialogType"
        :forceUpdate="forceUpdate"
      />

      <!-- 设备地图展示 -->
      <DisplayByMap
        v-if="inited && displayType == 'map' && edit != '1'"
        :buildingId="buildingId"
        :searchKey="searchKey"
        :category="category"
        :type="type"
        :deviceType="deviceType"
        :deviceMainType="deviceMainType"
        :iconDisplay="iconDisplay"
        :mapNameShow="mapNameShow"
        :mapSwiftShow="mapSwiftShow"
        :mapSwift="mapSwift"
        :mapDeviceShow="mapDeviceShow"
        :sliderShow="sliderShow"
        :globalVal="globalVal"
        :statusKey="statusKey"
        :showImages="showImages"
        :hasEnergy="hasEnergy"
        :hasWarning="hasWarning"
        :hasDeviceInfo="hasDeviceInfo"
        :hasMaintenance="hasMaintenance"
        :hasEleProspection="hasEleProspection"
        :hasCOLink="hasCOLink"
        :hasEditPosition="hasEditPosition"
        :hasLock="hasLock"
        :summaryType="summaryType"
        :displayKeys="displayKeys"
        :refreshSpan="refreshSpan"
        :defaultScale="defaultScale"
        :legendSet="legendSet"
        :showTabs="showTabs"
        :viewDetail="viewDetail"
        :protoTypeDisplay="protoTypeDisplay"
        :dialogType="dialogType"
        :forceUpdate="forceUpdate"
        @mapEditOpen="mapEditOpen"
        :cardType="cardType"
      />

      <!-- 设备地图位置编辑状态 -->
      <DisplayByMapEdit
        v-if="inited && displayType == 'map' && edit == '1'"
        :buildingId="buildingId"
        :searchKey="searchKey"
        :category="category"
        :type="type"
        :deviceType="deviceType"
        :deviceMainType="deviceMainType"
        :iconDisplay="iconDisplay"
        :mapNameShow="mapNameShow"
        :mapSwiftShow="mapSwiftShow"
        :mapDeviceShow="mapDeviceShow"
        :sliderShow="sliderShow"
        :globalVal="globalVal"
        :showImages="showImages"
        :hasEnergy="hasEnergy"
        :hasWarning="hasWarning"
        :hasDeviceInfo="hasDeviceInfo"
        :hasMaintenance="hasMaintenance"
        :hasEleProspection="hasEleProspection"
        :hasCOLink="hasCOLink"
        :hasEditPosition="hasEditPosition"
        :statusKey="statusKey"
        :summaryType="summaryType"
        :displayKeys="displayKeys"
        :refreshSpan="refreshSpan"
        :defaultScale="defaultScale"
        :showTabs="showTabs"
        :viewDetail="viewDetail"
        :protoTypeDisplay="protoTypeDisplay"
        :dialogType="dialogType"
        :forceUpdate="forceUpdate"
        @mapEditClose="mapEditClose"
      />

      <!-- 设备原理图展示 -->
      <DisplayByPrototype
        v-if="inited && displayType == 'pt'"
        :buildingId="buildingId"
        :searchKey="searchKey"
        :cellHeight="cellHeight"
        :category="category"
        :type="type"
        :deviceType="deviceType"
        :deviceMainType="deviceMainType"
        :globalVal="globalVal"
        :showImages="showImages"
        :hasEnergy="hasEnergy"
        :hasWarning="hasWarning"
        :hasDeviceInfo="hasDeviceInfo"
        :hasMaintenance="hasMaintenance"
        :hasEleProspection="hasEleProspection"
        :hasCOLink="hasCOLink"
        :hasEditPosition="hasEditPosition"
        :hasLock="hasLock"
        :summaryType="summaryType"
        :displayKeys="displayKeys"
        :refreshSpan="refreshSpan"
        :orderBy="orderBy"
        :showTabs="showTabs"
        :viewDetail="viewDetail"
        :protoTypeDisplay="protoTypeDisplay"
        :dialogType="dialogType"
        :cardDataNum="cardDataNum"
        :forceUpdate="forceUpdate"
        :defaultScale="defaultScale"
      />

      <!-- 设备原理图 speed3d 模式 -->
      <DisplayByPrototypeSp3d
        v-if="inited && displayType == 'sp3d'"
        :buildingId="buildingId"
        :searchKey="searchKey"
        :cellHeight="cellHeight"
        :category="category"
        :type="type"
        :deviceType="deviceType"
        :deviceMainType="deviceMainType"
        :globalVal="globalVal"
        :showImages="showImages"
        :hasEnergy="hasEnergy"
        :hasWarning="hasWarning"
        :hasDeviceInfo="hasDeviceInfo"
        :hasMaintenance="hasMaintenance"
        :hasEleProspection="hasEleProspection"
        :hasCOLink="hasCOLink"
        :hasEditPosition="hasEditPosition"
        :hasLock="hasLock"
        :summaryType="summaryType"
        :displayKeys="displayKeys"
        :refreshSpan="refreshSpan"
        :orderBy="orderBy"
        :showTabs="showTabs"
        :viewDetail="viewDetail"
        :protoTypeDisplay="protoTypeDisplay"
        :dialogType="dialogType"
        :cardDataNum="cardDataNum"
        :forceUpdate="forceUpdate"
      />
      <!-- 运行模式 -->
      <!-- <MapControlCards
        class="mt20"
        v-if="deviceType"
        :deviceType="deviceMainType"
      ></MapControlCards> -->

      <!-- 添加统计按钮 -->
      <el-button
        v-if="(dataSummary.datas || []).length > 0"
        type="primary"
        class="statistics-btn"
        @click="showstatistics = true"
      >
        统计
      </el-button>

      <el-drawer
        :visible.sync="showstatistics"
        direction="rtl"
        size="383px"
        :style="{ height: '50%', marginTop: '13%' }"
        :modal="false"
        :with-header="false"
        custom-class="strategy-drawer"
      >
        <SummaryCards
          class="mt20"
          v-if="(dataSummary.datas || []).length > 0"
          :data="dataSummary"
        />
      </el-drawer>
      <!-- 添加策略按钮 -->
      <el-button
        v-if="deviceType && showcontrol"
        type="primary"
        class="strategy-btn"
        @click="showStrategy = true"
      >
        策略
      </el-button>

      <!-- 运行模式面板 -->
      <el-drawer
        :visible.sync="showStrategy"
        direction="rtl"
        size="383px"
        :style="{ height: '50%', marginTop: '18%' }"
        :modal="false"
        :with-header="false"
        custom-class="strategy-drawer"
      >
        <MapControlCards
          v-if="deviceType"
          :deviceType="deviceMainType"
        ></MapControlCards>
      </el-drawer>
    </div>
      <!-- d_device_list_display -->
      <PageSettings 
      v-if="pageSettingsFlag" 
      :dialogVisible.sync="pageSettingsFlag" 
      title="页面配置"
      :deviceType="deviceTypeCopy"
      @fresh-settings="freshPageSettings"/>
  </div>
</template>

<script>
import {
  deviceTaskList,
  updateDeviceTasks,
  deviceWarningSummary,
} from "@/api/device/apis";
import DisplayByCard from "./DisplayByCard";
import DisplayByList from "./DisplayByList2";
import DisplayByMap from "./DisplayByMap";
import DisplayBy3D from "./DisplayByThreeScene";
import DisplayByIframe from "./DisplayByIframe";
import DisplayByMapEdit from "./DisplayByMapEdit";
import DisplayByPrototype from "./DisplayByPrototype";
import DisplayByPrototypeSp3d from "./DisplayByPrototypeSp3d";
import SummaryCards from "./components/summaryCards";
import ResourceModelSelect from "./ResourceModelSelect";
import MapControlCards from "./components/mapControlCards.vue";
import { updateData, getDicts, listData } from "@/api/system/dict/data";
import PageSettings from "./components/pageSettings.vue";

export default {
  name: "Device",
  props: {},
  components: {
    DisplayByCard,
    DisplayByList,
    DisplayBy3D, // 晋总原 3d
    DisplayByIframe, // 钱总 3d
    DisplayByMap,
    DisplayByMapEdit, // 设备地图编辑器
    DisplayByPrototype, // 设备原理图模式
    DisplayByPrototypeSp3d, // 晋总新 speed3d 原理图模式 (多个设备对应同一个原理图)
    SummaryCards,
    ResourceModelSelect, // 分组模式切换
    MapControlCards,
    PageSettings
  },
  computed: {
    getRefreshSpanName () {
      const it = this.refreshSpanOpts.find(item => item.value == this.refreshSpan) || {};
      return it.label || ''
    }
  },
  data() {
    return {
      // 页面配置是否准备好
      inited: false,
      settings: this.gf.projectSettings(),
      displayTypeMap: this.gf.deviceDisplayTypeMap(), // 顶部按钮配置项
      pageTitle: "空调机组",
      displayType: "card",
      displayTypes: ["card", "list"],
      edit: 0, // 是否开启编辑状态
      searchKey: "", // 查找
      mapNameShow: false,
      mapSwiftShow: false,
      // 快捷开关值
      mapSwift: {
        active: 1,
        inactive: 0,
      },
      mapDeviceShow: true,
      // 是否显示地图的放大滑块 slider
      sliderShow: false,

      buildingId: this.gf.getBuildingId(), //建筑ID
      category: "楼层", // resource category
      type: null, // resource type
      deviceType: "airCondition", // 所有可加载的设备类别(map视图中)
      deviceMainType: "airCondition", // 主设备类别
      iconDisplay: "icon", // 模式地图上以 svg 图标显示

      statusKey: "运行状态", // 默认运行状态对应数据点位名称
      hideHeader: false, // 顶部title+菜单显示

      // 适配不同属性数量的cell块，统一显示成相同大小，防止错位
      cellHeight: "",

      displayKeys: [], // 卡片上显示的三个属性值，如果没有，则按oid取前3个
      // 汇总信息：某些页面底部需要显示统计数据
      dataSummary: {
        title: "",
      },
      showcontrol: false, //是否有控制策略
      orderBy: "", // 列表排序

      // data dict 类型会做 merge 合并，而不是替换
      globalVal: {
        // "运行": null,
        // "模式": null,
        // "风速": null,
      },

      // 定时关机总开关
      globalTaskName: "空调定时关机",
      globalTask: {
        dictCode: "",
        remark: "",
      }, // 当前任务配置
      globalTaskStatus: "0", // 全局任务状态(只要有一个status=1, 则为开启状态)

      showImages: "0", // 启用原理图+现场图标签
      hasLock: "0", // 设备带锁定功能
      hasEnergy: "0", // 设备带能耗数据显示
      hasWarning: "1", // 设备详情是否展示报警管理
      hasDeviceInfo: "1", // 设备详情是否展示设备信息
      hasMaintenance: "1", // 设备详情是否展示报修管理
      hasEleProspection: "0", // 电气设备是否显示
      hasCOLink: "0", // CO联动是否显示
      summaryType: "device", // 默认统计样式，device,sensor
      hasEditPosition: "0", // 位置编辑是否显示

      refreshSpan: null,
      refreshSpanOpts: this.gf.refreshSpanOpts(),

      defaultScale: 1, // 默认地图比例

      viewDetail: "dialog", // 默认基本详情弹框
      protoTypeDisplay: "map", // 默认打开组态详情对应组件模块
      dialogType: "", // 默认详情弹框样式， mult(复杂样式)
      cardDataNum: 3, // 卡片形式下数据展示个数
      cardType: "", // 设备卡片展示形式 light fan elevator aircondition
      legendSet: {}, // 图例设置
      forceUpdate: 0, // 是否强制下发指令(即使数据没变化，也会下发指令)
      issueTimeoutSetInSecond: [], //设定多次下发指令的时间间隔，未设定则只执行一次
      //当前类型下的设备状态数量统计
      deviceStatusSum: [
        {
          name: "设备总数",
          code: "total",
          num: 0,
          color: "#2294FE",
          devide: true,
        },
        {
          name: "运行",
          code: "run",
          by: "workStatusStop",
          num: 0,
          color: "#21BD69",
        },
        {
          name: "停止",
          code: "workStatusStop",
          num: 0,
          devide: true,
          color: "#5F7C9E",
        },
        {
          name: "健康",
          code: "warningStatusHealth",
          num: 0,
          color: "#21BD69",
        },
        {
          name: "异常",
          code: "anomaly",
          by: "warningStatusHealth",
          num: 0,
          devide: true,
          color: "#BD2D2D",
        },
        {
          name: "离线",
          code: "communicateStatusOffline",
          num: 0,
          color: "#C4C4C4",
        },
      ],
      deviceTypename: "",
      dictLabel: "",
      showStrategy: false, // 控制策略面板显示
      showstatistics: false, // 控制统计面板显示
      defaultPageSize: 10,//默认分页页长
      defaultSortBy: "",//默认排序条件
      groupControlIssue: "",//群控下发提示语句
      pageSettingsFlag: false,
      listMoreFieldsNum: 6, //列表展示时 表格选项默认展示的字段数
    };
  },
  created() {
    // 初始化设备类型，从 props 或路由解析
    // this.deviceTypename = this.type || this.$route.path.split("/").pop();
    // console.log("index created", this.deviceTypename);
    // this.queryDataFun();
    // this.getDicts("d_device_list_display").then((response) => {
    //   console.log(response.data, this.deviceMainType, "response1");

    //   this.dictLabel = JSON.parse(
    //     response.data
    //       .filter((d) => d.dictLabel === this.deviceMainType)
    //       .map((item) => item.dictValue)[0].dictValue
    //   );
    //   console.log(this.dictLabel, "this.");

    //   // this.getDeviceTask();
    // });


  },
  watch: {
    displayType(newVal, oldVal) {
      if (typeof this.cardBtnClick == "function" && newVal == "card") {
        this.cardBtnClick();
      }
    },
  },
  mounted() {
    this.initMountedPage();
  },
  methods: {
    async initMountedPage () {
      // 加载显示配置
      let that = this;
      let data = that.$data;
      let modifyData = {};
      await this.updateDeviceBaseConfig()
        .then((resp) => {
          resp.data.map((d) => {
            console.log(that.deviceMainType,"that.deviceMainType")
            if (d.dictLabel == that.deviceMainType) {
              try {
                let conf = JSON.parse(d.dictValue);
                for (let o in conf) {
                  modifyData[o] = conf[o];
                  console.log("device config", o, conf[o]);
                }
              } catch (e) {
                //pass
                console.log(
                  "d_device_list_display parse error",
                  e,
                  "val",
                  d.dictValue
                );
              }
            }
          });

          // 通过 globalVals 计算出 globalVal
          if (
            modifyData.hasOwnProperty("globalVals") &&
            modifyData.globalVals != ""
          ) {
            modifyData.globalVal = {};
            let l = modifyData.globalVals.split(",");
            l.map((k) => {
              modifyData.globalVal[k] = null;
            });
          }

          let newData = {
            ...data,
            ...modifyData,
          };
          Object.assign(this.$data, newData);
          that.inited = true; // 初始化完成后，加载具体内容
          console.log(this.deviceMainType,newData, "deviceMainType111");
          that.queryDataFun();
          that.getStatusDeviceSum();
        })
        .then(() => {
          console.log("Device.index getConfigs modifyData", modifyData);
          // pass
        });
        if (this.settings.device_refresh_span) {
          try {
            this.refreshSpanOpts = JSON.parse(this.settings.device_refresh_span);
          } catch (e) {
            // pass
          }
        }
        console.log(this.refreshSpanOpts, this.refreshSpan);
        if (!this.refreshSpan) {
          if (this.refreshSpanOpts && this.refreshSpanOpts.length > 0) {
            this.refreshSpanOpts.map((r) => {
              if (r.selected) {
                this.refreshSpan = r.value;
              }
            });
          }
        }
    },
    // 异步查询字典配置数据
    async queryDataFun() {

      // await listData({
      //   dictType: "d_device_type",
      // })
      let res = await this.gf.getDicts("d_device_type");
        // 将 deviceMainType 按逗号分割成数组
        const deviceTypes = this.deviceMainType.split(",");

        // 检查是否至少有一个设备类型的 remark 包含 strategy
        const hasStrategy = deviceTypes.some((type) => {
          const dictItem = res.data?.find((item) => item.dictValue == type);
          return dictItem?.remark?.includes("strategy");
        });

        // 如果是单个设备类型，保持原有逻辑
        if (deviceTypes.length === 1) {
          const dictItem = res.data?.find(
            (item) => item.dictValue == this.deviceMainType
          );
          this.dictLabel = dictItem?.remark || "";
          this.showcontrol = this.dictLabel.includes("strategy");
        } else {
          // 如果是多个设备类型，只要有一个包含 strategy 就显示控制
          this.showcontrol = hasStrategy;
        }
        console.log("queryDataFun", this.deviceMainType, this.showcontrol);
    },
    handleDisplayType(type) {
      if (this.displayType != type) {
        // 自定义card显示模式
        if (typeof this.cardBtnClick == "function" && type == "card") {
          this.cardBtnClick();
        } else {
          this.displayType = type;
        }
      }
    },
    handleGlobalTaskStatus() {
      this.$confirm("确定保存更改?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          updateDeviceTasks({
            ids: this.globalTask.dictValue,
            status: this.globalTaskStatus,
          }).then(() => {
            this.getDeviceTask();
          });
        })
        .catch(() => {
          this.getDeviceTask();
        });
    },

    updateDeviceBaseConfig() {
      let that = this;
      return this.getDicts("d_device_list_display").then((resp) => {
        return resp;
      });
    },

    getDeviceTask() {
      if (
        this.globalTask != undefined &&
        this.globalTask.dictCode != "" &&
        this.globalTask.dictValue != ""
      ) {
        // deviceTaskList({ids: this.globalTask.dictValue})
        //   .then(resp => {
        //     this.taskList = resp.data;
        //     if(this.taskList.length > 0) {
        //       this.globalTaskStatus = "0";
        //       this.taskList.map(t => {
        //         if(t.status == 1) {
        //           this.globalTaskStatus = "1";
        //         }
        //       });
        //     }
        //   });
      }
    },

    // 控制模式
    changeType(deviceType, t) {
      this.controlTypes[deviceType] = t;
    },

    // 开关地图编辑模式
    mapEditOpen() {
      this.edit = 1;
    },
    mapEditClose() {
      this.edit = 0;
    },

    //获取当前设备类型下所有状态的设备数量
    getStatusDeviceSum() {
      const params = {
        buildingId: this.buildingId,
        deviceTypes: this.deviceMainType,
      };
      deviceWarningSummary(params).then(({ data = [] }) => {
        const deviceSummary = {
          total: 0,
          workStatusRun: 0,
          workStatusStop: 0,
          communicateStatusOnline: 0,
          communicateStatusOffline: 0,
          warningStatusHealth: 0,
          warningStatusWarn: 0,
        };
        data.forEach((item) => {
          for (let it in deviceSummary) {
            deviceSummary[it] += item[it];
          }
        });
        this.deviceStatusSum.forEach((item) => {
          if (item.by) {
            item.num = deviceSummary.total - deviceSummary[item.by];
          } else {
            item.num = deviceSummary[item.code] || 0;
          }
        });
      });
    },
    handleCommand (command) {
      this.refreshSpan = command;
    },
    showPageSettings () {
      this.pageSettingsFlag = true;
    },
    freshPageSettings () {
      this.initMountedPage();
    }
  },
};
</script>

<style lang="scss" scoped>
.statistics-btn {
  position: fixed;
  right: 0;
  top: 40%;
  transform: translateY(-50%);
  writing-mode: vertical-lr;
  padding: 15px 8px;
  border-radius: 4px 0 0 4px;
  z-index: 2000;
}
.strategy-btn {
  position: fixed;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  writing-mode: vertical-lr;
  padding: 15px 8px;
  border-radius: 4px 0 0 4px;
  z-index: 2000;
}

.strategy-drawer {
  ::v-deep {
    .el-drawer__header {
      margin-bottom: 0;
      padding: 16px 20px;
      border-bottom: 1px solid #dcdfe6;
      span {
        font-size: 16px;
        color: #303133;
      }
    }
    .el-drawer__body {
      padding: 20px;
    }
  }
}
::v-deep .el-drawer {
  background: #08162a;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid rgba(34, 148, 254, 0.35);
  margin-right: 50px;
  overflow-y: auto;
}

.dropdown_fresh {
  width: 133px;
  font-size: 12px;
  text-align: right;
  cursor: pointer;
  color: #6C8097;
  font-weight: 400;
  i {
    margin-left: 6px;
    font-size: 16px;
    color: #31A6CD;
  }
}

.tools_tip {
  position: relative;
  left: -16px;
  font-size: 16px;
  cursor: pointer;
}
::v-deep {
  .el-dropdown-menu__item.active {
    color: #31A6CD;
  }
}
</style>
