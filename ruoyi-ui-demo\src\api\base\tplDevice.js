import request from '@/utils/request'


// 查询设备数据模板列表
export function listTplDevice(query) {
  return request({
    url: '/base/tplDevice/list',
    method: 'get',
    params: query
  })
}

// 查询设备数据模板详细
export function getTplDevice(id) {
  return request({
    url: '/base/tplDevice/' + id,
    method: 'get'
  })
}

// 新增设备数据模板
export function addTplDevice(data) {
  return request({
    url: '/base/tplDevice',
    method: 'post',
    data: data
  })
}

// 修改设备数据模板
export function updateTplDevice(data) {
  return request({
    url: '/base/tplDevice',
    method: 'put',
    data: data
  })
}

// 删除设备数据模板
export function delTplDevice(id) {
  return request({
    url: '/base/tplDevice/' + id,
    method: 'delete'
  })
}

// 导出设备数据模板
export function exportTplDevice(query) {
  return request({
    url: '/base/tplDevice/export',
    method: 'get',
    params: query
  })
}


//////////////////////////////////////////////
// @ApiImplicitParam(name="deviceIds", value="设备Ids", dataType="string", required=true),
export function exportDeviceDataTpl(query) {
  return request({
    url: '/base/api/exportDeviceDataTpl',
    method: 'post',
    params: query
  })
}

// @ApiImplicitParam(name="deviceIds", value="设备Ids", dataType="string", required=true),
export function deviceDataCheckList(query) {
  return request({
    url: '/base/api/deviceDataCheckList',
    method: 'post',
    params: query
  })
}

// @ApiImplicitParam(name="itemDataId",value="数据点ID",dataType="string",required=true)
// @ApiImplicitParam(name="collectorId", value="采集器ID",dataType="string",required=false)
// @ApiImplicitParam(name="pointTableDeviceId", value="设备ID",dataType="string", required=false),
// @ApiImplicitParam(name="pointTableAddr",value="地址",dataType="string", required=false)
// @ApiImplicitParam(name="pointTableLength", value="长度",dataType="'string", required=false)
// @ApiImplicitParam(name="pointTableDataGroup", value="分组",dataType="string",reguired=false)
// @ApiImplicitParam(name="pointTableFunc", value="函数",dataType="string",reguired=false),
// @ApiImplicitParam(name="pointTableFuncList'value="函数列表"，dataType='string"，required=false)
export function updatePointTable(query) {
  return request({
    url: '/base/api/updatePointTable',
    method: 'post',
    params: query
  })
}

// @ApiImplicitParam(name="itemId",value="数据点ID",dataType="string",required=true)
// @ApiImplicitParam(name="DeviceId", value="设备ID",dataType="string", required=false),
export function updateDeviceId(query) {
  return request({
    url: '/base/api/updateDeviceId',
    method: 'post',
    params: query
  })
}
/*
    数据生成
    @ApiImplicitParam(name="templateId", value="模板Id", dataType="string", required=true),
    @ApiImplicitParam(name="deviceIdFrom", value="设备Id起始值", dataType="string", required=true),
    @ApiImplicitParam(name="deviceIdTo", value="设备Id结束值", dataType="string", required=true),
    @ApiImplicitParam(name="forceUpdate", value="是否覆盖更新; 1:是", dataType="string", required=false),
*/
export function tplGenDevice(query) {
  return request({
    url: '/base/api/tplGenDevice',
    method: 'post',
    params: query
  })
}

// @ApiImplicitParam(name="deviceIds", value="设备Ids; 逗号分割", dataType="string", required=true),
// @ApiImplicitParam(name="range", value="清理范围; 可选: base,history,log,warning,maintenance", dataType="string", required=true),
// @ApiImplicitParam(name="from", value="时间开始范围; YYYY-MM-DD HH:mm:ss", dataType="string", required=false),
// @ApiImplicitParam(name="to", value="时间结束范围; YYYY-MM-DD HH:mm:ss", dataType="string", required=false),
export function clearDeviceData(query) {
  return request({
    url: '/base/api/clearDeviceData',
    method: 'post',
    params: query
  })
}


//同步读写点数据
export function updateWriteVal(query) {
  return request({
    url: '/base/api/updateWriteVal',
    method: 'post',
    params: query
  })
}