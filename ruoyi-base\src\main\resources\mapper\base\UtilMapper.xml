<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.base.mapper.UtilMapper">

    <select id="getHistoryTableNameList" resultType="String">
        SELECT
            table_name
        FROM information_schema.`tables`
        WHERE TABLE_SCHEMA = #{dbName}
            AND table_name LIKE concat(#{tableName}, '%')
    </select>

    <update id="createDeviceHistoryTable">
        CREATE TABLE ${tableName}  (
            `id` int(0) NOT NULL AUTO_INCREMENT,
            `device_id` int(0) NULL DEFAULT NULL COMMENT '设备ID',
            `item_data_id` int(0) NULL DEFAULT NULL COMMENT '数据点位ID',
            `indication` decimal(20, 4) NULL DEFAULT NULL,
            `other_data` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
            `recorded_at` datetime NULL DEFAULT NULL COMMENT '记录时间',
            `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
            `updated_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
        PRIMARY KEY (`id`) USING BTREE,
        UNIQUE INDEX `_dulpKey`(`item_data_id`, `recorded_at`, `device_id`) USING BTREE,
        INDEX `_recorded_at`(`recorded_at`) USING BTREE,
        INDEX `_device_id`(`device_id`) USING BTREE,
        INDEX `_device_record`(`device_id`, `recorded_at`) USING BTREE,
        INDEX `_item_data_id`(`item_data_id`) USING BTREE,
        INDEX `_itid_rd`(`item_data_id`, `recorded_at`) USING BTREE,
        INDEX `_itid_ud`(`item_data_id`, `updated_at`) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '设备历史数据' ROW_FORMAT = Compact;
    </update>

    <update id="createEnergyHistoryTable">
        CREATE TABLE ${tableName}  (
            `id` int NOT NULL AUTO_INCREMENT,
            `device_id` int NOT NULL,
            `indication` decimal(20, 4) NULL DEFAULT NULL,
            `diff_indication` decimal(20, 4) NULL DEFAULT NULL,
            `diff_fee` decimal(20, 3) NOT NULL DEFAULT 0.000 COMMENT '费用',
            `other_data` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '其他数据 (峰谷标记)',
            `recorded_at` datetime NULL DEFAULT NULL COMMENT '记录时间',
            `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`) USING BTREE,
            INDEX `_recorded_at`(`recorded_at` ASC) USING BTREE,
            INDEX `_device_id`(`device_id` ASC) USING BTREE,
            INDEX `_diff_indication`(`diff_indication` ASC) USING BTREE,
            INDEX `_created_at`(`created_at` ASC) USING BTREE,
            INDEX `_updated_at`(`updated_at` ASC) USING BTREE,
            INDEX `_device_record`(`device_id` ASC, `recorded_at` ASC) USING BTREE,
            INDEX `_indication`(`indication` ASC) USING BTREE
        ) ENGINE = InnoDB AUTO_INCREMENT = 0 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '能耗历史数据' ROW_FORMAT = DYNAMIC;
    </update>

    <insert id="addLicenseCode">
        insert into ly_metx.a_license_history (md, license_code, note, exp_date)
        values ( #{md}, #{code}, #{note}, #{expDate})
    </insert>

    <update id="updateLicenseCode">
        update ly_metx.a_license_history
        set lock_sign = #{dongleInfo}, lock_code=#{dongleCode}, local_code=#{dbCode}, exp_date=#{expDate}
        where md = #{md} limit 1
    </update>

    <select id="licenseCodeList" resultType="java.util.Map">
        select
            id,
            md,
            license_code as licenseCode,
            ifnull(note, '') as note,
            ifnull(exp_date, '') as expDate,
            ifnull(lock_sign, '') as lockSign,
            ifnull(lock_code, '') as lockCode,
            ifnull(local_code, '') as localCode,
            created_at as createdAt
        from ly_metx.a_license_history
        <where>
            <if test="md != null "> and md = #{md}</if>
            <if test="note != null  and note != ''"> and note like concat('%', #{note}, '%')</if>
        </where>
        order by createdAt desc
    </select>

    <select id="executeSql" resultType="java.util.Map">
        ${sql}
    </select>

    <insert id="executeSqlImport" parameterType="java.util.Map">
        ${sql}
    </insert>

    <update id="executeSqlUpdate" parameterType="java.util.Map">
        ${sql}
    </update>

    <delete id="executeSqlClear" parameterType="java.util.Map">
        ${sql}
    </delete>

    <select id="getDDeviceList" resultType="java.util.Map">
        select
            id,
            `type`,
            source_id,
            name,
            brand,
            model,
            description,
            position,
            note,
            tag,
            icon,
            thumb,
            pictures,
            ifnull(work_status, '') as workStatus,
            ifnull(warning_status, '') as warningStatus,
            ifnull(communicate_status,'') as communicateStatus,
            ifnull(status,'') as status
        from d_device
        where id in
        <foreach item="item" index="index" collection="idList" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteItem">
        delete from a_item where id = #{id};
    </delete>
    <delete id="deletePointTableData">
        delete from a_point_table
        where item_data_id in (
            select id from a_item_data where item_id = #{itemId}
        )
    </delete>
    <delete id="deleteItemData">
        delete from a_item_data where item_id = #{itemId};
    </delete>
    <delete id="deleteDevice">
        delete from d_device where id = #{id};
    </delete>
    <delete id="deleteDeviceItemDataMap">
        delete from d_device_item_data_map where device_id = #{deviceId};
    </delete>
    <delete id="deleteDeviceResourceMap">
        delete from d_device_resource_map where device_id = #{deviceId};
    </delete>
    <delete id="deleteEDevice">
        delete from e_device where id = #{id};
    </delete>
    <delete id="deleteEDeviceItemDataMap">
        delete from e_device_item_data_map where device_id = #{deviceId};
    </delete>
    <delete id="deleteEDeviceGroupMap">
        delete from e_device_group_map where device_id = #{deviceId};
    </delete>
    <delete id="deleteWarningRule">
        delete from a_warning_rule where id in (
            select rule_id from a_item_rule_map where item_id = #{itemId}
        )
    </delete>
    <delete id="deleteItemRuleMap">
        delete from a_item_rule_map where item_id = #{itemId}
    </delete>

    <delete id="deleteDeviceDataHistory">
        delete from d_device_data_history
            where device_id = #{deviceId}
            <if test="from != null and from != '' ">
                and created_at &gt;= #{from}
            </if>
            <if test="to != null and to != '' ">
                and created_at &lt;= #{to}
            </if>
    </delete>
    <delete id="deleteEDeviceDataHistory">
        delete from e_energy_data_history
        where device_id = #{deviceId}
        <if test="from != null and from != '' ">
            and created_at &gt;= #{from}
        </if>
        <if test="to != null and to != '' ">
            and created_at &lt;= #{to}
        </if>
    </delete>
    <delete id="deleteEDeviceDataHistoryDaily">
        delete from e_energy_data_history_daily
        where device_id = #{deviceId}
        <if test="from != null and from != '' ">
            and created_at &gt;= #{from}
        </if>
        <if test="to != null and to != '' ">
            and created_at &lt;= #{to}
        </if>
    </delete>
    <delete id="deleteDeviceOperLog">
        delete from d_device_oper_log
        where device_id = #{deviceId}
        <if test="from != null and from != '' ">
            and oper_time &gt;= #{from}
        </if>
        <if test="to != null and to != '' ">
            and oper_time &lt;= #{to}
        </if>
    </delete>
    <delete id="deleteItemWarning">
        delete from a_item_warning
        where item_id = #{deviceId}
        <if test="from != null and from != '' ">
            and created_at &gt;= #{from}
        </if>
        <if test="to != null and to != '' ">
            and created_at &lt;= #{to}
        </if>
    </delete>
    <delete id="deleteEWarning">
        delete from e_warning
        where rule_id in (
            select id from e_warning_rule where device_id = #{deviceId}
        )
        <if test="from != null and from != '' ">
            and created_at &gt;= #{from}
        </if>
        <if test="to != null and to != '' ">
            and created_at &lt;= #{to}
        </if>
    </delete>
    <delete id="deleteDeviceMaintenance">
        delete from d_device_maintenance
        where device_id = #{deviceId}
        <if test="from != null and from != '' ">
            and created_at &gt;= #{from}
        </if>
        <if test="to != null and to != '' ">
            and created_at &lt;= #{to}
        </if>
    </delete>


    <insert id="insertItem">
        insert into a_item (id, collector_id, name, description)
        values ( #{id}, #{collectorId}, #{name}, #{description} )
    </insert>
    <insert id="insertDevice" >
        insert into d_device (id, `type`, source_id, name, brand, model, note, icon, thumb, building_id
                              , resource_id, files, service_life, energy_lever, rated_power, ajustable_power)
        values ( #{id}, #{type}, #{id}, #{name}, #{brand}, #{model}, #{note}, #{icon}, #{thumb}, #{buildingId}
                   , #{resourceId}, #{files}, #{serviceLife}, #{energyLever}, #{ratedPower}, #{ajustablePower})
    </insert>
    <insert id="insertDeviceResourceMap">
        insert into d_device_resource_map ( device_id, resource_id )
        values ( #{deviceId}, #{resourceId} )
    </insert>
    <insert id="insertWarningRule">
        insert into a_warning_rule (id, name, description, `key`, val, `compare`, `severity`, err_msg, solution_ref,
                                    `tag`, time_span, `func`, mail_to, sms_to, sms_template, warn_start, warn_end)
        values ( #{id}, #{name}, #{description}, #{key}, #{val}, #{compare}, #{severity}, #{errMsg}, #{solutionRef},
                #{tag}, #{timeSpan}, #{func}, #{mailTo}, #{smsTo}, #{smsTemplate}, #{warnStart}, #{warnEnd} )
    </insert>
    <insert id="insertItemRuleMap">
        insert into a_item_rule_map (item_id, rule_id)
        values ( #{itemId}, #{ruleId} )
    </insert>
    <insert id="insertEDevice">
        insert into e_device (id, `type`, `name`, `description`, building_id, `remark`)
        values ( #{id}, #{type}, #{name}, #{description}, #{buildingId}, #{remark} )
    </insert>
    <insert id="insertItemData">
        insert into a_item_data (id, item_id, name, `alias`, data_type, data_unit, `note`, `func`)
        values ( #{id}, #{itemId}, #{name}, #{alias}, #{dataType}, #{dataUnit}, #{note}, #{func} )
    </insert>
    <insert id="insertPointTable">
        -- 更新或替换
        insert into a_point_table (id, collector_id, item_data_id, data_name, `alias`, `type`)
        values ( #{id}, #{collectorId}, #{itemDataId}, #{dataName}, #{alias}, #{type} )
        ON DUPLICATE KEY UPDATE
            collector_id = VALUES(collector_id),
            item_data_id = VALUES(item_data_id),
            data_name = VALUES(data_name),
            alias = VALUES(alias),
            type = VALUES(type);
    </insert>
    <insert id="insertPointTable2">
        -- 更新或替换
        insert into a_point_table (id, collector_id, item_data_id, data_name, `alias`, `type`,
                                   device_id, `addr`, `length`, data_group, `func`, func_list)
        values ( #{id}, #{collectorId}, #{itemDataId}, #{dataName}, #{alias}, #{type},
                #{pDeviceId}, #{pAddr}, #{pLength}, #{pDataGroup}, #{pFunc}, #{pFuncList})
        ON DUPLICATE KEY UPDATE
             collector_id = VALUES(collector_id),
             item_data_id = VALUES(item_data_id),
             data_name = VALUES(data_name),
             alias = VALUES(alias),
             `type` = VALUES(type),
             device_id = VALUES(device_id),
             addr = VALUES(addr),
             `length` = VALUES(length),
             data_group = VALUES(data_group),
             `func` = VALUES(func),
             func_list = VALUES(func_list);
    </insert>
    <insert id="insertDeviceItemDataMap">
        insert into d_device_item_data_map (id, device_id, item_data_id, oid, `name`, `alias`, relate_item_data_id, `tag`, note)
        values ( #{id}, #{deviceId}, #{itemDataId}, #{oid}, #{name}, #{alias}, #{relateItemDataId}, #{tag}, #{note} )
    </insert>
    <update id="updateDeviceItemDataMap">
        update d_device_item_data_map set relate_item_data_id = #{relateItemDataId} where id = #{id}
    </update>
    <insert id="insertEDeviceItemDataMap">
        insert into e_device_item_data_map (id, device_id, item_data_id, `name`)
        values ( #{id}, #{deviceId}, #{itemDataId}, #{name} )
    </insert>

    <select id="getDeviceDataTemplate" resultType="com.ruoyi.base.domain.vo.DeviceDataTpl">
        select
            ifnull(it.collector_id,'') as collectorId,
            ifnull(it.id,'') as itemId,
            ifnull(it.code,'') as itemCode,
            ifnull(itd.id,'') as itemDataId,
            ifnull(itd.name,'') as itemDataName,
            ifnull(itd.func,'') as itemDataFunc,
            ifnull(pt.addr,'') as pointTableAddr,
            ifnull(pt.device_id,'') as pointTableDeviceId,
            ifnull(pt.length,'') as pointTableLength,
            ifnull(pt.data_group,'') as pointTableDataGroup,
            ifnull(pt.func,'') as pointTableFunc,
            ifnull(pt.func_list,'') as pointTableFuncList,
            ifnull(pt.type,'') as pointTableType
        from a_item it
        left join a_item_data itd on it.id=itd.item_id
        left join a_point_table pt on pt.item_data_id = itd.id
        where 1 = 1
        <if test="deviceIdList != null">
            and it.id in
            <foreach item="id" collection="deviceIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by it.collector_id, it.id, itd.id
    </select>

    <select id="getDeviceDataTemplateFull" resultType="java.util.Map">
        select
        ifnull(it.collector_id,'') as collectorId,
        ifnull(it.id,'') as itemId,
        ifnull(it.name,'') as itemName,
        ifnull(it.alias,'') as itemAlias,
        ifnull(it.description,'') as itemDescription,
        ifnull(it.code,'') as itemCode,
        ifnull(itd.id,'') as itemDataId,
        ifnull(itd.name,'') as itemDataName,
        ifnull(itd.alias,'') as itemDataAlias,
        ifnull(itd.val,'') as itemDataValue,
        ifnull(itd.max_val,'') as itemDataMaxVal,
        ifnull(itd.min_val,'') as itemDataMinVal,
        ifnull(itd.data_unit,'') as itemDataDataUnit,
        ifnull(itd.note,'') as itemDataNote,
        ifnull(itd.func,'') as itemDataFunc,
        ifnull(itd.updated_at,'') as itemDataUpdatedAt,
        ifnull(pt.addr,'') as pointTableAddr,
        ifnull(pt.device_id,'') as pointTableDeviceId,
        ifnull(pt.length,'') as pointTableLength,
        ifnull(pt.data_group,'') as pointTableDataGroup,
        ifnull(pt.func,'') as pointTableFunc,
        ifnull(pt.func_list,'') as pointTableFuncList,
        ifnull(pt.type,'') as pointTableType
        from a_item it
        left join a_item_data itd on it.id=itd.item_id
        left join a_point_table pt on pt.item_data_id = itd.id
        where 1 = 1
        <if test="deviceIdList != null">
            and it.id in
            <foreach item="id" collection="deviceIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by it.collector_id, it.id, itd.id
    </select>

    <update id="updateItemCode">
        update a_item set code = #{code} where id = #{id}
    </update>
    <update id="updatePointTable">
        update a_point_table set
            `addr` = #{addr},
            device_id = #{deviceId},
            `length` = #{length},
            `data_group` = #{dataGroup},
            `func` = #{func},
            `func_list` = #{funcList}
         where item_data_id = #{itemDataId} and collector_id = #{collectorId}
    </update>

    <update id="updatePointTable2">
        update a_point_table set
            `collector_id` = #{collectorId},
            `addr` = #{addr},
            device_id = #{deviceId},
            `length` = #{length},
            `data_group` = #{dataGroup},
            `func` = #{func},
            `func_list` = #{funcList}
        where item_data_id = #{itemDataId}
    </update>

    <select id="getIotMonitorList" resultType="java.util.Map">
        select
            id,
            ifnull(name,'') as name,
            ifnull(note,'') as note,
            process_list as processList,
            ip,
            system_info as systemInfo,
            cpu_usage as cpuUsage,
            disk_space as diskSpace,
            created_at as createdAt,
            updated_at as updatedAt
        from a_iot_server_monitor
        <where>
            <if test="name != null and name != '' ">
                and name like concat('%', #{name}, '%')
            </if>
        </where>
        order by created_at desc
    </select>

    <select id="getIotMonitorLast" resultType="java.util.Map">
        select
        id,
        ifnull(name,'') as name,
        ifnull(note,'') as note,
        process_list as processList,
        ip,
        system_info as systemInfo,
        cpu_usage as cpuUsage,
        disk_space as diskSpace,
        created_at as createdAt,
        updated_at as updatedAt
        from a_iot_server_monitor
        <where>
            <if test="name != null and name != '' ">
                and name like concat('%', #{name}, '%')
            </if>
        </where>
        order by created_at desc
        limit 1
    </select>

    <delete id="clearIotMonitorHistory">
        delete t1 FROM a_iot_server_monitor t1
        JOIN (
            SELECT
                id,
                DATE(created_at) AS date,
                name,
                ip,
                ROW_NUMBER() OVER (PARTITION BY DATE(created_at), name, ip ORDER BY created_at DESC) AS row_num
            FROM a_iot_server_monitor
            WHERE created_at &lt; #{dateStr}
        ) t2
        ON t1.id = t2.id
        WHERE t2.row_num > 1;
    </delete>

    <select id="getResouce" resultType="java.util.Map">
        select
            id,
            ifnull(building_id,'') as building_id,
            ifnull(category,'') as category,
            ifnull(`type`,'') as `type`,
            ifnull(group_name,'') as groupName,
            ifnull(oid,'') as oid,
            ifnull(`name`,'') as `name`,
            ifnull(val,'') as val,
            ifnull(width,'') as width,
            ifnull(height,'') as height,
            ifnull(note,'') as note,
            ifnull(other_data,'') as otherData,
            ifnull(created_at,'') as createdAt,
            ifnull(updated_at,'') as updatedAt,
            ifnull(source_id,'') as sourceId
        from a_resource
        <where>
            <if test="buildingId != null ">
                and building_id = #{buildingId}
            </if>
            <if test="category != null ">
                and category = #{category}
            </if>
            <if test="type != null ">
                and `type` = #{type}
            </if>
            <if test="groupName != null ">
                and `group_name` = #{groupName}
            </if>
            <if test="name != null ">
                and `name` like concat('%', #{name}, '%')
            </if>
            <if test="note != null ">
                and `note` like concat('%', #{note}, '%')
            </if>
        </where>
        order by oid
    </select>

    <update id="updateWriteVal" >
        update a_item_data AS w
            JOIN a_item_data AS r
        ON w.id = r.id + 1  -- 写点ID是读点ID+1
            AND w.func = 'write'         -- 限定写点类型
            AND r.func = 'read'           -- 限定读点类型
            AND w.name = CONCAT(r.name, '更新') -- 名称匹配规则
            SET w.val = r.val      -- 更新写点的值为读点值
    </update>

</mapper>