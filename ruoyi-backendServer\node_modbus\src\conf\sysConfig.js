
function sysConfig() {
    return {
        "serverHeatbeatSpan": 30000,  // 服务心跳间隔
        "offLineSecond": 30*60,   // 默认数采离线时间判断 30分钟
        "localServer": {
            "name": "lanxing.tech",
            "host": "0.0.0.0",  // 支持所有地址可访问
            "port": 8079,
            "timeout": 3000,
            "_name": "httpServer", // 对应 a_collector.name
            "path": "/data",      // 本地接收数据地址
        },
        "remoteServer": {
            "name": "武汉党校物联中台",
            "host": "**********",  // 推送地址
            "port": 16289,
            "timeout": 10000
        },
        "mysql": {
            "host": "rm-uf625r51qfcrxlt36fo.mysql.rds.aliyuncs.com",
            "database": "ly_ibms",
            "username": "llproj",
            "encodePwd": "7ed8684751ac6e78dd6060d81f9a5b8f",
            "timezone": "08:00",
            "connectionLimit": 50

            //"host": "localhost",
            //"database": "bms",
            //"username": "root",
            //"password": "root123"
        },
        // 如果key配置成 mysql2，会启动同步数据到 mysql2
        "mysql_2": {
            "host": "localhost",
            "database": "bms2",
            "username": "root",
            "password": "root123",
            "timezone": "08:00",
            "connectionLimit": 15
        },
        "mssql": {
            "server": "",
            "port": 1433,
            "database": "",
            "username": "",
            "password": "",
            "encrypt": false
        },
        "dmsql": {
            "host": "*************",
            "port": 5236,
            "database": "ly_ibms",
            "username": "SYSDBA",
            "password": "SYSDBA",
            "poolMax": 5,
            "poolMin": 2,
        },
        // mqtt 公共服务，多系统间消息互传
        "mqtt": {
            // host: "mqtt://***********",
            // port: 1883,
            // username: "ykong",
            // password: "ykong",
            // prefix: "ly_ibms",   // 通用转发 topic
            // metxRedirect: "lanxing_amr_metx_com",  // 合一抄表转发 topic

            host: "mqtt://**************",
            port: 1883,
            username: "lanxing",
            password: "lanxing",
            prefix: "yk_ibms"

            // "host": "mqtt://127.0.0.1",
            // "port": 1883,
            // "username": "lanxing",
            // "password": "lanxing",
            // "prefix": "ly_ibms"
        },
        "ly_jx_xc" : {
            host: "mqtt://**************",
            port: 1883,
            username: "lanxing",
            password: "lanxing"
        },
        // logstash 日志
        "logstash": {
            "host": "**************",
            "port": 4560
        },
        "redis": {
            "host": "localhost",
            "port": 6379,
            "auth_pass": "",
            "database" : 1,
            "db" : 1,
        },
        "influxdb2": {
            "token": "d6ImjE4519UAksSLRN6e1JHHeT1xffBx-8wTLmzxU_C6BhSVS4WbI6xmsvO1sqrbS6VqUEwN2IvhTS4hJTpykw==",
            "url": "http://localhost:8086",
            "org": "yukon",
            "bucket": "ibms",

        },
        // 串口透传数据服务端配置
        "serialPortBridge": {
            portPath: "COM1",
            baudRate: 9600,
            dataBits: 8,
            parity: "none",
            stopBits: 1,
            stopStr: ";;;;;;;;;;",
            mobiles: "15821130654,", //15620562845
        },
        // 每日定时关空调
        "closeHour": "03",
        // 调用 java api
        "backendServer": "http://127.0.0.1:8044",
        // 调用 ai(windows_check) api
        "baseServer": {
            "windowsCheck": "http://*************:5000/api/v1/window-detection",
        },
        //3 = 又是节假日并且是周末
        "holidays": {
            "2021": {},
            "2022": {}
        }
    };
};

module.exports.sysConfig = sysConfig;
