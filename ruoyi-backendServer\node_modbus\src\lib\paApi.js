/*
   常州中以国际 广播数据
   文档 PasystemHttp_20240830.pdf
*/

const request = require("request");
const querystring = require("querystring");

const helper = require("../helper");

function SerApi(opts) {
	var that = this;

	this.opts = {
		fakeData: false, // 是否模拟数据返回
		timeout: 30000,
		accessToken: null,
		...opts,
	};

	// 服务器返回错误码描述
	this.ajaxStatusCodes = {
		0: "success",
	};

	// 从 localstorage 恢复数据
	this._init_ = function () {};
}

SerApi.prototype._ajaxData = async function (param) {
	var that = this;
	return new Promise(function (resolve, reject) {
		that.ajaxData(
			param,
			function (data) {
				resolve(data);
			},
			function (err) {
				reject(err);
			}
		);
	});
};

SerApi.prototype.removePropertyOfNull = function (obj) {
	Object.keys(obj).forEach(item => {
		if (obj[item] == null) {
			delete obj[item];
		}
	});
	return obj;
};

SerApi.prototype.ajaxData = function (param, successFunc, errorFunc) {
	let that = this;
	// 所有请求逻辑
	var query = {
		url: param.url,
		method: param.method.toUpperCase(),
		headers: {
			"Content-Type": "application/json; charset=utf-8",
		},
		body: JSON.stringify(param.data),
		timeout: this.opts.timeout,
		rejectUnauthorized: false, // 禁用证书验证
	};
	// 如果有 token 则补充头
	if (this.opts.accessToken) {
		query.headers.Cookie = "JSESSIONID=" + this.opts.accessToken;
	}
	if (param.method.toUpperCase() == "GET") {
		query.url = query.url + "?" + querystring.stringify(param.data);
	}
	helper.debug("ajaxData query", query);

	return request(query, function (error, response, body) {
		if (error) {
			if (typeof errorFunc == "function") {
				errorFunc(error);
			} else {
				that.errorFunc(error);
			}
			return;
		}
		// 成功后执行
		var res = JSON.parse(body);
		if (res) {
			if (typeof successFunc == "function") {
				successFunc(res);
			}
		} else {
			if (typeof errorFunc == "function") {
				errorFunc(res);
			} else {
				that.errorFunc(res);
			}
		}
	});
};

SerApi.prototype.errorFunc = function (res) {
	helper.log("errorFunc", res);
	return new Error("server error: " + JSON.stringify(res));
};

// 1.1 获取token
SerApi.prototype.getToken = async function () {
	let that = this;
	let res = await this._ajaxData({
		url: that.opts.server + "/sys/login?username=" + that.opts.username + "&password=" + that.opts.password + "&isencode=false",
		method: "post",
		data: {},
	});
	that.opts.accessToken = res.token;
	helper.debug("............token", res);
	return res;
};
// 1.2 实时任务列表
SerApi.prototype.realtimeTask = async function () {
	let that = this;
	let res = await this._ajaxData({
		url: that.opts.server + "/sys/realtimetask/list?page=1&limit=9999",
		method: "post",
		data: {},
	});
	return res;
};
// 1.3 计划任务列表
SerApi.prototype.planTask = async function () {
	let that = this;
	let res = await this._ajaxData({
		url: that.opts.server + "/sys/plan/list?page=1&limit=9999",
		method: "post",
		data: {},
	});
	return res;
};
// 1.4 联动任务列表
SerApi.prototype.linkageTask = async function () {
	let that = this;
	let res = await this._ajaxData({
		url: that.opts.server + "/sys/linkagetask/list?page=1&limit=9999",
		method: "post",
		data: {},
	});
	return res;
};
// 1.5 媒体库 文件夹列表
SerApi.prototype.folderList = async function () {
	let that = this;
	let res = await this._ajaxData({
		url: that.opts.server + "/sys/userfolder/folderList",
		method: "post",
		data: {},
	});
	return res;
};
// 1.6 终端列表
SerApi.prototype.deviceList = async function () {
	let that = this;
	let res = await this._ajaxData({
		url: that.opts.server + "/sys/device/list?page=1&limit=9999",
		method: "post",
		data: {},
	});
	return res;
};

// 1.6 终端详情
SerApi.prototype.deviceInfo = async function (id) {
	let that = this;
	let res = await this._ajaxData({
		url: that.opts.server + "/sys/device/info/" + id,
		method: "post",
		data: {},
	});
	return res;
};
// 1.7 批量修改音量
SerApi.prototype.devicebatchUpdateFoOthers = async function (data) {
	let that = this;
	let res = await this._ajaxData({
		url: that.opts.server + "/sys/device/devicebatchUpdateFoOthers",
		method: "post",
		data: {
			ids: data.ids,
			volume: data.volume,
		},
	});
	return res;
};
// 1.8 分区列表  查询报 404
SerApi.prototype.zoneList = async function () {
	let that = this;
	let res = await this._ajaxData({
		url: that.opts.server + "/sys/device/zonelist",
		method: "post",
		data: {},
	});
	return res;
};
// 1.9 历史记录列表
SerApi.prototype.recordHistory = async function (startTime, endTime) {
	let that = this;
	let res = await this._ajaxData({
		url: that.opts.server + "/sys/recordhistory/list?page=1&limit=100&startTime=" + startTime + "&endTime=" + endTime,
		method: "post",
		data: {},
	});
	return res;
};

module.exports.SerApi = SerApi;
