<template>
    <div class="item_data flex-row" v-loading="loading">
        <CustCard :cardTitleShow="false" class="lt mr20">
            <div>设备选择</div>
            <el-input class="mt20 mb20" placeholder="请输入搜索内容" v-model="filterText" size="mini">
            </el-input>
            <el-tree class="el_tree" default-expand-all :data="treeData" :show-checkbox="false" node-key="id" ref="tree"
                :props="defaultProps" :filter-node-method="filterNode" :current-node-key="currentNodeKey"
                :highlight-current="true" @node-click="nodeClick" />
        </CustCard>
        <CustCard class="rt" :title="`设备id ${deviceDetail.id || ''}`">
            <div class="rt_cont">
                <div class="fields flex-row flex-align-v">
                    <div>设备名称：{{ deviceDetail.name }}</div>
                    <div>设备编码：{{ deviceDetail.code }}</div>
                    <el-button-group>
                        <el-button size="mini" plain type="text" icon="el-icon-edit">一键修改</el-button>
                    </el-button-group>
                </div>

                <el-table class="mt20" :data="pointTable" stripe height="640px">
                    <el-table-column label="itemDataUpdatedAt" align="center" prop="itemDataUpdatedAt" />
                    <el-table-column label="itemDataValue" align="center" prop="itemDataValue" class-name="highlight" />
                    <el-table-column label="itemDataNote" align="center" prop="itemDataNote" />
                    <el-table-column label="itemId" align="center" prop="itemId" />
                    <el-table-column label="itemDataName" align="center" prop="itemDataName" />
                    <el-table-column label="pointTableDeviceId" align="center" prop="pointTableDeviceId" />
                    <el-table-column label="pointTableAddr" align="center" prop="pointTableAddr" />
                    <el-table-column label="pointTableLength" align="center" prop="pointTableLength" />
                    <el-table-column label="pointTableDataGroup" align="center" prop="pointTableDataGroup" />
                    <el-table-column label="pointTableFunc" align="center" prop="pointTableFunc" />
                    <el-table-column label="pointTableFuncList" align="center" prop="pointTableFuncList" />
                    <el-table-column label="pointTableType" align="center" prop="pointTableType" />
                    <el-table-column label="操作" fixed="right" align="center" class-name="small-padding fixed-width"
                        width="200">
                        <template slot-scope="scope">
                            <el-button-group>
                                <el-button size="mini" plain type="text" icon="el-icon-edit"
                                    @click="modifyPointAttr(scope.row)"
                                    v-hasPermi="['tool:tplDevice:edit']">修改点位参数</el-button>
                            </el-button-group>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </CustCard>
        <el-dialog title="修改点位参数" :visible.sync="pointDialogVisible" width="30%" v-dialogDrag="true">
            <el-form :model="pointForm" ref="pointFormRef">
                <el-form-item label="collectorId" prop="collectorId">
                    <el-input v-model="pointForm.collectorId" />
                </el-form-item>
                <el-form-item label="pointTableDeviceId" prop="pointTableDeviceId">
                    <el-input v-model="pointForm.pointTableDeviceId" />
                </el-form-item>
                <el-form-item label="pointTableAddr" prop="pointTableAddr">
                    <el-input v-model="pointForm.pointTableAddr" />
                </el-form-item>
                <el-form-item label="pointTableLength" prop="pointTableLength">
                    <el-input v-model="pointForm.pointTableLength" />
                </el-form-item>
                <el-form-item label="pointTableDataGroup" prop="pointTableDataGroup">
                    <el-input v-model="pointForm.pointTableDataGroup" />
                </el-form-item>
                <el-form-item label="pointTableFunc" prop="pointTableFunc">
                    <el-input v-model="pointForm.pointTableFunc" />
                </el-form-item>
                <el-form-item label="pointTableFuncList" prop="pointTableFuncList">
                    <el-input v-model="pointForm.pointTableFuncList" />
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="pointDialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="confirmTime">确 定</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import {
    listCollector,
} from "@/api/base/collector";
import { listItem } from "@/api/base/item";
import CustCard from '@/views/components/cO2View/components/common/screen/components/custCard.vue'
import DisplayBaseFunc from "@/views/device/monitor/components/Device/DisplayBaseFunc.js";
import { updatePointTable, deviceDataCheckList } from "@/api/base/tplDevice";
export default {
    data() {
        return {
            treeData: [],
            defaultProps: {
                children: 'children',
                label: 'name'
            },
            filterText: "",
            loading: false,
            deviceDetail: {},
            pointTable: [],
            pointForm: {},
            pointDialogVisible: false,
            currentNodeKey: null, // 当前选中的节点key
        };
    },
    mixins: [DisplayBaseFunc],
    components: { CustCard },
    watch: {
        filterText(val) {
            this.$refs.tree.filter(val);
        }
    },
    methods: {
        filterNode(value, data) {
            if (!value) return true;
            return data[this.defaultProps.label].indexOf(value) !== -1;
        },
        getCollectorList() {
            listCollector().then(res => {
                console.log(res, "resres")
                const collectorList = res.rows;
                const promiseList = [];
                collectorList.forEach(item => {
                    item.children = [];
                    promiseList.push(this.getDeviceListInCollector(item.id));
                });
                this.loading = true;
                Promise.all(promiseList).then(res => {
                    this.loading = false;
                    console.log(res, "promiseList")
                    collectorList.forEach(item => {
                        let dt = res.find(item2 => item2.collectorId === item.id) || {};
                        item.name = `${item.name}（${item.id}）`
                        item.children = dt.children || [];
                    })
                    this.treeData = collectorList
                    console.log(this.treeData, "this.treeData")
                })
            })
        },
        getDeviceListInCollector(collectorId) {
            return listItem({ collectorId }).then(res => {
                return {
                    collectorId,
                    children: res.rows
                }
            })
        },
        nodeClick(data) {
            this.deviceDetail = data;
            this.currentNodeKey = data.id; // 设置当前选中的节点key
            deviceDataCheckList({
                deviceIds: data.id
            }).then(res => {
                this.pointTable = res.data;
            })
        },
        //修改collectorId,pointTableDeviceId,pointTableAddr,pointTableLength,pointTableDataGroup,pointTableFunc,pointTableFuncList
        modifyPointAttr(row) {
            console.log(row)
            const { collectorId, pointTableDeviceId, pointTableAddr, pointTableLength, pointTableDataGroup, pointTableFunc, pointTableFuncList, itemDataId } = row;
            this.pointForm = {
                collectorId, pointTableDeviceId, pointTableAddr, pointTableLength, pointTableDataGroup, pointTableFunc, pointTableFuncList, itemDataId
            }
            this.pointDialogVisible = true;
        },
        confirmTime() {
            updatePointTable(this.pointForm).then(res => {
                this.nodeClick(this.deviceDetail);
                this.msgSuccess("点位参数更新成功");
                this.pointDialogVisible = false;
            })
        },
    },
    created() {
        this.getCollectorList();
    }
};
</script>

<style scoped lang="scss">
.item_data {
    .lt {
        width: 305px;
        flex-shrink: 0;

        .el_tree {
            height: calc(100vh - 270px);
            overflow-y: auto;
        }
    }

    .rt {
        height: 100%;

        .rt_cont {
            padding: 16px;
            height: calc(100vh - 218px);

            .fields {
                line-height: 30px;

                &>div {
                    font-size: 14px;
                    margin-right: 20px;
                }
            }
        }

    }
}
</style>
