:: 调用本地服务监控服务是否正常，服务停止或者 pm2 list 为空则运行 run.bat 重启服务，当天重启3次后发钉钉信息，重启8次默认服务起不来，需人工处理，需要无窗口运行时运行脚本 check_api_hidden.vbs
@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

:: 设置钉钉信息内容
set "msg=[ibms]海螺南区服务重启失败,请抓紧处理"

:: 获取当前日期作为文件名
for /f "tokens=2-4 delims=/ " %%a in ('date /t') do (
    set "date_str=%%a%%b%%c"
)

:: 设置重启次数文件
set "restart_file=D:\soft\localDev\pm2_restart_count_%date_str%.txt"

:: 如果重启次数文件不存在，创建并写入0
if not exist "%restart_file%" (
    echo 0 > "%restart_file%"
)

:: 读取当前重启次数
set /p restart_count=<"%restart_file%"

:loop

:: 直接检查 PM2 进程状态
pm2 list | findstr /c:"online" >nul
set "pm2_status=!errorlevel!"

:: 使用 curl 获取接口内容并保存到临时文件
curl http://localhost:8044/common/base/settings > D:\soft\localDev\response.txt

:: 查找返回内容中是否包含 code: 200（使用更宽松的匹配条件）
findstr /c:"\"code\"" D:\soft\localDev\response.txt | findstr /c:"200" >nul
set "api_status=!errorlevel!"

if !api_status! neq 0 goto restart_service
if !pm2_status! neq 0 goto restart_service
echo API 和 PM2 状态正常，无需执行 run.bat
echo 0 > "%restart_file%"
goto continue_loop

:restart_service
set /a "restart_count+=1"
echo !restart_count! > "%restart_file%"
if !api_status! neq 0 (
		echo "code": 200 not found
) else if !pm2_status! neq 0 (
		echo PM2 进程为空
)
if !restart_count! geq 6 (
		echo 今日重启次数已超过 !restart_count! 次
		:: 获取当前日期和时间
		for /f "tokens=2 delims==" %%a in ('wmic os get localdatetime /value') do set "dt=%%a"
		set "year=!dt:~0,4!"
		set "month=!dt:~4,2!"
		set "day=!dt:~6,2!"
		set "hour=!dt:~8,2!"
		set "minute=!dt:~10,2!"
		:: 格式化日期时间
		set "current_time=!year!-!month!-!day! !hour!:!minute!"
		:: 设置API地址
		set "API_URL=https://oapi.dingtalk.com/robot/send?access_token=c0fe126f08d5302c3fb98aa32485a16865101a7701c5ef01acc901075e0220ec"

		:: 设置要发送的消息内容
		set "MESSAGE=!msg! -- !current_time!"

		:: 如果需要JSON格式，可以这样设置
		echo {"msgtype": "text","text":{"content":"!MESSAGE!"}} > dingtalk.json

		:: 使用curl发送POST请求
		echo Sending message to API...
		curl -X POST "!API_URL!" -H "Content-Type: application/json" -d @dingtalk.json
)
call D:\soft\localDev\run.bat
goto continue_loop

:continue_loop
:: 删除临时文件
del D:\soft\localDev\response.txt

endlocal
