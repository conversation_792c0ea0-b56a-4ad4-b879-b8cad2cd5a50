<template>
  <div class="baseMult airConditionCard" :class="item.status + 'Out'">
    <div class="header">
      <span class="text">
        <el-tooltip effect="dark" :content="item.name + ' (' + item.id + ')'" placement="top">
          <span class="itemName" v-text="item.name"></span>
        </el-tooltip>
      </span>
      <el-checkbox class="chbox" v-model="checked" :label="item.id" :key="item.id"> {{ ct }} </el-checkbox>
      <div class="clearfix"></div>
    </div>

    <div class="body">
      <div style="flex-wrap: wrap;" :class="['bls', getBgClass(item)]"></div>
      <div class="bls_btm flex flex-sp mt10">
        <div>
          <el-dropdown size="mini" type="primary" trigger="click" @command="selectChange" class="dropdown">
            <!-- <span class="el-dropdown-link">
              自动状态<i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in getOptions" :key="item.id" :command="item.id"
                :class="{ 'selected': optionSelect == item.id }">{{ item.name }}</el-dropdown-item>
            </el-dropdown-menu> -->
          </el-dropdown>
        </div>
        <el-tooltip content="开关" placement="top-start" trigger="hover" class="switch" v-if="hasStatusTagData">
          <el-switch v-model="statusSet" active-color="#2294FE" inactive-color="#5F7C9E" :active-value="getActiveValue"
          :inactive-value="getInactiveValue" @change="statusChange">
          </el-switch>
        </el-tooltip>
        <el-tooltip content="设备详情" placement="top-start" trigger="hover">
          <div class="detail" @click="handleDeviceSummaryShow(item, deviceInd)">详情</div>
        </el-tooltip>
      </div>
      <div class="curren_mode">手自动状态 | {{ getSelectedName }}</div>
    </div>
  </div>
</template>


<script>
import DeviceBaseFunc from "../DeviceBaseFunc";
export default {
  name: "BaseMultNewCard",
  mixins: [DeviceBaseFunc], // 继承父模块方法
  props: {
    "displayKeys": {
      type: Array,
      default: () => { return [] },
      desc: "卡片上显示的三个属性值，如果没有，则按oid取前3个"
    },
    "summaryType": {
      type: String,
      default: "device",
      desc: "设备统计模块显示内容, device/sensor"
    },
    "cardDataNum": {
      type: Number,
      default: 3,
      desc: "卡片形式下数据展示个数"
    },
    "dialogType": {
      type: String,
      default: "",
      desc: "设备弹框详情样式，复杂样式:mult"
    },
  },
  components: {},
  computed: {
    getSelectedName() {
      const it = this.getOptions.find(item => this.optionSelect == item.id) || {};
      return it.name
    },
    getOptions() {
      const it = this.item.deviceDataBase.find(item => item.dmTag.indexOf(this.modeTag) > -1) || {};
      let kl = (it.dOtherData || '').trim().split(";");
      let ddMap = [];
      for (var i in kl) {
        var s = kl[i].trim().split(":");
        if (s.length == 2) {
          ddMap.push({
            id: s[0],
            name: s[1]
          });
        }
      };
      this.optionSelect = it.dVal;
      return ddMap
    },
    getActiveValue () {
      const it = this.statusMap.find(item => item.name === "on");
      return it.code
    },
    getInactiveValue () {
      const it = this.statusMap.find(item => item.name === "off");
      return it.code
    }
  },
  data() {
    return {
      checked: true, // 选中状态
      ct: "", // 占位，checkbox 不显示文字

      // 卡片上显示的数据
      // datas: [],
      statusMap: [
        {
          name: "off",
          maybe: ['停', '关'],
          code: "",
          drRealVal: 0
        },
        {
          name: "on",
          maybe: ['开', '启', '运'],
          code: "",
          drRealVal: 1
        }
      ],
      statusSet: "0",
      optionSelect: "",
      tag: "status",
      modeTag: "autoState",
      faultTag: "fault",
      hasStatusTagData: true
    }
  },
  created() {
  },
  destroyed() { },
  watch: {
    device: {
      handler(val, oldVal) {
        if (val) {
          console.log(val, 'device')
          this.backUpData();
        }
      },
      immediate: true,
      deep: true,
    }
  },
  mounted() {

  },
  methods: {
    backUpData() {
      let item = JSON.parse(JSON.stringify(this.device));
      this.item = item;
      this.getStatusData();
    },
    getBgClass() {
      let status = 'off'
      const realVal = (this.item.swichObj || {}).drRealVal || 0;
      const it = this.statusMap.find(item => item.drRealVal == realVal);
      status = it.name;
      const dStatus = this.item.dStatus || '';
      if (dStatus != 'default') {
        status = "fault"
      }
      return `fan_${status}`
    },
    statusChange(val) {
      const dt = this.item.deviceDataBase.find(item => item.dmTag.indexOf(this.tag) > -1);
      dt.val = val;
      this.handleSaveDevice(dt);
    },
    selectChange(val) {
      console.log(val, 'val')
      const dt = this.item.deviceDataBase.find(item => item.dmTag.indexOf(this.modeTag) > -1);
      dt.val = val;
      this.handleSaveDevice(dt);
    },
    getStatusData () {
      const dt = this.item.deviceDataBase.find(item => item.drId && item.dmTag.indexOf(this.tag) > -1) || {};
      this.statusSet = dt.dVal || "0";
      if(dt && dt.drId && dt.drOtherData){
        for (const k in (dt.otherRDataMap || {})) {
          const it = this.statusMap.find(item => {
            const had = item.maybe.find(i => dt.otherRDataMap[k].indexOf(i) > -1);
            return had
          });
          if(it){
            it.code = k
          }
        }
      } else {
        this.hasStatusTagData = false;
      }
    }
  }
}
</script>

<style scoped lang="scss">
.header {
  border: none;

  .itemName {
    width: calc(100% - 25px);
  }

  .chbox {
    top: -10px;
    right: -10px;
  }
}

.detail {
  border-radius: 2px;
  border: 1px solid rgba(60, 204, 249, 0.4);
  font-size: 12px;
  color: #3CCCF9;
  padding: 4px 10px;
  cursor: pointer;
}

.el-dropdown-menu__item {
  &.selected {
    color: #2294FE;
  }
}

.body {
  position: relative;
  padding: 18px 12px 12px;

  .bls {
    min-height: 86px;

    &.fan_on {
      background: url('/image/fan_on.png') no-repeat center;
      background-size: 84px;
    }

    &.fan_off {
      background: url('/image/fan_off.png') no-repeat center;
      background-size: 84px;
    }

    &.fan_fault {
      background: url('/image/fan_fault.png') no-repeat center;
      background-size: 84px;
    }
  }

  .bls_btm {
    padding: 0;
    position: relative;

    .switch {
      position: absolute;
      left: 50%;
      transform: translate(-50%, 0);
      top: 3px;
    }

    .dropdown {
      line-height: 26px;
    }
  }

  .curren_mode {
    position: absolute;
    top: -8px;
    left: 12px;
    font-size: 12px;
  }
}
</style>
