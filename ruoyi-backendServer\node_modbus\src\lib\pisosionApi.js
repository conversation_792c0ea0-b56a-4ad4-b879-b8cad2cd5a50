/*
   正大天晴 http 温湿度数据
   文档 E:\项目\项目资料\正大天晴\温湿度通用API接口.docx
*/

const request = require("request");
const querystring = require("querystring");

const helper = require("../helper");

function RjApi(opts) {
    var that = this;

    this.opts = {
        fakeData: false, // 是否模拟数据返回
        timeout: 30000,
        accessToken: null,
        tokenType: null,
        ...opts,
    };

    // 服务器返回错误码描述
    this.ajaxStatusCodes = {
        0: "success",
    };

    // 从 localstorage 恢复数据
    this._init_ = function () {};
}

RjApi.prototype._ajaxData = async function (param) {
    var that = this;
    return new Promise(function (resolve, reject) {
        that.ajaxData(
            param,
            function (data) {
                resolve(data);
            },
            function (err) {
                reject(err);
            }
        );
    });
};

RjApi.prototype.removePropertyOfNull = function (obj) {
    Object.keys(obj).forEach(item => {
        if (obj[item] == null) {
            delete obj[item];
        }
    });
    return obj;
};

RjApi.prototype.ajaxData = function (param, successFunc, errorFunc) {
    let that = this;
    let bodyData = null
    if(param.url.indexOf('/get/token')>-1){
        bodyData = querystring.stringify(param.data)
    }else {
        bodyData = JSON.stringify(param.data)
    }
    // 所有请求逻辑
    var query = {
        url: param.url,
        method: param.method.toUpperCase(),
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        body: bodyData,
        timeout: this.opts.timeout,
        rejectUnauthorized: false, // 禁用证书验证
    };
    // 如果有 token 则补充头
    if(this.opts.accessToken && this.opts.tokenType) {
        query.headers.Authorization = this.opts.tokenType + " " + this.opts.accessToken;
    }
    if (param.method.toUpperCase() == "GET") {
        query.url = query.url + "?" + querystring.stringify(param.data);
    }
    helper.debug("ajaxData query", query);

    return request(query, function (error, response, body) {
        if (error) {
            if (typeof errorFunc == "function") {
                errorFunc(error);
            } else {
                that.errorFunc(error);
            }
            return;
        }
        // 成功后执行
        var res = JSON.parse(body);
        if (res) {
            if (typeof successFunc == "function") {
                successFunc(res);
            }
        } else {
            if (typeof errorFunc == "function") {
                errorFunc(res);
            } else {
                that.errorFunc(res);
            }
        }
    });
};

RjApi.prototype.errorFunc = function (res) {
    helper.log("errorFunc", res);
    return new Error("server error: " + JSON.stringify(res));
};

// 1.1 获取token
RjApi.prototype.getToken = async function () {
    let that = this;
    let res = await this._ajaxData({
        url: that.opts.server + "/get/token",
        method: "post",
        data: {
            username: this.opts.username,
            password: this.opts.password,
            login_type: 2
        },
    });
    that.opts.accessToken = res.data.access_token;
    that.opts.tokenType = res.data.token_type;
    helper.debug('............token',res)
    return res;
};
// 1.1 获取设备数据
RjApi.prototype.getDeviceData = async function (code) {
    let that = this;
    let res = await this._ajaxData({
        url: that.opts.server + "/monitor_currency?login_type=2&shebeibianhao=&page=1&limit=20",
        method: "get",
        data: {},
    });
    helper.debug('............getDeviceData',res)
    return res;
};

module.exports.RjApi = RjApi;
