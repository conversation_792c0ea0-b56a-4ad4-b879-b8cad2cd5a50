<template>
	<view class="login">
		<view class="bg">
			<image mode="aspectFill" :src="`${api}/image/miniStatic/login_bg.png`"></image>
			<view class="text">
				<view class="text_welcome">欢迎登录</view>
				<view class="text_appname">{{ appName }}</view>
			</view>
		</view>
		<view class="bd">
			<form @submit="formSubmit" @reset="formReset">
				<view class="uni-form-item uni-column">
					<view class="name">
						<image mode="aspectFill" src="@/static/login_account.png"></image>
						<span>账号</span>
					</view>
					<input class="ipt" placeholder="请输入账号" v-model="loginForm.username" />
				</view>
				<view class="uni-form-item uni-column psw">
					<view class="name">
						<image mode="aspectFit" src="@/static/login_psw.png"></image>
						<span>密码</span>
					</view>
					<view class="psw_ipt">
						<template v-if="passIptType !== 'password'">
							<input class="ipt" :type="passIptType" placeholder="请输入密码" v-model="loginForm.password" />
							<image src="@/static/eye.png" alt="查看密码" @click="changePassIptType" />
						</template>
						<template v-else>
							<input class="ipt" :type="passIptType" placeholder="请输入密码" v-model="loginForm.password" />
							<image src="@/static/eye_close.png" alt="查看密码" @click="changePassIptType" />
						</template>
					</view>
				</view>

				<view class="uni-form-item uni-column">
					<view class="name">
						<image mode="aspectFill" src="@/static/login_verifycode.png"></image>
						<span>验证码</span>
					</view>
					<view class="login-code">
						<input v-model="loginForm.code" type="number" class="ipt" placeholder="请输入验证码" maxlength="4" />
						<image :src="codeUrl" @click="getCode" class="login-code-img"></image>
					</view>
				</view>
			</form>
			<button :disabled="!getSubmitBtnDisabled" :class="['submit', { 'disabled': !getSubmitBtnDisabled }]"
				@click="accountLogin">登录</button>
			<!-- 第三方登录 -->
			<view class="third-party">
				<view class="line">
					<view>第三方登录</view>
				</view>
				<view class="icon-list">
					<image v-for="item in iconList" :key="item.code" mode="aspectFill" :src="item.icon"
						@click="thirdPartyLogin(item.code)"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, toRef, watch, defineEmits, computed, onBeforeMount } from 'vue';
import { getCaptchaImage, login, getPublicKey, wechatMpLogin, getBuildingList } from '@/api/loginApi.js'
import { userCheck, getInfo } from '@/api/userApi.js'
import { encrypt } from '@/common/common.js'
import { cloneDeep } from 'lodash'

const api = uni.$g.VUE_APP_BASE_API;
const appName = uni.$g.VUE_APP_NAME;
const loginForm = ref({
	username: "",//admin
	password: "",//lanxing121!
	uuid: "",
	code: ""
})
const iconList = [{
	name: "微信",
	code: "weixin",
	icon: "../../static/weixin.png"
}]
const codeUrl = ref('')
const publicKey = ref(null)
const passIptType = ref('password')
const getSubmitBtnDisabled = computed(() => {
	const { username, password, code } = loginForm.value
	return username && password && code
})
//账号登陆
const accountLogin = async () => {
	const loginData = cloneDeep(loginForm.value)
	loginData.passwordBeforeEncrypt = loginData.password;
	loginData.password = encrypt(loginData.password, publicKey.value);
	try {
		await login(loginData).then(async resp => {
			console.log(resp)
			const { token } = resp
			uni.setStorageSync("account_info", loginData)
			uni.setStorageSync('user_token', token);
			await getBuildings();
			getUserinfo();
			loginSuccess();
		})
	} catch (err) {
		uni.showToast({
			title: err.msg,
			icon: "none"
		})
		setTimeout(() => {
			getCode();
		}, 1000)
	}
}
// 登录成功 进入首页
const loginSuccess = async () => {
	uni.reLaunch({
		url: '/pages/manage/index',
	})
}

// 获取图形验证码
const getCode = () => {
	getCaptchaImage().then(res => {
		codeUrl.value = "data:image/gif;base64," + res.img;
		loginForm.value.uuid = res.uuid;
	});
}

const getPublicKeyFun = async () => {
	await getPublicKey()
		.then(res => {
			uni.setStorageSync('public_key', res.publicKey);
			publicKey.value = res.publicKey;
		})
}
//第三方登录
const thirdPartyLogin = (code) => {

	if (false) {
		//游客模式无任何入库操作  不采集用户任何信息
		uni.setStorageSync("user_info", {
			nickName: "游客模式"
		});
		loginSuccess();
	} else {
		uni.login({
			provider: code,
			"onlyAuthorize": true, // 微信登录仅请求授权认证
			success: function (loginRes) {
				console.log(loginRes, 'loginRes')
				const {
					code
				} = loginRes;
				const parmas = {
					code
				};
				wechatMpLogin(parmas).then(res => {
					const platformInfo = uni.getStorageSync('platform_info');
					const {
						unionid,
						openid
					} = res.data;
					const params = {
						platformSource: platformInfo.hostName,
						unionId: unionid,
						openId: openid,
					}
					uni.setStorageSync('wxAccount_info', params);
					userCheck(params).then(async ({data}) => {
						console.log(data, "data")
						uni.setStorageSync("user_info", {
							nickName: "游客模式",
							userId: data.id,
							phone: data.phone,
							visitor: true
						});
						loginSuccess();
					})
				})
			},
			fail: (res) => {
				console.log(res)
			}
		});
	}
}

const getUserinfo = () => {
	getInfo().then(res => {
		const { user } = res;
		user.deptName = user.dept.deptName;
		console.log(user, 'user')
		uni.setStorageSync("user_info", user);
	})
}

const getBuildings = async () => {
	await getBuildingList().then(res => {
		const buildingList = res.data || [];
		uni.setStorageSync('building_list', buildingList);
		buildingList[0] && uni.setStorageSync('current_building', buildingList[0]);
	})
}

const changePassIptType = () => {
	passIptType.value = passIptType.value === 'password' ? 'text' : 'password'
}
const getAccountHistory = () => {
	const accountInfo = uni.getStorageSync("account_info") || {};
	loginForm.value = {
		username: accountInfo.username || '',
		password: accountInfo.passwordBeforeEncrypt || '',
	}
}
onBeforeMount(async () => {
	await getPublicKeyFun();
	getAccountHistory();
	getCode();
})
</script>

<style scoped lang="scss">
.login {
	height: 100vh;
	background-color: #fff;

	.bg {
		height: 400rpx;
		position: relative;

		image {
			width: 100%;
		}

		.text {
			position: absolute;
			left: 6%;
			top: 45%;
			transform: translate(0%, -50%);
			z-index: 1;
			color: #FFFFFF;

			&_welcome {
				font-size: 40rpx;
			}

			&_appname {
				font-size: 48rpx;
				margin-top: 16rpx;
			}
		}
	}

	.bd {
		background: #fff;
		border-radius: 40rpx;
		position: relative;
		top: -20rpx;
		padding: 32rpx;

		.psw {
			margin: 50rpx 0;

			&_ipt {
				position: relative;

				image {
					position: absolute;
					right: 0;
					bottom: 16rpx;
					z-index: 10;
					width: 44rpx;
					height: 44rpx;
					padding: 0 30rpx;
				}
			}

		}

		.name {
			display: flex;
			align-items: center;

			span {
				color: #313233;
				font-size: 34rpx;
				margin-left: 8rpx;
			}

			image {
				width: 44rpx;
				height: 44rpx;
				margin-right: 8rpx;
			}
		}

		.login-code {
			position: relative;

			.login-code-img {
				position: absolute;
				right: 0;
				top: 0;
				width: 200rpx;
				height: 76rpx;
				z-index: 10;
			}
		}
	}

	.ipt {
		border-bottom: 1px solid rgba(176, 181, 184, 0.16);
		padding: 20rpx;
	}

	.submit {
		width: 100%;
		background: #2294FE;
		color: #fff;
		font-size: 34rpx;
		border-radius: 74rpx;
		line-height: 90rpx;
		margin-top: 90rpx;

		&.disabled {
			background: #2294fe91;
		}
	}



	.third-party {
		position: fixed;
		width: 80%;
		bottom: 20rpx;
		left: 10%;

		.line {
			border-top: 1px solid #CACFD3;
			text-align: center;

			&>view {
				display: inline-block;
				position: relative;
				padding: 0 20rpx;
				background: #fff;
				top: -25rpx;
				color: #CACFD3;
				font-size: 24rpx;
			}
		}

		.icon-list {
			display: flex;
			justify-content: center;
			align-items: center;
			padding-bottom: 20rpx;

			image {
				width: 88rpx;
				height: 88rpx;
			}
		}
	}

}
</style>