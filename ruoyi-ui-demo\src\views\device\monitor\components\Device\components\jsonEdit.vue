<template>
    <el-dialog class="jsonEdit" :visible.sync="dialogVisible" :title="title" width="70%" append-to-body
        :before-close="handleClose" v-dialogDrag>
        <div id="jsonEditor" style="height: 600px;" class="json-editor"></div>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="handleClose">取 消</el-button>
        </div>
    </el-dialog>
</template>

<script>
import JSONEditor from 'jsoneditor'
import 'jsoneditor/dist/jsoneditor.css'
export default {
    props: {
        title: {
            type: String,
            default: "JSON编辑工具"
        },
        dialogVisible: {
            type: Boolean,
            default: false
        },
        jsonData: {
            type: String | Object,
            default: ""
        }
    },
    data() {
        return {
            editor: null
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.initJsonEdit();
        })
    },
    beforeDestroy() {
        if (this.editor) {
            this.editor.destroy()
        }
    },
    methods: {
        initJsonEdit() {
            const container = document.getElementById('jsonEditor')
            this.editor = new JSONEditor(container, {
                modes: ['tree', 'view', 'form', 'text', 'code'], // 编辑器模式，可以是 'tree', 'view', 'form', 'text', 'code'
                mode: 'text',
                // theme: 'dark', // 内置暗色主题
                // onChange: this.editChange,
            })

            // 设置初始JSON数据
            let jsonData = {};
            if (typeof this.jsonData === "string") {
                try {
                    jsonData = JSON.parse(this.jsonData)
                } catch (error) { }
            } else {
                jsonData = this.jsonData;
            }
            this.editor.set(jsonData)
        },
        handleClose() {
            this.$emit("update:dialogVisible", false)
        },
        submitForm() {
            this.editChange();
            this.handleClose();
        },
        editChange(val) {
            const data = this.editor.get();
            this.$emit("fresh-jsonData", data)
        }
    }
};
</script>

<style scoped lang='scss'>
::v-deep {
    .jsoneditor-poweredBy {
        display: none;
    }
}
</style>