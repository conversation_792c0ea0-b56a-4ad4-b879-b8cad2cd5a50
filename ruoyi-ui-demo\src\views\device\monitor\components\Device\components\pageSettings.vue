<template>
    <el-dialog class="pageSettings" :visible.sync="dialogVisible" :title="title" width="60%" append-to-body
        :before-close="handleClose" v-dialogDrag>
        <el-form ref="form" class="form" :model="dictData" label-width="140px">
            <el-form-item class="item" :label="item.name" v-for="item in getDictOptions" :key="item.name">
                <div class="flex-row item_flex">
                    <template v-if="item.editType === 'input'">
                        <el-input v-model="dictData[item.name]"
                            :placeholder="item.placeholder ? `例：${item.placeholder}` : `请输入${item.name}`"
                            :type="item.isArea ? 'textarea' : 'text'" />
                    </template>
                    <template v-if="item.editType === 'select'">
                        <el-select class="item_flex_select" v-model="dictData[item.name]"
                            :placeholder="`请选择${item.name}`" clearable :multiple="item.multiple">
                            <el-option v-for="i in item.options" :key="i.id || i.value" :label="i.label"
                                :value="i.value">
                            </el-option>
                        </el-select>
                    </template>
                    <template v-if="item.editType === 'checkbox'">
                        <el-checkbox v-model="dictData[item.name]" :true-label="item.trueLabel"
                            :false-label="item.falseLabel" />
                    </template>
                    <el-tooltip v-if="item.remark" class="ml10" effect="dark" :content="item.remark"
                        popper-class="custom_tooltip" placement="top-start">
                        <i class="el-icon-question" style="font-size: 16px;"></i>
                    </el-tooltip>
                </div>
                <div class="edit_json">
                    <el-button v-if="item.isJson" size="mini" @click="showEditJson(item.name)">编辑</el-button>
                </div>
            </el-form-item>
        </el-form>
        <div class="miss_keys" v-if="missKeys.length > 0">
            <span>页面缺少的配置项：</span>{{ missKeys.join(",") }}
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="handleClose">取 消</el-button>
        </div>
        <JsonEditor v-if="JsonEditorFlag" :dialogVisible.sync="JsonEditorFlag" title="JSON编辑工具"
            :jsonData="jsonEditData.data" @fresh-jsonData="freshJsonData" />
    </el-dialog>
</template>

<script>
import { addData, updateData, listData } from "@/api/system/dict/data";
import { doSql } from "@/api/base/apis";
import JsonEditor from "./jsonEdit"
export default {
    props: {
        title: {
            type: String,
            default: ""
        },
        dialogVisible: {
            type: Boolean,
            default: false
        },
        deviceType: {
            type: String,
            default: ""
        },
    },
    components: {
        JsonEditor, // 注册组件
    },
    data() {
        return {
            dictData: {
                statusKey: "运行状态"
            },
            deviceTypeOptions: [],
            categorySql: ["NKccJC3n6gGEi35Ir7hzHyKcKNKpzw/mFcO2HX+tCRDPXhIyBkcsCD06OM8WTa6ZxdIaXvLGhbX6S0iYzLca4w=="],
            categoryOptions: [],
            typeOptions: [],
            summaryTypeOptions: [
                {
                    label: "device",
                    value: "device",
                },
                {
                    label: "sensor",
                    value: "sensor",
                },
            ],
            displayTypesOptions: [
                {
                    label: "card",
                    value: "card",
                },
                {
                    label: "list",
                    value: "list",
                },
                {
                    label: "map",
                    value: "map",
                },
                {
                    label: "pt",
                    value: "pt",
                },
                {
                    label: "3d",
                    value: "3d",
                },
                {
                    label: "iframe",
                    value: "iframe",
                },
            ],
            iconDisplayOptions: [
                {
                    label: "dot",
                    value: "dot"
                },
                {
                    label: "icon",
                    value: "icon"
                },
                {
                    label: "image",
                    value: "image"
                },
            ],
            iconDisplayOptions: [
                {
                    label: "dialog(基本弹框)",
                    value: "dialog"
                },
                {
                    label: "prototype(跳转组态)",
                    value: "prototype"
                },
            ],
            protoTypeDisplayOptions: [
                {
                    label: "map",
                    value: "map"
                },
                {
                    label: "by",
                    value: "by"
                },
                {
                    label: "3d",
                    value: "3d"
                },
                {
                    label: "mult",
                    value: "mult"
                },
            ],
            JsonEditorFlag: false,
            jsonEditData: {},
            missKeys: [],
            dialogTypeOptions: [
                {
                    label: "mult(大弹框)",
                    value: "mult"
                },
                {
                    label: "multBc(大弹框，楼控专用)",
                    value: "multBc"
                },
                {
                    label: "small(小弹框)",
                    value: "small"
                },
            ],
            refreshSpanOptions: this.gf.refreshSpanOpts(),
            cardTypeOptions: [
                {
                    label: "base(默认)",
                    value: "base"
                },
                {
                    label: "light(照明)",
                    value: "light"
                },
                {
                    label: "fan(风机)",
                    value: "fan"
                },
                {
                    label: "fan2(风机2)",
                    value: "fan2"
                },
                {
                    label: "elevator(电梯)",
                    value: "elevator"
                },
                {
                    label: "aircondition(空调)",
                    value: "aircondition"
                },
                {
                    label: "aircondition2(空调2)",
                    value: "aircondition2"
                },
                {
                    label: "camera(摄像头)",
                    value: "camera"
                },
                {
                    label: "cameraWxhg(无锡海归摄像头)",
                    value: "cameraWxhg"
                },
            ],
            defaultPageSizeOptions: [
                {
                    label: 10,
                    value: 10
                },
                {
                    label: 20,
                    value: 20
                },
                {
                    label: 30,
                    value: 30
                },
                {
                    label: 50,
                    value: 50
                },
            ],
            defaultSortByOptions: [
                {
                    label: "设备编号",
                    value: "id"
                },
                {
                    label: "设备名称",
                    value: "name"
                },
                {
                    label: "设备编码",
                    value: "description"
                },
                {
                    label: "设备位置",
                    value: "position"
                }
            ]
        };
    },
    computed: {
        getDictOptions() {
            return [
                //page
                {
                    name: "pageTitle",
                    editType: "input",
                    remark: "页面标题"
                },
                {
                    name: "deviceType",
                    editType: "select",
                    options: this.deviceTypeOptions,
                    remark: "全部支持的设备类型，map 会体现多种设备共存",
                    multiple: true,
                    needSplit: true
                },
                {
                    name: "deviceMainType",
                    editType: "select",
                    options: this.deviceTypeOptions,
                    remark: "当前主要设备类型",
                    multiple: true,
                    needSplit: true
                },
                {
                    name: "category",
                    editType: "select",
                    options: this.categoryOptions,
                    remark: "a_resource表，当前选中的资源，category字段"
                },
                {
                    name: "type",
                    editType: "select",
                    options: this.typeOptions,
                    remark: "a_resource表，当前选中的资源，type字段"
                },
                {
                    name: "summaryType",
                    editType: "select",
                    options: this.summaryTypeOptions,
                    remark: "默认统计样式"
                },
                {
                    name: "forceUpdate",
                    editType: "checkbox",
                    trueLabel: "1",
                    falseLabel: "0",
                    remark: "是否强制下发指令(即使数据没变化，也会下发指令)"
                },
                {
                    name: "issueTimeoutSetInSecond",
                    editType: "input",
                    isArea: true,
                    isJson: true,
                    remark: "设定多次下发指令的时间间隔，未设定则只执行一次",
                    placeholder: "[3,6]"
                },

                //设备展示
                {
                    name: "defaultPageSize",
                    editType: "select",
                    options: this.defaultPageSizeOptions,
                    remark: "默认分页长度"
                },
                {
                    name: "refreshSpan",
                    editType: "select",
                    options: this.refreshSpanOptions,
                    remark: "默认统计样式"
                },
                {
                    name: "displayTypes",
                    editType: "select",
                    options: this.displayTypesOptions,
                    remark: "呈现方式,其中 map/by/3d/sp3d 只能出现一个",
                    multiple: true
                },
                {
                    name: "displayType",
                    editType: "select",
                    options: this.displayTypesOptions,
                    remark: "默认呈现方式",
                },
                {
                    name: "cardType",
                    editType: "select",
                    options: this.cardTypeOptions,
                    remark: "设备卡片展示形式",
                },
                {
                    name: "dialogType",
                    editType: "select",
                    options: this.dialogTypeOptions,
                    remark: "详情显示模式",
                },
                {
                    name: "viewDetail",
                    editType: "select",
                    options: this.iconDisplayOptions,
                    remark: "设备详情跳转模式, dialog(基本弹框), prototype(跳转组态)"
                },
                {
                    name: "cardDataNum",
                    editType: "input",
                    remark: "默认(base)卡片形式下数据展示个数",
                    placeholder: 4
                },
                {
                    name: "protoTypeDisplay",
                    editType: "select",
                    options: this.protoTypeDisplayOptions,
                    remark: "打开设备弹框详情，跳转组态图类型, map, by, 3d, mult",
                },
                {
                    name: "statusKey",
                    editType: "input",
                    remark: "默认运行状态对应数据点位名称",
                    placeholder: "开关状态"
                },
                {
                    name: "globalVals",
                    editType: "input",
                    remark: "群控点名字，对应于 d_device_item_data_map.name",
                    placeholder: "运行状态,手自动状态,运行模式"
                },
                {
                    name: "defaultSortBy",
                    editType: "select",
                    options: this.defaultSortByOptions,
                    remark: "默认排序条件",
                    placeholder: "name"
                },
                //map
                {
                    name: "iconDisplay",
                    editType: "select",
                    options: this.iconDisplayOptions,
                    remark: "map上图标显示 dot/icon/image",
                },
                {
                    name: "defaultScale",
                    editType: "input",
                    remark: "默认载入map的缩放比例",
                    placeholder: 1
                },
                {
                    name: "showImages",
                    editType: "checkbox",
                    trueLabel: "1",
                    falseLabel: "0",
                    remark: "是否启用原理图+现场图标签"
                },
                {
                    name: "sliderShow",
                    editType: "checkbox",
                    trueLabel: true,
                    falseLabel: false,
                    remark: "是否显示地图的放大滑块"
                },
                {
                    name: "edit",
                    editType: "checkbox",
                    trueLabel: true,
                    falseLabel: false,
                    remark: "地图是否为默认编辑模式"
                },
                {
                    name: "legendSet",
                    editType: "input",
                    isArea: true,
                    isJson: true,
                    remark: "图例设置",
                    placeholder: `{"url":"/uploads/ibms_2922_2x_ZM_legend.png","position":{"right":100,"bottom":200},"scale":1}`
                },
                {
                    name: "listMoreFieldsNum",
                    editType: "input",
                    remark: "默认展示表格选项长度",
                    placeholder: 6
                },
                {
                    name: "dataGroup",
                    editType: "input",
                    isArea: true,
                    isJson: true,
                    remark: "设备详情运行状态点位分组",
                    placeholder: `[{"key":"状态","val":"device_status"},{"key":"温度","val":"device_temp"}]`
                },
                {
                    name: "orderBy",
                    editType: "input",
                    remark: "设备列表排序依据,接口参数",
                    placeholder: "name"
                },
                //用能
                {
                    name: "hasEnergy",
                    editType: "checkbox",
                    trueLabel: "1",
                    falseLabel: "0",
                    remark: "是否支持能耗数据显示"
                },
                {
                    name: "hasLock",
                    editType: "checkbox",
                    trueLabel: "1",
                    falseLabel: "0",
                    remark: "是否支持锁定功能"
                },
                {
                    name: "hasWarning",
                    editType: "checkbox",
                    trueLabel: "1",
                    falseLabel: "0",
                    remark: "是否支持报警管理显示"
                },
                {
                    name: "hasDeviceInfo",
                    editType: "checkbox",
                    trueLabel: "1",
                    falseLabel: "0",
                    remark: "是否支持设备信息显示"
                },
                {
                    name: "hasMaintenance",
                    editType: "checkbox",
                    trueLabel: "1",
                    falseLabel: "0",
                    remark: "是否支持运维相关tab显示  报修管理、巡检管理、保养管理"
                },
                {
                    name: "hasEleProspection",
                    editType: "checkbox",
                    trueLabel: "1",
                    falseLabel: "0",
                    remark: "电气设备是否显示"
                },
                {
                    name: "hasCOLink",
                    editType: "checkbox",
                    trueLabel: "1",
                    falseLabel: "0",
                    remark: "CO联动是否显示"
                },
                //附加项
                {
                    name: "dataSummary",
                    editType: "input",
                    isArea: true,
                    isJson: true,
                    remark: "汇总信息：某些页面底部需要显示统计数据",
                    placeholder: `{"title":"统计","colomnNum":3,"datas":[{"name":"统计1","icon":"/images/xc_05.png","list":[{"deviceIds":"1200115","function":"sum","dataName":"送风温度","unit":"℃"},{"deviceIds":"1200115","function":"sum","dataName":"低温防冻报警"}]}]}`
                },
                //群控
                {
                    name: "groupControlIssue",
                    editType: "input",
                    remark: "群控下发限制提示语句",
                    placeholder: "手动设备无法下发命令"
                },
            ]
        },
    },
    mounted() {
        this.initData();
        console.log(this.deviceType, "settings")
    },
    methods: {
        handleClose() {
            this.$emit("update:dialogVisible", false)
        },
        submitForm() {
            this.getDictOptions.find(item => {
                let dictData = this.dictData;
                if (dictData[item.name]) {
                    if (item.needSplit) {
                        dictData[item.name] = dictData[item.name].join(",");
                    }
                    if (item.isJson) {
                        try {
                            dictData[item.name] = JSON.parse(dictData[item.name]);
                        } catch (error) { }
                    }
                }
            })
            const dictParams = {
                ...this.dictCopy,
                dictValue: JSON.stringify(this.dictData)
            }
            if(!dictParams.dictCode){
                dictParams.dictLabel = this.deviceType;
                dictParams.dictType = "d_device_list_display";
            }
            let methodName = dictParams.dictCode ? updateData : addData;
            methodName(dictParams).then(res => {
                this.$message({
                    message: "配置已更新",
                    type: "success",
                });
                localStorage.removeItem("dict_d_device_list_display");
                this.$emit("fresh-settings")
                this.handleClose();
            })
        },
        async initData() {
            console.log(this.deviceMainType, "this.deviceMainType");
            await this.getDicts("d_device_list_display").then(res => {
                this.dictCopy = (res.data || []).find(item => item.dictLabel === this.deviceType);
                console.log(this.dictCopy,"this.dictCopy")

                const dictValStr = (this.dictCopy || {}).dictValue;
                let dictData = {};
                try {
                    dictData = { ...this.dictData, ...JSON.parse(dictValStr) };
                } catch (error) { }
                this.getDictOptions.find(item => {
                    if (item.isJson && dictData[item.name]) {
                        try {
                            dictData[item.name] = JSON.stringify(dictData[item.name]);
                        } catch (error) { }
                    }
                    if (item.needSplit && dictData[item.name]) {
                        dictData[item.name] = dictData[item.name].split(",")
                    }
                })
                this.dictData = { ...dictData };
            });
            this.getDicts("d_device_type").then(res => {
                this.deviceTypeOptions = res.data.map(item => {
                    return {
                        label: `${item.dictValue}(${item.dictLabel})`,
                        value: item.dictValue,
                        id: item.dictCode
                    }
                });
            })
            doSql({
                sql: JSON.stringify(this.categorySql)
            }).then(res => {
                let categoryOptions = [];
                let typeOptions = [];
                (res.data || []).forEach(item => {
                    if (!categoryOptions.find(i => i.value === item.category)) {
                        categoryOptions.push({
                            label: item.category,
                            value: item.category,
                        })
                    }
                    if (!typeOptions.find(i => i.value === item.type)) {
                        typeOptions.push({
                            label: item.type,
                            value: item.type,
                        })
                    }
                });
                this.categoryOptions = [...categoryOptions];
                this.typeOptions = [...typeOptions];
            });
            //输出当前的字典配置中 页面中未出现的配置项
            let keys = [];
            for (const k in this.dictData) {
                const dt = this.getDictOptions.find(item => item.name === k);
                if (!dt) {
                    keys.push(k);
                }
            };
            this.missKeys = [...keys];
        },
        showEditJson(name) {
            this.jsonEditData = {
                name,
                data: this.dictData[name]
            };
            this.JsonEditorFlag = true;
        },
        freshJsonData(data) {
            this.dictData[this.jsonEditData.name] = JSON.stringify(data);
        }
    }
};
</script>

<style scoped lang='scss'>
.pageSettings {
    .miss_keys {
        margin-top: 16px;
        font-size: 12px;
        text-align: right;
        color: #ffffffbf;

        .span {
            width: 140px;
        }
    }

    .form {
        height: 600px;
        overflow-y: auto;
        padding-right: 10px;

        .item {
            .item_flex {
                align-items: center;

                &_select {
                    width: 100%;
                }
            }

            .edit_json {
                text-align: right;
                padding-right: 25px;
            }

        }
    }
}
</style>