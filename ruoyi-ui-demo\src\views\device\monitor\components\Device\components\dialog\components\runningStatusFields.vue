<template>
  <div class="runningStatusFields">
    <div class="txt_align_right mt10" v-if="dataGroup.length > 0">
      <el-button-group>
        <el-button size="mini" key="all" :type="currentTab === 'all' ? 'primary' : ''" @click="tabsChange('all')">全部</el-button>
        <el-button size="mini" v-for="(item,index) in dataGroup" :key="item.val" :type="currentTab === item.val ? 'primary' : ''" @click="tabsChange(item.val)">{{item.key}}</el-button>
      </el-button-group>
    </div>
    <el-table class="status-table" :data="deviceDataBaseFilter" stripe :height="`${dataGroup.length > 0 ? 276 : 310}px`">
          <el-table-column  prop="dmName" label="名称" />
          <el-table-column  prop="valStr" label="数值" />
          <el-table-column  prop="dUpdatedAt" label="采集时间" />
          <!-- <el-table-column  prop="dmNote" label="备注">
            <template slot-scope="scope">
               {{ scope.row.dmNote }}
              <i class="el-icon-edit text-success" type="primary" style="cursor: pointer; margin-top: 1px;" @click="editOtherData(scope.row)" />
            </template>
          </el-table-column> -->
          <el-table-column label="功能" width="120">
            <template slot-scope="scope">
              <el-checkbox-group v-model="scope.row.functions" @change="functionsChange($event, scope.row)">
                <el-checkbox label="monitor">监控</el-checkbox>
                <el-checkbox label="operatorLog">日志</el-checkbox>
              </el-checkbox-group>
            </template>
          </el-table-column>
    </el-table>
  </div>
</template>

<script>
import DeviceBaseFunc from "../../DeviceBaseFunc";
import { updateDeviceItemDataMap} from "@/api/device/deviceItemDataMap";
export default {
  name: "DeviceItemData",
  props: {
    device: {
      type: Object,
      default: () => {},
    },
    dataGroup: {
      type: Array,
      default () {
        return []
      }
    }
  },
  mixins: [DeviceBaseFunc], // 继承父模块
  components: {},
  data() {
    return {
      item: {
        deviceDataBase: [],
        deviceDataEnv: [],
      },
      activeName: "设备数据",
      currentTab: "all",
    };
  },
  computed: {
    deviceDataBaseFilter () {
      if(this.currentTab && this.currentTab !== 'all'){
        return (this.item.deviceDataBase || []).filter(i => i.dmTag.indexOf(this.currentTab) > -1);
      } else {
        return this.item.deviceDataBase || [];
      }
    }
  },
  watch: {
    device: {
      handler(val, oldVal) {
        if (val) {
          this.backUpData();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
  },
  methods: {
    // 备份数据
    backUpData() {
      let item = JSON.parse(JSON.stringify(this.device));
      console.log("DeviceItemData", item);
      item.deviceDataBase = item.deviceDataBase ? item.deviceDataBase.filter(item => item.dmTag.indexOf("hidden") < 0) : [];
      item.deviceDataEnv = item.deviceDataEnv ? item.deviceDataEnv : [];
      item.deviceDataWarn = item.deviceDataWarn ? item.deviceDataWarn : [];
      item.deviceDataBase.forEach(dt => {
          let functions = [];
          if(dt.dmTag.indexOf("monitor") > -1){
            functions.push("monitor")
          }
          if(dt.dmTag.indexOf("operatorLog") > -1){
            functions.push("operatorLog")
          }
          dt.functions = functions;
          dt.functionsCopy = functions;
      });
      this.item = item;
      this.tabsChange("all")
      console.log("runningStatusFields", this.item.deviceDataBase);
    },
    // 取消设备修改
    handleCancelDevice() {
      this.$emit("updateData"); // 通知父组件
    },
    editOtherData(row) {
      this.$prompt('', '备注修改', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: row.dmNote,
      }).then(({ value }) => {
        this.doPromise({
          note: value,
          id: row.dmId
        });
      })
    },
    functionsChange (val, row) {
      let that = this;
      this.$confirm('是否修改当前点位的功能类型?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function() {
        let tag = row.dmTag ? row.dmTag.split(",") : [];
        let dmTag = tag.filter(item => item !== 'monitor' && item !== 'operatorLog');
        dmTag = [...dmTag, ...val]
        that.doPromise({
          tag: dmTag.join(','),
          id: row.dmId
        });
      }).catch(() => {
        row.functions = row.functionsCopy;
      })
    },
    doPromise (params) {
      updateDeviceItemDataMap(params).then(response => {
        this.$emit("updateData"); // 通知父组件
        this.$message.success("更新成功");
      })
    },
    tabsChange (val) {
      let res = val;
      if(!val && this.dataGroup.length > 0){
        res = this.dataGroup[0].val;
      }
      this.currentTab = res;
    }
  },
};
</script>

<style scoped lang="scss">
.status-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.status-table th {
  background-color: #264467;
  color: #fff;
  font-weight: 400;
  padding: 12px;
  font-size: 15px;
  border: 1px solid #304156;
  text-align: center;
}

.status-table td {
  padding: 10px;
  border: 1px solid #304156;
  color: #fff;
  text-align: center;
}

.status-table tbody tr:nth-child(odd) {
  background-color: #12253b;
}
.status-table tbody tr:nth-child(even) {
  background-color: #264467;
}

.status-table tbody tr:hover {
  background-color: #204773 !important;
  cursor: pointer;
}
::v-deep .el-checkbox {
  margin-right: 0;
  &:first-child {
    margin-right: 4px;
  }
}
::v-deep .el-checkbox__label {
  padding-left: 4px;
}
</style>
