<template>
<!--  同比分析-->
    <div class="data-compare app-container">
        <div class="main-card">
            <h3 class="mb10" v-if="pageTitle">
                <div class="pull-left">
                    <span v-text="pageTitle"></span>
                </div>
                <div class="clearfix"></div>
                <el-divider></el-divider>
            </h3>
        </div>
      <el-tabs v-model="energyType" @tab-click="energyTypeChange">
        <el-tab-pane v-for="item in energyTypes" :key="item.type" :label="item.typeName" :name="item.type" />
      </el-tabs>
        <div class="main">
            <!-- left -->
            <div class="left">
                <div class="title">数据选择</div>
                <div class="type">
                    <el-input placeholder="输入关键字进行过滤" v-model="inputTreeDataFilterText" size="small" />
                    <el-tree class="filter-tree mt10"
                       :data="deviceGroupList"
                       :props="defaultProps"
                        default-expand-all
                       :filter-node-method="filterNode"
                       ref="inputTree"
                       show-checkbox
                       node-key="id"
                       :check-strictly="true"
                       @check="inputTreeChange" />
                </div>
            </div>
            <!-- right -->
            <div class="right">
                <div class="filters">
                    <div class="lt">
                      <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="130">
                        <el-form-item prop="showType">
                          <el-select v-model="queryParams.showType" placeholder="请选择" size="mini" style="width: 100px">
                            <el-option v-for="item in dataShowTypes" :key="item.code" :label="item.label"
                                       :value="item.code"></el-option>
                          </el-select>
                        </el-form-item>
                        <el-form-item prop="dateArea">
                          <el-date-picker v-model="queryParams.dateArea" :type="getDatePickerType('type')"
                                          range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="mini"
                                          style="width: 240px" unlink-panels :format="getDatePickerType('format')"
                                          value-format="yyyy-MM-dd">
                          </el-date-picker>
                        </el-form-item>
                        <el-form-item prop="compareYear" label="同比:" v-if="queryParams.showType !== 'year'">
                          <el-date-picker v-model="queryParams.compareYear" type="year" placeholder="选择年" size="mini"
                                          style="width: 100px" value-format="yyyy">
                          </el-date-picker>
                        </el-form-item>
                        <el-form-item class="mr0">
                          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
                          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
                        </el-form-item>
                      </el-form>
                    </div>
                    <el-button size="mini" icon="el-icon-download" type="warning" plain
                        @click="handleExport">导出数据</el-button>
                </div>
              <CustCard title="数据展示" autoHeight>
                <BaseChart ref="baseChart" :optionData="chartOption" :height="`280px`" v-loading="loading" />
                <el-table v-loading="loading" id="dataTable" ref="tableRef" v-if="tableData && tableData.length > 0"
                          :data="tableData" style="width: 100%;margin-top: 10px;" height="320px" stripe>
                  <el-table-column v-for="item in columns" :key="item.code" :prop="item.code" :label="item.name"
                                   :width="item.width" :min-width="item.minWidth"></el-table-column>
                </el-table>
              </CustCard>
            </div>
        </div>

    </div>
</template>

<script>
import {buildingEnergyDeviceSummaryList, buildingGroupList} from "@/api/energy/apis";
import { deviceItemDataList, deviceDataSummary } from "@/api/device/apis";
import CustCard from '@/views/components/cO2View/components/common/screen/components/custCard.vue'
import BaseChart from "@/views/ScreenCommBase/components/common/BaseChart.vue";
import {groupDeviceList, listDeviceGroup} from "@/api/energy/deviceGroup";
import {listData} from "@/api/system/dict/data";
export default {
    components: {
        CustCard,
        BaseChart,
    },
    data() {
        return {
            chartTypes: [
                {
                    name: "趋势图",
                    code: "bar"
                },
                {
                    name: "柱状图",
                    code: "bar"
                },
            ],
            activeChartType: "bar",
            chartOption: {},
            chartDataList: [],
            viewColumns: [
                {
                    name: "名称",
                    code: "name",
                    width: 100
                },
                {
                    name: "最大值",
                    code: "max"
                },
                {
                    name: "最小值",
                    code: "min"
                },
                {
                    name: "平均值",
                    code: "avg"
                },
            ],
            viewDatas: [],
            excelData: [],
            loading: false,

            defaultProps: {
              label: 'name',
              children: 'children'
            },
            inputTreeDataFilterText: '',
            outputTreeDataFilterText: '',
            inputCheckList: [],
            deviceGroupList: [],
            pageConfigs: this.gf.getLocalObject("pageConfigs"),
            deviceList: [],
            deviceSelectList: [],
            freshEchartKey: 0,
            pageTitle: "同比分析",
          // 查询参数
          treeParams: {
            pageNum: 1,
            pageSize: 100,
            searchKey: "",
            deviceType: "electricity",
            buildingId: this.gf.getBuildingId(), //建筑ID
          },
          dataShowTypes: [
            {
              code: "day",
              type: "daterange",
              label: "日",
              format: "yyyy-MM-dd",
              momentFormat: "YYYY-MM-DD"
            },
            {
              code: "month",
              type: "monthrange",
              label: "月",
              format: "yyyy-MM",
              momentFormat: "YYYY-MM"
            },
            {
              code: "year",
              type: "monthrange",
              label: "年",
              format: "yyyy",
              momentFormat: "YYYY"
            }
          ],
          queryParams: {
            groupIds: [],
            showType: "day",
            compareYear: this.$moment().add(-1, "year").format("YYYY"),
            dateArea: [this.$moment().add(-1, "months").startOf("month").format("YYYY-MM-DD"), this.$moment().add(-1, "months").endOf("month").format("YYYY-MM-DD")]
          },
          columns: [],
          tableData: [],
          energyTypes: [],
          currentTypeData: {},
          groupListObj: {},
          energyType: "electricity",
          buildingId: this.gf.getBuildingId(),
          defaultOptions: {
            grid: {
              left: 35,
              top: 50,
              bottom: 10,
              containLabel: true
            },
            color: [
              "#88CAF3",
              "#FCD869",
              "#2294FE",
              "#E45757",
              "#E45757",
              "#37D9B2",
              "#9380F7",
              "#3CCCF9",
              "#FF7D00",
              "#165DFF",
              "#D91AD9",
              "#14C9C9",
              "#722ED1"
            ],
            legend: {
              left: "right",
              top: "top",
              itemHeight: 8,
              data: []
            },
            xAxis: {
              data: [],
              axisLine: {
                show: true,
                lineStyle: {
                  color: "#3489BF",
                  width: 1
                }
              }
            },
            yAxis: [
              {
                splitLine: {
                  lineStyle: {
                    color: "rgba(0, 83, 209, .4)",
                    width: 0.5
                  }
                },
                name: '{a|单位：}{b|kWh}',
                nameGap: 20,
                nameTextStyle: {
                  rich: {
                    a: {
                      color: '#fff',
                      lineHeight: 10,
                      fontSize: 14
                    },
                    b: {
                      color: '#3CCCF9',
                      lineHeight: 10,
                      fontSize: 14
                    }
                  }
                },
              }
            ],
            "series": [],
            "dataZoom": [
              {
                "type": "inside",
                "show": false
              }
            ],
            "tooltip": {
              "trigger": "axis",
              confine: true,
            }
          },
        };
    },
    watch: {
        inputTreeDataFilterText(val) {
            this.$refs.inputTree.filter(val);
        },
        outputTreeDataFilterText(val) {
            this.$refs.outputTree.filter(val);
        }
    },
    methods: {
      async getEnergyTypes() {
        await this.gf.getBuildingEnergyTypes(this.buildingId).then(list => {
          this.energyTypes = [...(list || [])];
          this.getCurrentTypeData();
        })
      },
      getCurrentTypeData() {
        const dt = this.energyTypes.find(item => item.type === this.energyType)
        this.currentTypeData = { ...dt }
        if(this.chartOption.yAxis && this.chartOption.yAxis[0]){
          this.chartOption.yAxis[0].name = "{a|单位：}{b|" + this.currentTypeData.typeUnit + "}"
        }
      },
      energyTypeChange(val) {
        this.treeParams.deviceType = this.energyType
        this.getCurrentTypeData()
        this.getGroup()
      },
      getGroup() {
        listDeviceGroup(this.treeParams).then(response => {
          const treeData = this.handleTree(response.data, "id", "parent");
          this.deviceGroupList = [...treeData];
        });
      },
      async getGroupDevices() {
        this.groupListObj = {}
        const promises = this.inputCheckList.map(async (ii) => {
          const response = await groupDeviceList({groupId: ii.groupId});
          if(response.data && response.data.length){
            this.groupListObj[ii.groupId] = response.data.map(da => da.id).join(',');
          }
        });
        await Promise.all(promises);
      },
        handleExport() {
            this.$nextTick(() => {
                const startDate = this.queryParams.dateArea[0].replace(/-/g, "")
                const endDate = this.queryParams.dateArea[1].replace(/-/g, "")
                this.et('dataTable', this.pageTitle + "_" + startDate + '-' + endDate);
            })
        },
        filterNode(value, data) {
            if (!value) return true;
            return data.name.indexOf(value) !== -1;
        },
        inputTreeChange() {
            const checkedNodes = this.$refs.inputTree.getCheckedNodes();
            this.inputCheckList = checkedNodes.map(item => {
              return {
                groupId: item.id,
                groupName: item.name,
              }
            });
            this.queryParams.groupIds = this.inputCheckList
        },
      getDatePickerType(key) {
        const it = this.dataShowTypes.find(item => item.code === this.queryParams.showType)
        return it[key]
      },
      initChartData() {
        let from, to, compareFrom, compareTo;
        if (!this.queryParams.dateArea || this.queryParams.dateArea.length < 2 || !this.queryParams.groupIds || this.queryParams.groupIds.length < 1) {
          this.resetQuery();
          return;
        }
        const format = this.getDatePickerType("momentFormat");
        from = this.$moment(this.queryParams.dateArea[0]).format(format);
        to = this.$moment(this.queryParams.dateArea[1]).format(format);
        compareFrom = this.queryParams.compareYear + from.substring(4);
        compareTo = this.queryParams.compareYear + to.substring(4);
        //补充时间格式
        if (this.queryParams.showType !== "day") {
          from = this.$moment(from).startOf(this.queryParams.showType).format("YYYY-MM-DD");
          compareFrom = this.$moment(compareFrom).startOf(this.queryParams.showType).format("YYYY-MM-DD");
          to = this.$moment(to).endOf(this.queryParams.showType).format("YYYY-MM-DD");
          compareTo = this.$moment(compareTo).endOf(this.queryParams.showType).format("YYYY-MM-DD");
        }
        const promiseList = [];
        this.queryParams.groupIds.forEach(item => {
          //当期
          let ids = this.groupListObj[item.groupId] || ''
          console.log('参数...........',item.groupId,ids)
          const promiseIt1 = buildingEnergyDeviceSummaryList({
            buildingId: this.buildingId,
            from,
            to,
            energyType: this.energyType,
            displayType: this.queryParams.showType,
            deviceIds: ids,
          }).then(res => {
            return {
              data: res.data,
              groupName: item.groupName,
              type: "bar"
            }
          });
          promiseList.push(promiseIt1)
          //同比
          if (this.queryParams.showType !== 'year') {
            const promiseIt2 = buildingEnergyDeviceSummaryList({
              buildingId: this.buildingId,
              from: compareFrom,
              to: compareTo,
              energyType: this.energyType,
              displayType: this.queryParams.showType,
              deviceIds: ids,
            }).then(res => {
              return {
                data: res.data,
                groupName: item.groupName + "同比",
                type: "bar"
              }
            });
            promiseList.push(promiseIt2)
          }
        });
        this.loading = true;
        Promise.all(promiseList).then(resList => {
          this.setOptions(resList);
          this.loading = false;
        });
      },
      async handleQuery() {
        await this.getGroupDevices()
        this.initChartData();
      },
      resetQuery() {
        this.queryParams = {
          groupIds: [],
          showType: "day",
          compareYear: this.$moment().add(-1, "year").format("YYYY"),
          dateArea: [this.$moment().add(-1, "months").startOf("month").format("YYYY-MM-DD"), this.$moment().add(-1, "months").endOf("month").format("YYYY-MM-DD")]
        }
        this.getCurrentTypeData();
        this.defaultEchartSet();
        this.resetTableData();
      },
      setOptions(resList) {
        const series = [];
        const legendData = [];
        this.setxAxisData();
        const xAxisData = this.chartOption.xAxis.data;
        //每个时间节点可能会有多条代表数据  求和
        resList.forEach(item => {
          const { data, groupName, type } = item;
          const list = [];
          xAxisData.forEach(i => {
            let sum = 0;
            data.forEach(i2 => {
              if (i2.recordedAt === i) {
                sum += i2.totalVal
              }
            })
            list.push(sum);
          })
          let seriesItem = {
            "name": groupName,
            type,
            "barMaxWidth": 20,
            "data": [...list]
          }
          series.push(seriesItem);
          legendData.push(groupName)
        })

        this.chartOption.series = series;
        this.chartOption.legend.data = legendData;
        let that = this;
        this.chartOption.tooltip.formatter = function (para) {
          let params = []
          if (Array.isArray(para)) {
            params = [...para]
          } else {
            params.push(para)
          }
          // params 是一个数组，数组中包含每个系列的数据信息
          const timeName = params[0] ? params[0].name : ''; // 系列名称
          let result = `<div style="margin-bottom: 10px;">${timeName}</div>`;
          params.forEach(function (item) {
            // item 是每一个系列的数据
            let seriesName = item.seriesName; // 系列名称
            const value = parseFloat(item.value).toFixed(2); // 数据值
            const marker = item.marker; // 标志图形
            if (seriesName.indexOf('同比') < 0) {
              seriesName = seriesName + "用能"
            }
            result += `${marker}<span style="width: 140px;display:inline-block;">${seriesName}</span><span style="margin-left: 10px;">${value} ${that.currentTypeData.typeUnit}</span><br/>`;
          });
          return result
        }
        this.setTableData(xAxisData, series, legendData);
      },
      setTableData(xAxisData, series, legendData) {
        console.log(xAxisData, series, legendData, 'setTableData');
        const tableData = [];
        const columns = [{
          name: "日期",
          code: "recordedAt",
          width: "120px"
        }];
        legendData.forEach((item, index) => {
          columns.push({
            name: item.indexOf("同比") > -1 ? `${item}用${this.currentTypeData.typeName}(${this.currentTypeData.typeUnit})` : `${item}本期用${this.currentTypeData.typeName}(${this.currentTypeData.typeUnit})`,
            code: `groupIdx_${index}`,
            minWidth: "180px"
          })
          if (item.indexOf("同比") > -1) {
            columns.push({
              name: `${item}(${this.queryParams.compareYear})`,
              code: `groupIdx_${index}_p`,
              minWidth: "140px"
            })
          }
        })
        //tabledata
        xAxisData.forEach((item2, index2) => {
          let dt = {
            recordedAt: item2
          };
          legendData.forEach((item, index) => {
            const it = series.find(item2 => item2.name === item);
            dt[`groupIdx_${index}`] = it.data[index2].toFixed(2);
            if (item.indexOf("同比") > -1) {
              dt[`groupIdx_${index}_p`] = parseFloat(dt[`groupIdx_${index}`])  === 0 ? '--' : ((parseFloat(dt[`groupIdx_${index -1}`]) / parseFloat(dt[`groupIdx_${index}`])) * 100).toFixed(2) + "%"
            }
          })
          tableData.push(dt)
        })
        this.columns = [...columns];
        this.tableData = [...tableData];
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout(); // 确保表格重新布局
        });
      },
      resetTableData() {
        this.columns = [];
        this.tableData = [];
      },
      setxAxisData() {
        const xAxisData = [];
        const from = this.$moment(this.queryParams.dateArea[0]);
        const to = this.$moment(this.queryParams.dateArea[1]);
        let currentDate = from;
        let maxNum = 0;
        const format = this.getDatePickerType("momentFormat");
        const code = this.getDatePickerType("code");
        while (currentDate.isSameOrBefore(to) && maxNum < 9999) { // 包含结束日期
          xAxisData.push(currentDate.format(format)); // 格式化为字符串
          currentDate.add(1, code); // 逐日增加
          maxNum++;
        }
        this.chartOption.xAxis.data = xAxisData;
      },
      defaultEchartSet() {
        this.setxAxisData();
        const xAxisData = this.chartOption.xAxis.data;
        let list = [];
        const series = [];
        xAxisData.forEach(i => {
          list.push(0);
        })
        let seriesItem = {
          "name": "",
          type: "bar",
          "barMaxWidth": 20,
          "data": [...list]
        }
        series.push(seriesItem);
        this.chartOption.series = series;
      },
    },
    async created() {
       this.chartOption = { ...this.defaultOptions };
       this.getGroup()
       await this.getEnergyTypes();
    },
    mounted() {
    }
};
</script>

<style scoped lang='scss'>
.data-compare {
    height: 100%;
    .main {
        display: flex;
        height: calc(100% - 30px);
        padding: 12px;
        overflow-x: auto;

        .left {
            background: linear-gradient(180deg, rgba(6, 16, 31, 0) 0%, #06101F 26%, #0A1B2C 100%);
            border: 1px solid;
            border-image: linear-gradient(360deg, rgba(46, 98, 145, 0.6), rgba(46, 98, 145, 0)) 1 1;
            padding: 12px;
            width: 300px;
            flex-shrink: 0;

            &.left2 {
                margin-left: 12px;
            }

            .title {
                font-size: 16px;
                color: #FFFFFF;
            }

            .type {
                height: calc(100% - 30px);
                margin: 16px 0;

                span {
                    width: 70px;
                    flex-shrink: 0;
                    font-size: 14px;
                }

                .filter-tree {
                    height: calc(100% - 46px);
                    overflow-y: auto;
                }

                ::v-deep .el-input__inner {
                    border-color: #2294FE;
                }
            }

            .checkbox {
                height: calc(100% - 120px);

                ::v-deep .el-input__inner {
                    border-color: rgba(34, 148, 254, 0.50);
                }

                .el-checkbox-group {
                    display: flex;
                    flex-direction: column;
                    height: calc(100% - 40px);
                    overflow-y: auto;
                    margin-top: 20px;

                    .el-checkbox {
                        margin-top: 20px;

                        &:first-child {
                            margin-top: 0;
                        }
                    }
                }
            }
        }


        .right {
            width: 100%;
            overflow-x: auto;
            margin-left: 12px;
            background: linear-gradient(180deg, rgba(6, 16, 31, 0) 0%, #06101F 26%, #0A1B2C 100%);
            border: 1px solid;
            border-image: linear-gradient(360deg, rgba(46, 98, 145, 0.6), rgba(46, 98, 145, 0)) 1 1;
            padding: 12px;

            .filters {
                display: flex;
                align-items: center;
                justify-content: space-between;
                font-size: 14px;



                .lt {
                    display: flex;
                    align-items: center;

                    .el-date-editor {
                        width: 264px;
                        margin-right: 16px;
                    }

                    .el-select {
                        min-width: 100px;
                    }

                    .search {
                        background: linear-gradient(360deg, #116CD7 0%, #06101F 100%);
                        box-shadow: inset 0px 0px 6px 0px #2294FE;
                        border-radius: 2px 2px 2px 2px;
                        border: 1px solid #3B79C6;
                        font-size: 14px;
                        color: #D0DEEE;
                        width: 60px;
                        height: 28px;
                        line-height: 28px;
                        text-align: center;
                        margin-left: 44px;
                        cursor: pointer;
                    }
                }

                .rt {
                    background: linear-gradient(360deg, #116CD7 0%, #06101F 100%);
                    border-radius: 2px 2px 2px 2px;
                    border: 1px solid #3B79C6;
                    font-size: 14px;
                    color: #D0DEEE;
                    width: 60px;
                    height: 28px;
                    line-height: 28px;
                    text-align: center;
                }
            }

            .chart {
                margin-top: 16px;

                .device_select {
                    width: 300px;
                }

                .chart-body {
                    padding: 10px 0 0;
                }
            }
        }
    }
}
</style>
