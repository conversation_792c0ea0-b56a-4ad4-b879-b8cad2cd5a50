/**
* 通用css样式布局处理
* Copyright (c) 2019 ruoyi
*/

#app {
  height: 100%;
  color: #fff;
}

/** 基础通用 **/
* {
  margin: 0;
  padding: 0;
}
img {
  box-sizing: border-box;
  vertical-align: middle;
}
.p0 {
  padding: 0 !important;
}
.pt5 {
  padding-top: 5px;
}
.pr5 {
  padding-right: 5px;
}
.pb5 {
  padding-bottom: 5px;
}

.p15 {
  padding: 15px;
}
.pl0 { padding-left: 0px !important; }
.pl15 {
  padding-left: 15px;
}
.pr15 {
  padding-right: 15px;
}
.mt3 {
  margin-top: 3px !important;
}
.mt5 {
  margin-top: 5px;
}
.mr5 {
  margin-right: 5px;
}
.mb0 {
  margin-bottom: 0px !important;
}
.mb5 {
  margin-bottom: 5px;
}
.mb8 {
  margin-bottom: 8px;
}
.ml5 {
  margin-left: 5px;
}
.mt10 {
  margin-top: 10px;
}
.mr0 {
  margin-right: 0 !important;
}
.mr10 {
  margin-right: 10px !important;
}
.mb10 {
  margin-bottom: 10px !important;
}
.ml0 {
  margin-left: 0px !important;
}
.ml10 {
  margin-left: 10px !important;
}
.mt20 {
  margin-top: 20px !important;
}
.mr20 {
  margin-right: 20px !important;
}
.mr30 {
  margin-right: 30px !important;
}
.mb20 {
  margin-bottom: 20px;
}
.mb30 {
  margin-bottom: 30px;
}
.mb15 {
  margin-bottom: 15px;
}
.m10 {
  margin: 10px;
}
.m20 {
  margin: 20px;
}
.ml20 {
  margin-left: 20px;
}
.txt_align_right {
  text-align: right;
}

.w80 { width: 80px!important; }
.w100 { width: 100px!important; }
.w120 { width: 120px!important; }
.w140 { width: 140px!important; }
.w160 { width: 160px!important; }
.w180 { width: 180px!important; }
.w240 { width: 240px!important; }
.w300 { width: 300px!important; }
.w360 { width: 360px!important; }

.t10 { top: 10px!important; }

.inlineBlock { display: inline-block; }

.verticalSplitLine { width:15px; height:15px;  border-left:1px solid rgb(108, 128, 151); vertical-align:middle; display: inline-block; }

.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-column {
  display: flex;
  flex-direction: column;
}

.el-menu--vertical { background: rgba(6, 16, 31, 0.95); }
.el-dialog {
  border:1px solid #ddd;
  box-shadow:0 2px 8px rgba(0,0,0,0.75);
}
.el-table {
  .el-table__header-wrapper, .el-table__fixed-header-wrapper {
    th {
      word-break: break-word;
      font-weight:bold;
    }
  }
  .el-table__body-wrapper {
    .el-button [class*="el-icon-"] + span {
      margin-left: 1px;
    }
  }
}
.loading .el-loading-spinner{
  i,
  .el-loading-text {
    color: #2294FE;
  }
}

.el-transfer-panel { width: 310px !important; }
.el-transfer__buttons { width: 115px; }
.el-transfer__buttons .el-button { margin: 0px!important; }

.el-menu--horizontal .el-menu .el-menu-item:hover {
  color: #fff !important;
}
.el-menu--horizontal .el-menu .is-active {
  color: #fff !important;
  background: linear-gradient(90deg, #2294fe, rgba(34, 148, 254, 0));
  box-shadow: inset 4px 0 0 #3cccf9;
}

.el-message-box__message p { word-break: break-all; }

.page_nav {
  position: relative;
  height: 36px;
  z-index: 221;
  background: url(/image/co2_nav_bg.png) no-repeat left center;
  background-size: auto 100%;
  .text {
    position: absolute;
    font-size: 16px;
  }
}
.new_page_nav {
  position: relative;
  height: 36px;
  background: url(/image/co2_nav_bg.png) no-repeat left center;
  background-size: auto 100%;
  &::after2 {
    content: '';
    position: absolute;
    top: 15px;
    left: 948px;
    right: 0;
    height: 14px;
    border-top: 2px solid #2B5989;
    background: rgba(0,82,207,0.24);
  }
  .text {
    position: absolute;
    top: 6px;
    left: 26px;
    font-family: pangmenzhendao;
    font-size: 20px;
  }
  .nav_select {
    top: 40%;
  }
}

.page_nav2 {
  padding: 0 15px;
  height: 36px;
  line-height: 36px;
  font-size: 14px;
  background: linear-gradient(90deg, rgba(1, 59, 152, 0.26) 0%, rgba(2, 73, 188, 0.26) 49%, rgba(1, 115, 245, 0) 100%);
  span {
    float: right;
    font-size: 14px;
    color: #B1C6D5;
    b {
      padding-right: 5px;
      font-size: 18px;
      color: #19ECFF;
    }
  }
}

.device_run_sataus {
  position: relative;
  display: inline-block;
  padding-left: 15px;
  width: 50px;
  height: 23px;
  line-height: 23px;
  font-size: 12px;
  text-align: left;
  color: #E45757;
  border: 1px solid #E45757;
  border-radius: 20px;
  &:hover {
    opacity: 0.6;
  }
  &::before {
    content: '';
    position: absolute;
    top: 8px;
    left: 6px;
    width: 5px;
    height: 5px;
    background-color: #E45757;
    border-radius: 50%;
  }
  &.is_open {
    color: #3CCCF9;
    border-color: #3CCCF9;
    &::before {
      background-color: #3CCCF9;
    }
  }
}

/** 表单布局 **/
.form-header {
  font-size:15px;
  color:#6379bb;
  border-bottom:1px solid #ddd;
  margin:8px 10px 25px 10px;
  padding-bottom:5px
}

/** 表格布局 **/
.pagination-container {
  position: relative;
  height: 25px;
  margin-bottom: 10px;
  margin-top: 15px;
  padding: 10px 20px !important;
}

/* tree border */
.tree-border {
  margin-top: 5px;
  border: 1px solid #e5e6e7;
  background: #FFFFFF none;
  border-radius:4px;
}

.pagination-container .el-pagination {
  right: 0;
  position: absolute;
}

// .el-table .fixed-width .el-button--mini {
// 	padding-left: 0;
// 	padding-right: 0;
// 	width: inherit;
// }

.el-tree-node__content > .el-checkbox {
  margin-right: 8px;
}

.list-group-striped > .list-group-item {
  border-left: 0;
  border-right: 0;
  border-radius: 0;
  padding-left: 0;
  padding-right: 0;
}

.list-group {
  margin-top: 20px;
  padding-left: 0px;
  list-style: none;
}

.list-group-item {
  border-bottom: 1px solid rgba(108, 128, 151, 0.2);
  border-top: 1px solid rgba(108, 128, 151, 0.2);
  margin-bottom: -1px;
  padding: 11px 0px;
  font-size: 13px;
}
.list-group-item .svg-icon {
  margin-right: 6px;
}

.pull-right {
  float: right !important;
}

.el-card__header {
  padding: 14px 15px 7px;
  min-height: 40px;
}

.el-card__body {
  padding: 15px 20px 20px 20px;
}

.card-box {
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 10px;
}

/* button color */
.el-button--cyan.is-active,
.el-button--cyan:active {
  background: #20B2AA;
  border-color: #20B2AA;
  color: #FFFFFF;
}

.el-button--cyan:focus,
.el-button--cyan:hover {
  background: #48D1CC;
  border-color: #48D1CC;
  color: #FFFFFF;
}

.el-button--cyan {
  background-color: #20B2AA;
  border-color: #20B2AA;
  color: #FFFFFF;
}

/* text color */
.text-navy {
  color: #1ab394;
}

.text-primary {
  color: inherit;
}

.text-success {
  color: #1c84c6;
}

.text-info {
  color: #23c6c8;
}

.text-warning {
  color: #f8ac59;
}

.text-danger {
  color: #ed5565;
}

.text-muted {
  color: #888888;
}

/* image */
.img-circle {
  border-radius: 50%;
}

.img-lg {
  width: 120px;
  height: 120px;
}

.avatar-upload-preview {
  position: absolute;
  top: 50%;
  transform: translate(50%, -50%);
  width: 180px;
  height: 180px;
  border-radius: 50%;
  box-shadow: 0 0 4px #ccc;
  overflow: hidden;
}

/* 拖拽列样式 */
.sortable-ghost{
  opacity: .8;
  color: #fff!important;
  background: #42b983!important;
}

.top-right-btn {
  position: relative;
  float: right;
}

.sidebar-container { border:none; border-right:1px solid #6C8097;
  box-shadow:5px 0 10px rgba(0, 0, 0, 0.5); }
.sidebar-container .el-scrollbar__view > .el-menu::after {
  height: 120px;
}
#app .sidebar-container .el-scrollbar__view { padding-top: 30px; }

.image-slot-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: rgba(36, 46, 61, 0.9);
  color: #ddd;
  font-size: 20px;
}

.page-title { font-weight:bold; font-size:90%; margin:5px 15px 15px 0; cursor: pointer; }

.tb { font-weight:bold; }
.f120 { font-size: 120%; }
.cb { color: #2294FE; }
.cy { color: #D78737; }
.cg { color: #6C8097; }
.cgy { color: #b1c6d5; }
.cgr { color: #0DD3A3; }

.b1b { border:1px solid #2294FE; }
.b1y { border:1px solid #D78737; }
.b1g { border:1px solid #6C8097; }
.b1gy { border:1px solid #b1c6d5; }
.b1gr { border:1px solid #0DD3A3; }

.flex { display:flex; }
.flex-column { flex-direction: column; /* 垂直方向排列 */ }
.flex-1 { flex-grow: 1; }
.flex-0 { flex-grow: 0; }

.flex-sp { justify-content: space-between; }
.flex-sa { justify-content: space-around; }
.flex-fc { justify-content: flex-center; }
.flex-fe { justify-content: flex-end; }

.p10 { padding:10px !important; }
.p20 { padding:20px !important; }
.pt0 { padding-top: 0px !important; }
.pt5 { padding-top: 5px !important; }
.pt10 { padding-top: 10px !important; }
.pt15 { padding-top: 15px !important; }
.pt20 { padding-top:20px !important; }
.pt40 { padding-top:40px !important; }
.pt60 { padding-top:60px !important; }
.pt80 { padding-top:80px !important; }
.pt100 { padding-top:100px !important; }
.pl20 { padding-left:20px !important; }
.pb0 { padding-bottom: 0px !important; }
.pr5 { padding-right: 5px !important; }
.pr10 { padding-right: 10px !important; }

.mt10 { margin-top:10px !important; }
.mt20 { margin-top:20px !important; }
.mt30 { margin-top:30px !important; }
.mt40 { margin-top:40px !important; }
.mt50 { margin-top:50px !important; }
.mt60 { margin-top:60px !important; }

.ml5  { margin-left:  5px !important; }
.ml10 { margin-left: 10px !important; }
.ml20 { margin-left: 20px !important; }
.ml30 { margin-left: 30px !important; }
.ml50 { margin-left: 50px !important; }

.mr5  { margin-right:  5px !important; }
.mr10 { margin-right: 10px !important; }
.mr20 { margin-right: 20px !important; }
.mr30 { margin-right: 30px !important; }
.mr50 { margin-right: 50px !important; }

.mb0 {  margin-bottom:  0px !important; }
.mb3 {  margin-bottom:  3px !important; }
.mb10 { margin-bottom: 10px !important; }
.mb20 { margin-bottom: 20px !important; }
.mb30 { margin-bottom: 30px !important; }
.mb50 { margin-bottom: 50px !important; }


.bt1 { border-top:1px solid #ddd; }
.br1 { border-right:1px solid #ddd; }
.bb1 { border-bottom:1px solid #ddd; }
.bb5 { border-bottom: 10rpx solid #f2f2f2; }
.bb10 { border-bottom: 20rpx solid #f2f2f2; }
.bb60 { border-bottom: 120rpx solid #f2f2f2; }
.bb70 { border-bottom: 140rpx solid #f2f2f2; }

.w50 { width:50%; }
.w33 { width:33.33%; }
.w25 { width:25%; }

.wr30 { width: 30%; }
.wr40 { width: 40%; }
.wr50 { width: 50%; }
.wr60 { width: 60%; }
.wr70 { width: 70%; }


.curPo { cursor: pointer; }

.icon50 .icon_box img { width: 50px; }

h3 .time-span { font-size:16px; color: rgba(255,255,255,0.75); }
.block_border {
  border: 1px solid #2294FE;
  border-image: linear-gradient(180deg, rgba(34, 148, 254, 0.3), rgba(34, 148, 254, 0.5)) 1 1;
}

.el-table tr.stop-row { color: #5F7C9E; }
.el-table tr.running-row { color: #21BD69; }
.el-table tr.fault-row { color: #CA8F30; }
.el-table tr.warning-row { color: #BD2D2D; }
.el-table tr.offline-row { color: #C4C4C4; }

/** dashboard 公共布局 **/
.dataView { background-color: #000205; min-height:100vh; height:100%; color:#fff; }
.dataView .el-main {  color: #fff; padding:0px; width:100%; height:100%; }
.dataView .dataMain { margin-top:100px; height: calc(100vh - 100px); overflow:auto; }

.dataView .dvHeader { width:100%; height:80px; display:flex; justify-content: space-between; position:fixed; top:0px; background: url(../image/header_bg_new.png)  center top no-repeat; padding:0px; background-size:130% 85px; }
.dataView ._logo { font-size:18px; font-weight:bold; margin:0px 5px 5px; }
.dataView ._logos { font-size:28px; line-height:40px; height:40px; margin:0px 0px 0px 5px; white-space: nowrap; font-family: 'Alimama_ShuHeiTi_Bold'; padding-top:3px; }
.dataView .sidebar-title { margin: 0 8px; }
// .dataView ._logos {display: flex;align-items: center;height: 90px; font-size: 26px; color: #FBFBFE; font-weight:bold; margin:0 20px; font-family: 'Alibaba PuHuiTi';text-shadow: 0px 0px 8px rgba(34, 148, 254, 0.26);}
.dataView .dvHeader h2 { font-size:16px; margin-left:13px; white-space: nowrap; }

.dataView .menus { height:40px; margin-top:36px; display: flex; display: -webkit-flex; flex-wrap: nowrap; justify-content: space-around; align-items: stretch; font-size:18px; color:rgba(255,255,255,0.6); margin-left: 30px; }
.dataView .menus .menu { padding:5px 16px; }
.dataView .menus .menu:hover { color: #00FFD2; }
.dataView .menus .menu .svg-icon { margin-right: 8px; font-size:20px; }
.dataView .menus .active { color: #00FFD2; font-weight:bold; border-bottom:3px solid #00FFD2; }
.dataView .menus .menu:hover .svg-icon,
.dataView .menus .active .svg-icon { color: #00FFD2; }
.dataView .menus .el-menu--horizontal > .el-menu-item { height: 40px !important;}

.dataView .el-main aside { padding:0px 20px 8px 20px; background:rgba(2,10,36, 1); color:#fff;  }

.dataView .el-main .acCard .item,
.dataView .el-main .deviceDetail .item { border-color: rgba(255,255,255,0.5); }

.dataView .el-main .el-collapse-item__wrap,
.dataView .el-main .el-collapse-item__header { background-color: transparent; }
/*
.dataView .el-tabs__item { color: #6C8097; }
.dataView .el-tabs__item.is-active { color: #14fcd5; }
*/
.dataView ::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}
.dataView ::-webkit-scrollbar-thumb {
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(255, 255, 255, 255.2);
  background: rgba(255, 255, 255, 0.2);
}
.dataView ::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.2);
  border-radius: 0;
  background: rgba(255, 255, 255, 0.1);
}

/** 表单、弹窗样式重置 2022-04-16  start**/
body {
  .el-dialog {
    box-shadow:0 2px 8px rgba(0,0,0,0.75);
  }
  // 输入框、选择框
  .el-input__inner,
  .el-textarea__inner,
  .vue-treeselect__control  {
    color: #E2EAF0;
    border: 1px solid #087198;
    background: rgba(255, 255, 255, 0.002);
    &:hover,
    &:focus {
      border-color: #087198;
    }
    &::-webkit-input-placeholder,
    &::-moz-placeholder,
    &::-ms-input-placeholder,
    &::placeholder {
      color: #6c8097;
    }
  }
  .vue-treeselect__placeholder {
    color: #6c8097;
  }
  .el-date-editor .el-range-input {
    color: #fff;
    &::-webkit-input-placeholder,
    &::-moz-placeholder,
    &::-ms-input-placeholder,
    &::placeholder {
      color: #6c8097;
    }
  }
  .vue-treeselect:not(.vue-treeselect--disabled):not(.vue-treeselect--focused) .vue-treeselect__control:hover,
  .el-select .el-input__inner:focus,
  .el-range-editor.is-active {
    border-color: #087198;
  }
  .el-select:hover,
  .el-select .el-input.is-focus {
    .el-input__inner {
      border-color: #087198;
    }
  }
  .el-select .el-tag__close.el-icon-close,
  .el-date-editor .el-range-input {
    background-color: rgba(60, 204, 249, 0.15);
  }
  .el-input__icon,
  .el-input__prefix,
  .el-input__suffix,
  .el-select .el-input .el-select__caret,
  .el-tree-node__expand-icon,
  .vue-treeselect__control-arrow,
  .el-date-editor .el-range__icon,
  .el-date-editor .el-range__close-icon,
  .el-table__expand-icon {
    color: #18C8F2;
  }

  .el-input-number__decrease,
  .el-input-number__increase,
  .el-input-number.is-controls-right .el-input-number__decrease {
    color: #3CCCF9;
    border-color: #087198;
    background-color: rgba(60, 204, 249, 0.15);
  }
  .el-input-number__decrease:hover,
  .el-input-number__increase:hover {
    color: #3CCCF9;
  }
  .el-input-number__decrease:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled),
  .el-input-number__increase:hover:not(.is-disabled)~.el-input .el-input__inner:not(.is-disabled) {
    border-color: #087198;
  }
  .el-input-group__append,
  .el-input-group__prepend,
  .el-input.is-disabled .el-input__inner,
  .el-upload--picture-card,
  .el-upload--picture-card:hover,
  .el-radio__inner,
  .el-checkbox__inner {
    border-color: #087198;
    background-color: rgba(60, 204, 249, 0.15);
  }


  .el-radio__input.is-checked+.el-radio__label,
  .el-checkbox__input.is-checked+.el-checkbox__label {
    color: #2294FE;
  }
  .el-radio__inner:hover,
  .el-checkbox__inner:hover  {
    border-color: #2294FE;
  }
  .el-radio__input.is-checked .el-radio__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner{
    border-color: #2294FE;
    background-color: #2294FE;
  }
  .el-upload--picture-card i {
    color: #087198;
  }
  // 表单弹窗
  .el-popper[x-placement^=bottom],
  .el-popper[x-placement^=left],
  .el-popper[x-placement^=right],
  .el-popper[x-placement^=top] {
    border-color: #087198;
    background-color: #0A192F;
    .popper__arrow {
      &::after {
        border-block-color: #0A192F;
      }
    }
    .el-date-table td.current:not(.disabled) span {
      color: #fff;
      background-color: #2294FE;
    }
  }
  .el-popper[x-placement^=bottom] .popper__arrow {
    border-bottom-color: #087198;
  }
  .el-popper[x-placement^=top] .popper__arrow {
    border-top-color: #087198;
  }
  .el-popper[x-placement^=left] .popper__arrow {
    border-left-color: #087198;
  }
  .el-popper[x-placement^=right] .popper__arrow {
    border-right-color: #087198;
  }
  .el-picker-panel__footer {
    border-color: #087198;
    background-color: #0A192F;
  }
  .el-date-table td.available:hover,
  .el-date-table td.today span,
  .el-select-dropdown__item.selected,
  .el-time-panel__btn.confirm {
    color: #2294FE;
  }
  .el-time-panel {
    border-color: #087198;
    background-color: #0A192F;
  }
  .el-date-picker__time-header,
  .el-date-table th,
  .el-time-panel__footer,
  .el-date-range-picker__content.is-left {
    border-color: #6C8097;
  }
  .el-time-spinner__item.active:not(.disabled) {
    color: #2294FE;
  }
  .el-time-spinner__item:hover:not(.disabled):not(.active),
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover,
  .el-date-table td.in-range div,
  .el-tree-node__content:hover,
  .el-tree-node:focus>.el-tree-node__content {
    background-color: rgba(60, 204, 249, 0.15);
  }
  .el-date-table td.end-date div,
  .el-date-table td.start-date div{
    color: #fff;
    background-color: rgba(60, 204, 249, 0.15);
    span {
      background-color: #2294FE;
    }
  }
  .el-date-table td.in-range div:hover {
    background-color: #087198;
  }

  // 选项卡
  .el-tabs__nav-wrap.is-top{
    .el-tabs__item {
      font-size: 15px;
    }
  }
  .el-tabs__item.is-active,
  .el-tabs__item:hover {
    color: #2294FE;
  }
  .el-tabs__nav-wrap::after {
    background-color: #6C8097;
  }
  .el-tabs__active-bar {
    background-color: #2294FE;
  }
  // 按钮
  .el-button {
    color: #3CCCF9;
    border-color: #087198;
    background-color: transparent;
  }
  .el-button:focus,
  .el-button:hover,
  .el-button--primary,
  .el-button--primary:focus,
  .el-button--primary:hover,
  .el-button.is-plain:focus,
  .el-button.is-plain:hover {
    color: #3CCCF9;
    border-color: #087198;
    background-color: rgba(60, 204, 249, 0.35);
  }

  .el-button--mini {
    padding: 7px 15px;
  }
  // .el-button--primary,
  .el-button--primary:focus,
  .el-button--primary:hover {
    color: #fff;
    border-color: #2294FE;
    background-color: #2294FE;
  }

  .el-button--success {
    color: #fff;
    background-color: #0DD3A3;
    border-color: #0DD3A3;
  }
  .el-button--danger {
    color: #fff;
    background-color: #fc5055;
    border-color: #fc5055;
  }
  .el-button--warning {
    color: #fff;
    background-color: #ef9723;
    border-color: #ef9723;
  }
  .el-button.is-disabled,
  .el-button.is-disabled:focus,
  .el-button.is-disabled:hover {
    border-color: #1f2232;
  }

  .main-card {
    .el-tabs__nav-wrap.is-top {
      padding: 0 30px
    }
    background-color: rgba(6, 16, 31, 0.85);
  }
  .main-card > h3 { margin-bottom: 10px; }

  .new-system-container {
    .el-tabs__item {
      .tab-badge {
        margin-left: 2px;
        padding: 2px 10px;
        background: rgba(108, 128, 151, 0.2);
        border-radius: 50px;
      }
      .tab-badge-active {
        background: #f56c6c !important;
        color: #fff !important;
      }
      &.is-active {
        color: #2294FE;
        .tab-badge {
          background: rgba(34, 148, 254, 0.1);
        }
      }

    }
    .el-divider,
    .el-tabs__nav-wrap::after {
      opacity: 0.2;
      height: 1px;
    }
  }

  // 侧边栏
  aside {
    margin-bottom: 0;
  }
  .el-aside {
    border-right-color: #6C8097;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner{
    background-color: #2294FE;
    border-color: #2294FE;
  }
  .el-checkbox__input.is-focus .el-checkbox__inner{
    border-color: #2294FE;
  }
  .el-checkbox__inner::after {
    border-color: #fff;
  }

  // 弹窗
  .el-message-box {
    border-color: #087198;
    background-color: #0A192F;
  }
  .el-dialog {
    border: 0.5px solid #244E6B;
    background-color: rgba(14,27,44, 0.97);
    // background-color: #0E1B2C;
    // &::before {
    //   content: '';
    //   position: absolute;
    //   bottom: -1px;
    //   left: -1px;
    //   width: 80px;
    //   height: 80px;
    //   background: url(../image/dialog_icon_lb.png) no-repeat bottom left;
    // }
    // &::after {
    //   content: '';
    //   position: absolute;
    //   bottom: -1px;
    //   right: -1px;
    //   width: 30px;
    //   height: 30px;
    //   background: url(../image/dialog_icon_rb.png) no-repeat bottom right;
    // }
    .is-top{
      .el-tabs__item {
        font-size: 14px;
      }
    }
  }
  .common-dialog {
    .el-select,
    .el-cascader,
    .el-date-editor {
      width: 100%;
    }
    .el-icon-question {
        margin-left: 4px;
    }
    .dialog-form {
      padding-right: 20px;
      max-height: 480px;
      overflow-y: auto;
    }
  }
  .el-dialog__header {
    border-bottom: 1px solid #087198;
    position: relative;
    z-index: 10;
    padding-top: 15px;
    padding-bottom: 15px;
    padding-right: 30px;
    padding-left: 30px;
    &::before {
      content: '';
      position: absolute;
      z-index: 5;
      top: -1px;
      left: -1px;
      width: 80px;
      height: 80px;
      background: url(../image/dialog_icon_lt.png) no-repeat top left;
    }
    &::after {
      display: none;
      content: '';
      position: absolute;
      z-index: 5;
      top: -1px;
      right: -1px;
      width: 80px;
      height: 80px;
      background: url(../image/dialog_icon_rt.png) no-repeat top right;
    }
    .el-dialog__headerbtn {
      top: 15px;
      right: 20px;
      z-index: 10;
      &:hover .el-dialog__close,
      .el-dialog__close {
        font-size: 20px;
        font-weight: bolder;
        color: #3CCCF9;
      }
    }
  }
  .el-dialog__body {
    color: #fff;
    position: relative;
    z-index: 10;
    padding-bottom: 20px !important;
    padding-right: 20px !important;
    padding-left: 20px !important;

  }
  .el-dialog__footer {
    position: relative;
    /*z-index: 10;*/
    padding-right: 20px;
    padding-left: 20px;
    padding-bottom: 20px;
  }

  // 树结构
  .el-tree {
    background-color: #0A192F;
    border-color: #087198;
  }
  .el-menu--horizontal>.el-submenu.is-active .el-submenu__title {
    color: #2294FE;
    border-color: #2294FE;
  }
}
/* 资产档案详情单框 */
body .assetsDetail .el-dialog__body {
  padding: 10px 20px!important;
}
body .video-dialog .el-dialog__body {
  padding: 10px 20px!important;
}
.app-main,.co2_main {
  // 表格
  .el-table {
    color: #fff;
    background-color: transparent;
    &::before {
      background-color: transparent;
    }
  }
  .el-table tr {
    background-color: rgba(6, 16, 31, 0.9);
  }
  .el-table td,
  .el-table th.is-leaf {
    border-bottom: none;
  }
  .el-table--striped .el-table__body tr.el-table__row--striped td {
    background-color: rgba(17, 77, 118, 0.25) !important;
  }
  .border-table {
    border-right: 1px solid rgba(108, 128, 151, .5);
    border-top:1px solid rgba(108, 128, 151, .5);
  }
  .border-table td {
    vertical-align: top;
    padding: 10px;
    border-left: 1px solid rgba(108, 128, 151, .5);
  }
  .border-table::before {
    background-color: rgba(108, 128, 151, .5);
  }
  .border-table .border td {
    border-top: 2px solid rgba(108, 128, 151, .5);
    border-bottom: 1px solid rgba(108, 128, 151, .25);
  }
  .el-divider {
    background-color: #6C8097;
    margin-top:10px;
    margin-bottom: 0px;
  }
  .el-table th {
    color: rgba(186, 202, 222, 0.85);
    background-color: #114D76;
  }

  .el-tree {
    background-color: transparent;
  }
  .el-table__fixed::before,
  .el-table__fixed-right::before {
    background-color: transparent;
  }
  .el-table__fixed td,
  .el-table__fixed-right td {
    background-color: transparent !important;
  }
  // 分页
  .el-pagination button:disabled,
  .el-pager li,
  .el-pagination .btn-next, .el-pagination .btn-prev {
    background-color: transparent;
  }
  .el-pager li.active,
  .el-pager li:hover,
  .el-pagination .btn-prev:hover,
  .el-pagination .btn-next:hover {
    color: #2294FE;
  }
  .el-pagination.is-background .el-pager li:not(.disabled).active{
    color: #fff;
    background-color: #2294FE;
  }
  .el-pagination__sizes .el-input .el-input__inner:hover {
    border-color: #087198;
  }
  // 表单选框
  .el-radio-button:first-child .el-radio-button__inner {
    border-left-color: rgba(60, 204, 249, 0.15);
  }
  .el-radio-button__inner {
    color: #BACADE;
    border-color: rgba(60, 204, 249, 0.15);
    background-color: rgba(60, 204, 249, 0.15);
  }
  .el-radio-button__orig-radio:checked+.el-radio-button__inner {
    color: #fff;
    border-color: #2294FE;
    background-color: #2294FE;
    box-shadow: -1px 0 0 0 #2294FE;
  }
  .el-radio-button__inner:hover {
    color: #2294FE;
  }

  .el-switch__core:after {
    background-color: #ffffff;
  }
  .device-summary .el-tag {
    color: #1f2232;
    border-color: transparent;
  }
  .device-summary .el-select .el-tag {
    color: inherit;
    border-color: transparent;
  }

  .el-card {
    border-color: #0A192F;
    background-color: #0A192F;
    .el-card__header {
      border-color: rgba(108, 128, 151, 0.7);
    }
  }

  .el-button--text {
    padding-left: 10px;
    padding-right: 10px;

  }

  .el-button--text,
  .el-button--text:focus,
  .el-button--text:hover {
    color: #3CCCF9;
    border-color: #087198;
  }

  .el-table .el-button {
    margin: 5px 5px 0;
    padding: 5px;
  }
  .fixed-width .el-button--mini {
    width: auto;
  }
  .main-box-card {
    padding-bottom: 20px;
    .el-card__header {
      padding: 5px 10px 0px;
      border: none;
    }
  }
  .el-dialog .el-dialog__title { min-height:15px; display:inline-block; }
}
// 模块导航
.main-card-nav {
  position: relative;
  margin-bottom: 20px;
  height: 38px;
  background: url(../image/co2_nav_bg.png) no-repeat left center;
  background-size: auto 100%;
  overflow: hidden;
  &::after {
    content: '';
    position: absolute;
    top: 16px;
    right: 0;
    left: 385px;
    height: 14px;
    border-top: 2px solid #2B5989;
    background: rgba(0, 82, 207, 0.24);
  }

  .text {
    position: absolute;
    top: 6px;
    left: 28px;
    font-size: 16px;
  }
}

/** 表单、弹窗样式重置  end**/

.energy-summary, .device-summary, .app-container {
  min-height: 100%;
  /* background-color: rgba(6, 16, 31, 0.85); */
}
body .energy-summary > .main-card,
body .device-summary > .main-card,
body .app-container > .main-card {
  background-color: transparent;
}

.energy-summary .el-container {
  min-height: calc(100vh - 185px);
}
/** 能耗 **/
.energy-summary, .inner-page {
  padding: 0px 20px 10px;
}

.energy-summary .main-card h3, .inner-page > h3 { margin-top: 0px;}
.energy-summary .main-card h3 { font-size:20px; margin-top: 0; padding: 5px 0 0; }
.energy-summary .main-card h3 .energy-types { margin-left:30px; vertical-align: middle; }
.energy-summary .main-card h3 .energy-types a { display:inline-block; margin-right:30px; padding:5px 5px 5px 0; font-size:16px; color:#1890ff; font-weight:normal; border-bottom:1px solid #1890ff; position:relative; top:-3px; }
.energy-summary .main-card h3>div>i { margin-left:15px; }
.energy-summary .main-card h3 .el-input__prefix i { margin-left:0px; }
.el-tree-node__content { height:30px; }
.energy-summary .main-card h3 .el-form-item { margin-bottom: 0px; }

.energy-summary .exportBtn { position:absolute; z-index:100; right:40px; }

body .energy-summary .device-summary { padding: 0 20px; }


.time-span {}
.time-span .el-form-item { margin-top:10px; margin-bottom:-10px; }


.el-aside { background:rgba(6, 16, 31, 0.8);; padding:10px 10px 0 10px; border-right:3px solid #DCDFE6;
  transition: width 0.4s; }

.el-aside .left-nav a { display: block; height: 40px; line-height: 40px; font-size: 14px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; font-weight: 400; color:#eee; padding-left:10px; }
.el-aside .left-nav a:hover,
.el-aside .left-nav a.active { color:#00adff;  }
/*.el-aside .left-nav a.active { background:rgb(48, 65, 86); }*/
.el-aside .el-divider { margin: 12px 0;}
.el-aside .el-form { width: 160px; }
.el-aside .el-form-item { margin-bottom: 10px; }
.el-aside .el-date-editor.el-input,
.el-aside .el-date-editor.el-input__inner
.el-aside .el-form-item { width:100%; }

.el-aside .folderBar { position:absolute; z-index:1000; width:10px; padding-top:160px; height:450px; background:#DCDFE6; margin-left:170px; margin-top:-10px; border-radius:10px; cursor:pointer;
  transition: margin-left 0.4s; }
.el-aside .folderBar i { margin-left:-2px; color:#222; }

.panel-group-statics {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  font-size:14px;
  .card-panel {
    flex: 1;
    margin: 0px 20px 0 0;
    min-width:300px;
    max-width:360px;
    border-right:1px solid #ddd;
    padding-right:20px;

    .card-panel-icon {
      font-size: 40px;
      float:left;
      margin-right:20px;
      margin-top:5px;
      color: #409EFF;
    }

    .el-divider { margin:10px 0; }

    .card-panel-row {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      font-size:14px;

      .card-row-text {
        line-height: 1.6;
      }
    }
  }
}
.card-panel:last-child {
  border:none !important;
}

.card-panel-description {
  flex: 1;
  .card-panel-text {
    font-size: 16px;
    margin-bottom:5px;
  }
  .card-panel-num {
    font-size: 20px;
  }
}

.panel-group {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  font-size:14px;
  overflow-x:auto;
  padding-bottom:15px;
  margin-bottom:-15px;
  h4 { margin: 0px; margin-bottom:15px; font-size: 18px; line-height:1; }
  .card-panel {
    flex: 1;
    margin: 0px 20px 0 0;
    min-width:160px;
    border-right:1px solid #6C8097;
    color: rgb(191, 203, 217);
    line-height:2;
  }
}
.energy-summary .el-main { padding:10px 20px !important; }


/** dashboard */
.dataView .el-main aside { padding:8px 20px 8px 20px; background:rgba(2,10,36, 0.7); border:none; }
.dataView .el-header, .dataView .el-main { z-index: 1; }

.ibg { position: fixed; width: 100%; height: 100%; background-position: center center; background-size:contain; background-repeat: no-repeat; }
.vbg { position: fixed; width: 100%; height: 100%; z-index:0; top: 60px; background: rgba(2,10,36, 1); }
.vbg video { width: 100%; }
.vbg source{  height: auto;  width: auto;  }

.block { position: relative; }
.block ._title { font-size:20px; font-weight:bold; padding:10px 0; margin-bottom:15px; border-bottom:5px solid rgba(255,255,255,0.1); }
.block ._title::after { content:""; position:absolute; margin-top:10px; display:block; width: 30px; border-bottom:5px solid #00FFD2; }



/** 设备 **/
.device-summary { padding:10px 20px 10px; }
.device-summary .search-form { display:inline-block; margin:0 15px; width:150px; }
.device-summary .main-card > h3 { font-size: 20px; margin-top:0; padding: 5px 0px 0;}
.device-summary .main-card > h3 .title-icon {font-size: 14px;}
.device-summary .main-card > h3 .title-icon i { margin-left:20px; }
.device-summary .main-card > h3 .title-icon .on { color:#409EFF; }
.device-summary .main-card > h3 .title-icon > em { margin-left: 20px; padding-left: 20px; border-left: 1px solid #ddd; font-style:normal; }
.device-summary .main-card > h3 .title-icon  .el-switch { margin-left: 30px; margin-right: 50px;}
// .device-summary .el-dialog__body { padding: 0px 20px 20px; }
// .device-summary .el-col .el-card__header { padding:10px 20px; width:100%; height:22px; overflow:hidden; text-overflow:ellipsis; white-space:nowrap; }
.device-summary .el-col .el-card__header { padding:0; width:100%;  overflow:hidden; text-overflow:ellipsis; white-space:nowrap; border-color: transparent;}
.device-summary .el-col .el-card__header .svg-icon { margin-right:5px; }
.device-summary .el-card__header .thumb { max-width:30px; max-height:30px; margin-right:5px; vertical-align: middle; }

.device-summary .resourceTabs { height: calc(100vh - 400px); min-height: 420px; }
.device-summary .tabs_cont { position: absolute; top: 100px; right: 0px; width: calc(100% - 80px); }
.device-summary .tabs_cont .innerFrame { width: 100%; height: calc(100vh - 280px); min-height: 360px; }
.device-summary .el-tabs--left, .el-tabs--right { overflow: auto; }
.device-summary .main-card .status-sum {
  display: inline-flex;
  align-items: center;
  margin-left: 28px;
  &>div {
    display: flex;
    align-items: center;
    margin-right: 28px;
    &.devide {
      &::after {
        content: "";
        display: block;
        width: 2px;
        height: 20px;
        border-right: 1px solid rgba(255,255,255,0.48);
        position: relative;
        left: 14px;
      }
    }
    .name {
      font-weight: 400;
      font-size: 14px;
      color: rgba(255,255,255,0.88);
    }
    .num {
      font-weight: 500;
      font-size: 20px;
    }
  }
}
.device-summary .main-card .page-title {
  i {
    font-size: 20px;
    color: #2294FE;
    font-weight: bold;
    line-height: 20px;
    position: relative;
    top: 2px;
  }
}
.resourceTabsParent { margin: 0 0px; }

/**** 设备统计 ****/
.device-sum { padding: 0 0px; }
.global-control .el-tag { font-size:14px; font-weight:normal; padding: 3px 8px 5px; height:auto; margin-top:0px; margin-bottom: 5px;
  color:#fff!important;
  border:1px solid #275681!important;
  background: linear-gradient(0deg, rgba(34, 148, 254, 0.35) 0%, rgba(34, 148, 254, 0.08) 30%, rgba(34, 148, 254, 0) 100%, rgba(34, 148, 254, 0.45) 100%, )!important;
}
.global-control .el-tag-active {
  background: linear-gradient(180deg, rgba(34, 148, 254, 0.55) 0%, rgba(34, 148, 254, 0.28) 30%, rgba(34, 148, 254, 0) 100%, rgba(34, 148, 254, 0.65) 100%) !important;
}

.global-control .el-tag span { font-size:24px; margin:0 5px 0 10px;}
.global-control .el-tag::before {
  content: "\e717";
  font-family: element-icons!important;
  speak: none;
  font-style: normal;
  font-weight: 400;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: baseline;
  display: inline-block;
  -webkit-font-smoothing: antialiased;
  border: 0.5px solid #ddd;
  margin-right: 10px;
  border-radius: 50%;
  padding: 3px;
}
.global-control .el-tag--primary span, .global-control .el-tag--primary::before { color: #2294FE; border-color:#2294FE; content: "\e717"; }
.global-control .el-tag--success span, .global-control .el-tag--success::before { color: #0DD3A3; border-color:#0DD3A3; content: "\e6f6"; }
.global-control .el-tag--danger span, .global-control .el-tag--danger::before { color: #CA334E; border-color:#CA334E; content: "\e71b"; }
.global-control .el-tag--warning span, .global-control .el-tag--warning::before { color: #BD2D2D; border-color:#BD2D2D; content: "\e725"; }
.global-control .el-tag--default span, .global-control .el-tag--default::before { color: #CA8F30; border-color:#CA8F30; content: "\e780"; }
.global-control .el-tag--info span, .global-control .el-tag--info::before { color: #999; border-color:#999; content: "\e753"; }
.global-control .el-tag--danger span, .global-control .el-tag--danger::before { color: #5b7086; border-color:#5b7086;  content: "\e71b"; }

.global-control .el-tag--mini { font-size:12px; padding: 2px 5px; margin-bottom: 0px; margin-left:-5px; }
.global-control .el-tag--mini::before { content: "\e7bb"; font-size:10px; border:none; margin:0px; margin-left:-5px; }
.global-control .el-tag--mini::before { content: ""; }

/** 设备下面汇总统计 **/

//统计页面样式
.data-summary {
  width: 100%;
  padding-top: 16px;
  .title {
    font-size: 16px;
    margin-bottom: 8px;
  }
  .cards {
    width: 100%;
    margin-left: 0 !important;
    margin-right: 0 !important;
    .block {
      border-radius: 4px 4px 4px 4px;
      border: 1px solid rgba(34,148,254,0.35);
      padding: 16px 24px 6px;
      background: rgba(11,26,50,0.6);
      // height: 120px;
      &_head {
        display: flex;
        align-items: center;
        border-bottom: 1px solid rgba(17, 66, 116, .4);
        padding-bottom: 8px;
        margin-bottom: 16px;
        .icon {
          width: 16px;
          margin-left: 14px;
        }
        &_title {
          border-left: 4px solid #2294FE;
          padding-left: 8px;
          line-height: 17px;
        }
      }
      &_list {
        display: flex;
        // justify-content: space-between;
        flex-wrap: wrap;
        // overflow-x: auto;
        padding-bottom: 6px;
        &_item {
          .val {
            color: #3CCCF9;
            font-size: 20px;
            font-weight: 400;
          }
          .name {
            font-size: 14px;
            letter-spacing: 1px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .unit {
            color: #fff;
            font-size: 14px;
          }
        }
      }
    }
  }
}
/* 设备下的控制卡片显示 */
.map_control_cards {
  width: calc(100% - 16px);
  margin: 8px 0 0 8px;
  // border: 1px solid rgba(34,148,254,0.35);
  padding: 2px;
  background: rgba(11,26,50,0.6);
  position: relative;
  .title {
    font-size: 16px;
    margin-bottom: 8px;
  }
  .cards {
    width: 100%;
    margin-left: 0 !important;
    margin-right: 0 !important;
    // display: flex;
    overflow-x: auto;
    &>div {
      min-width: 25%;
    }
    .block {
      border-radius: 4px 4px 4px 4px;
      padding: 8px 15px;
      background: none;
      &_head {
        display: flex;
        align-items: center;
        padding-bottom: 8px;
        margin-bottom: 8px;
        .icon {
          width: 16px;
          margin-left: 14px;
        }
        &_title {
          border-left: 4px solid #2294FE;
          padding-left: 8px;
          line-height: 17px;
        }
      }
      &_list {
        display: flex;
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 6px;
        background: rgba(13,35,65,0.5);
        flex-wrap: wrap;
        // padding: 8px 10%;
        &_item {
          width: calc(50% - 12px);
          border-image: linear-gradient(180deg, rgba(60, 204, 249, 0), rgba(34, 148, 254, 0.23)) 1 1;
          background: #0F356B;
          border-radius: 4px 4px 4px 4px;
          text-align: center;
          margin: 5px;
          .name {
            font-size: 14px;
            letter-spacing: 1px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding: 6px 0;
            background: #0F356B;
            opacity: .5;
            cursor: pointer;
            height: 32px;
            &.work {
              background: linear-gradient( 180deg, rgba(15,53,107,0) 0%, rgba(34,148,254,0.12) 100%), rgba(255,255,255,0);
              box-shadow: inset 0px 0px 6px 0px #2294FE;
              border-radius: 4px 4px 4px 4px;
              border: 1px solid rgba(34,148,254,0.8);
              opacity: 1;
            }
          }
        }
      }
    }
  }
  .link {
    position: absolute;
    right: 20px;
    top: 10px;
    color: #3492fa;
    cursor: pointer;

  }
}



.detail-list { font-size:12px; line-height: 2.8; }
.detail-list .item {}
.detail-list .item .val { float: right; color: #1890ff; margin-left: 10px; display: inline-block; width: 30px; height:20px; }
.detail-list .item .val-unit { float: right; margin-left: 10px; }
.detail-list h3 { margin:0px;  }
.detail-list .item .el-button { display: block; margin-bottom: 10px; margin-left: 0px; }

/*** 设备页 ***/
.global-control { font-size:12px; }
.global-control .item { float:left; margin: 0 20px 0 0px; line-height: 35px; height: 35px; }
.global-control .item .pull-right { margin:0 0px 0 5px; }
.global-control .item .el-input,
.global-control .item .el-select{ width: 90px; }

.deviceDetail { padding:0 15px 15px 15px; }
.deviceDetail .el-dialog__body { padding:20px 20px; }
.deviceDetail .item { line-height:3; border-bottom:1px dashed #eee; }
.deviceDetail .el-collapse .el-collapse-item .item:last-child { border:none; }
.deviceDetail .item .label { width:90px; display:inline-block; }
.deviceDetail .item .labelw { margin-left:5px; }
.el-drawer{ overflow: scroll; }
.deviceDetail .el-collapse-item__header i.el-icon-info { margin-left:15px; }
.deviceDetail .runStatus .item .pull-right { width:120px; text-align:right; }
.deviceDetail .item .pull-right { width:120px; text-align:right; }
.deviceDetail .item .pull-right .el-input-number--mini { width:120px; }

.el-row {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
}
.el-col {
  border-radius: 4px;
  margin-bottom: 10px;
}
.demo-form-inline { margin-bottom: 20px; }
.el-table .el-button+.el-button { margin-left:0px; }

/*** 设备页 -- 卡片展示 ***/
.box-card { min-width: 160px; padding-bottom:20px; }
.box-card .el-card__body { padding-top:5px; padding-bottom:5px; }
.box-card .card-title { font-weight:bold; }
.box-card .item, .box-card .item2 {
  height: 40px;
  line-height:40px;
  font-size: 12px;
  padding-left: 10px;
  padding-right: 10px;
  border-bottom:1px dashed rgba(108, 128, 151, 0.5);
}
.box-card .item > span {
  word-break: keep-all;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 200px;
  display: inline-block;
}
.box-card .item > span.label {
  width: 80px;
}

.box-card .item:last-child {
  margin-bottom: 0;
  padding-bottom: 0px;
}
.box-card .item .pull-right,
.box-card .item .pull-right { width: fit-content; text-align:right; }
.box-card .item .pull-right .el-select,
.box-card .itemLine .pull-right .el-select { width:140px; }
.box-card .item .pull-right .el-input,
.box-card .itemLine .pull-right .el-input { width:140px; }
.box-card .item .pull-right .el-input-number--mini,
.box-card .itemLine .pull-right .el-input-number--mini { width:140px; }
.box-card .item .el-button--mini,
.box-card .itemLine .el-button--mini { color: #3CCCF9; }

.box-card .item-small { height: 28px; line-height:28px; border-bottom:none; }

.item-detail { margin-top:10px; margin-bottom:10px; }
.item-detail .item { line-height:2; height:auto; border:none; }
.item-detail .item .label { color:#fff; margin-right:15px; }
.item-detail .item > span { max-width: 89px; }

.baseMult { margin: 0 10px 10px 0; }
.baseMult .el-tabs.el-tabs--top { height: 400px; }
.baseMult .el-tabs.el-tabs--top > .el-tabs__content {height: 345px; overflow: auto;}
.baseMult .el-tabs.el-tabs--top .el-tabs__header.is-left {height: 325px;}
.baseMult .el-input__inner { border-color:#087198; }
.baseMult .el-date-editor { margin-right:10px; }

.baseMult .el-card .el-card__header { padding: 8px 15px 0px; }
.baseMult .header { border-bottom: none; padding: 2px 0 8px 5px; }
.baseMult .header .text { left: 12px; }
.baseMult .header .titleIcon { margin-right:5px; }
.baseMult .header .itemName { display: inline-block; width: calc(100% - 50px); height:24px; white-space: nowrap;  overflow: hidden; position: relative; top: -3px; margin-bottom: -3px; font-size: 14px;}
.baseMult .header .iconLink { cursor: pointer; }
.baseMult .header .itemRight { max-width: calc(100% - 70px); white-space: nowrap; text-overflow: ellipsis; overflow: hidden; }
.baseMult .header .itemRight:hover { overflow: visible; text-overflow: clip; }

.baseMult {  border: 1px solid #087198; width: 250px; float: left; }
.baseMult .box-card { margin-left: -10px; margin-right: -10px; margin-top: -5px; margin-bottom: -10px; }

.displayByMapEdit .el-tabs__content { overflow-x: auto; }

/*********** airConditionCard ***********/


.airConditionCard { width: 280px; font-size: 12px; float: left;background: rgba(6,36,21,0.9);box-shadow: inset 0px 0px 30px 0px rgba(33,189,105,0.4);border-radius: 5px;border: 1px solid rgba(33,189,105,0.5);}
.airConditionCard .header { padding: 12px; padding-bottom: 6px; border-bottom: 1px solid rgba(108, 128, 151, 0.2); font-size: 14px; }
.airConditionCard .header .itemName { display: inline-block; width: calc(100% - 45px); white-space: nowrap;  overflow: hidden; position: relative; top: -3px; margin-bottom: -3px; }
.airConditionCard .header .titleIcon { margin-right: 5px; }
.airConditionCard .header .iconLink { margin-right: 0px; }
.airConditionCard .header .chbox { position: relative; top: -6px; }

.airConditionCard .body { padding: 12px; }
.airConditionCard .body .bls { padding: 0 8px; }
.airConditionCard .body hr {  margin-bottom: 10px; height: 0px; border: none; border-bottom: 1px solid rgba(108, 128, 151, 0.2); }
.airConditionCard .body .bl { text-align: center; overflow: hidden; }
.airConditionCard .body .bl .v { font-size: 20px; line-height: 1.25; display: inline-block; height: 45px; word-break: keep-all; }
.airConditionCard .body .bl .u { display: block; font-size: 12px; color: #6C8097; width: 100%; height:20px;  height:14px; word-break: keep-all; }
.airConditionCard .body .bl .n { font-size: 12px; color: #fff;  height:16px; word-break: keep-all; overflow: hidden; white-space: nowrap;  text-overflow: ellipsis; max-width: 68px;  }
.airConditionCard .body .bl .el-divider { margin: 0px; height:24px; }

.airConditionCard .body .el-tag { margin: 5px 5px 5px 0; }

.airConditionCard .itemRight { max-width: calc(100% - 70px); white-space: nowrap; text-overflow: ellipsis; overflow: hidden; }
.airConditionCard .itemRight:hover { overflow: visible; text-overflow: clip; }

.airConditionCard .el-card__header { padding: 0px; }

.infoOut { border-color: #58697C; background: linear-gradient(43.97deg, rgba(88, 105, 124, 0.05) 43.73%, rgba(88, 105, 124, 0.2) 83.87%); }
// .runningOut { border-color: rgba(33, 189, 105, 0.16); background: linear-gradient(43.97deg, rgba(13, 211, 163, 0.15) 43.73%, rgba(13, 211, 163, 0.3) 83.87%); }
// .warningOut { border-color: rgba(189, 45, 45, 0.16); background: linear-gradient(43.97deg, rgba(215, 135, 55, 0.15) 43.73%, rgba(215, 135, 55, 0.3) 83.87%); }
// .offlineOut { border-color: rgba(95, 124, 158, 0.40); background: linear-gradient(43.97deg, rgba(202, 51, 78, 0.15) 43.73%, rgba(202, 51, 78, 0.3) 83.87%); }

.offlineOut { background: rgba(31,31,31,0.88);box-shadow: inset 0px 0px 30px 0px rgba(196,196,196,0.6);border-radius: 5px;border: 1px solid rgba(196,196,196,0.5);}
.runningOut { background: rgba(6,36,21,0.9);box-shadow: inset 0px 0px 30px 0px rgba(33,189,105,0.4);border-radius: 5px;border: 1px solid rgba(33,189,105,0.5);}
.faultOut { background: rgba(36,24,6,0.9);box-shadow: inset 0px 0px 30px 0px rgba(202,143,48,0.4);border-radius: 5px;border: 1px solid rgba(202,143,48,0.6); }
.stopOut { background: rgba(11,25,41,0.9);box-shadow: inset 0px 0px 30px 0px rgba(95,124,158,0.8);border-radius: 5px;border: 1px solid rgba(95,124,158,0.6);}
.warningOut { background: rgba(36,6,6,0.9);box-shadow: inset 0px 0px 30px 0px rgba(189,45,45,0.6);border-radius: 5px;border: 1px solid rgba(189,45,45,0.6);}
.defaultTxt { color: #444444 !important; }
.stopTxt    { color: #6C8097 !important; }
.runningTxt { color: #0DD3A3 !important; }

.healthyTxt { color: #0DD3A3 !important; }
.faultTxt { color: #783014 !important; }
.warningTxt   { color: #F29600 !important; }

.onlineTxt { color: #0DD3A3 !important; }
.offlineTxt { color: #6C8097 !important; }


/*** 设备页 -- 地图展示 ***/
.device-resource { position:relative; z-index:100; mini-height:300px; }
.device-resource-list { position:absolute; z-index:200; width: 100%; }
.device-resource-list .device { position:absolute;
  background:#fff; border-radius:50%;
  box-shadow: 0px 1px 2px rgba(0,0,0,0.5); }
.device-resource-list .healthy { background:#0DD3A3; }
.device-resource-list .offline { background:#999; }
.device-resource-list .running { background:#0DD3A3; }
.device-resource-list .fault { background:#CA8F30; }
.device-resource-list .warning { background:#BD2D2D; }
.device-resource-list .error { background:#BD2D2D; }
.device-resource-list .alert { background:#BD2D2D; }
.device-resource-list .stop { background:#5F7C9E; }
.device-resource-list .device > span { position: absolute; margin-left: 5px; font-size: 12px; color:#222; background: rgba(255,255,255,0.7); white-space: nowrap; padding: 0px 5px; border-radius: 3px; }
.device-resource-list .isDT {
  background: none; border: none;box-shadow: none;
  .base {
    font-size: 12px;
    position: relative;
    top: -6px;
    width: 60px;
    left: -10px;
    &>span {
      display: block;
      &.status {
        display: flex;
        align-items: center;
        justify-content: center;
        background-repeat: no-repeat;
        background-size: 100%;
        font-size: 10px;
        width: 40px;
        margin-left: 10px;
        .el-image {
          position: relative;
          top: 1px
        }
      }
    }
  }
}

.device-resource-list .bg_healthy { background: none; border:none; box-shadow:none; }
.device-resource-list .bg_offline { background:none; border:none; box-shadow:none; }
.device-resource-list .bg_running { background:radial-gradient(rgba(64,158,255,0.5),rgba(64,158,255,0)); border:none; box-shadow:none; }
.device-resource-list .bg_fault { background:none; border:none; box-shadow:none; }
.device-resource-list .bg_warning { background:none; border:none; box-shadow:none; }
.device-resource-list .bg_error { background:none; border:none; box-shadow:none; }
.device-resource-list .bg_alert { background:none; border:none; box-shadow:none; }
.device-resource-list .bg_stop { background: none; border:none; box-shadow:none; }

.device-resource-list .deviceImg { position:absolute; background: none; border: none; box-shadow: none; }

.resourceName { position:absolute; z-index: 998; top:10px; left: 10px;
  display: block; width:60px; height: 60px; opacity: 0.75; background:rgba(48, 65, 86, 0.85); border-radius: 4px; border:1px solid #ddd; font-size: 16px; text-align: center; line-height: 60px;  }

.resourceType { position: absolute; z-index: 998; width: 120px; height:fit-content; bottom:10px; left: 10px;  bottom:10px; display: inline-block;
  background: rgba(48, 65, 86, 0.85); border:1px solid #ddd; border-radius: 4px; font-size: 16px; }
.resourceType .title { padding: 5px 10px; }
.resourceType ul {
  display: block;
  margin:0px;
  padding: 5px 15px;
}
.resourceType ul li { display: block; line-height: 2; }
.resourceType ul li .svg-icon {
  margin-right: 20px;
}
.resourceType2 { left: inherit; right: 20px; bottom: inherit; top:60px; width: fit-content;
  max-height:90%; overflow:auto; font-size: 14px; border-color:rgba(255,255,255,0.5); z-index: 1100; }
.resourceType2 ul li { line-height: 3; }
.resourceType2 ul li .svg-icon { margin-right: 0px; margin-left: 10px; }
.resourceType.legend {
  position: absolute;
  left: auto;
  top: auto;
  img {
    width: 100%;
  }
  .legend_scale {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    display: flex;
    justify-content: flex-end;
    &>div {
      font-size: 20px;
      font-weight: 400;
      margin-right: 10px;
      cursor: pointer;
    }
  }
}
.deviceMapDialog {
  .el-dialog {
    margin-bottom: 0;
  }
  .el-dialog__body {
    padding-top: 20px;
    background: #050E1B;
  }
  .el-dialog__header {
    background: #050E1B;
  }
}
.deviceMapDialog .detailBody { height: 325px; overflow:auto; }
.deviceMapDialog .detailControl { padding: 0 15%; }

.deviceByDialog {
  .el-dialog {
    //width: calc(100vw - 130px);
    height: calc(100vh - 282px);
    margin-top: 226px !important;
    margin-left: 128px;
    .el-dialog__body {
      height: calc(100% - 51px);
      width: 100%;
      overflow-y: auto;
    }
  }
}
.DeviceDialogSmall2 {
  // .box-card {
  //   padding: 0;
  // }
  .el-dialog__body {
    padding: 0 20px 20px;
  }
  .deviceDetailSmall2 {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    .lt {
      width: 228px;
      text-align: center;
      border-right: 1px solid;
      flex-shrink: 0;
      border-image: linear-gradient(360deg, rgba(8, 23, 34, .5), rgba(196, 225, 234, .5), rgba(5, 17, 25, .5)) 1 1;
      .el-image {
        width: 172px;
        height: 172px;
        margin-top: 8px;
      }
      .lt_fields {
        text-align: left;
        font-weight: 400;
        font-size: 14px;
        color: rgba(255,255,255,0.8);
        padding: 0 12px;
        margin-top: 4px;
        &>div{
          margin-bottom: 16px;
          display: flex;
          align-items: baseline;
          &>span {
            display: inline-block;
            width: calc(100% - 74px)
          }
        }
      }
      .btns {
        display: flex;
        justify-content: space-around;
        align-items: center;
        .el-button {
          font-size: 12px;
          padding: 6px 10px;
        }
      }
    }
    .rt {
      margin-left: 10px;
      width: calc(100% - 228px);
      .detailControl2 {
        .items {
          height: 274px;
          overflow-y: auto;
          .itemLine {
            border-bottom: none;
            display: flex;
            align-items: baseline;
            span {
              &:first-child {
                width: 140px;
                margin-right: 10px;
                line-height: normal;
              }
            }
          }
        }
        .btns {
          padding-top: 8px;
          margin-top: 12px;
          border-top: 1px dashed rgba(108, 128, 151, 0.5);
          justify-content: flex-end;
          display: flex;
          align-items: center;
          .right {
            margin-left: 20px;
          }
        }
      }
      .deviceDataChart_itemselect {
        width: 170px !important;
      }
    }
  }
}



/** 空调机组-主控界面 **/
.hvac-block .el-card { margin-bottom: 12px;  }
.hvac-block .card-body { font-size:80%; line-height:2; }
.hvac-block .card-body .el-row { margin-bottom:0px; }
.hvac-block .card-body .el-divider--horizontal { margin-top:5px; margin-bottom:15px; }

/** 原理图 **/
.sence { position:relative; background-position:center center; background-repeat:no-repeat; background-size:100% 100%; }
.sence > * { position:absolute; background-position:center center; background-repeat:no-repeat; background-size:100% 100%; }

.deviceCardDialogSmall .el-card__header { display:none; }
.deviceCardDialogSmall .el-card__body { padding-top:0px; }
.deviceCardDialogSmall .el-dialog__body { padding:0px; }


/** 公共内页  **/
.dataView .xcContainer { padding:0px 20px; }
.dataView .xcContainer h3 { margin-top: 10px; font-size:20px; margin-top: 0; padding: 15px 0 0; }

.video {
  width: 100%;
}

/** 自定义弹窗 **/
.dialog_wrap {
  position: absolute;
  z-index: 99;
  background-color: #0C1426;
}
.dialog_wrap .dialog_head{
  padding:  15px 10px;
  color: #bfcbd9;
  font-size: 14px;
}
.dialog_wrap .el-table {
  background-color: transparent;
}
.dialog_wrap .el-select {
  margin-right: 10px;
  width: 120px;
}
.dialog_wrap .dialog_footer {
  padding: 15px 10px;
  text-align: right;
  font-size: 14px;
}
.dialog_wrap .el-table th {
  color: #fff;
  background-color: #16213e;
}
.dialog_wrap .el-table td {
  color: #ddd;
  background-color: #080a1c;
}
.el-select-dropdown {
  background: #0C1426;
}
.public__head {
  padding: 15px;
  margin-bottom: 10px;
  background: #0C1426;
  overflow: hidden;
  .pattern {
    float: left;
    .text {
      margin-left: 5px;
      font-size: 14px;
      color: #bfcbd9;
    }
  }
  .system_type {
    float: right;
  }
  .device_bnt {
    margin-left: 30px;
  }
  .device_bnt {
    border-color: #1f3647;
    background: #05a18d;
  }
  .el-radio-button__inner {
    color: #bfcbd9;
    border-left-color: #1f3647 !important;
    border-color: #1f3647;
    background: #16213e;
  }
  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    color: #fff;
    border-color: #05a18d;
    background: #05a18d;
    box-shadow: none;
  }
}
.switch_list {
  position: absolute;
  top: 5%;
  right: 2%;
  height: 80%;
  overflow-y: auto;
  padding: 10px 30px;
  color: #bfcbd9;
  font-size: 12px;
  background-color: #0C1426;
  border-radius: 4px;
  li {
    padding: 10px 0;
  }
  .el-switch__core {

    width: 52px !important;
    &::after {
      z-index: 10;
    }

  }
  .el-switch__label {
    position: absolute;
    font-size: 12px;
    &.is-active {
      color: #fff;
      z-index: 1;
    }
    * {
      font-size: 12px;
    }
  }
  .el-switch__label--left {
    position: absolute;
    top: 1px;
    right: 6px;
    z-index: -1;
    margin-right: 0;
    color: #05a18d;
  }
  .el-switch__label--right {
    position: absolute;
    top: 1px;
    left: 6px;
    z-index: -1;
    margin-left: 0;
    color: #ff4949;
  }
  .device_name {
    display: block;
    margin-bottom: 5px;
  }
  .device_type {
    display: inline-block;
    margin-left: 8px;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    background-color:#1BA0EC;
    border-radius: 50%;
    &.off {
      background-color: #999;
    }
  }
}


.bb1 { border-bottom:1px solid #087198; }


/** dataView **/
.dataView2Dh { position: absolute; z-index: 900; }
.dataView2Dh .vbg { position: fixed; width: 100%; height: 100%; z-index:0; top: 100px; background: rgba(2,10,36, 1); background: #ddd; }
.dataView2Dh .vbg .mask { position: absolute; z-index: 10px; margin-top: -100px; margin-left: -150px; width: calc(100vw + 300px); height: calc(100vh + 100px); border: 600px solid rgba(2,10,36, 0.8); border-top-width:300px; border-bottom-width:300px; filter: blur(50px); display: none; }
.dataView2Dh .vbg video { width: 100%; position: absolute; z-index: 0; }
.dataView2Dh .vbg .el-image { width: 100%; position: absolute; z-index: 0; }
.dataView2Dh .vbg .sliderOut { position: absolute; z-index: 1; width: 40%; margin-left: 30%; top: 110px; }
.dataView2Dh .vbg .sliderOut .svg-icon { font-size: 200%; color: #fff; margin: 5px 0; }
.dataView2Dh.model-view, .dataView2Dh .bimModel { position: absolute; z-index: 1; }
.dataView2Dh.model-view, .dataView2Dh .threeModel { position: absolute; z-index: 1; }

.cameraPic { filter: blur(5px); }

/** 地图上的点 **/
.dataView2Dh .mainBlock .deviceList {}
.dataView2Dh .mainBlock .deviceList .device { position:absolute; background:rgba(2,10,36, 0.4); cursor: pointer; }
.dataView2Dh .mainBlock .deviceList .device .iconOuter { padding: 8px;
  background:
    linear-gradient(90deg, #ddd 50%, transparent 0) repeat-x,
    linear-gradient(90deg, #ddd 50%, transparent 0) repeat-x,
    linear-gradient(0deg, #ddd 50%, transparent 0) repeat-y,
    linear-gradient(0deg, #ddd 50%, transparent 0) repeat-y;
  background-size: 6px 3px, 6px 3px, 3px 6px, 3px 6px;
  background-position: 0 0, 0 100%, 0 0, 100% 0;
  animation: linearGradientMove .3s infinite linear;
}
@keyframes linearGradientMove {
  100% {
    background-position: 6px 0, -6px 100%, 0 -6px, 100% 6px;
  }
}

.dataView2Dh .el-dialog__body { padding: 10px 20px; }

.dataView2Dh .ctrlBlock { display: block; }
.dataView2Dh .header { position: relative; z-index: 2000; height: 100px; top:0px; }
.dataView2Dh .leftBlock,
.dataView2Dh .rightSubMenu,
.dataView2Dh .rightBlock { width: 380px; margin: 10px; float: left; position: relative; z-index: 1000; }
.dataView2Dh .rightSubMenu { position: absolute; right: 90px; top: calc(50vh - 200px); }
.dataView2Dh .rightBlock { float: right; }
.dataView2Dh .mainBlock,
.dataView2Dh .mainBlockTop,
.dataView2Dh .mainBlockBtm { position: absolute; z-index: 1000; left: 400px; margin:10px; width: calc(100vw - 820px); }
.dataView2Dh .mainBlockBtm { position: absolute; left: 400px; bottom: 170px; height: 180px; }

.dataView2Dh ._block { background: rgba(2,10,36, 0.6); margin-bottom: 10px; padding:0 15px; border-radius: 3px; }
.dataView2Dh ._block:hover { background: rgba(2,10,36, 0.75); }
.dataView2Dh ._block .title { font-size:20px; font-weight:bold; padding:10px 0; margin-bottom:15px; border-bottom:5px solid rgba(255,255,255,0.1); }
.dataView2Dh ._block .title::after { content:""; position:absolute; margin-top:10px; display:block; width: 30px; border-bottom:5px solid #00FFD2; }
.dataView2Dh ._block .chart { height: auto; }

.dataView2Dh ._block .chart .slotVal { margin-bottom: -10px; }
.dataView2Dh ._block .chart .slotBar { width: 30px; height: 3px; display: inline-block; }
.dataView2Dh ._block .chart .sby { background-color: #EF9723 ; }
.dataView2Dh ._block .chart .sbg { background-color: #14FCD5 ; }

.dataView2Dh .rightSubMenu ._block { display: block; width: fit-content; text-align: center; }
.dataView2Dh .rightSubMenu ._block .menu { height: 40px; line-height: 40px; cursor: pointer; }

/** SecurityView **/
.dataView2Dh .mainBlockTop .summblock { display: flex; display: -webkit-flex; justify-content: space-between; }
.dataView2Dh .mainBlockTop ._block { margin: 10px; padding:10px 20px; width: 200px; text-align: center; color: #ccc; }
.dataView2Dh .mainBlockTop ._block .v { font-size: 220%; color: #eee; }

.dataView2Dh .mainBlockBtm ._block .outsideBlock { overflow: auto; }
.dataView2Dh .mainBlockBtm ._block .outBlock { width: 1500px; overflow-x: auto; }
.dataView2Dh .mainBlockBtm ._block .innerBlock { width: 500px; height: 160px; padding: 5px 10px; margin-bottom: 10px; float: left; border-right: 1px solid #ccc; }
.dataView2Dh .mainBlockBtm ._block .innerBlock h3 { margin: 5px 0 8px 0; line-height: 1; }
.dataView2Dh .leftBlock ._block h3 { margin: 20px 0 15px 0; line-height: 1; }
.dataView2Dh .leftBlock ._block .info { line-height: 1.5; color: #ccc; }
.dataView2Dh .leftBlock ._block .el-divider { margin: 10px 0 5px; }
.dataView2Dh .leftBlock ._block .info label { margin-right: 15px; }
.dataView2Dh .leftBlock ._block .info .el-button { margin-bottom: 10px; float:left; }

/** PassView **/
.dataView2Dh .leftBlock ._block .person, .dataView2Dh .rightBlock ._block .person { margin: 8px; line-height: 2; }
.dataView2Dh .leftBlock ._block .person:last-child, .dataView2Dh .rightBlock ._block .person:last-child { padding-bottom: 20px; }
.dataView2Dh .leftBlock ._block .person .svg-icon, .dataView2Dh .rightBlock ._block .person .svg-icon { margin-right: 15px; }
.dataView2Dh .leftBlock ._block .person .t, .dataView2Dh .rightBlock ._block .person .t { margin-left: 15px; }

/** AssetsView **/
.dataView2Dh .rightBlock .assetsInfo { padding: 0 20px 20px 20px; }
.dataView2Dh .rightBlock .assetsInfo b { font-size: 200%; }

.dataView2Dh .mainBlockBtm ._block .title em { margin-left: 30px; font-style: normal; }
.dataView2Dh .mainBlockBtm .datablock { }

/** DeviceView **/
.workOrderBlock { display: block; margin-bottom: 10px; padding: 0 15px 0 0; border-bottom: 1px dashed #aaa; }
.workOrderBlock .svg-icon { float: left; margin-right: 20px; height: 50px; vertical-align: top; }

.deviceInfo {}
.deviceInfo label { color:#bbb }

/** SurroundingView **/
.dataView2Dh .rightBlock .officeUses .person { margin-bottom: 8px; }
.dataView2Dh .rightBlock .officeUses .person .el-tag { margin-right: 15px; }
.dataView2Dh .rightBlock .officeUses .person .svg-icon { margin: 0px; }


/** 电器图 **/
.elec-on { background-image: url("/images/IdcsGif/bianpeidian/Switch-vc.gif"); }
.elec-off { background-image: url("/images/IdcsGif/bianpeidian/Switch-vo.gif"); }

.icon-on, .icon-item:hover { color:#1890ff; }
.icon-on {  font-weight:bold; }
.icon-item span {
  display: block;
  font-size: 14px;
  margin-top: 5px;
}
.icon-item .disabled{
  pointer-events: none;
}

.electricity .bl { padding:0 10px 30px; margin-bottom:15px; border:1px solid #ddd; }
.electricity .bl:hover { background:rgba(255,255,255,0.2); }
.electricity .icon-inner { display: inline-block; background-size: 100% 100%; vertical-align: middle; position: relative; top: 20px; width: 24px; height: 48px; float: left; }
.electricity .t:before { position: absolute;
  margin-left: 15px;
  margin-top:22px;
  content: "";
  display: inline-block;
  height: 100px;
  width: 1px;
  border-top:30px solid #fff;
  border-bottom:30px solid #fff; }
.electricity .tl:before { background:#fff; }
.electricity p { font-size: 12px; margin-block-start: 8px; margin-block-end: 6px; }
.electricity p.t { text-align: left; font-weight: bold; padding-bottom:8px;  border-bottom:1px solid rgba(255,255,255,.5); }
.electricity p.b { border: 1px solid rgba(255,255,255,.5); padding: 2px; display: inline; }


.page_nav {
  position: relative;
  height: 36px;
  z-index: 221;
  background: url(/image/co2_nav_bg.png) no-repeat left center;
  background-size: auto 100%;
  .text {
    position: absolute;
    top: 6px;
    left: 26px;
    font-size: 20px;
    font-family: pangmenzhendao;
  }
}

/*** CO2 ***/
@font-face {
  font-family: pangmenzhendao;
  src: url(../icons/pangmenzhengdao3.0.ttf);
}
.el-popover__reference-wrapper .h { cursor:pointer; }
.pop { }
.pop p { margin:8px; }
.pop em { width: 100px; display:inline-block; font-style: normal; }
.pop b { color: #00adff; font-weight: bold; font-size: 18px; }
.pop i { color: #B1C6D5; font-size: 14px; }
.nav_wrap {
  margin-top: 10px;
  width: fit-content;
  height: auto;
  max-height: 36vh;
  overflow-y: auto;
  border: 1px solid #255A92 !important;
  background-color: #02080F !important;
  a {
    padding: 0 15px;
    display: block;
    font-size: 14px;
    line-height: 34px;
    color: #fff;
    &.active {
      background-color: #255A92;
    }
    &:hover {
      background-color: #255A92;
    }
  }
}
.page_fixed_nav_box {
  display: none;
  position: absolute;
  right: 4%;
  text-align: center;
  cursor: pointer;
  pointer-events: all;

  .nav_wrap {
    margin-top: 10px;
    padding: 10px 0;
    width: 110px;
    height: auto;
    max-height: 36vh;
    overflow-y: auto;
    border: 2px solid #255A92;
    background-color: #02080F;
    a {
      display: block;
      font-size: 14px;
      line-height: 34px;
      &.active {
        background-color: #255A92;
      }
    }
  }
}

.co2_container {
  color: #fff;
  img {
    max-width: 100%;
    vertical-align: middle;
  }
  .layout_header_img {
    position: relative;
    z-index: 501;
    width: 100%;
    height: 100px;
    background: url('../image/co2_header_img.png');
    background-size: 100% 100%;
    pointer-events:none;
  }

  .layout_bottom_img {
    position: absolute;
    left: 0;
    width: 100%;
    bottom: 0;
    z-index: 505;
    width: 100%;
    height: 50px;
    background: url('../image/co2_bottom_img.png');
    background-size: 100% 100%;
    pointer-events:none;
  }
  .co2_header_nav {
    position: relative;
    z-index: 220;
    width: 1000px;
    margin: auto;
    text-align: center;
    img {
      margin: 0 4px;
    }
  }
}
.co2_main {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  .co2_body,
  .co2_body>.el-col {
    height: 100%;
    &.el-col-sm-5 {
      width: 23%;
    }
    &.el-col-sm-14 {
      width: 54%;
    }
  }
  .fl {
    width: 23%;
    height: 100vh;
    position:absolute;
    top: 0;
    left: 0;
    z-index: 200;
  }
  .fr {
    width: 23%;
    height: 100vh;
    position:absolute;
    top: 0;
    right: 0;
    z-index: 200;
  }
  .ft {
    width: 54%;
    position:absolute;
    left: 23%;
    top: 16%;
    z-index: 210;
    pointer-events: none;
  }
  .fb {
    width: 54%;
    position:absolute;
    left: 23%;
    bottom: 1%;
    height: 100%;
    z-index: 200;
    pointer-events: none;
  }

  .co2_main_left {
    padding-top: 105px;
    padding-bottom: 60px;
    padding-left: 35px;
    padding-right: 25px;
    height: 100%;
    background: url(../image/co2_left_bg.png) no-repeat top left;
    background-size: 100% 100%;
    .co2_main_left_content {
      height: 100%;
    }
  }
  .co2_main_center {
    height: 100%;
    position: relative;
    .quickMenu {
      width:80px;
      height: 90px;
    }
  }
  .co2_main_right {
    padding-top: 105px;
    padding-bottom: 60px;
    padding-left: 25px;
    padding-right: 35px;
    height: 100%;
    background: url(../image/co2_right_bg.png) no-repeat top right;
    background-size: 100% 100%;

  }
  /*** 进度条 ***/
  .co2_progress {
    margin-bottom: 5%;
    padding: 0 15px 0 20px;
    .text_cont {
      overflow: hidden;
      line-height: 36px;
      color: #fff;
      span {
        float: left;
        font-size: 14px;
      }
      i {
        float: right;
        font-size: 18px;
        color: #19ECFF;
      }
      b {
        color: #B1C6D5;
        font-size: 13px;
      }
    }
    .bg-blue {
      background-color: #1eb6fe !important;
    }
    .progress_bar {
      height: 6px;
      background: #474849;
      overflow: hidden;
      border-radius: 20px;
      span {
        display: block;
        height: 6px;
        width: 0;
      }
    }
  }

  .co2_progress_group {
    display: flex;
    margin-left: 10px;
    margin-top: 10px;
    margin-bottom: -5px;
    margin-right: 10px;
    align-items: center;
    position: relative;
    padding-top: 4px;
    padding-bottom: 4px;
    padding-left: 12px;
    .left_line {
      position: absolute;
      top: 5px;
      left: 0;
      display: inline-block;
      width: 2px;
      height: 15px;
      background: #69ECFC;
    }
    .bottom_line {
      position: absolute;
      bottom: 0;
      left: 6px;
      right: 30px;
      height: 2px;
      background: rgba(0, 110, 229, 0.2);
      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 2px;
        height: 1px;
        background-color: #61E8B1;
      }
    }
    .title {
      display: inline-block;
      width: 55px;
      font-size: 12px;
      margin: 0;
      text-align: left;
    }
    .bg-blue {
      background-color: #1eb6fe !important;
    }
    .progress_bar {
      flex: 1;
      .progress_bar_group {
        position: relative;
        height: 8px;
        overflow: hidden;
        i {
          display: block;
          height: 100%;
          opacity: 0.15
        }
        span {
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          width: 0;
          opacity: 0.8;
        }
      }
    }
    .value {
      font-size: 14px;
      width: 45px;
      text-align: right;
    }
    .progress_bar_text {
      margin-bottom: 5px;
      color: #ccc;
      font-size: 13px;
      overflow: hidden;
      .left_text {
        float: left;
      }
      .right_text {
        float: right;
      }
    }
  }
  .co2_progress_2 {
    align-items: flex-end;
    .progress_bar {
      .progress_bar_group {
        height: 14px;
      }
    }
  }
  .el-select .el-input {
    .el-select__caret {
      color: #18C8F2;
    }
  }
  .el-input {
    width: 120px;
    .el-input__inner {
      color: #E2EAF0;
      border-color: #087198;
      background-color: #060F1B;
      &:focus {
        border-color: #087198;
      }
    }

    .el-input__suffix {
      color: #18C8F2;
    }
    &.is-focus,
    &:hover{
      .el-input__inner {
        border-color: #087198 !important;
      }
    }
  }
  .el-table--enable-row-hover .el-table__body tr:hover>td,
  .el-table--striped .el-table__body tr.el-table__row--striped td {
    background-color: rgba(17, 77, 118, .43);
  }
  .pagination {
    margin-top: 10px;
    .el-input {
      margin-left: 2px;
      margin-right: 2px !important;
      .el-input__inner {
        height: 24px;
        line-height: 24px;
      }
    }
    .el-pagination {
      text-align: right;
    }
    .btn-next,
    .btn-prev,
    button:disabled {
      color: #f9f9f9;
      background-color: transparent;
    }
    li {
      background-color: transparent;
      &.active {
        color: #19D5FF;
      }

    }
  }

  .page_nav {
    position: relative;
    height: 36px;
    z-index: 221;
    background: url(/image/co2_nav_bg.png) no-repeat left center;
    background-size: auto 100%;
    h2 { font-weight:normal; font-size:20px; color:#fff; }
    .text {
      position: absolute;
      top: 6px;
      left: 26px;
      font-family: pangmenzhendao;
      font-size: 20px;
    }
    .page_nav_right {
      position: absolute;
      z-index: 200;
      right: 0;
      padding-right: 3%;
      width: 40%;
      bottom: -5px;
      font-size: 14px;
      text-align: right;
      color: #D9ECFF;
      text-shadow: 0px 1px 14px #278BF7;
      background: url(../image/co2_nav_bg1.png) no-repeat center center;
      background-size: 100%;
    }
  }
  .page_nav1 {
    position: relative;
    background-position: left;
    &::after {
      content: '';
      position: absolute;
      z-index:0;
      top: 15px;
      right: 0;
      width: 70%;
      height: 13px;
      border-top: 2px solid #2B5989;
      background: rgba(0,82,207,0.24);
    }
  }
  .page_nav2 {
    padding: 0 15px;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    background: linear-gradient(90deg, rgba(1, 59, 152, 0.26) 0%, rgba(2, 73, 188, 0.26) 49%, rgba(1, 115, 245, 0) 100%);
    span {
      float: right;
      font-size: 14px;
      color: #B1C6D5;
      b {
        padding-right: 5px;
        font-size: 18px;
        color: #3CCCF9;
      }
    }
  }
  .tabs_box {
    margin-top: 10px;
    a {
      display: inline-block;
      padding: 6px 14px;
      margin-right: 12px;
      font-size: 14px;
      background: url(../image/co2_nav_bg5.png) no-repeat center center;
      background-size: 100% 100%;
      &.active {
        background: url(../image/co2_nav_bg4.png) no-repeat center center;
        background-size: 100% 100%;
      }
      &:last-child{
        margin-right: 0;
      }
    }

  }
  .box_card {
    margin-top: 10px;
    position: relative;
    padding: 5px 10px 7px;
    border-radius: 6px;
    overflow: hidden;
    background: url(../image/co2_bg_5.png) no-repeat center center;
    background-size: 100% 100%;
    font-size: 13px;
    min-height: 65px;
    .title {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      .dot {
        width: 10px;
        height: 10px;
        background: #F9DA4F;
        border-radius: 50%;
      }
      h3 {
        flex: 1;
        padding-left: 10px;
        font-size: 15px;
        color: #F9DA4F;
      }
      .num {
        font-size: 15px;
        color: #F9DA4F;
      }

      &.title_out {
        .dot {
          background-color: #00D1C8;
        }
        h3 {
          color: #00D1C8;
        }
      }
    }
    p {
      margin-top: 2px;
      i { width: 80px; display:inline-block; }
      a {
        text-decoration: underline !important;
      }
    }
    b {
      font-weight: bold;
    }
  }
  /*** 能源管理 ***/
  .co2_energy_left {
    position: relative;
    z-index: 221;
    .co2_survey {
      padding-top: 5px;
      padding-left: 2%;
      li {
        display: flex;
        align-items: center;
        padding: 6px 0;
        margin-top: 10px;
        border: 1px solid;
        border-image: linear-gradient(167deg, rgba(88, 169, 228, 0.72), rgba(90, 173, 233, 1), rgba(84, 161, 223, 0.22)) 1 1;
        div {
          flex: auto;
          &.img_box {
            flex: none;
            padding-left: 20px;
            padding-right: 20px;
          }
        }
        p {
          margin: 0;
          padding-top: 6px;
          font-size: 12px;
        }
        b {
          color: #00adff;
          font-weight: bold;
          font-size: 18px;
        }
        i {
          color: #B1C6D5;
          font-size: 14px;
        }
        span,
        a {
          font-size: 14px;
          color: #00C2FF;
          font-weight: bold;
        }
        a {
          text-decoration: underline;
        }
      }
    }
    .charts_box {
      height: calc( 100% - 291px);
    }
    .electricity_charts_box {
      padding-top: 3%;
      height: 46%;
    }
    .water_charts_box {
      padding-top: 3%;
      height: 50%;
    }
    .electricity_analysis {
      padding-top: 10px;
      padding-left: 2%;
      height: calc(100% - 36px);

      .electricity_analysis_charts {
        height: calc(100% - 46px);
      }
      .electricity_pie {
        padding: 5px 0;
        height: 50%;
      }
      .electricity_line {
        height: 50%;
      }

    }
    .water_analysis {
      padding-top: 10px;
      padding-left: 2%;
      height: calc(100% - 36px);
      .water_analysis_charts {
        display: flex;
        width: 100%;
        margin-left: -3%;
        height: calc(100% - 46px);

        .water_chart_pie {
          width: 48%;
          height: 100%;
        }
        .water_chart_bar {
          margin-left: 2%;
          width: 48%;
          height: 100%;
        }
      }
    }
  }

  .co2_energy_carbon {
    position: absolute;
    left: 4%;
    right: 4%;
    bottom: 2%;
    padding: 1% 2%;
    min-height: 25.5%;
    background: rgba(7, 7, 7, 0.8);
    pointer-events: all;

    .page_nav2 {
      margin:10px 0;
    }

    .select_form {
      position:relative;
      z-index: 10000;
      background:#16213e;
    }

    .co2_survey {
      padding-left: 1%;
      li {
        display: flex;
        align-items: center;
        padding: 6px 0;
        margin-top: 10px;
        border: 1px solid;
        border-image: linear-gradient(167deg, rgba(88, 169, 228, 0.72), rgba(90, 173, 233, 1), rgba(84, 161, 223, 0.22)) 1 1;
        div {
          flex-grow: 1;
          justify-content: center;
          &.img_box {
            font-size: 14px;
            line-height: 32px;
            width: 150px;
            padding-left: 20px;
            padding-right: 20px;
            img {
              height: 30px;
              margin-right: 5px;
            }
          }
        }
        b {
          color: #00adff;
          font-weight: bold;
          font-size: 18px;
        }
        i {
          color: #B1C6D5;
          font-size: 14px;
          margin: 0 25px 0 5px;
        }
      }
    }

    .co2_progress {
      margin: 10px 0;
      .text_cont {
        line-height: 20px;
      }
    }

    .co2_energy_carbon_content {
      padding-top: 10px;
      padding-left: 4%;
    }
    .co2_select {
      .el-input {
        width: 110px;
      }
      .el-input__inner {
        border: none;
        color: #D9ECFF;
        background: url(../image/co2_select_bg.png) no-repeat center center;
        background-size: 100% 100%;
      }
      .el-input__suffix i {
        color: #fff !important;
      }
    }
    .carbon_line_chart {
      height: calc(100% - 74px);
      padding: 0 10px;
    }
  }
  .fb_btm {
    position: absolute;
    bottom: 4.6%;
    padding: 0;
    min-height: 25.5%;
    background: rgba(7, 7, 7, 0.8);
    pointer-events: all;
  }
  .co2_energy_right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .co2_select {
      .el-input {
        width: 110px;
      }
    }
  }
  .co2__carbon_member {
    height: 35%;
  }
  .co2__carbon_pie {
    height: calc(100% - 36px);
    width: 105%;
  }
  .co2__carbon_reduce {
    height: 30%;
    .co2__carbon_reduce_inner {
      padding-top:10px;
      padding-left: 2%;
      height: calc(100% - 40px);
      .co2_carbon_reduce_pie {
        height: calc(90% - 40px);
      }
    }
  }
  .co2_carbon_bottom {
    margin-top: 10px;
    padding-left: 2%;
    height: 100%;
  }

  .co2_carbon_reduce_pie {
    display: flex;
    align-items: center;
    width: 100%;
    height: calc(100% - 56px);
    .reduce_chart_pie {
      flex: 1;
    }
    ul {
      flex: 2;
      padding-left: 4%;
    }
    li {
      position: relative;
      float: left;
      width: 50%;
      padding-left: 18px;
      margin-top: 5%;
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        border-left: 1px solid #979797;
      }
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -4px;
        z-index: 10;
        width: 8px;
        height: 8px;
        background-color: #00adff;
        border-radius: 50%;
      }
      &.color_0 {
        &::before {
          background-color: #69ECFC;
        }
        .text {
          color: #69ECFC;
        }
      }
      &.color_1 {
        &::before {
          background-color: #457DE3;
        }
        .text {
          color: #457DE3;
        }
      }
      &.color_2 {
        &::before {
          background-color: #FFFFFF;
        }
        .text {
          color: #FFFFFF;
        }
      }
      &.color_3 {
        &::before {
          background-color: #67C23A;
        }
        .text {
          color: #67C23A;
        }
      }
    }
    h4 {
      margin-top: -4px;
      font-size: 13px;
      color: #F9FFFE;
      font-weight: normal;
    }
    .text {
      font-size: 18px;
      font-weight: bold;
    }
    span {
      padding-left: 2px;
      color: #A2B5C4;
      font-size: 12px;
    }
  }
  .co2_carbon_results {
    h4 {
      margin-top: 0;
      margin-bottom: 5px;
      font-size: 16px;
    }
  }
  .progress_fill_0 {
    background: linear-gradient(90deg, rgba(13, 211, 163, 0.1) 0%, #0DD3A3 100%);
  }
  .progress_fill_1 {
    background: linear-gradient(90deg, rgba(60, 204, 249, 0.1) 0%, #3CCCF9 100%);
  }
  .progress_fill_2 {
    background: linear-gradient(90deg, rgba(86, 40, 238, 0.1) 0%, #5628EE 100%);
  }
  .progress_fill_3 {
    background: linear-gradient(90deg, rgba(252, 216, 105, 0.1) 0%, #FCD869 100%);
  }
  .progress_fill_4 {
    background: linear-gradient(90deg, rgba(215, 135, 55, 0.1) 0%, #D78737 100%);
  }
  .progress_fill_5 {
    background: linear-gradient(90deg, rgba(202, 51, 78, 0.1) 0%, #CA334E 100%);
  }
  .tree_wrap {
    display: flex;
    align-items: center;
    padding-top: 5px;
    .tit {
      padding-right: 5px;
      font-size: 14px;
      color: #F4FAFF;
    }
    .tree_content {
      display: flex;
      flex: 1;
      padding-left: 10px;
      padding-right: 10px;
      padding-bottom: 5px;
      background: linear-gradient(180deg, rgba(247, 253, 255, 0) 0%, rgba(88, 217, 249, 0.11) 100%);
      border-radius: 14px;
    }
    .img_box {
      flex: 1;
    }
    img {
      margin: 0 0.7%;
    }
    .num {
      color: #F4FAFF;
      font-size: 20px;
      font-weight: bold;
      background: linear-gradient(180deg, #FFFFFF 0%, #1CD3FF 100%);
      -webkit-background-clip:text;
      -webkit-text-fill-color:transparent;
    }
    i {
      padding-left: 2px;
      color: #B1C6D5;
      font-size: 14px;
    }
  }

  /*** 设备管理 ***/
  .co2_device_left {
    padding-left: 1%;
    height: calc(100% - 34px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .co2_device_content {
      padding-left: 2%;
    }
  }
  .co2_device_box {
    .co2_device_content {
      display: flex;
      // width: 100%;
      height: calc(100% - 36px);
      flex-direction: column;
      justify-content: flex-start;
    }
    .box_header {
      li {
        display: flex;
        align-items: center;
        &>.img_box img{
          width: 32px;
        }
      }
      .text {
        margin-top: 2px;
        margin-bottom: 0;
        font-size: 12px;
        text-align: center;
      }
      .cont {
        flex: 1;
        padding-left: 2%;
        overflow: hidden;
      }
      .btn {
        float: left;
        display: block;
        margin: 0 0%;
        padding: 0 2%;
        height: 34px;
        line-height: 32px;
        font-size: 14px;
        background: url(../image/co2_nav_bg3.png) no-repeat center center;
        background-size: 100% 100%;
        &:last-child {
          margin-right: 0;
        }
        &.active {
          color: #CBB337;
        }
      }
      .grade {
        display: flex;
        align-items: end;
        font-size: 14px;
        padding: 0 3%;
        text-align: center;
        &.device_num {
          padding-left: 0%;
          text-align: left;
        }
        .img_box {
          flex: 1;
          padding: 0 5%;
        }
        b { margin-left: 6px; font-size:18px; color:#00adff; font-weight:bold; }
      }
    }
    .box_bottom {
      .el-col {
        margin-bottom: 0;
      }
    }
    .norm_wrap {
      display: flex;
      align-items: center;
      ul {
        width: 50%;
      }
      li {
        display: flex;
        align-items: center;
        padding: 8% 0;
        img {
          margin-right: 8px;
        }
        h4 {
          font-size: 16px;
          color: #55A5FF;
          &.green {
            color: #07CD72;
          }
          i { font-size:12px; color:#fff; font-weight:normal; }
        }
        p {
          font-size: 13px;
        }
      }
      .device_chart_pie {
        width: 50%;
        height: 120px;
      }
    }
  }
  .co2_device_center_body {
    position: absolute;
    left: 4%;
    right: 4%;
    bottom: 4%;
    padding: 1% 0 2%;
    height: 25.5%;
    background: rgba(7, 7, 7, 0.75);
    pointer-events: all;

    .body_box {
      display: flex;
      height: 100%;
      .item {
        flex: 1;
        padding: 0 3%;
      }
      .table_box {
        margin-top: 15px;
        height: calc(100% - 55px);
        overflow-y: auto;
      }
      tr:nth-child(even),
      thead {
        background: linear-gradient(90deg, rgba(1, 59, 152, 0.26) 0%, rgba(2, 73, 188, 0.26) 49%, rgba(1, 115, 245, 0) 100%);
      }

      .device_progress {
        margin-top: 6%;
        .co2_progress_group {
          margin-top: 5%;
        }
        .progress_bar_group {
          i {
            background-color: #193939;
          }
          span {
            &.device_bar_0 {
              background-color: #3691FE;
            }
            &.device_bar_1 {
              background-color: #0DD3A3;
            }
            &.device_bar_2 {
              background-color: #3CCCF9;
            }
          }
        }
      }
    }
  }
  .co2_device_right {
    height: calc(100% - 36px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .co2_device_right_bottom {
      .box_bottom {
        margin-top: 10px;
        height: calc(100% - 64px);
        .el-col {
          height: 100%;
        }
        .device_chart_pie {
          height: 100%;
        }
      }
      .progress_bar_text {
        margin-bottom: 4px;
        font-size: 14px;
        .left_text {
          color: #fff;
        }
      }
      .progress_bar_group {
        height: 4px;
        border-radius: 10px;
      }
    }
    .tabs_box,
    .box_header {
      padding-left: 3%;
    }
    .tabs_box {
      margin-top: 15px;
    }
    .box_header {
      padding-top: 15px;
    }
    .device_num {
      margin-bottom: 10px;
    }
    .table_box {
      padding-left: 4%;
      margin-top: 15px;
      margin-bottom: 15px;
      height: calc(100% - 64px);
      overflow-y: auto;
    }
    .co2_table {
      font-size: 14px;
      tbody tr {
        background: linear-gradient(90deg, rgba(1, 59, 152, 0.26) 0%, rgba(2, 73, 188, 0.26) 49%, rgba(1, 115, 245, 0) 100%);
      }
      .num {
        color: #19ECFF;
        font-size: 16px;
      }
      .unit {
        color: #B1C6D5;
        font-size: 12px;
      }
      td {
        color: #fff;
        padding: 5px 0;
        border-top: 4px solid rgba(7, 7, 7, 0.8);
        border-bottom: 4px solid rgba(7, 7, 7, 0.8);
      }
    }

  }
  .table_box {
    overflow-y: auto;
  }
  .co2_table {
    width: 100%;
    background-color: transparent;

    &::before,
    th,tr {
      background-color: transparent;
    }
    th,td {
      border-bottom-color: transparent !important;
    }
    &.striped {
      tr:nth-child(even),
      thead {
        background: linear-gradient(90deg, rgba(1, 59, 152, 0.26) 0%, rgba(2, 73, 188, 0.26) 49%, rgba(1, 115, 245, 0) 100%);
      }
      th, td {
        padding: 5px 0;
      }
      td {
        color: #EAFAFF;
        &.column_type {
          border-left:  4px solid #0A3B4D;
        }
        &.state_green {
          color: #07CD72;
        }
        &.state_yellow {
          color: #DC951C;
        }
        &.device_working {
          color: #EAFAFF;
        }
        &.device_red {
          color: #D9001B;
        }
      }
    }
    tr {
      padding: 7px 0;
    }
    th {
      color: #ffffff;
      font-weight: normal;
      font-size: 16px;
    }

  }

  /*** 安全管理 ***/
  .co2_security_left {
    padding-left: 1%;
    height: calc(100% - 34px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .co2_security_left_header {
      height: 13%;
    }
    .co2_security_left_center {
      height: 50%;
    }
    .co2_security_left_bottom {
      height: 30%;
    }

    .detail_btn {
      color: #19D5FF;
      cursor: pointer,
    }

    .security_left_box {
      padding-left: 2%;
      height: calc(100% - 36px);
    }
    .video_surveillance {
      margin-top: 15px;
      margin-bottom: 10px;
      position: relative;
      font-size: 13px;
      .total_box {
        position: absolute;
        top: 2.5%;
        left: 20%;
        h4 {
          margin-bottom: 2px;
          font-size: 26px;
        }
      }
      ul {
        position: absolute;
        bottom: 18%;
        right: 2%;
        width: 50%;
        display: flex;
        li {
          flex: 1;
          text-align: center;
          &:first-child {
            padding-left: 9%;
          }
          &:last-child {
            padding-right: 6%;
          }
        }
        h5 {
          margin-bottom: 2px;
          font-size: 16px;
        }
      }
    }
    .total_card {
      position: relative;
      margin-left: 2%;
      margin-top: 15px;
      margin-bottom: 10px;
      font-size: 13px;
      line-height: 22px;
      span {
        color: #FFEE89;
        font-size: 16px;
      }
      .total_card_left {
        position: absolute;
        bottom: 5%;
        left: 20%;
      }
      .total_card_right {
        position: absolute;
        right: 6%;
        bottom: 5%;
      }
    }
    .tabs_box {
      margin-bottom: 10px;
    }
    .table_box {
      height: 180px;
      overflow-y: auto;
    }
    .fire_monitor_title {
      margin-top: 15px;
      margin-bottom: 10px;
      margin-left: 2%;
      font-size: 15px;
      font-weight: normal;
    }
    .fire_monitor_bar {
      height: calc(100% - 275px);
    }
    .transformer_bar {
      height: calc(100% - 80px);
    }
  }
  .device_list_wrap {
    position: absolute;
    z-index: 10;
    bottom: 2%;
    left: 5%;
    width: 90%;
    padding: 1.5% 3% 2.5%;
    background: url(../image/co2_bg_3.png) no-repeat center center;
    background-size: 100% 100%;
    pointer-events: all;

    .title {
      margin: 15px 0 0 0;
      text-align: left;
      h4 {
        font-size: 20px;
        font-family: pangmenzhendao;
        font-weight: normal;
        line-height: 1.6;
        float: left;
      }
    }
    .search_form {
      float: right;
      margin-top: 0px;
      margin-bottom: 15px;
    }
    .el-input {
      margin-right: 20px;
    }
    .co2_table {
      thead {
        background: #114D76;
      }
      .detail_btn {
        color: #19D5FF;
      }
    }
    .online {
      color: #07CD72;
    }
    .offline {
      color: #DC951C;
    }
  }
  .co2_security_right {
    height: calc(100% - 36px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .co2_security_right_header {
      .water_monitor_bar {
      }
      .total_card {
        position: relative;
        margin-left: 2%;
        margin-top: 10px;
        margin-bottom: 10px;
        font-size: 13px;
        line-height: 22px;
        span {
          color: #FFEE89;
          font-size: 16px;
        }
        .total_card_left {
          position: absolute;
          bottom: 5%;
          left: 20%;
        }
        .total_card_right {
          position: absolute;
          right: 6%;
          bottom: 5%;
        }
      }
    }
    .co2_security_right_center {
      .table_box {
        height: 150px;
      }
    }
    .co2_security_right_bottom {
      .glass_progress {
        margin-top: 10px;
        .co2_progress_group {
          .progress_bar_text {
            margin-bottom: 2px;
          }
        }
      }
    }

    .tabs_box {
      margin-top: 10px;
      margin-bottom: 0px;
      padding-left: 2%;
      a {
        margin-right: 1%;
        padding-left: 2%;
        padding-right: 3%;
      }
    }
    .census_card_box {
      overflow: hidden;
      li {
        position: relative;
        > img {  }
        > span {
          position: absolute;
          top: 25%;
          left: 14%;
          font-size: 14px;
        }
        &:first-child {
          float: left;
          width: 65%;
        }
        &:last-child {
          float: right;
          margin-right: 2%;
        }
        h4 {
          position: absolute;
          top: 20%;
          right: 15%;
          color: #41F8AC;
          font-size: 18px;
        }
        i {
          font-size: 12px;
        }
      }
    }
    .select_form {
      margin-top: 10px;
      text-align: right;
      .co2_select {
        width: 100px;
      }
      .el-input {
        margin-right: 0;
        width: 100%;
      }
    }
    .progress_bar_text .right_text {
      color: #FA9B01;
    }
    .progress_bar_group {
      height: 8px !important;
      i {
        background-color: #193939;
      }
      span {
        &.bar_color_0 {
          background-color: #3691FE;
        }
        &.bar_color_1 {
          background-color: #0DD3A3;
        }
        &.bar_color_2 {
          background-color: #00D1C8;
        }
        background-color: #3CCCF9;
      }
    }
  }

  /*** 运维管理 ***/
  .co2_renewable_left {
    padding-left: 1%;
    height: calc(100% - 34px);
    .tabs_box {
      margin-bottom: 10px;
      .btn {
        margin-right: 4%;
        padding-left: 0;
        padding-right: 0;
        width: 48%;
        text-align: center;
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .amount_view {
      display: flex;
      align-items: center;
      position: relative;
      padding-left: 5%;
      padding-top: 16px;
      padding-bottom: 16px;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        width: 13px;
        background: url(../image/co2_vector_1.png) no-repeat top left;
        background-size: 100% 100%;
      }
      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 13px;
        background: url(../image/co2_vector_2.png) no-repeat top left;
        background-size: 100% 100%;
      }
      .cont {
        padding-left: 5%;
      }
      h3 {
        color: #19ECFF;
        font-size: 22px;
      }
      span {
        padding-left: 5px;
        font-size: 14px;
        color: #ffffff;
        font-weight: normal;
      }
      p {
        margin-top: 2px;
        font-size: 12px;
        color: #ffffff;
      }
    }
    .page_nav3 {
      background: linear-gradient(90deg, rgba(21, 154, 255, 0.4) 0%, rgba(21, 154, 255, 0) 100%);
      span {
        color: #fff;
        b {
          color: #3CCCF9;
        }
      }
    }
    .cumulative_generation {
      background: url(../image/co2_frame_1.png) no-repeat center -6%;
      background-size: 100% auto;
      .general_view {
        display: flex;
        align-items: center;
        width: 100%;
        .el-col {
          flex: none;
        }
      }
      .general_view_item {
        margin-top: -15%;
        display: flex;
        align-items: center;
        h3 {
          color: #3CCCF9;
          font-size: 18px;
        }
        span {
          padding-left: 2px;
          font-size: 14px;
          color: #ffffff;
          font-weight: normal;
        }
        p {
          font-size: 12px;
          color: #ffffff;
        }
        .icon {
          margin: 0 7%;
        }
        .cont {
          flex: 1;
        }
        &.day_view {
          text-align: right;
        }
      }
    }
    .generating_power {
      padding-top: 20px;
      padding-bottom: 5px;
      .box {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #fff;
      }
      .cont {
        padding-left: 5%;
      }
      h3 {
        font-size: 22px;
        color: #0DD3A3;
        span {
          padding-left: 5px;
          font-size: 12px;
          color: #fff;
        }
      }
    }
  }
  .co2_renewable_left_header {
    // height: 30%;
  }
  .co2_renewable_content {
    display: flex;
    padding-left: 2%;
    height: calc(100% - 36px);
    flex-direction: column;
    justify-content: flex-start;
  }
  .real_time_load {
    display: flex;
    width: 100%;
    padding-top: 10px;
    padding-bottom: 15px;
    li {
      flex: 1;
      text-align: center;
    }
    .gauge_chart {
      margin: auto;
      width: 108px;
      height: 108px;
      background: url(../image/co2_bg_19.png) no-repeat center center;
      background-size: 100% 100%;
    }
    p {
      margin-top: -10px;
      font-size: 12px;
    }
  }
  .co2_renewable_left_bottom {
    height: calc(100% - 653px);
  }
  .co2_radiation_curve_chart {
    height: calc(100% - 36px);
  }
  .co2_renewable_right {
    .text_cont i {
      color: #3CCCF9;
    }
    .co2_progress {
      padding-left: 2%;
      padding-right: 0;
    }
    .progress_bar {
      height: 10px;
      background-color: #193939;
      border-radius: 3px;
      .progress_fill {
        height: 10px;
        background-color: #3CCCF9;
        border-radius: 3px;
      }
    }
  }
  .charging_pile_nums {
    display: flex;
    margin-top: 15px;
    margin-bottom: 20px;
    padding-left: 2%;
    li {
      flex: 1;
      display: flex;
      align-items: center;
    }
    .cont {
      flex: 1;
      padding-left: 10px;
    }
    h3 {
      font-size: 22px;
      &.device_1_num {
        color: #2294FE;
      }
      &.device_2_num {
        color: #19ECFF;
      }
      &.device_3_num {
        color: #D78737;
      }
      span {
        padding-left: 4px;
        font-size: 12px;
        color: #fff;
        font-weight: normal;
      }
    }
    p {
      font-size: 12px;
      color: #fff;
    }
  }
  .charging_pile_list {
    .state_tips {
      padding-left: 2%;
      margin-bottom: 20px;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.64);
      span {
        padding-left: 10px;
        padding-right: 15px;
        position: relative;
        &::before {
          content: '';
          position: absolute;
          top: 5px;
          left: 0;
          width: 4px;
          height: 4px;
          background-color: #0DD3A3;
        }
        &:last-child::before {
          background-color: #3CCCF9;
        }
      }
    }
    ul {
      li {
        margin-bottom: 8px;
        margin-left: 2%;
        float: left;
        width: 66px;
        height: 94px;
        text-align: center;
        border: 1px solid #3CCCF9;
        background: rgba(196, 196, 196, 0.01);
        border-radius: 4px;
        &.being_used {
          border-color: #0DD3A3;
          p {
            background: rgba(13, 211, 163, 0.4);
          }
        }
      }
      .img_box {
        height: 66px;
        line-height: 66px;
      }
      p {
        color: #ffffff;
        font-size: 14px;
        height: 28px;
        line-height: 28px;
        background: rgba(60, 204, 249, 0.4);
      }
    }
  }
  .energy_storage {
    margin-left: 2%;
    overflow: hidden;
    padding: 15px 5% 0;
    background: url(../image/co2_bg_17.png) no-repeat center bottom;
    background-size: 100% auto;
    li {
      margin-bottom: 15px;
      padding-top: 20px;
      float: left;
      width: 50%;
      text-align: center;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 106px;
        height: 106px;
        margin-top: -62px;
        margin-left: -53px;
        border: 1px solid #fff;
        border-radius: 50%;
      }
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 120px;
        height: 120px;
        margin-top: -69px;
        margin-left: -60px;
        border: 1px dashed #fff;
        border-radius: 50%;
      }
      &:first-child {
        background: url(../image/co2_vector_3.png) no-repeat left center;
        background-size: auto 100%;
        .wave_box {
          background: linear-gradient(90deg, rgba(34, 147, 252, .9) 30%, rgba(46, 183, 210, 1) 69.36%);
        }
      }
      &:last-child {
        background: url(../image/co2_vector_4.png) no-repeat right center;
        background-size: auto 100%;
        .wave_box {
          background: linear-gradient(83.2deg, #D78737 24.95%, #CA334E 72.2%);
        }
        .wave {
          margin-top: -170%;
        }
      }
    }
    .storage_title {
      margin-top: 20px;
      font-size: 14px;
    }
  }
  .wave_box {
    position: relative;
    margin: 0 auto;
    padding: 0;
    border: 0;
    width: 96px;
    height: 96px;
    border: 4px solid #275681;
    border-radius: 50%;
    overflow: hidden;

    .wave {
      position: absolute;
      width: 200px;
      height: 200px;
      margin-top: -180%;
      margin-left: -70%;
      z-index: 10;
      background-color: #030A20;
      border-radius: 47%;
      animation: spin 10s linear 0s infinite;
    }
    .cont {
      position: absolute;
      top: 30%;
      left: 0;
      right: 0;
      z-index: 10;
      h4 {
        font-size: 22px;
      }
      p {
        font-size: 14px;
      }
    }
  }
  /*** 可再生能源 ***/
  .co2_devops_left {
    padding-left: 1%;
    height: calc(100% - 34px);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .co2_devops_left_top {
      height: 52%;
    }
    .co2_devops_left_bottom {
      height: 48%;
    }
    .co2_devops_content {
      height: calc(100% - 36px);
    }
    .tabs_two_line {
      margin-bottom: 10px;
      .btn {
        margin-right: 2%;
        margin-bottom: 8px;
        padding-left: 0;
        padding-right: 0;
        width: 32%;
        text-align: center;
        &:nth-child(3n) {
          margin-right: 0;
        }
      }
    }
    .tabs_box_inline {
      .btn {
        margin-right: 1%;
        margin-bottom: 8px;
        padding-left: 5px;
        padding-right: 5px;
        text-align: center;
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .device_state_nums {
      padding-bottom: 10px;
      padding-left: 3%;
      overflow: hidden;
      background: url(../image/co2_rectangle.png) no-repeat center bottom;
      background-size: 100% auto;
      li {
        display: flex;
        align-items: center;
        float: left;
        width: 50%;
      }
      .cont {
        flex: 1;
        padding-left: 10px;
      }
      h3 {
        font-size: 22px;
      }
      .being_used_num {
        color: #19ECFF;
      }
      .fault_num {
        color: #FCD869;
      }
      p {
        font-size: 13px;
      }
    }
    .co2_device_fault_chart {
      height: calc(100% - 315px);
    }
    .co2_safe_fault_chart {
      height: calc(100% - 276px);
    }
    .page_nav4 {
      background: linear-gradient(90deg, rgba(202, 51, 78, 0.3) 0%, rgba(202, 51, 78, 0.1) 100%);
      span b {
        color: #CA334E;
      }
    }
    .alarm_box {
      padding: 10px;
      background: url(../image/co2_bg_10.png) no-repeat center center;
      background-size: 100% 100%;
      ul {
        width: 100%;
        overflow: hidden;
        li {
          float: left;
          padding: 12px 5% 20px;
          width: 45%;
          font-size: 14px;
          .name {
            padding: 0 10px;
          }
        }
        .damage_box {
          margin-right: 5%;
          background: url(../image/co2_bg_6.png) no-repeat center center;
          background-size: 100% 100%;
          .num {
            color: #CA334E;
          }
        }
        .false_alarm_box {
          margin-left: 5%;
          background: url(../image/co2_bg_7.png) no-repeat center center;
          background-size: 100% 100%;
          .num {
            color: #EDD06F;
          }
        }
      }
    }
    .device_fault {
      margin-top: 15px;
      .header_box {
        display: flex;
        align-items: center;
        span {
          flex: 1;
          font-size: 15px;
        }
        .el-input {
          width: 80px;
        }
      }
    }
  }
  .co2_devops_content {
    display: flex;
    padding-left: 2%;
    height: calc(100% - 36px);
    flex-direction: column;
    justify-content: center;
  }
  .co2_devops_center {
    bottom: 4% !important;
    .co2_table {
      margin-top: 15px;
      .table_btn {
        padding: 0 5px;
        color: #159AFF;
        text-decoration: underline;
      }
    }
  }
  .co2_devops_right {
    height: calc(100% - 32px);
    .page_nav3 {
      padding-right: 5px;
      img {
        margin-right: 5px;
      }
      background: linear-gradient(90deg, rgba(21, 154, 255, 0.4) 0%, rgba(21, 154, 255, 0) 100%);
      b {
        color: #0DD3A3;
      }
    }
    .co2_devops_right_center {
      height: calc(100% - 585px);
    }
  }
  .co2_devops_right_header {
    ul {
      padding-bottom: 10px;
      padding-left: 3%;
      padding-right: 3%;
      overflow: hidden;
      background: url(../image/co2_rectangle.png) no-repeat center bottom;
      background-size: 100% auto;
      li {
        float: left;
        text-align: center;
        width: 25%;
        padding-top: 15px;
        padding-bottom: 15px;
        &.one_level {
          background: url(../image/co2_bg_14.png) no-repeat center bottom;
          background-size: 100% auto;
          h4 {
            color: #CA334E;
          }
        }
        &.two_level {
          margin-left: 12.5%;
          margin-right: 12.5%;
          background: url(../image/co2_bg_13.png) no-repeat center bottom;
          background-size: 100% auto;
          h4 {
            color: #D78737;
          }
        }
        &.three_level {
          background: url(../image/co2_bg_12.png) no-repeat center bottom;
          background-size: 100% auto;
          h4 {
            color: #FCD869;
          }
        }
      }
      span {
        font-size: 22px;
        font-weight: 500;
      }
      h4 {
        margin-top: 6px;
        font-family: pangmenzhendao;
        font-weight: normal;
        font-size: 20px;
      }
    }

    .deal_time_box {
      margin-top: 10px;
      margin-bottom: 10px;
      .deal_time_item {
        position: relative;
        font-size: 14px;
        float: left;
        width: 48%;
        height: 58px;
        background: url(../image/co2_bg_11.png) no-repeat center center;
        background-size: 100% 100%;
        &:last-child {
          float: right;
        }
        .cont {
          position: absolute;
          top: 50%;
          left: 25px;
          right: 0;
          padding-right: 8px;
          transform: translateY(-50%);
        }
        .value {
          margin-top: 2px;
          float: right;
        }
        b {
          color: #0DD3A3;
          font-size: 16px;
        }
      }
    }
  }
  .co2_repair_wrap {
    display: flex;
    align-items: center;
    .co2_repair_chart_box {
      width: 35%;
      .co2_repair_chart {
        position: relative;
        margin-top: -12%;
        width: 144px;
        height: 144px;
        border: 1px solid #fff ;
        background-color: rgba(13, 44, 59, 0.5);
        border-radius: 50%;
        &::after {
          content: '';
          position: absolute;
          top: -8px;
          left: -8px;
          width: 160px;
          height: 160px;
          border: 1px dashed #aaa ;
          border-radius: 50%;
        }
      }
    }
    .co2_repair_centent {
      flex: 1;
    }
    .co2_repair_item {
      display: flex;
      width: 100%;
      align-items: center;
      padding-top: 10px;
      padding-bottom: 10px;
      .img_box {
        padding-left: 18%;
        text-align: center;
        font-size: 14px;
        p {
          margin-top: 5px;
        }
      }
      .cont {
        flex: 1;
        padding: 0 4%;
        p {
          padding: 1px 0;
        }
        .name {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.64);
        }
        .value {
          float: right;
          font-size: 14px;
        }
        i {
          display: inline-block;
          margin-left: 6px;
          margin-right: 6px;
          width: 4px;
          height: 4px;
        }
      }
    }
    .co2_safe_device {
      margin-bottom: 5px;
      background: url(../image/co2_bg_15.png) no-repeat center center;
      background-size: 100% 100%;
    }
    .co2_electrical_device {
      background: url(../image/co2_bg_16.png) no-repeat center center;
      background-size: 100% 100%;
    }

  }
  .co2_devops_right_bottom {
    .page_nav3 {
      margin-top: 15px;
      margin-bottom: 15px;
      b {
        color: #3CCCF9;
      }
    }
    .co2_progress {
      padding-left: 0;
      padding-right: 0;
      .text_cont { line-height: 1.5; }
    }
    .progress_bar {
      position: relative;
      height: 16px;
      background-color: transparent;
      border-radius: 0;
      .short_line {
        display: inline-block;
        margin: 0 0.5%;
        width: 1%;
        height: 14px;
        border-radius: 50px;
      }
      .red_line {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        .short_line {
          background: #CA334E;
        }
      }
      .green_line {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        .short_line {
          background: #0DD3A3;
        }
      }
    }
  }



  @keyframes spin {
    0% {
      transform: translate(-0%, -0%) rotate(0deg) scale(1);
    }

    25% {
      transform: translate(-1%, -2%) rotate(90deg) scale(1.0);
    }

    50% {
      transform: translate(-0%, -2%) rotate(180deg) scale(1.0);
    }

    75% {
      transform: translate(1%, -1%) rotate(270deg) scale(1.0);
    }

    100% {
      transform: translate(-0%, -0%) rotate(360deg) scale(1.0);
    }
  }

  /* 隐藏中间底部模块 */
  ._hidden {
    bottom:auto;
    top: 80%;
  }
}

.page_nav .input-small {
  width: 140px!important;
}
.page_nav .el-date-editor, .page_nav .el-select, .page_nav .el-date-picker {
  background: #060F1B;
}
/** 能效测评 **/
.energy_drop {
  padding: 16px;
  width: 200px;
  h4 {
    margin: 0;
    padding-bottom: 10px;
    font-size: 14px;
    color: #f9fcff;
  }
  ul {
    padding-top: 16px;
    margin-bottom: 16px;
    border-top: 1px solid rgba(108, 128, 151, 0.2);
    border-bottom: 1px solid rgba(108, 128, 151, 0.2);
    li {
      font-size: 14px;
      color: #f5f5f5;
      margin-bottom: 14px;
    }
    span {
      display: inline-block;
      width: 62px;
      line-height: 26px;
      font-size: 16px;
      text-align: center;
      border: 1px solid;
      border-radius: 20px;
      &.constraint {
        color: #fc5055;
        background: rgba(252, 80, 85, 0.1);
      }
      &.benchmark {
        color: #ef9723;
        background: rgba(239, 151, 35, 0.1);
      }
      &.guide {
        color: #14fcd5;
        background: rgba(20, 252, 213, 0.1);
      }
    }
  }
  p {
    margin: 0;
    color: #f5f5f5;
    font-size: 12px;
    line-height: 20px;
  }
}


div.starfield {
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
div.starfield .static {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 5px;
  height: 5px;
  opacity: 0.25;
  border-radius: 100%;
  transform-origin: -31vw -38vh;
  box-shadow: 36vw -46vh 1px 0.75px rgba(255, 244, 245, 0.5235723442), -68vw -54vh 1px 0.75px rgba(252, 243, 255, 0.9688846036), 27vw -66vh 1px 0.75px #f3f6f1, 21vw -38vh 1px 0.75px rgba(241, 249, 243, 0.8680567471), -15vw -3vh 1px 0.75px rgba(252, 244, 250, 0.8741643268), -95vw -40vh 1px 0.75px #f1f4ff, -88vw 14vh 1px 0.75px #fcf3fb, -83vw 12vh 1px 0.75px #f6f8fe, -61vw 39vh 1px 0.75px #f7f2ff, 41vw 34vh 1px 0.75px rgba(246, 247, 248, 0.9318846392), -53vw -73vh 1px 0.75px rgba(255, 250, 247, 0.9158435917), -39vw 28vh 1px 0.75px #f8f8fa, 73vw -66vh 1px 0.75px #f1fbf5, 82vw -1vh 1px 0.75px rgba(242, 242, 242, 0.8414716373), 92vw -27vh 1px 0.75px rgba(245, 251, 250, 0.9663508769), 28vw -64vh 1px 0.75px #f1f9f1, 76vw 82vh 1px 0.75px #f1f9f6, 53vw -70vh 1px 0.75px #f7fdf7, -11vw -70vh 1px 0.75px rgba(251, 253, 241, 0.5709810522), -73vw -40vh 1px 0.75px #f9f1fb, -36vw 41vh 1px 0.75px #fdf1fe, -12vw -9vh 1px 0.75px rgba(247, 244, 242, 0.8454906488), 60vw 62vh 1px 0.75px #fdfaf4, -83vw 87vh 1px 0.75px rgba(245, 241, 244, 0.9176305322), 44vw -54vh 1px 0.75px rgba(242, 252, 248, 0.9693348061), 36vw -32vh 1px 0.75px #f9fafa, 41vw -61vh 1px 0.75px #f2faf8, 42vw -49vh 1px 0.75px rgba(249, 246, 254, 0.9134938038), -52vw 53vh 1px 0.75px #f3feff, 3vw -97vh 1px 0.75px rgba(249, 245, 247, 0.8889316068), -90vw 46vh 1px 0.75px #f8fafd, 23vw -29vh 1px 0.75px rgba(246, 247, 250, 0.6862141599), -33vw -63vh 1px 0.75px rgba(243, 253, 249, 0.6395580736), -2vw 50vh 1px 0.75px #f5faff, -96vw 67vh 1px 0.75px rgba(255, 241, 243, 0.8407892564), 29vw -95vh 1px 0.75px #f5f5f3, 3vw -27vh 1px 0.75px #fbfef6, -87vw 83vh 1px 0.75px rgba(253, 248, 246, 0.8165139252), 47vw 57vh 1px 0.75px #f9f5f1, -9vw -14vh 1px 0.75px rgba(245, 243, 246, 0.6535044176), 54vw 31vh 1px 0.75px #fff5f3, 25vw -52vh 1px 0.75px #f7faf3, 91vw -6vh 1px 0.75px #f8fff8, -21vw 88vh 1px 0.75px rgba(253, 245, 248, 0.7648848183), -29vw 45vh 1px 0.75px rgba(253, 241, 244, 0.6657591369), 61vw -47vh 1px 0.75px #f3fcf6, 57vw 23vh 1px 0.75px #f9f3fa, -90vw 100vh 1px 0.75px rgba(251, 244, 248, 0.9775995177), -27vw -93vh 1px 0.75px rgba(248, 251, 250, 0.5803350999), 28vw -29vh 1px 0.75px #f9f4fc, -80vw 67vh 1px 0.75px #f9f9fe, 90vw -18vh 1px 0.75px rgba(251, 251, 252, 0.6814187659), -82vw 27vh 1px 0.75px rgba(244, 248, 243, 0.893709482), -66vw 33vh 1px 0.75px #f1f8fc, 97vw 2vh 1px 0.75px rgba(244, 250, 241, 0.5214614359), -31vw -34vh 1px 0.75px rgba(245, 248, 244, 0.5625745825), 40vw -33vh 1px 0.75px #f6f6f9, 10vw 47vh 1px 0.75px #fefcf4, -49vw -20vh 1px 0.75px #f3f3f7, -61vw -11vh 1px 0.75px rgba(250, 244, 248, 0.7422571864), -67vw -2vh 1px 0.75px rgba(254, 243, 243, 0.8733439445), -39vw 17vh 1px 0.75px #f8f2f1, -36vw -56vh 1px 0.75px #f8faf6, 18vw -36vh 1px 0.75px rgba(249, 255, 248, 0.7808456449), -91vw 37vh 1px 0.75px #fcfef1, -67vw -26vh 1px 0.75px #f4f4fc, 50vw -79vh 1px 0.75px rgba(245, 244, 252, 0.6027408961), 73vw 82vh 1px 0.75px rgba(243, 252, 255, 0.575434172), -24vw -97vh 1px 0.75px #f4f6ff, -87vw -23vh 1px 0.75px #fffcf9, 81vw 78vh 1px 0.75px rgba(250, 251, 255, 0.800520426), -18vw -98vh 1px 0.75px #fbfefc, 19vw -33vh 1px 0.75px #fefdff, -36vw -21vh 1px 0.75px #f3f4f2, 16vw -39vh 1px 0.75px rgba(243, 254, 247, 0.726988897), 47vw -57vh 1px 0.75px rgba(248, 244, 245, 0.5195989269), -83vw -81vh 1px 0.75px #fefdf2, -44vw -19vh 1px 0.75px rgba(245, 242, 243, 0.8433148122), 58vw -37vh 1px 0.75px rgba(255, 245, 249, 0.9858396814), 61vw -76vh 1px 0.75px rgba(249, 247, 245, 0.7973323753), -59vw 45vh 1px 0.75px rgba(246, 254, 254, 0.5407901041), -20vw 73vh 1px 0.75px #fef8f1, -57vw -77vh 1px 0.75px #fefff2, -84vw 20vh 1px 0.75px rgba(247, 250, 254, 0.620131291), 77vw 20vh 1px 0.75px #f6fafd, 87vw -15vh 1px 0.75px rgba(249, 250, 246, 0.7752325345), 19vw 60vh 1px 0.75px #fbfafb, 84vw 37vh 1px 0.75px #f5fef7, 69vw -79vh 1px 0.75px #fff4fb, 64vw -90vh 1px 0.75px #f2f2f2, 70vw 21vh 1px 0.75px #fcf8f3, -63vw 86vh 1px 0.75px rgba(241, 253, 250, 0.881736872), -99vw -89vh 1px 0.75px rgba(255, 255, 245, 0.808938129), 13vw 58vh 1px 0.75px rgba(251, 247, 251, 0.6117021217), 69vw -43vh 1px 0.75px rgba(241, 252, 255, 0.7327540053), 35vw -94vh 1px 0.75px #f4fbf5, 21vw -13vh 1px 0.75px rgba(251, 253, 244, 0.5327758874), -6vw -99vh 1px 0.75px #fdfdf4, -28vw -81vh 1px 0.75px #fdf6fb, -91vw 39vh 1px 0.75px #f1f2f1;
  width: 1px;
  height: 1px;
}
div.starfield .moving-1 {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 5px;
  height: 5px;
  opacity: 0.5;
  border-radius: 100%;
  transform-origin: -31vw -38vh;
  box-shadow: 98vw -71vh 1px 0.75px rgba(251, 254, 249, 0.5796560731), 60vw -74vh 1px 0.75px #f8f7f7, 28vw 43vh 1px 0.75px rgba(244, 241, 248, 0.6620956293), 46vw -74vh 1px 0.75px #f5f9f9, 23vw 30vh 1px 0.75px #f2f6f2, -63vw 77vh 1px 0.75px #f6f6f9, -30vw 20vh 1px 0.75px rgba(255, 254, 242, 0.95287468), -55vw -26vh 1px 0.75px rgba(241, 244, 253, 0.883166328), 26vw 2vh 1px 0.75px #f2f3f7, 71vw -60vh 1px 0.75px rgba(248, 252, 241, 0.5509532549), 74vw -17vh 1px 0.75px rgba(249, 246, 244, 0.7413498004), 96vw 16vh 1px 0.75px rgba(247, 246, 253, 0.6869293477), -36vw 53vh 1px 0.75px #fbf8f5, 22vw 94vh 1px 0.75px #f1f2fe, -59vw 90vh 1px 0.75px #f5f5f1, 45vw 59vh 1px 0.75px #fbf2f5, -59vw 90vh 1px 0.75px rgba(252, 245, 252, 0.937959723), 42vw -83vh 1px 0.75px rgba(244, 251, 247, 0.759251987), -29vw 83vh 1px 0.75px #f5fdf4, 34vw -96vh 1px 0.75px rgba(248, 254, 254, 0.547830485), 16vw -5vh 1px 0.75px rgba(251, 245, 252, 0.8815244488), 69vw 64vh 1px 0.75px #fcfcf4, 13vw -25vh 1px 0.75px rgba(254, 251, 242, 0.9875626299), 94vw -52vh 1px 0.75px rgba(246, 249, 244, 0.7827653594), 53vw 40vh 1px 0.75px #f5fbf3, -36vw 27vh 1px 0.75px #f7faff, -16vw 48vh 1px 0.75px #fef8f9, -46vw -68vh 1px 0.75px rgba(253, 241, 252, 0.8559579086), -31vw -88vh 1px 0.75px rgba(241, 241, 243, 0.5032175611), 61vw 91vh 1px 0.75px rgba(251, 244, 248, 0.5219877666), -82vw 27vh 1px 0.75px #f4f9f7, 19vw -96vh 1px 0.75px #f5fdf4, -74vw -74vh 1px 0.75px #f2fef5, -30vw 86vh 1px 0.75px rgba(247, 250, 243, 0.996487551), 92vw -2vh 1px 0.75px rgba(252, 245, 246, 0.7235354241), 99vw 1vh 1px 0.75px rgba(248, 250, 250, 0.9360883972), 88vw -73vh 1px 0.75px #fafbfc, 77vw 96vh 1px 0.75px #faf6fa, 17vw 95vh 1px 0.75px rgba(244, 248, 241, 0.5054011713), 42vw 5vh 1px 0.75px rgba(246, 249, 254, 0.785195719), -97vw 68vh 1px 0.75px rgba(251, 245, 245, 0.7938347435), 7vw -95vh 1px 0.75px rgba(243, 249, 255, 0.5113630258), 75vw -99vh 1px 0.75px #f3fcf7, 23vw -96vh 1px 0.75px #f1fdfd, 7vw 37vh 1px 0.75px rgba(242, 251, 254, 0.7921848084), -76vw 50vh 1px 0.75px rgba(254, 251, 253, 0.5466062608), -89vw -47vh 1px 0.75px #f7f4fc, 64vw 60vh 1px 0.75px #f5f3f4, 35vw -93vh 1px 0.75px rgba(244, 244, 253, 0.9113821496), -32vw 14vh 1px 0.75px rgba(255, 249, 247, 0.5448893182), 93vw 79vh 1px 0.75px rgba(243, 253, 243, 0.5715573667), 30vw -37vh 1px 0.75px rgba(246, 252, 252, 0.5706466807), 56vw 4vh 1px 0.75px rgba(248, 248, 252, 0.8380061132), 29vw -89vh 1px 0.75px rgba(245, 251, 241, 0.7265369866), 85vw -99vh 1px 0.75px rgba(247, 248, 252, 0.7803772343), -10vw 9vh 1px 0.75px rgba(250, 249, 253, 0.5637405381), 44vw 22vh 1px 0.75px rgba(246, 243, 241, 0.8564573626), 79vw 67vh 1px 0.75px #fff4fe, 3vw -54vh 1px 0.75px rgba(253, 253, 254, 0.70205046), -36vw -14vh 1px 0.75px rgba(249, 250, 244, 0.9694684202), 32vw -34vh 1px 0.75px #fffffb, -6vw -95vh 1px 0.75px rgba(253, 252, 252, 0.9187564777), 96vw -45vh 1px 0.75px rgba(242, 250, 252, 0.8899203059), 79vw -68vh 1px 0.75px rgba(253, 245, 244, 0.7345401342), -19vw 60vh 1px 0.75px #f1fbfb, 33vw 97vh 1px 0.75px #fbf1ff, -28vw 25vh 1px 0.75px rgba(242, 248, 245, 0.5307262917), -41vw -98vh 1px 0.75px #faf6f1, -24vw -83vh 1px 0.75px #fff4f9, 45vw -20vh 1px 0.75px rgba(248, 243, 250, 0.5240054996), 28vw 39vh 1px 0.75px rgba(250, 243, 250, 0.5274017566), 90vw 31vh 1px 0.75px #f8f6f9, -96vw -21vh 1px 0.75px #f8f7f2, 96vw 9vh 1px 0.75px rgba(247, 251, 247, 0.7435375515), -64vw -52vh 1px 0.75px #f5f8f3, 95vw -87vh 1px 0.75px rgba(243, 248, 243, 0.9133779279), 39vw -71vh 1px 0.75px #fbfcfa, -58vw 0vh 1px 0.75px #fbf5f7, -99vw -40vh 1px 0.75px #fff8f1, 80vw 84vh 1px 0.75px rgba(243, 243, 254, 0.8503211098), -46vw 2vh 1px 0.75px rgba(253, 242, 245, 0.5192076591), -99vw 54vh 1px 0.75px rgba(252, 241, 246, 0.7197874037), -71vw -13vh 1px 0.75px rgba(245, 253, 242, 0.8075053814), 8vw 77vh 1px 0.75px #f5f1f1, 80vw -98vh 1px 0.75px #fff8f1, -35vw -4vh 1px 0.75px #f6f3fb, -5vw -90vh 1px 0.75px #fbfdf1, 90vw -29vh 1px 0.75px rgba(251, 249, 241, 0.807057173), -20vw 73vh 1px 0.75px rgba(246, 242, 252, 0.5286088298), -46vw 83vh 1px 0.75px #f2faf1, -18vw 13vh 1px 0.75px #f1f2fc, 70vw 50vh 1px 0.75px #fff9fa, -82vw -18vh 1px 0.75px rgba(249, 254, 255, 0.835895734), 27vw -52vh 1px 0.75px rgba(246, 243, 243, 0.5915919663), 79vw -97vh 1px 0.75px #f3faf6, -27vw -84vh 1px 0.75px rgba(246, 245, 249, 0.6195757299), 25vw 95vh 1px 0.75px #f6f6fd, -83vw 95vh 1px 0.75px rgba(248, 250, 255, 0.995678514), 18vw -96vh 1px 0.75px #fafbf9, -87vw -52vh 1px 0.75px #f6fcf4;
  animation: star-movement 9s cubic-bezier(0.55, 0, 1, 0.45) infinite, direction-movement 30s ease-in-out alternate infinite;
}
div.starfield .moving-2 {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 5px;
  height: 5px;
  opacity: 0.75;
  border-radius: 100%;
  transform-origin: -31vw -38vh;
  box-shadow: 40vw -74vh 1px 0.75px rgba(243, 253, 243, 0.6976853084), -93vw 42vh 1px 0.75px rgba(241, 241, 245, 0.5218902844), -6vw 27vh 1px 0.75px #f3ffff, -21vw -9vh 1px 0.75px #fef9f9, 70vw -13vh 1px 0.75px rgba(245, 253, 246, 0.632556686), 24vw -76vh 1px 0.75px rgba(247, 241, 242, 0.6379328074), 36vw -80vh 1px 0.75px #f1faf9, 33vw -49vh 1px 0.75px #f4fdf6, 23vw 2vh 1px 0.75px rgba(244, 250, 246, 0.7003595363), -46vw 41vh 1px 0.75px #fef6f3, 49vw -62vh 1px 0.75px rgba(249, 251, 241, 0.7464029821), 36vw 62vh 1px 0.75px #f3fafe, 87vw 44vh 1px 0.75px #f8f6f6, 24vw 70vh 1px 0.75px rgba(243, 249, 250, 0.9627452695), -36vw 81vh 1px 0.75px rgba(245, 241, 250, 0.8348169547), 65vw -86vh 1px 0.75px rgba(251, 250, 255, 0.6697964858), -78vw 27vh 1px 0.75px rgba(251, 250, 245, 0.6678135489), -99vw 84vh 1px 0.75px #fbfbf2, -66vw 63vh 1px 0.75px rgba(248, 247, 251, 0.6429325607), 54vw -53vh 1px 0.75px rgba(245, 241, 252, 0.8622728604), -51vw -13vh 1px 0.75px #fcf7f2, -93vw -91vh 1px 0.75px rgba(246, 242, 242, 0.7987353667), 80vw 91vh 1px 0.75px #f8f5f3, 67vw -66vh 1px 0.75px #f9f3f1, 47vw 78vh 1px 0.75px rgba(245, 250, 244, 0.74760595), -22vw 57vh 1px 0.75px rgba(246, 247, 247, 0.7581258614), -96vw -5vh 1px 0.75px #f9f5f5, 82vw -26vh 1px 0.75px #f3f7fc, 62vw -6vh 1px 0.75px #f2f7f1, 49vw 83vh 1px 0.75px rgba(248, 251, 252, 0.7898566349), 30vw 45vh 1px 0.75px rgba(247, 241, 253, 0.8091298881), -24vw -54vh 1px 0.75px #f7fcf8, 53vw -22vh 1px 0.75px rgba(254, 244, 249, 0.573485027), -48vw 88vh 1px 0.75px #f3f8f8, -89vw -52vh 1px 0.75px rgba(247, 249, 249, 0.6259997331), 0vw 82vh 1px 0.75px #f1f7f5, -25vw 9vh 1px 0.75px rgba(252, 242, 255, 0.5956961941), -7vw 31vh 1px 0.75px rgba(253, 253, 247, 0.553963104), 35vw 62vh 1px 0.75px rgba(254, 245, 254, 0.8865271661), -9vw -53vh 1px 0.75px #f8fbfa, 10vw -81vh 1px 0.75px #faf4fa, -75vw 8vh 1px 0.75px #f6f1f2, 50vw -51vh 1px 0.75px #fbf9fc, 99vw 32vh 1px 0.75px #fffaf1, -98vw -1vh 1px 0.75px rgba(248, 245, 249, 0.5978577971), 79vw -65vh 1px 0.75px rgba(243, 243, 245, 0.8759631083), 18vw 29vh 1px 0.75px #f5f3f6, -90vw -94vh 1px 0.75px #f2f5fb, -4vw -51vh 1px 0.75px #f2fef6, 62vw 71vh 1px 0.75px #f9f6fc, 70vw 33vh 1px 0.75px rgba(243, 246, 249, 0.8400624172), -14vw 71vh 1px 0.75px rgba(249, 246, 241, 0.719868869), 77vw -49vh 1px 0.75px #f9f9f4, 56vw -68vh 1px 0.75px #f1f1f6, 87vw -50vh 1px 0.75px #fdf9fd, -13vw 24vh 1px 0.75px rgba(254, 255, 245, 0.8050234245), 20vw -5vh 1px 0.75px rgba(254, 249, 255, 0.7744509303), 29vw -22vh 1px 0.75px rgba(254, 252, 249, 0.8683826021), 54vw -35vh 1px 0.75px snow, 19vw -30vh 1px 0.75px #f5f5f3, 99vw -48vh 1px 0.75px rgba(245, 245, 249, 0.5407473116), -84vw -26vh 1px 0.75px #f7f5fe, 38vw -24vh 1px 0.75px #f1fbf6, -48vw 33vh 1px 0.75px rgba(246, 255, 254, 0.6254991258), 28vw 15vh 1px 0.75px rgba(250, 245, 253, 0.8310338887), 73vw 91vh 1px 0.75px rgba(253, 242, 254, 0.7767698708), -65vw 96vh 1px 0.75px #f4f1f8, 8vw -48vh 1px 0.75px rgba(253, 246, 246, 0.8645967089), -22vw -85vh 1px 0.75px #f9f3f8, 36vw 63vh 1px 0.75px rgba(254, 253, 241, 0.6785751691), 99vw 37vh 1px 0.75px rgba(247, 247, 247, 0.582057336), 56vw -76vh 1px 0.75px #fffcfd, 83vw -12vh 1px 0.75px rgba(247, 250, 248, 0.8136677806), 26vw -12vh 1px 0.75px #fef9fd, 100vw 82vh 1px 0.75px rgba(250, 242, 248, 0.6861693788), 73vw -21vh 1px 0.75px #fbfcfb, -91vw -24vh 1px 0.75px rgba(253, 245, 241, 0.7598353681), 94vw 49vh 1px 0.75px rgba(254, 252, 251, 0.7513094056), -18vw 14vh 1px 0.75px rgba(247, 241, 247, 0.8442176478), -80vw 99vh 1px 0.75px #fcf3f8, -21vw 22vh 1px 0.75px #fdfaf6, 9vw 56vh 1px 0.75px rgba(241, 252, 250, 0.9484880841), -86vw 63vh 1px 0.75px #fffff7, 97vw 93vh 1px 0.75px rgba(246, 243, 246, 0.7090860489), -47vw 100vh 1px 0.75px #fbf6f6, 57vw -62vh 1px 0.75px #f9f2f5, -60vw 60vh 1px 0.75px rgba(250, 248, 246, 0.8128038178), -63vw 85vh 1px 0.75px #fcfcf9, -76vw -58vh 1px 0.75px #f4faf4, -8vw 93vh 1px 0.75px rgba(253, 250, 245, 0.7038606127), -95vw 32vh 1px 0.75px rgba(242, 252, 245, 0.8777265504), 53vw -8vh 1px 0.75px #f7f9f6, 49vw -13vh 1px 0.75px #f5f9fd, -41vw -60vh 1px 0.75px #f1faf1, -67vw 41vh 1px 0.75px rgba(254, 255, 254, 0.7231559202), 2vw 68vh 1px 0.75px #f9f3f6, -63vw 100vh 1px 0.75px #fbf1fc, -82vw 41vh 1px 0.75px #f8fff9, -72vw 95vh 1px 0.75px rgba(246, 244, 254, 0.8975317144), -42vw -56vh 1px 0.75px rgba(248, 253, 247, 0.5535049695);
  animation: star-movement 9s -3s cubic-bezier(0.55, 0, 1, 0.45) infinite, direction-movement 30s ease-in-out alternate infinite;
}
div.starfield .moving-3 {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 5px;
  height: 5px;
  opacity: 0.75;
  border-radius: 100%;
  transform-origin: -31vw -38vh;
  box-shadow: -44vw 6vh 1px 0.75px #f2f3fb, -80vw 15vh 1px 0.75px rgba(243, 250, 243, 0.516430015), 27vw -53vh 1px 0.75px #f3f7f6, -72vw 67vh 1px 0.75px #f2f7fd, 67vw -22vh 1px 0.75px #fef1fe, 84vw 49vh 1px 0.75px rgba(251, 243, 255, 0.6223981162), -99vw 33vh 1px 0.75px #fdf7f1, -19vw 69vh 1px 0.75px #f1f4f3, 24vw 62vh 1px 0.75px rgba(249, 244, 245, 0.847846961), -48vw 15vh 1px 0.75px rgba(252, 252, 243, 0.9462591898), -18vw 42vh 1px 0.75px rgba(242, 252, 248, 0.5148233329), -7vw -11vh 1px 0.75px rgba(254, 251, 242, 0.523792914), -37vw 21vh 1px 0.75px rgba(253, 251, 250, 0.6881076159), -47vw -23vh 1px 0.75px rgba(243, 242, 243, 0.6893285365), -79vw -28vh 1px 0.75px #f4f5fd, -33vw 85vh 1px 0.75px rgba(243, 241, 253, 0.8328932503), 63vw -7vh 1px 0.75px #fdf7ff, 94vw 26vh 1px 0.75px #f1fefc, -21vw 27vh 1px 0.75px rgba(244, 241, 251, 0.9303236812), -3vw -78vh 1px 0.75px rgba(254, 245, 245, 0.8262886947), 50vw -42vh 1px 0.75px #f6fef4, 75vw 7vh 1px 0.75px #fbf4f7, 52vw -37vh 1px 0.75px #f1f6f8, 11vw 64vh 1px 0.75px #f7f2fe, -89vw 23vh 1px 0.75px rgba(245, 242, 250, 0.5728733726), 38vw -48vh 1px 0.75px #f4fdf7, 61vw 66vh 1px 0.75px #f5f3f5, -25vw -43vh 1px 0.75px #f8f3f4, 6vw 77vh 1px 0.75px rgba(241, 246, 252, 0.7924307884), 66vw 56vh 1px 0.75px rgba(247, 247, 248, 0.7896422377), 95vw 2vh 1px 0.75px rgba(249, 247, 246, 0.590453276), 9vw -33vh 1px 0.75px #fefcff, -44vw -94vh 1px 0.75px rgba(250, 249, 248, 0.7011202204), 43vw 0vh 1px 0.75px #fdfaf8, 43vw 87vh 1px 0.75px rgba(250, 241, 246, 0.7653099926), -18vw -25vh 1px 0.75px rgba(241, 249, 241, 0.5380415257), -16vw 42vh 1px 0.75px #f9fef1, -82vw 62vh 1px 0.75px rgba(243, 241, 243, 0.7823410134), -88vw -31vh 1px 0.75px rgba(245, 249, 243, 0.5612056516), -84vw 69vh 1px 0.75px #f5fcf9, -40vw 4vh 1px 0.75px rgba(246, 245, 252, 0.6526124422), -18vw 32vh 1px 0.75px #f6f9f8, -26vw 47vh 1px 0.75px rgba(252, 247, 243, 0.5120760699), 11vw 74vh 1px 0.75px #f4f4f6, 88vw 64vh 1px 0.75px rgba(254, 242, 255, 0.6278793349), 1vw 100vh 1px 0.75px #fdf5fb, -12vw -52vh 1px 0.75px #fff1fa, -81vw 2vh 1px 0.75px #f9f9fa, -74vw 78vh 1px 0.75px rgba(253, 247, 250, 0.6531596142), 29vw 35vh 1px 0.75px rgba(254, 244, 244, 0.5155631285), -50vw 11vh 1px 0.75px #f4fcfc, -95vw -18vh 1px 0.75px rgba(245, 241, 241, 0.5907147267), 75vw 76vh 1px 0.75px rgba(252, 253, 245, 0.6198726616), 8vw 26vh 1px 0.75px rgba(242, 254, 244, 0.6958144356), -84vw -22vh 1px 0.75px #f6f5fd, 55vw -21vh 1px 0.75px #fdf1fa, -27vw 38vh 1px 0.75px rgba(241, 248, 248, 0.5923234092), -41vw -69vh 1px 0.75px rgba(244, 243, 250, 0.7658755818), 99vw 71vh 1px 0.75px rgba(253, 251, 248, 0.7687157225), -35vw -13vh 1px 0.75px rgba(254, 255, 246, 0.9431341704), -66vw -30vh 1px 0.75px #f8f3f2, -54vw -97vh 1px 0.75px #f5fffe, -31vw 73vh 1px 0.75px rgba(242, 249, 249, 0.6221945971), -78vw 51vh 1px 0.75px rgba(241, 255, 244, 0.9122044389), 74vw -79vh 1px 0.75px rgba(249, 250, 253, 0.6143912375), -85vw -54vh 1px 0.75px #fff7ff, 8vw -50vh 1px 0.75px #f3fbf5, -55vw 8vh 1px 0.75px #fff5f9, 49vw -98vh 1px 0.75px #f1f3fd, -87vw 27vh 1px 0.75px rgba(248, 242, 246, 0.7316524966), -50vw 43vh 1px 0.75px rgba(242, 255, 242, 0.9689863526), -81vw -81vh 1px 0.75px rgba(247, 245, 251, 0.7452617459), 8vw -90vh 1px 0.75px #f7f8f1, -10vw 52vh 1px 0.75px rgba(245, 251, 242, 0.8692656615), -30vw -52vh 1px 0.75px #f9fff1, 49vw -46vh 1px 0.75px #f2f6f8, 63vw 99vh 1px 0.75px #f7f9fd, -67vw 14vh 1px 0.75px #f1f8fc, 69vw 95vh 1px 0.75px #f3f3f2, 56vw -55vh 1px 0.75px rgba(247, 253, 253, 0.7457878729), -4vw 84vh 1px 0.75px rgba(242, 252, 243, 0.7350566017), -61vw -68vh 1px 0.75px rgba(245, 252, 243, 0.5956419672), -30vw 88vh 1px 0.75px rgba(247, 249, 245, 0.7603800395), 80vw 41vh 1px 0.75px #fefdf7, -33vw 88vh 1px 0.75px rgba(248, 252, 250, 0.6284337101), -45vw 3vh 1px 0.75px #fdf8fd, 71vw 57vh 1px 0.75px #fbfcf5, -67vw 36vh 1px 0.75px #fffef1, 16vw 32vh 1px 0.75px #f2fff9, -8vw 9vh 1px 0.75px rgba(249, 245, 243, 0.5095446679), -10vw -57vh 1px 0.75px rgba(253, 255, 246, 0.5957843896), 56vw 92vh 1px 0.75px rgba(244, 244, 248, 0.6443797484), 62vw 51vh 1px 0.75px rgba(254, 241, 255, 0.6773818306), -41vw 29vh 1px 0.75px rgba(245, 248, 242, 0.8849121272), 75vw 74vh 1px 0.75px #f3fefd, -20vw 91vh 1px 0.75px #fcfaf1, -49vw 56vh 1px 0.75px #fbfbf3, -80vw -50vh 1px 0.75px rgba(241, 241, 242, 0.8231185693), -97vw 13vh 1px 0.75px #f6faf2, 43vw -50vh 1px 0.75px rgba(253, 255, 252, 0.8920248562);
  animation: star-movement 9s -6s cubic-bezier(0.55, 0, 1, 0.45) infinite, direction-movement 30s ease-in-out alternate infinite;
}

@keyframes star-movement {
  0% {
    transform: scale(0.5) translateZ(0);
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  99% {
    opacity: 1;
  }
  100% {
    transform: scale(2) translateZ(0);
    opacity: 0;
  }
}
@keyframes direction-movement {
  from {
    transform-origin: -12vw -2vh;
  }
  to {
    transform-origin: 25vw 23vh;
  }
}
div.word {
  /* position: absolute; */
  /* right: 1vw; */
  /* text-align: right; */
  /* font-size: 2vh; */
  /* animation: word-appear 60s infinite; */
  /* opacity: 0; */
  /* transform: translate3d(calc(1vw + 100%), 0, 0); */
}

@keyframes word-appear {
  0% {
    opacity: 0;
    transform: translate3d(calc(1vw + 100%), 0, 0);
  }
  2% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
  80% {
    opacity: 0;
    transform: translate3d(0, 0, 0);
  }
}



.flex_column {
  flex-direction: column;
}
.screen_wrap {
  background: #000205;
  position: relative;
  min-height: 100%;
  overflow: hidden;
  .bg_img {
    position: absolute;
    top: 20%;
    left: 27%;
    z-index: 1;
    width: 46%;
    opacity: 0.8;
  }
  &.demand_wrap {
    background: url('/image/ningbo/bg_line.png') no-repeat bottom center;
    background-size: 100% auto;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 322px;
      z-index: 10;
      background: #01040C linear-gradient(180deg, rgba(16,80,160,0) 0%, rgba(16,80,160,0.4) 100%);
    }
    .screen_bottom_img {
      z-index: 12;
    }
    .screen_main {
      .s_side {
        width: 26.5%;
      }
      .main_content {
        width: 53%;
      }
    }

    .side_chart {
      flex-direction: column;
      .side_item {
        height: 50%;
      }
      .chart_box {
        height: calc(100% - 36px) !important;
      }
    }
    .demand_main_content {
      position: relative;
    }
    .demand_chart_statistics {
      position: relative;
      padding-top: 8%;
      img {
        width: 100%;
      }
      .text {
        position: absolute;
        text-align: center;
        width: 120px;
        h4 {
          font-size: 32px;
        }
        span {
          font-size: 14px;
        }
      }
      .text_left {
        top: 16%;
        left: 23%;
        margin-left: -50px;
        h4 {
          color: #3CCCF9;
        }
      }
      .text_top {
        top: 4%;
        left: 50%;
        margin-left: -50px;
        h4 {
          color: #FCD869;
        }
      }
      .text_right {
        top: 24%;
        right: 24%;
        margin-right: -50px;
        h4 {
          color: #2294FE;
        }
      }
    }
    .demand_table_statistics {
      position: absolute;
      left: 5%;
      bottom: 1%;
      width: 90%;
      padding-top: 10px;
      padding-left: 5%;
      border: 1px solid #3CCCF9;
      background: rgba(60,204,249,0.15);
      .demand_table_item {
        display: flex;
        align-items: center;
        padding: 5px 0;
      }
      .cont {
        padding-left: 10px;
      }
      .value {
        color: #3CCCF9;
        font-size: 18px;
        padding: 0 2px;
      }
      .unit {
        font-size: 14px;
      }
    }
  }
  .screen_side_img {
    position: absolute;
    top: 0;
    bottom: 0;
    z-index: 10;
    height: 100%;
  }
  .screen_side_left {
    left: 0;
  }
  .screen_side_right {
    right: 0;
  }
  .screen_bottom_img {
    position: absolute;
    left: 0;
    bottom: -5px;
    width: 100%;
  }
  .screen_content {
    position: relative;
    z-index: 100;
    height: 93%;

    display: flex;
    flex-direction: column;
  }
  .screen_header {
    position: relative;
    .header_img {
      width: 100%;
    }
    .screen_header_left,
    .screen_header_right {
      position: absolute;
      bottom: 15%;
      white-space: nowrap;
    }
    .screen_header_left {
      left: 3%;
    }
    .screen_header_right {
      right: 5%;
    }
    .weather,
    .building_group,
    .date_time,
    .user {
      display: inline-block;
    }
    .building_group h2 {
      font-size: 16px;
    }
    .building_group,
    .user {
      margin-left: 15px;
    }
  }
  .screen_main {
    position: relative;
    padding: 0 25px;
    flex: 1;
    color: #fff;
    .screen_item {
      display: flex;
    }
    .s_side {
      width: 25%;
      .s_side_content {
        margin-top: 10px;
        margin-left: 1.8%;
      }
    }
    .main_content {
      position: relative;
      flex: 1;
    }
    .body_content {
      padding: 10px;
    }
    .total_capacity {
      padding-top: 0px;
      li {
        margin-top: 20px;
        margin-bottom: 10px;
      }
      .head {
        align-items: center;
        color: #fff;
        font-size: 14px;
      }
      .short_line {
        display: inline-block;
        margin-right: 6px;
        width: 3px;
        height: 8px;
        background: #2294FE;
      }
      .per {
        margin-left: 5%;
        font-size: 12px;
      }
      .progress_bar {
        position: relative;
        margin-top: 5px;
        .bar {
          display: block;
          height: 7px;
          border-radius: 10px;
        }
        .base_bar {
          background: #2294FE;
          opacity: 0.2;
        }
        .gradient_bar {
          position: absolute;
          top: 0;
          left: 0;
        }
      }
    }
    .bg_gradient {
      border: 1px solid;
      border-top: none;
      background: linear-gradient(0deg, #0A1B2C 0%, #000205 100%);
      border-image: linear-gradient(00deg, rgba(46, 98, 145, 0.61), rgba(46, 98, 145, 0.69), rgba(46, 98, 145, 0)) 1 1;
    }
    .address_wrap {
      position: relative;
      padding: 0 6%;
      height: 100%;
      .bg_img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
      }
      .city_factory {
        position: absolute;
        top: 0;
        left: 4%;
        width: 90%;
        padding-left: 20px;
        z-index: 100;
        &:before {
          content: '';
          position: absolute;
          top: 5px;
          left: 0;
          width: 7px;
          height: 7px;
          background: #3BC8F6;
          box-shadow: 0px 0px 12px 0px #3BC8F6;
          border-radius: 50%;
        }
        &::after {
          content: '';
          position: absolute;
          top: 5px;
          left: 3px;
          width: 1px;
          height: 57px;
          background: linear-gradient(360deg, #FFFFFF 0%, rgba(217,217,217,0) 100%);
          border-radius: 0px 0px 0px 0px;
        }
        .city_name {
          margin-bottom: 5px;
          font-family: pangmenzhendao;
          font-weight: normal;
          font-size: 22px;
        }
        .map_select {
          width: 180px;
          background: #0A1519;
          border: 2px solid;
          box-shadow: inset 0px 0px 12px 0px #2294FE;
          border-image: linear-gradient(135deg, rgba(73, 168, 255, 1), rgba(34, 148, 254, 1)) 2 2;
        }
        .el-input__inner {
          color: #2294FE;
        }
      }
    }
    &.flex_column {
      display: flex;
      flex-direction: column;
    }
    .charts_wrap {
      padding-top: 20px;
      .chart_box {
        flex-direction: column;
      }
      .chart_cont {
        padding: 0 2%;
        flex: 1;
      }
    }
    .main_chart {
      height: 100%;
      padding: 0 10px;
      .height100 {
        height: 100%;
      }
    }
    .page_fixed_nav_box {
      top: 6%;
    }

    // 能碳管理
    .factory_overview {
      .overview_item {
        padding: 10px 0;
        border: 1px solid;
        border-image: linear-gradient(88deg, rgba(34, 148, 254, 1), rgba(34, 148, 254, 0.25)) 1 1;
      }
      .new_card_data {
        padding: 0 10px;
        .icon_box {
          margin-right: 8px;
        }
        .icon {
          width: 42px;
        }
        &.card_value .value {
          font-size: 18px;
        }
      }
    }
    .carbon_side {
      flex-direction: column;
    }
    .data_total {
      padding-top: 3px;
      padding-bottom: 3px;
      .icon {
        width: 24px;
      }
    }
    .energy_center {
      .chart_box {
        position: absolute;
        left: 5%;
        bottom: 0;
        width: 90%;
      }
    }

    // 设备管理
    .aircondition_content {
      .statistics_item {
        margin-bottom: 6px;
        padding: 5px 10px;
        border: 1px solid;
        border-image: linear-gradient(89deg, rgba(60, 204, 249, 1), rgba(60, 204, 249, 0.2)) 1 1;
        .new_card_data {
          flex: 1;
        }
        .icon_box {
          min-width: 50px;
          margin-right: 0;
        }
        .unit {
          color: #3CCCF9;
        }
      }
      .chart_box {
        position: relative;
      }
    }
    .statistics_wrap {
      padding: 0 5% 1%;
      display: flex;
      align-items: center;
      background: url('~@/assets/image/ningbo/device_stas_bg.png') no-repeat center bottom;
      background-size: 100% auto;

      .value {
        font-size: 24px;
      }
    }

    .pv_statistics {
      li {
        position: relative;
        width: 48%;
        padding: 10px 10px 10px 15px;
        &:first-child {
          margin-right: 4%;
        }
        &:before,
        &::after {
          content: '';
          position: absolute;
          top: 0;
          bottom: 0;
          width: 10px;
          border: 1px solid #275681;
        }
        &:before {
          left: 0;
          border-right: none;
        }
        &:after {
          right: 0;
          border-left: none;
        }
        .value {
          color: #19ECFF;
          font-size: 24px;
          font-weight: bold;
        }
      }

    }
    .power_generation {
      position: relative;
      border: 1px solid;
      border-image: linear-gradient(180deg, rgba(46, 98, 145, 0), rgba(46, 98, 145, 1)) 1 1;
      .data_total {
        padding-top: 10px;
        padding-left: 15px;
        padding-bottom: 10px;
      }
      li {
        padding-top: 20px;
        padding-bottom: 20px;
        padding-left: 10px;
        .value {
          font-size: 18px;
        }
      }
      .center_img {
        position: absolute;
        left: 50%;
        bottom: -16px;
        width: 25%;
        transform: translateX(-50%);
      }
      .text_right {
        .cont {
          padding-right: 15px;
          text-align: right;
        }
      }
    }
    .energy_storage_top {
      padding: 15px 15px 10px;
      border: 1px solid;
      border-top: none;
      background: linear-gradient(180deg, rgba(60, 204, 249, 0) 0%, rgba(60, 204, 249, 0.2) 100%);
      border-image: linear-gradient(0deg, #2E6291, rgba(46, 98, 145, 0)) 1 1;
      .value {
        font-size: 24px;
      }
    }
    .energy_storage_detail {
      position: relative;
      padding: 10px 20px;
      &:before,
      &::after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 22px;
        border: 1px solid #275681;
      }
      &:before {
        left: 0;
        border-right: none;
      }
      &:after {
        right: 0;
        border-left: none;
      }
      li {
        align-items: center;
      }
      .img_box {
        position: relative;
        .center {
          position: absolute;
          top: 50%;
          left: 50%;
          text-align: center;
          transform: translate3d(-50%, -50%, 0);
        }
        .title {
          font-size: 16px;
        }
      }
      .unit {
        font-size: 12px;
      }
      .cont {
        padding-left: 10px;
        font-size: 12px;
        .title {
          margin-bottom: 3px;
        }
        .value {
          padding-right: 2px;
          font-size: 16px;
          color: #3CCCF9;
        }
      }
    }

    .center_content {
      position: absolute;
      left: 5%;
      bottom: 0;
      width: 90%;
      min-height: 30%;
    }

    .smart_wrap {
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-gap: 16px;
      padding-left: 3%;
      font-size: 12px;
      color: #fff;
      li {
        padding: 9% 10px;
        text-align: center;
        border: 1px solid #275681;
        background: rgba(60,204,249,0.1);
      }
      .value {
        padding-right: 2px;
        color: #3CCCF9;
        font-size: 24px;
      }
    }

  }
  // 设备管理
  &.device_wrap {
    .new_card_data {
      .value {
        color: #3CCCF9;
      }
      .title {
        margin-top: 0;
      }
    }
  }
  .nav_select {
    position: absolute;
    top: 0;
    right: 0;
    width: 60px;
    z-index: 10;
    .el-input__inner {
      height: 24px;
      line-height: 24px;
      border-color: #134A7D !important;
      background: #06101F;
    }
    .el-input__icon {
      line-height: 24px;
    }
  }

  .total_data_capacity {
    margin-bottom: 10px;
    .title {
      font-size: 14px;
    }
    .content {
      font-family: pangmenzhendao;
    }
  }
  .screen_dialog {
    position: absolute;
    padding: 0 20px;
    right: 4%;
    bottom: 0;
    width: 289px;
    height: 268px;
    background: url('~@/assets/image/ningbo/map_dialog1.png') no-repeat center center;
    background-size: 100% 100%;
    z-index: 100;
    .dialog_header {
      position: relative;
      padding-top: 6%;
      padding-left: 7%;
      font-size: 18px;
      margin-bottom: 20px;
      span {
        font-family: pangmenzhendao;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .close_btn {
        position: absolute;
        right: -5px;
        top: 14px;
        cursor: pointer;
      }
    }
    .factory_list {
      height: 170px;
      font-size: 14px;
      overflow-y: auto;
      li {
        margin-top: 10px;
        align-items: center;
      }
      .index {
        padding: 0 5px;
        height: 20px;
        border: 1px solid;
        background: linear-gradient(90deg, #0049B9 0%, #002E75 100%);
        border-image: linear-gradient(135deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0), rgba(255, 255, 255, 0), rgba(255, 255, 255, 0.27)) 1 1;
      }
      .factory_name {
        padding: 0 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: rgba(255,255,255,0.8);
      }
    }
  }
  .factory_dialog {
    left: 4%;
    right: auto;
    height: 194px;
    background: url('~@/assets/image/ningbo/map_dialog2.png') no-repeat center center;
    background-size: 100% 100%;
    z-index: 100;
    .dialog_header{
      align-items: center;
    }
    .dialog_btn {
      margin-right: 20px;
    }
  }
  .dialog_btn {
    padding: 0 12px;
    height: 24px;
    line-height: 24px;
    white-space: nowrap;
    color: #2193FD;
    font-size: 12px;
    background: #091622;
    box-shadow: inset 0px 0px 6px 0px #2294FE;
    border-radius: 2px;
    border: 1px solid #2193FD;
  }
  .energy_storage {
    padding: 10px;
    padding-bottom: 0;
    li {
      padding: 10px;
      margin-bottom: 10px;
      border: 1px solid #1C3F5E;
      background: rgba(60,204,249,0.04);
    }
    .total_cont {
      padding-bottom: 10px;
      margin-bottom: 10px;
      border-bottom: 1px solid rgba(60, 204, 249, .1);
    }
    .new_card_data .value {
      color: #3CCCF9;
    }
  }
  .tabs_box {
    a {
      display: inline-block;
      padding: 6px 30px;
      margin-right: 10px;
      font-size: 12px;
      letter-spacing: 1px;
      background: url(../image/co2_nav_bg5.png) no-repeat center center;
      background-size: 100% 100%;
      text-shadow: 0px 0px 4px #0046FF;
      &.active {
        background: url(../image/co2_nav_bg4.png) no-repeat center center;
        background-size: 100% 100%;
        text-shadow: 0px 0px 4px #D1B839;
      }
      &:last-child{
        margin-right: 0;
      }
    }

  }

  // 需求响应2
  &.demand_preview {
    z-index: 190;
    top: -50px;
    height: 100vh;
    .screen_content {
      height: calc(100% - 170px);
    }
    .project_load {
      .load_bg {
        width: 100%;
      }
      .load_content {
        position: relative;
      }
      .load_data {
        position: absolute;
        top: 32%;
        left: 5%;
        right: 5%;
        display: flex;
        .value {
          margin-right: 4px;
          font-size: 20px;
          font-family: pangmenzhendao;
          font-weight: bold;
          letter-spacing: 2px;
        }
        .title {
          font-size: 13px;
        }
        .load_left {
          text-align: right;
          flex: 1;
          .title {
            text-align: right;
          }
        }
        .load_center {
          width: 46%;
          text-align: center;
          .value {
            color: #fff;
          }
        }
        .load_right {
          flex: 1;
          .title {
            text-align: left;
          }
        }
      }
    }
    .peak_shaving_box {
      padding-top: 20px;
      padding-bottom: 20px;
      overflow: hidden;
      li {
        float: left;
        width: 23%;
        margin: 0 1%;
        text-align: center;
      }
      .peak_cont {
        margin-bottom: 10px;
        position: relative;
        img {
          max-width: 100%;
        }
        .peak_text {
          position: absolute;
          top: 20%;
          left: 0;
          right: 0;
          font-size: 14px;
        }
        b {
          font-size: 22px;
          font-weight: bold;
        }
      }
      .peak_tit {
        color: #D0DEEE;
        font-size: 14px;
      }
    }
    .peak_shaving_task {
      padding-bottom: 10px;
      .edit_strategy_btn {
        padding-top: 10px;
        padding-right: 20px;
        text-align: right;
        .edit__btn {
          padding: 5px 10px;
          font-size: 12px;
          color: #FCFEFF;
          border: 1px solid #0249B4;
          background: rgba(3,77,185,0.3);
          border-radius: 2px;
        }
      }
      .task_box {
        margin: 10px auto 15px;
        width: 90%;
        padding: 20px 20px 10px;
        background: url(~@/assets/image/demand/task_bg.png) no-repeat center center;
        background-size: 100% 100%;
      }
      .task_form {
        margin: auto;
        .el-icon-date {
          position: absolute;
          right: 0px;
        }
        .el-date-editor .el-range-input {
          background: transparent;
        }
        .task_input {
          width: 86%;
        }
        .el-icon-circle-plus {
          margin: 0 5px;
          color: #2294FE;
          font-size: 20px;
        }
        .el-icon-remove {
          color: #E45757;
          font-size: 20px;
        }
      }
      .demand_btns {
        text-align: center;
        .demad_btn {
          margin: 0 10px;
          display: inline-block;
          min-width: 100px;
          height: 32px;
          line-height: 32px;
          text-align: center;
          color: #D0DEEE;
          font-size: 14px;
          border: 1px solid #3B79C6;
          border-radius: 2px;
        }
        .demad_btn1 {
          background: linear-gradient(360deg, rgba(17, 108, 215, .2) 0%, #06101F 100%);
        }
        .demad_btn2 {
          background: linear-gradient(360deg, #116CD7 0%, #06101F 100%);
          box-shadow: inset 0px 0px 6px 0px #2294FE;
        }
      }
    }
    .peak_shaving_overview_wrap {
      margin-top: 15px;
      padding-left: 5px;
      padding-right: 5px;
    }
    .peak_shaving_overview {
      margin: 0 5px;
      padding: 10px;
      background: url(~@/assets/image/demand/overview_bg.png) no-repeat center center;
      background-size: 100% 100%;
      .value {
        margin-right: 4px;
        font-size: 22px;
        font-family: pangmenzhendao;
        font-weight: bold;
        letter-spacing: 2px;
      }
      .title {
        font-size: 13px;
      }
    }
  }
  &.demand_preview_edit {
    .edit_main {
      background: #06101F;
    }
  }

}

.energy_chart_content {
  padding-bottom: 20px;
  height: 100%;
  border: 1px solid;
  border-top: none;
  background: linear-gradient(0deg, rgba(10, 27, 44, 0.6) 0%, rgba(6, 16, 31, 0.85) 100%);
  border-image: linear-gradient(0deg, rgba(46, 98, 145, 1), rgba(46, 98, 145, 0)) 1 1;
  .data_total {
    padding: 5px 10px;
    max-width: 200px;
  }
}

.page_nav_slect_wrap {
  position: absolute;
  top: 40%;
  right: 0;
  z-index: 10;
  padding-left: 10px;
  background-color: rgba(6, 16, 31, 1);
  .el-input__inner {
    height: 24px;
    line-height: 24px;
    border-color: #134A7D !important;
  }
  .el-input__icon {
    line-height: 24px;
  }
  .el-select {
    margin-left: 10px;
    width: 110px;
  }
}

// 用能监测-电
.energy_elec_wrap {
  .statistics_row {
    padding-left: 8px;
  }
  .statistics_col {
    border-radius: 0;
    ul {
      padding: 10px 20px;
      height: 100%;
      flex-direction: column;
      justify-content: space-between;
    }
    li {
      padding: 5px 0;
    }
    .new_card_data {
      flex: 1;
      color: #7F97B1;
      .cont {
        padding-left: 10%;
      }
      .value {
        margin-right: 5px;
        font-size: 24px;
        font-weight: bold;
      }
    }
    .slot_box {
      .icon_text {
        margin-top: 5px;
      }
      img {
        width: 40px;
      }
    }
  }
  .statistics_col_1 ul {
    border: 1px solid rgba(34, 148, 254, 0.5);
    background: linear-gradient(180deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0) 0%, rgba(34,148,254,0.15) 100%);
  }
  .statistics_col_2 ul{
    border: 1px solid rgba(60, 204, 249, 0.5);
    background: linear-gradient(180deg, rgba(255,255,255,0) 0%, rgba(60,204,249,0) 0%, rgba(60,204,249,0.15) 100%);
    .value,
    .percentage {
      color: #3CCCF9;
    }
    .percentage {
      font-size: 16px;
      font-weight: bold;
    }
  }
  .statistics_col_3 ul{
    border: 1px solid rgba(252, 216, 105, 0.5);
    background: linear-gradient(180deg, rgba(252,216,105,0) 0%, rgba(252,216,105,0.15) 100%);
    .value {
      color: #FCD869;
    }
  }
  .col_main {
    height: 100%;
    flex-direction: column;
    .col_body {
      padding: 15px;
      margin-left: 8px;
      margin-top: 10px;
      border: 1px solid #2294FE;
      background: linear-gradient(180deg, rgba(34,148,254,0) 0%, rgba(34,148,254,0.15) 100%);
    }
    .col_body_border {
      border-image: linear-gradient(180deg, rgba(34, 148, 254, 0.3), rgba(34, 148, 254, 0.5)) 1 1;
      &.ydqs {
        position: relative;
        .ydqs_lt {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          height: 28px;
          .custom_date {
            display: flex;
            align-items: center;
            .date-type {
              width: 100px;
            }
            .date-range {
              width: 300px;
            }
          }
        }
        .data_view {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          font-size: 16px;
          text-decoration: underline;
          cursor: pointer;
          color: #D0DEEE;
          position: absolute;
          right: 20px;
          img {
            width: 20px;
          }
        }
      }
    }
    .energy_sub_nav {
      margin: 10px 0;
      padding: 2px 10px;
      max-width: 200px;
      font-size: 18px;
      font-family: pangmenzhendao;
      border: 1px solid;
      border-right: none;
      background: linear-gradient(90deg, rgba(0, 85, 213, 0.4) 0%, rgba(0,85,213,0) 100%);
      border-image: linear-gradient(90deg, rgba(0, 89, 158, 1), rgba(39, 86, 129, 0)) 1 1;
      border-left: 2px solid #2294FE;
    }
  }
  .trends_chart_box {
    position: relative;
    color: #D0DEEE;
    float: right;
    margin-right: 20px;
    overflow: auto;
    li {
      float: right;
      margin-left: 50px;
      font-size: 14px;
    }
    .underline {
      margin-right: 8px;
      text-decoration: underline;
    }
    img {
      margin-right: 4px;
      width: 14px;
    }
    b {
      color: #3CCCF9;
      font-weight: bold;
    }
  }
}
.EnergyTrends-data-view-dialog {
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .export {
      margin-right: 40px;
    }
  }
  .cont {
    padding-bottom: 20px;
  }
}

// 空调
.air_condition_wrap {
  .air_menu {
    height: calc(100vh - 215px);
    overflow-y: auto;
    li {
      cursor: pointer;
      padding: 10px 15px;
      margin-bottom: 15px;
      font-size: 14px;
      border: 1px solid #3B79C6;
      background: rgba(0,85,213,0.1);
      box-shadow: inset 0px -6px 20px 0px rgba(34,148,254,0.2);
      border-radius: 2px;
      &:hover {
        background: rgba(0,85,213,0.2);
      }
      &.active {
        background: rgba(0,85,213,0.1) linear-gradient(360deg, #116CD7 0%, #06101F 100%);
      }
      .icon {
        margin-right: 15px;
      }
    }
  }
  .air_bg {

  }

  .air_controller {
    position: relative;
    text-align: center;
    padding: 80px 10px 0;
    border: 1px solid;
    background: linear-gradient(180deg, rgba(34,148,254,0) 0%, rgba(34,148,254,0.17) 100%);
    border-image: linear-gradient(180deg, rgba(60, 204, 249, 1), rgba(60, 204, 249, 0)) 1 1;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      background: url('~@/assets/image/ningbo/bg_line.png') no-repeat center 40px;
      background-size: 150% auto;
    }
  }
  .air_controller_image {
    display: inline-block;
    margin: 70px 0;
    position: relative;
    z-index: 10;
    img {
      max-width: 100%;
    }
    .air_text {
      position: absolute;
      z-index: 12;
    }
    .air_text_1,
    .air_text_3,
    .air_text_4{
      font-size: 20px;
      color: #FCD869;
    }
    .air_text_1 {
      top: 38%;
      left: 6%;
    }
    .air_text_2,
    .air_text_5 {
      font-size: 15px;
      color: #0DD3A3;
      text-align: left;
      line-height: 22px;
      i {
        color: #fff;
        font-size: 14px;
      }
    }
    .air_text_2 {
      top: -18%;
      left: 12%;

    }
    .air_text_3 {
      top: 62%;
      left: 64%;
    }
    .air_text_4 {
      top: 50%;
      right: 10%;
    }
    .air_text_5 {
      top: 63%;
      left: 28%;
    }
  }
  .air_controller_content {
    position: relative;
    z-index: 10;
    margin: 20px 0 10px;
    padding: 1.5% 1%;
    border: 1px solid rgba(34,148,254,0.35);
    background: rgba(8,18,70,0.3);
    box-shadow: 8px -6px 5px 0px rgba(0,117,255,0.05);
    border-radius: 4px;
    .air_system_controller {
      display: inline-block;
      text-align: left;
      font-size: 14px;
      li {
        margin-right: 2%;
      }
    }
    .air_system_title {
      padding-left: 5px;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: 400;
      line-height: 18px;
      border-left: 4px solid #2294FE;
    }
    .air_system_bg {
      border: 1px solid rgba(39,86,129,0.23);
      background: #09153B;
      border-radius: 4px;
    }

    .mode_control {
      padding: 12px;
      border-bottom: 1px solid #2B4053;
    }
    .air_form_item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      .flex-1 {
        white-space: nowrap;
      }
      .unit {
        width: 12px;
      }
    }
    .air_radio {
      .el-radio {
        margin-top: 12px;
        display: block;
      }
    }
    .air_input {
      margin-right: 4px;
      width: 64px;
      .el-input__inner {
        height: 22px;
        line-height: 22px;
        border-color: #09153B;
        background: rgba(34,148,254,0.25);
        border-radius: 2px;
      }
    }
    .air_switch {
      .air_switch_icon {
        margin-right: 5px;
      }
    }
    .air_switch_new {
      /* 调整按钮的宽度 */
      .el-switch__core,
      .el-switch__label {
        width: 50px !important;
        margin: 0;
      }
      .el-switch__label {
        position: absolute;
        display: none;
        color: #fff !important;
        &.is-active {
          display: block;
        }
      }
      .el-switch__label--right {
        z-index: 1;
        span {
          margin-left: 5px;
          font-size: 12px;
        }
      }
      .el-switch__label--left {
        z-index: 1;
        span{
          margin-left: 20px;
          font-size: 12px;
        }
      }
    }


  }

}

// 水冷机
.chiller_wrap {
  .title {
    margin-bottom: 0;
  }
  .air_controller {
    padding-top: 0;
    padding-bottom: 8%;
  }
  .chiller_controller {
    font-size: 14px;
    .device_name {
      position: absolute;
    }
    .name_1 {
      top: 18%;
      left: 0.7%;
    }
    .name_2 {
      top: 42.5%;
      left: 13%;
    }
    .name_3 {
      bottom: 14%;
      left: 13%;
    }
    .name_4 {
      top: 24.5%;
      left: 35.5%;
    }
    .name_5 {
      top: 50%;
      left: 35.5%;
    }
    .name_6 {
      bottom: 20%;
      left: 35.5%;
    }
    .name_7 {
      bottom: 18%;
      left: 53%;
    }
    .name_8 {
      top: 50%;
      right: 30%;
    }
    .name_9 {
      bottom: 19%;
      right: 30%;
    }
    .name_10 {
      bottom: -22px;
      right: 30%;
    }
    .name_11 {
      bottom: -22px;
      right: 5.5%;
    }
    .value {
      position: absolute;
      display: block;
      .val_p {
        color: #D78737;
        .icon {
          border-color: #5C4733;
          background-color: #D78737;
        }
      }
      .val_t {
        padding-left: 5px;
        color: #2294FE;
        .icon {
          border-color: #31597E;
          background-color: #2294FE;
        }
      }
      .icon {
        display: inline-block;
        width: 22px;
        height: 22px;
        color: #fff;
        border: 3px solid;
        font-weight: bold;
        text-align: center;
        border-radius: 50%;
      }
    }
    .value_1 {
      top: 28%;
      left: 5%;
      min-width: 60px;
      text-align: right;
    }
    .value_2 {
      bottom: 33.5%;
      left: 5%;
      min-width: 60px;
      text-align: right;
    }
    .value_3 {
      bottom: 32%;
      left: 19%;
      min-width: 130px;
    }
    .value_4 {
      bottom: 23.5%;
      left: 19%;
      min-width: 130px;
      color: #FCD869;
    }
    .value_5 {
      bottom: 45%;
      left: 49%;
      min-width: 60px;
      font-size: 20px;
      b {
        padding-right: 2px;
        color: #3CCCF9;
        font-weight: bold;
      }
    }
    .value_6 {
      top: 18%;
      right: 12%;
      min-width: 130px;
    }
    .value_7 {
      top: 28%;
      right: 12%;
      min-width: 130px;
      color: #FCD869;
    }
    .value_8 {
      bottom: 34%;
      right: 14.5%;
      min-width: 130px;
    }
    .value_9 {
      bottom: 25%;
      right: 14.5%;
      min-width: 130px;
      color: #FCD869;
    }

  }
  .chiller_head {
    position: absolute;
    top: 15px;
    right: 30px;
    z-index: 50;
    padding-top: 10px;
    font-size: 12px;
    width: 360px;
    border: 1px solid rgba(60, 204, 249, 0.3);
    background-color: rgba(60, 204, 249, 0.15);
    border-radius: 4px;
    .chiller_head_item {
      justify-content: space-between;
      b {
        color: #3CCCF9;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }
  .chiller_footer {
    position: absolute;
    left: 15px;
    bottom: 15px;
    z-index: 50;
    padding: 10px 10px 0;
    width: 400px;
    border: 1px solid rgba(60, 204, 249, 0.3);
    background-color: rgba(60, 204, 249, 0.15);
    border-radius: 4px;
    li {
      padding-top: 10px;
      border-bottom: 1px solid rgba(255,255,255,0.19);
      &:last-child {
        border-bottom: none;
      }
    }
    .title {
      min-width: 50px;
    }
    .cont {
      flex: 1;
      padding-left: 20px;
      font-size: 14px;
      text-align: left;
      .label {
        display: inline-block;
        width: 55%;
        color: rgba(255,255,255,0.64);
      }
      b {
        color: #3CCCF9;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }
  // 群控菜单
  .group_control_btn {
    position: absolute;
    bottom: 20px;
    right: 30px;
    z-index: 50;
    padding: 6px 20px;
    margin-bottom: 15px;
    font-size: 14px;
    border: 1px solid #3B79C6;
    background: rgba(0,85,213,0.05);
    box-shadow: inset 0px -4px 6px 0px rgba(34,148,254,0.3);
    border-radius: 4px;
    &.active {
      background: linear-gradient(180deg, rgba(34,148,254,0) 0%, rgba(34,148,254,0.3) 100%);
    }
  }
  .group_control_menu {
    position: absolute;
    bottom: 80px;
    right: 30px;
    z-index: 50;
    width: 450px;
    padding: 15px;
    background: #050D28;
    border: 1px solid #2294FE;
    text-align: left;
    box-shadow: 0px 0px 16px 0px #00427E;
    border-radius: 4px;
    .group_header {
      align-items: center;
      .title {
        padding-left: 10px;
        font-size: 16px;
        border-left: 4px solid #2294FE;
      }
    }
    .group_header_btns {
      a {
        display: inline-block;
        margin-left: 15px;
        min-width: 72px;
        height: 28px;
        line-height: 28px;
        font-size: 14px;
        color: #2294FE;
        text-align: center;
        border: 1px solid #2294FE;
        border-radius: 4px;
        &.run_status {
          &.stop {
            color: #CA334E;
            border-color: #CA334E;
          }
          &.running {
            color: #0DD3A3;
            border-color: #0DD3A3;
          }
          img {
            vertical-align: -2px;
          }
        }
      }
    }
    .time_control {
      margin: 15px 0;
      border: 1px solid rgba(39,86,129,0.23);
      background: #09153B;
      border-radius: 4px;
      ul {
        border-top: 1px solid #2194FF;
        padding: 8px 10px;
      }
      li {
        padding: 8px 0;
        font-size: 14px;
      }
      .cont {
        text-align: right;
        i {
          color: #AFAFAF;
          font-size: 12px;
          padding-right: 15px;
        }
      }
    }
    .group_setting {
      padding: 10px 10px 0;
      border: 1px solid #275681;
      background: #0A192F;
      border-radius: 4px;
    }
    .group_setting_item {
      padding: 10px 15px;
      border: 1px solid #275681;
      background: #0A192F;
      border-radius: 4px;
      .group_setting_title {
        font-size: 14px;
        color: #DEDEDE;
      }
      .el-slider__runway {
        background-color: #3E4248;
      }
      .el-slider__bar {
        background-color: #2294FE;
      }
      .el-slider__button {
        height: 12px;
        width: 20px;
        border-radius: 10px;
        border-color: #ffffff;
        background-color: #2294FE;
      }
      .value_content {
        .current_val {
          font-size: 16px;
          color: #2294FE;
        }
        b {
          font-weight: bold;
        }
        .total_val {
          color: #DEDEDE;
          font-size: 14px;
        }
      }
    }
  }

  .energy_saving_control {
    margin-top: 10px;
    border: 1px solid;
    border-image: linear-gradient(180deg, rgba(60, 204, 249, 0.5), rgba(60, 204, 249, 0)) 1 1;
    background: linear-gradient(180deg, rgba(34,148,254,0) 0%, rgba(34,148,254,0.15) 100%);
    .energy_saving_list {
      padding: 15px 0 10px;
    }
    .el-col {
      position: relative;
      text-align: center;
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 0;
        width: 1px;
        height: 24px;
        margin-top: -8px;
        background: rgba(34,148,254,0.4);
      }
      &:last-child::after {
        display: none;
      }
    }
    .energy_saving_item {
      display: inline-block;
      text-align: left;
      .icon_box {
        margin-right: 6px;
      }
      .value {
        font-size: 24px;
        font-weight: bold;
      }
    }
  }
  .chiller_right {
    flex-direction: column;
    height: 100%;
  }
  .chiller_chart {
    border: 1px solid #3CCCF9;
    background: linear-gradient(180deg, rgba(34,148,254,0) 0%, rgba(34,148,254,0.17) 100%);
  }
  .effect_trends_wrap {
    flex: 1;
  }
  .effect_box {
    align-items: center;
    padding: 15px;
    .effect_chart_box {
      position: relative;
      width: 46%;
      margin-right: 25px;
      img {
        width: 100%;
      }
      .effect_chart {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
      }
    }
    .effect_time {
      margin-bottom: 6px;
      text-align: right;
      font-size: 14px;
      font-family: pangmenzhendao;
      letter-spacing: 2px;
    }
    .effect_content {
      padding: 10px 12px;
      background: url('~@/assets/image/ningbo/chiller_bg_1.png') no-repeat center center;
      background-size: 100% 100%;
      .effect_item {
        padding: 6px 0;
        align-items: center;
        font-size: 13px;
      }
      .effect_value {
        position: relative;
        font-family: pangmenzhendao;
        text-align: right;
        width: 50%;
        &:before {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          width: 1px;
          height: 8px;
          margin-top: -4px;
          background: rgba(127,151,177,0.2);
        }
        span {
          color: #3CCCF9;
          font-size: 14px;
          letter-spacing: 1px;
        }
      }
    }
  }
  .effect_trends {
    padding: 15px;
    .time {
      text-align: right;
      .el-date-editor {
        width: 120px;
      }
    }
    .effect_trends_stat {
      padding: 10px 0;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      span {
        font-size: 16px;
        font-weight: bold;
        color: #3CCCF9;
      }
    }
  }
  .water_flow_list {
    padding: 20px 0;
    text-align: center;
    .el-col {
      position: relative;
      text-align: center;
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        right: 0;
        width: 1px;
        height: 24px;
        margin-top: -8px;
        background: rgba(34,148,254,0.4);
      }
      &:last-child::after {
        display: none;
      }
    }
    .water_flow_item {
      display: inline-block;
    }
    .img_box {
      position: relative;
    }
    .water_flow_value {
      position: absolute;
      top: 50%;
      left: 50%;
      text-align: left;
      transform: translate3d(-50%, -50%, 0);
      b {
        display: block;
        font-weight: bold;
      }
      i {
        font-size: 12px;
      }
    }
    .water_flow_title {
      margin-top: 10px;
      font-size: 14px;
    }
    .supply_water {
      color: #2294FE;
    }
    .return_water {
      color: #D78737;
    }
    .flow_rate {
      color: #0DD3A3;
    }
  }
}
.compressor_wrap {
  .title {
    margin-bottom: 0;
  }
  .nav_select {
    width: 80px;
    .el-input__inner {
      padding-left: 10px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      border-color: #134A7D !important;
      background: #06101F;
    }
    .el-input__icon {
      line-height: 24px;
    }
  }
  // 绿色低碳
  .green_low_carbon {
    margin-left: 8px;
    padding: 40px 15px 5px;
    border: 1px solid;
    background: linear-gradient(180deg, rgba(60,204,249,0) 0%, rgba(60,204,249,0.1) 100%);
    border-image: linear-gradient(180deg, rgba(60, 204, 249, 0), rgba(60, 204, 249, 0.3)) 1 1;
    .green_item {
      padding: 15px 10px;
      margin-bottom: 10px;
      text-align: left;
      background: url('~@/assets/image/ningbo/compressor_bg_1.png') no-repeat center center;
      background-size: 100% 100%;
      .cont > div {
        display: inline-block;
        border: 1px solid;
        border-top: none;
        padding: 3px 8px 1px;
        font-family: pangmenzhendao;
        letter-spacing: 2px;
      }
      .value,
      .title {
        font-size: 14px;
      }
      .title {
        margin-top: 10px;
      }
    }
    .green_item_1 {
      .cont > div {
        background: linear-gradient(180deg, rgba(13,211,163,0) 0%, rgba(13,211,163,0.6) 100%);
        border-image: linear-gradient(180deg, rgba(80, 204, 167, 0), rgba(80, 204, 167, 1)) 1 1;
      }
      .value {
        color: #60F1AB;
      }
    }
    .green_item_2 {
      .cont > div {
        background: linear-gradient(180deg, rgba(34,148,254,0) 0%, rgba(34,148,254,0.2) 100%);
        border-image: linear-gradient(180deg, rgba(34, 148, 254, 0), rgba(34, 148, 254, 1)) 1 1;
      }
      .value {
        color: #2294FE;
      }
    }
    .green_item_3 {
      .cont > div {
        background: linear-gradient(180deg, rgba(60,204,249,0) 0%, rgba(60,204,249,0.3) 100%);
        border-image: linear-gradient(180deg, rgba(60, 204, 249, 0), rgba(60, 204, 249, 1)) 1 1;
      }
      .value {
        color: #3CCCF9;
      }
    }
    .green_item_4 {
      .cont > div {
        background: linear-gradient(180deg, rgba(252,216,105,0) 0%, rgba(252,216,105,0.3) 100%);
        border-image: linear-gradient(180deg, rgba(252, 216, 105, 0), rgba(252, 216, 105, 1)) 1 1;
      }
      .value {
        color: #FCD869;
      }
    }
  }
  // 运营情况
  .operations_list {
    margin-left: 8px;
    margin-top: 4%;
    .operations_item {
      padding: 11% 0;
      text-align: center;
      background: url('~@/assets/image/ningbo/compressor_bg_2.png') no-repeat center center;
      background-size: 100% 100%;
      .value_unit {
        padding: 10px;
        font-family: pangmenzhendao;
        letter-spacing: 2px;
        .value {
          font-size: 20px;
          color: #0DD3A3;
          font-weight: 500;
        }
        .unit {
          padding-left: 2px;
          font-size: 12px;
        }
      }
      .title {
        font-size: 14px;
      }
    }
  }
  // 空压站
  .compressor_stat {
    margin-top: 10px;
    margin-bottom: 10px;
    margin-left: 8px;
    padding-top: 10px;
    text-align: center;
    border: 1px solid rgba(60,204,249,0.5);
    background: linear-gradient(180deg, rgba(60,204,249,0) 0%, rgba(60,204,249,0.15) 100%);
  }
  .compressor_stat_item {
    display: inline-block;
    .value {
      position: relative;
      z-index: 10;
      margin-bottom: -18px;
      font-size: 14px;
      font-family: pangmenzhendao;
      span {
        font-size: 20px;
        font-weight: bold;
        letter-spacing: 3px;
        color: #3CCCF9;
      }
    }
    .title {
      margin-top: 5px;
      color: #DEDEDE;
      font-size: 14px;
    }
  }
  .compressor_device {
    position: relative;
    margin-left: 8px;
    padding: 15px 4%;
    border: 1px solid;
    text-align: right;
    background: linear-gradient(180deg, rgba(34,148,254,0) 0%, rgba(34,148,254,0.17) 100%);
    border-image: linear-gradient(180deg, rgba(60, 204, 249, 1), rgba(60, 204, 249, 0)) 1 1;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      background: url('~@/assets/image/ningbo/bg_line.png') no-repeat center 10px;
      background-size: 190% auto;
    }
  }
  .compressor_device_cont {
    display: inline-block;
    position: relative;
    text-align: left;
    img {
      max-width: 100%;
    }
    .compressor_name_box {
      position: absolute;
      left: 4%;
      z-index: 2;
      display: flex;
      width: 25%;
      .run_dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        background: rgba(255, 255, 255, 0.6);
        border-radius: 50%;
      }
      .run_label {
        padding: 0 10px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.6);
      }
      .com_title {
        flex: 1;
        font-size: 14px;
      }
      .status_running {
        .run_dot {
          background-color: #0DD3A3;
        }
        .run_label {
          color: #ffffff;
        }
      }
    }
    .pos_1 {
      top: 11%;
    }
    .pos_2 {
      top: 27%;
    }
    .pos_3 {
      top: 43%;
    }
    .pos_4 {
      top: 59%;
    }
    .pos_5 {
      top: 75%;
    }
    .pos_6 {
      top: 92%;
    }
    .pos_7 {
      position: absolute;
      right: 0;
      bottom: 0.4%;
      z-index: 2;
      font-size: 12px;
      color: #DEDEDE;
    }

    .device_info {
      position: absolute;
      z-index: 2;
      padding: 0  5px 0 8px;
      font-size: 12px;
      overflow-y: auto;
      background: rgba(60,204,249,0.1);
      border: 1px solid  rgba(60, 204, 249, 0.1);
      li {
        padding: 3px 0;
      }
      .info_num {
        font-size: 14px;
      }
    }
    .flow_info {
      width: 80px;
      height: 92px;
      right: 103%;
      .flow_num {
        color: #FCD869;
        font-weight: bold;
      }
      .power_num {
        color: #2294FE;
      }
    }
    .flow_pos_1 {
      top: 0;
    }
    .flow_pos_2 {
      top: 16.5%;
    }
    .flow_pos_3 {
      top: 33%;
    }
    .flow_pos_4 {
      top: 49.5%;
    }
    .flow_pos_5 {
      top: 66%;
    }
    .flow_pos_6 {
      top: 82.5%;
    }

    .dryer_info {
      width: 74px;
      height: 84px;
      right: 27%;
      li {
        padding: 2px 0;
      }
      .info_num {
        color: #3CCCF9;
        font-weight: bold;
      }
    }
    .dryer_pos_1 {
      top: 8%;
    }
    .dryer_pos_2 {
      top: 24%;
    }
    .dryer_pos_3 {
      top: 40%;
    }
    .dryer_pos_4 {
      top: 56%;
    }

    .air_receiver {
      padding-top: 4px;
      width: 140px;
      height: 106px;
      right: 0;
      li {
        padding: 4px 0;
      }
      .info_num {
        padding-left: 4px;
        color: #3CCCF9;
        font-weight: bold;
      }
    }
    .air_receiver_pos_1 {
      top: 7%;
    }
    .air_receiver_pos_2 {
      top: 50%;
    }
    .power_info {
      position: absolute;
      width: 74px;
      right: 27%;
      .info_tit {
        font-size: 12px;
        color: #DEDEDE;
      }
      .info_val {
        padding-left: 10px;
        font-size: 14px;
        color: #3CCCF9;
        font-weight: bold;
      }
    }
    .power_info_pos_1 {
      top: 72%;
    }
    .power_info_pos_2 {
      top: 88.2%;
    }
  }

  .compressor_right {
    .compressor_right_stat {
      padding-top: 10px;
      margin-left: 8px;
      border: 1px solid;
      text-align: center;
      background: linear-gradient(180deg, rgba(34,148,254,0) 0%, rgba(34,148,254,0.1) 100%);
      border-image: linear-gradient(180deg, rgba(34, 148, 254, 0.5), rgba(34, 148, 254, 1)) 1 1;
      .right_stat_item {
        display: inline-block;
        text-align: left;
      }
      .unit,
      .title {
        color: #DEDEDE;
        font-size: 14px;
      }
    }
    .right_chart_box {
      margin-left: 8px;
      ul {
        margin-top: 10px;
        align-items: center;
      }
      .chart_stat {
        font-size: 14px;
        color: #DEDEDE;
        span {
          display: inline-block;
          padding-right: 15px;
        }
        b {
          padding: 0 2px;
          color: #0DD3A3;
          font-weight: bold;
        }
      }
    }
    .chart_select {
      text-align: right;
    }
    .event_window {
      margin-left: 8px;
      border: 1px solid;
      background: linear-gradient(180deg, rgba(60,204,249,0) 0%, rgba(60,204,249,0.1) 100%);
      border-image: linear-gradient(180deg, rgba(60, 204, 249, 0.5), rgba(60, 204, 249, 1)) 1 1;
    }
    .event_header {
      position: relative;
      padding: 10px;
      align-items: center;
      &::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 90%;
        height: 1px;
        background: linear-gradient(346deg, rgba(60,204,249,0) 0%, #3CCCF9 100%);
      }
    }
    .event_list {
      padding: 10px 10px 0;
      color: #DEDEDE;
      max-height: 160px;
      overflow-y: auto;
      li {
        display: flex;
        align-items: center;
        padding-bottom: 10px;
        span {
          padding-right: 15px;
          flex: 1;
          font-size: 14px;
        }
        i {
          font-size: 12px;
        }
      }
    }
  }
}



.leftBlock-enter-active, .leftBlock-leave-active {
  transition: all 0.5s;
}
.leftBlock-enter, .leftBlock-leave-to {
  position: absolute;
  transform: translateX(-50%);
  opacity: 0;
}

// 路由loading效果
.route-loading-style {
  border-radius: 8px;
}

//电子巡更公共样式
.electronic-patrol-container {
  .card-bg {
    background: linear-gradient( 360deg, #0A1B2C 0%, #06101F 100%);
    border-radius: 0px 0px 0px 0px;
    border: 1px solid;
    border-image: linear-gradient(360deg, rgba(46, 98, 145, 0.61), rgba(46, 98, 145, 0.69), rgb(6 13 27)) 1 1;
    padding: 12px;
  }
  .mt16 {
    margin-top: 16px;
  }
  .pagination {
    text-align: right;
  }
  .img {
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  }
  .iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
  .el-select {
    width: 100%;
  }
  .head {
      display: flex;
      justify-content: space-between;
      height: 32px;
      .title {
        font-family: PangMenZhengDao, PangMenZhengDao;
        font-weight: 400;
        font-size: 16px;
        height: 28px;
        line-height: 28px;
      }
      .radio-group {
        max-width: calc(100% - 100px);
        overflow-x: auto;
        &::-webkit-scrollbar { /*滚动条整体样式*/
          height: 3px !important;
          cursor: pointer !important;
        }
        .el-radio-group {
          white-space: nowrap;
        }
      }
  }

  .patrol_line {
      .list {
        padding: 0 12px;
        height: 600px;
          .item {
              background: rgba(34,148,254,0.22);
              border-radius: 6px 6px 6px 6px;
              padding: 8px;
              text-align: center;
              cursor: pointer;
              &.active {
                  background: linear-gradient( 360deg, #116CD7 0%, #0B3971 50%, #06101F 100%), rgba(255,255,255,0);
                  box-shadow: inset 0px 0px 32px 0px #2294FE;
              }
          }
      }
  }
}

//BIM卡片中tabs组件样式
.page_sum {
  min-height: 100%;
  padding: 16px;
  .page_sum_tabs {
    display: flex;
    border-bottom: 2px solid #0e295085;
    &>div {
      background: url('/image/screen/airConditioning_tabs.png') no-repeat center;
      background-size: 100% 100%;
      margin-right: 16px;
      height: 32px;
      line-height: 32px;
      padding: 0 16px;
      cursor: pointer;
      &.active {
        background: url('/image/screen/airConditioning_tabs_active.png') no-repeat center;
        background-size: 100% 100%;
      }
    }
  }
}


//全屏轮播蒙层
.carousel-mask {
  .el-dialog {
    border: none;
    background: none;
    box-shadow: none;
    margin: 0;
    &::after,&::before {
      display: none;
    }
    img {
      width: 100%;
      height: 100%;
    }
  }
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0 !important;
  }
}

//AI 智能问答
.ai-qa {
  position: fixed;
  right: 30px;
  bottom: 20px;
  z-index: 9999;
  cursor: pointer;
  height: fit-content;
  width: fit-content;
  img {
    width: 50px;
  }
}
.ai-question-answering {
  .iframe {
    width: 100%;
    height: 74vh;
  }
}


//tooltip自定义公共样式
.custom_tooltip{
  color: rgba(255,255,255,0.8) !important;
  background-color: #14253A !important;
  border: 1px solid #275681 !important;
  &.el-tooltip__popper[x-placement^=bottom] .popper__arrow {
    border-bottom-color: #275681 !important;
    &::after {
      border-bottom-color: #275681 !important;
    }
  }
  &.el-tooltip__popper[x-placement^=top] .popper__arrow {
    border-top-color: #275681 !important;
    &::after {
      border-top-color: #275681 !important;
    }
  }
}
