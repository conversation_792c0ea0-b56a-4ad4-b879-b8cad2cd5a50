'use strict';

/**
提供 合肥电信大楼 空调 modbus485 解决方案
  注: 1. 下发成功后，不修改读点
      2. 下发指令帧序号只能用固定值 000102

  服务启动:  node src/tcpCollectorModbusTcpBaseWrite.js [collecotInd] db debug
  发送采集指令给 http://lanxint.tech:8003/collect 获取返回结果
  
  通过传入 collecotInd 去获取 collectorConfig2 [collecotInd], 拿到采取器需要的所有配置
  注: 
    collectorId 和 a_collector.id 需要匹配
    a_item.description 为对应设备的采集器SN
  eg:
    {
        // ly_yy_dl 余姚电力局 485 4G透传 空调，modbus 采集方式
        ind: 23,
        collectorId: 302,
        requestUrl: "http://bms.lanxing.tech:8003/collect",
        "pointTable": {
            "type": "modbus",
            "name": "485空调",
            "fileName": "",
            "module": "modbus_airCondition",
            "dName": "modbus",
            //"address": "202101112033", // 非必填，对应 a_item.code
            "commands": []
        }
    }
*/

const schedule = require('node-schedule');
var request = require('request');
var path = require("path");
var moment = require('moment');

const Db = require("./mydb");
const helper = require("./helper");
const decUtil = require('./lib/_decUtil');
const crc16 = require('./lib/_crc16');

const CollectorConf = require("./conf/collectorConfig2").collectorConfig();
const sysConfig = require('./conf/sysConfig').sysConfig();

const collecotInd = typeof process.argv[2] != "undefined" ? process.argv[2] : -1 ;
const dbName = process.argv[3] && process.argv[3] != "debug" ? process.argv[3] : null;
global.isDebug = process.argv[4] == "debug" ? true : false;

const conf = CollectorConf[collecotInd];

let timeout = conf && conf.timeout || 5*1000;

// 参数替换 覆盖之前的配置
if(dbName) {
  sysConfig.mysql.database = dbName;
}
var _db = new Db(sysConfig.mysql);
// 双数据库同步
if(typeof sysConfig.mysql2 != "undefined") {
    _db.createServer2(sysConfig.mysql2);
}

// 支持的指令类型，可以扩充
const FunctionCode = {
   "1" : "readCoils",      // 读取开关合/分闸状态
   "2" : "readDiscreteInputs",// 读取是否能被远程控制
   "3" : "readHoldingRegisters",   // 读从机实时数据
   "4" : "readInputRegisters",     // 读从机参数
   "5" : "writeCoil",
   "01" : "readCoils",      // 读取开关合/分闸状态
   "02" : "readDiscreteInputs",// 读取是否能被远程控制
   "03" : "readHoldingRegisters",   // 读从机实时数据
   "04" : "readInputRegisters",     // 读从机参数
   "05" : "writeCoil",
   "15" : "writeRegister",          // 写线圈
   "16" : "writeRegisters",         // 写从机参数
};

let dataParsers = {};

var errorMaxNum = 10; // 最大错误次数后, 不记录日志
var errorCurNum = 0;  // 相同错误次数
var errorMsg = null;  // 当前错误

// 缓存设备数据
var dataPoints = [];
var itemDataMap = {};
// 缓存设备列表
var itemCodes = [];
// 缓存设备报警数据
var itemWarningMap = {};
var curInd = 0;

// 全局变量
var gtmp = {};
// 缓存报过的警, 如果一直未变化, 则不会重复报
var cacheWarning = {};
// 缓存设备最近几次数据
var cacheData = {};

var sql = `
    select
        p.*,
        it.item_id,
        it.val,
        it.has_sync,
        it.locked,
        i.code,
        c.note as collector_code
    from a_point_table p
    LEFT JOIN a_item_data it on it.id=p.item_data_id
    LEFT join a_item i on it.item_id = i.id
    LEFT join a_collector c on c.id = p.collector_id
    where p.collector_id = ? and p.type = ?
    and it.has_sync = ?
    and it.item_id is not null
    order by p.addr
`;

async function syncDbWriteToModbus() {
    if(gtmp.syncDbWriteToModbus) {
        // 任务未执行完成, 等待下次轮训
        return false;
    }
    gtmp.syncDbWriteToModbus = true;
    try {
        var points = await _db.doSql(sql, [conf.collectorId, "w", "N"]);
        helper.debug("syncDbWriteToModbus", points.length);
        if(points.length > 0) {
            for(let i=0; i<points.length; i++) {
                try {
                    let d = points[i];
                    let sres = await syncToModbusData(d);
                    //let funcList = JSON.parse(d.func_list);
                    //var val = decUtil.batch(d.val, funcList);
                    //helper.info(val, d);
                    // let sres = await syncToModbusData(d, val);
                    if((typeof sres.addr != "undefined" && sres.addr == d.addr)
                        && (typeof sres.deviceId != "undefined" && sres.deviceId == d.device_id)) {
                        // 统一数据入库
                        dealResult({
                          id: d.item_data_id,
                          val: d.val,
                          updatedAt: moment().format('YYYY-MM-DD HH:mm:ss'),
                        });
                        // var msg = {id: d.item_data_id, val: d.val};
                        // var msg2 = {id: (d.item_data_id - 1) , val: d.val};
                        // helper.info("[update db]", msg, d.locked);
                        // // 如果是锁定状态， 不更新同步状态，继续轮询
                        // if(d.locked > 0) {
                        //     await _db.updateDataWithoutHasSync(msg);
                        //     //await _db.updateData(msg2);
                        // } else {
                        //     await _db.updateData(msg);
                        //     //await _db.updateData(msg2);
                        // }
                    } else {
                        helper.debug("sres", sres);
                    }
                } catch(e) {
                    helper.info(e.message);
                }
            }
        }
    } catch(e) {
        console.trace(e);
        helper.info("syncDbWriteToModbus error", e.message);
        gtmp.syncDbWriteToModbus = false;
        // 尝试重启解决
        process.exit(1);
    }
    gtmp.syncDbWriteToModbus = false;
}

// 更新数据库操作
async function dealResult(msg) {
  helper.debug("[sync to db]", msg.id, msg.val);
  // 统一数据入库
  await helper.syncItemData2Db(msg);
}

async function syncToModbusData(curPoint) {
    let cmd = _genWriteBuffer(curPoint);
    helper.debug("syncToModbusData cmd:", cmd.toString("hex"));
    let res = await callApi(curPoint.collector_code, cmd.toString("hex"));
    helper.debug("ajaxData", res);
    let msg = _parsePoint(res);

    helper.remoteLog(conf.name+"下发指令", JSON.stringify({
        db: dbName,
        collectorId: conf.collectorId,
        sn: curPoint.code,
        cmd: cmd.toString("hex"),
        res: res,
        msg: msg,
    }));
    return msg;

    // helper.debug("ajaxData", res);
    // let resObj = {};
    // if(res) {
    //   try {
    //     resObj = devicePaser.parsePoint(Buffer.from(res,'hex'), i);
    //   } catch(e) {
    //     helper.log("devicePaser.parsePoint error", devicePaser.sn, i, devicePaser.cmds[i].toString("hex"), res);
    //   }
    //   msg = {
    //     ...msg,
    //     ...resObj,
    //   }
    // }
    // helper.debug("collectDeviceData", devicePaser.sn, "msg", msg);
    // // 更新数据点位
    // //await dealResult(msg);

    // return res;
}

// 生成读指令
function _genWriteBuffer(curPoint) {
    helper.debug("_genWriteBuffer curPoint", curPoint);
    if(curPoint) {
        let funcList = JSON.parse(curPoint.func_list);
        helper.debug("_genWriteBuffer", curPoint, curPoint.val, funcList);
        var val = decUtil.batch(curPoint.val, funcList);
        var bufferLength = 9;
        var buffer = Buffer.alloc(bufferLength);
        buffer.writeUInt8(curPoint.device_id, 0);
        buffer.writeUInt8(curPoint.func, 1);
        buffer.writeUInt16BE(curPoint.addr, 2);
        buffer.writeUInt16BE(1, 4);  // 寄存器数量, 写入1个寄存器
        buffer.writeUInt8(2, 6);  // 字节数, 数据区总字节数（2字节）
        // buffer.writeUInt16BE(258, 5);  // 固定帧号  00 01 02 (其他不支持)
        buffer.writeUInt16BE(val, 7);
        var crcValue = crc16(buffer);
        var crcBuffer = Buffer.alloc(2);
        crcBuffer.writeUInt16LE(crcValue, 0);
        let code = Buffer.concat([buffer, crcBuffer]);
        helper.debug("buffer指令地址:", curPoint.device_id, "code:", JSON.stringify(code));
        return code;
    } else {
        return "";
    }
}

// 解析数据
function _parsePoint(bufferHex) {
    let buffer = Buffer.from(bufferHex, 'hex');
    if(buffer.length > 4) {
        let addr = parseInt(buffer.slice(0, 1).toString('hex'), 16);  // 采集地址
        let funcCode = parseInt(buffer.slice(1, 2).toString('hex'), 16);  // 功能码
        let address = parseInt(buffer.slice(2, 4).toString('hex'), 16);  // 功能码
        return {
            addr: address,
            deviceId: addr,
            func: funcCode,
        }
    } else {
        return {}
    }
}

function callApi(sn, cmd){
  return new Promise(function(resolve,reject){
    let query = {
      url: conf.requestUrl,
      method: "get",
      headers: {
        "content-type": "application/json; charset=utf-8",
      },
      qs: {
        sn: sn,
        cmd: cmd,
      },
      timeout: timeout,
    };
    request(query, function(error, response, body) {
      if(error) {
          //reject(error);
      }
      // 成功后执行
      resolve(body);
    });
  });
}

async function start() {
    // 每1秒一次
    schedule.scheduleJob('*/1 * * * * *',()=>{
        syncDbWriteToModbus();
        //helper.log('syncLightDataToBeacool success');
    });
    // 1小时重启一次
    schedule.scheduleJob('1 1 * * * *',()=>{
        helper.log('Auto restart server');
        process.exit(1);
    });

    // 数据服务初始化
    helper.initDataServer(sysConfig);
    helper.info("initDataServer success");

    // 日志服务连接至远程logstash
    helper.connectLogstash(sysConfig);
    helper.info("connectLogstash success");

    // mqtt连接，系统互通各种消息通道
    helper.connectMqtt(dbName);
    helper.info("connectMqtt success");

    await syncDbWriteToModbus();
    helper.info("syncDbWriteToModbus success");
    helper.info("start success");
};
function restartServer() {
    process.exit(1);
}

if(collecotInd >= 0) {
    start();
}

module.exports.dealResult = dealResult;
