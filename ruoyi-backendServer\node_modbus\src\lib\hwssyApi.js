/*
   仁济医院，替换 mqtt采集
   平台 https://facility-console.cn-north-4.huaweicloud.com/iotfm/console/static/#/docCenter
*/

const request = require("request");
const querystring = require("querystring");

const helper = require("../helper");

function RjApi(opts) {
    var that = this;

    this.opts = {
        fakeData: false, // 是否模拟数据返回
        timeout: 30000,
        accessToken: null,
        ...opts,
    };

    // 服务器返回错误码描述
    this.ajaxStatusCodes = {
        0: "success",
    };

    // 从 localstorage 恢复数据
    this._init_ = function () {};
}

RjApi.prototype._ajaxData = async function (param) {
    var that = this;
    return new Promise(function (resolve, reject) {
        that.ajaxData(
            param,
            function (data) {
                resolve(data);
            },
            function (err) {
                reject(err);
            }
        );
    });
};

RjApi.prototype.removePropertyOfNull = function (obj) {
    Object.keys(obj).forEach(item => {
        if (obj[item] == null) {
            delete obj[item];
        }
    });
    return obj;
};

RjApi.prototype.ajaxData = function (param, successFunc, errorFunc) {
    let that = this;
    // 所有请求逻辑
    var query = {
        url: param.url,
        method: param.method.toUpperCase(),
        headers: {
            "Content-Type": "application/json; charset=utf-8",
            "X-Instance-Id": this.opts.instanceId,
        },
        body: JSON.stringify(param.data),
        timeout: this.opts.timeout,
        rejectUnauthorized: false, // 禁用证书验证
    };
    // 如果有 token 则补充头
    if (this.opts.accessToken) {
        query.headers["X-Auth-Token"] = this.opts.accessToken;
    }
    if (param.method.toUpperCase() == "GET") {
        query.url = query.url + "?" + querystring.stringify(param.data);
    }
    helper.debug("ajaxData query", query);

    return request(query, function (error, response, body) {
        if (error) {
            if (typeof errorFunc == "function") {
                errorFunc(error);
            } else {
                that.errorFunc(error);
            }
            return;
        }
        // 从 header 里面获取token
        if (param.url.includes("/v1/iotfm/auth/tokens") && response.headers) {
            that.opts.accessToken = response.headers["x-subject-token"];
        }
        // 成功后执行
        var res = JSON.parse(body);
        if (res) {
            if (typeof successFunc == "function") {
                successFunc(res);
            }
        } else {
            if (typeof errorFunc == "function") {
                errorFunc(res);
            } else {
                that.errorFunc(res);
            }
        }
    });
};

RjApi.prototype.errorFunc = function (res) {
    helper.log("errorFunc", res);
    return new Error("server error: " + JSON.stringify(res));
};

// 1.1 获取token
RjApi.prototype.getToken = async function () {
    let that = this;
    let res = await this._ajaxData({
        url: that.opts.server + "/v1/iotfm/auth/tokens",
        method: "post",
        data: {
            auth: {
                identity: {
                    methods: ["password"],
                    password: {
                        user: {
                            name: this.opts.username,
                            password: this.opts.password,
                        },
                    },
                },
            },
        },
    });
    return res;
};
// 1.1 获取设备数据
RjApi.prototype.getDeviceData = async function (code) {
    let that = this;
    let res = await this._ajaxData({
        url: that.opts.server + "/v1/iotfm/devices/" + code + "/properties?limit=50&offset=1",
        method: "get",
        data: {},
    });
    return res;
};

module.exports.RjApi = RjApi;
