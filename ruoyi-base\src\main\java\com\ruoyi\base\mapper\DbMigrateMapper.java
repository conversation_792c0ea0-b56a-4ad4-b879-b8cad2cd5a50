package com.ruoyi.base.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.Map;

public interface DbMigrateMapper {

    public void removeDuplicateDictData();
    public void addDictDataUniqueIndex();
    // 创建用能视图 v_e_device_diff_indication_avg_hourly
    public void genEnergyDiffAvgHourlyView();

    public void addEDeviceRemark();
    public void addEDeviceGroupId();
    public void addEDeviceMaxDiff();
    public void addEDeviceUnreal();

    public void genBuildingUserMap();
    public void addBuildingUserMapData();

    // 补充 device 字段 ajustable_power
    public void addDeviceAjustablePower();

    // 设备数据历史 indication 长度增加
    public void updateDeviceDataHistoryIndication();

    // 设备增加关联视频字段 camera_id
    public void addDeviceCameraId();
    public void addDeviceFiles();

    // 日志增加索引 d_device_oper_log oper_time
    public void addDeviceOperLogIndex();

    // 运维表，增加报警关联 warning_id
    public void addDeviceMaintenanceWarningId();

    // 能耗表增加 超标判断 max_diff
    public void addMaxDiffToDeviceEnergy();

    // 检查并创建 报修单表 d_repair_order
    public void checkAndCreateDeviceRepairOrder();

    // 检查并创建 报修运维Map表 d_repair_maintenance_map
    public void checkAndCreateRepairMaintenanceMap();

    // 检查并创建 报修评价表 d_evaluate
    public void checkAndCreateDeviceEvaluate();
    public void genWeatherData();
    // 修改 a_weather_data city_id to weather_id 字段
    public void changeCityId();

    // 修改 a_weather_data weather_id 字段为字符串
    public void changeWeatherIdType();

    public void changeWeatherCreateTime();

    public void changeWeatherUpdateTime();

    public void updateRepairMaintenanceMapUnique();

    public void updateEnergyWarningOperator();
    public void genEenergyWarningRule();
    public void updateSysOperLogLen();

    public void addDocument();

    public void addDocumentGroup();

    public void addDocumentGroupData();

    public void addPiPoint();
    public void addDictTypeForPiPoint();
    public void addDictDataForPiPoint();

    public void addPiRoute();

    public void addPiRoutePointMap();

    public void addPiPlan();
    public void addDictTypeForPiPointCycleType();
    public void addDictDataForPiPointCycleType();
    public void addDictTypeForPiPointTaskDistributeType();
    public void addDictDataForPiPointTaskDistributeType();

    public void addPiTask();
    public void addDictTypeForPiTaskStatus();
    public void addDictDataForPiTaskStatus();

    public void addPiTaskDetail();
    public void addDictTypeForCheckedStatus();
    public void addDictDataForCheckedStatus();

    public void addPiShiftSchedule();
    public void addDictTypeForShiftScheduleType();
    public void addDictDataForShiftScheduleType();

    public void addAItemAlias();
    public void addEEnergyDataHistoryDiffFee();
    public void addEFeePolicyDate();
    public void addEEnergyDataHistoryDailyJFPG();

    public void addDeviceTaskGroupTag();

    public void addTplDevice();
    public void addTplDeviceData();
    public void addTplDeviceDataWarning();

    public void addDeviceShelfTime();
    public void updateSysUserName();
    public void updateDDeviceSourceId();
    public void createDeviceMaintenanceConsumable();
    public void createDeviceEnergySynchronized();

    public void addDeviceMaintenancePlantId();
    public void createDeviceMaintenancePlan();

    public void addDDeviceDataHistoryInd();
    public void createEnergyConsumptionPrediction();

    public void addAItemWarningDataId();
    public void addDeviceMaintenanceInds();
    public void addDeviceMaintenanceIdAuto();

    public void addItemAlias();
    public void addItemDataAlias();
    public void addDeviceItemDataMapAlias();

    public void deleteItemWarningRuleView();
    public void addItemWarningRuleView();
    public void deleteValidDeviceWarningView();
    public void addDeviceItemDataView();
    public void addValidDeviceWarningView();

    public void addAItemWarningInds();
    public void addDDeviceMaintenanceInds();

    public void updateDeviceMaintenanceType();
    public void updateUserBuildingMapId();
    public void addDRepairOrderSUserId();
    public void addAWarningRuleAutoFlag();  // 设备报警规则添加是否自动转工单标记
    public void addDDeviceMaintenanceBuildingId();
    public void addDDeviceTaskGroupBuildingId();
    public void addDRepairOrderBuildingId();
    public void addDUser();
    public void addDDeviceActivationTime();
    public void addDDeviceScrapTime();
    public void modifyDDeviceActivationTime();
    public void modifyDDeviceScrapTime();

    public void addIotServerMonitor();
    public void addPiTaskTimes();
    public void addDeviceCommunicateWarning();
    public void addAItemDataKeepSeconds();
    public void addEDeviceSynchronizedDataCreatedInd();

    public void deleteEDeviceAvgHourlyView();
    public void addEDeviceAvgHourlyView();

    public void updateDDeviceHistoryData();
    public void addDDeviceDataHistoryPrimaryKeys();
    public int checkDDeviceDataHistoryPartitionExists(@Param("database") String database);
    public void addDDeviceDataHistoryPartitions(@Param("database") String database);

    public void updateEDeviceHistoryData();
    public void addEDeviceDataHistoryPrimaryKeys();
    public int checkEDeviceDataHistoryPartitionExists(@Param("database") String database);
    public void addEDeviceDataHistoryPartitions(@Param("database") String database);

    public void createALFChain();
    public void createALFScript();
    public void addAWeatherDataColoms();
    public void addATplDeviceDataPtColoms();
    public void updateDDeviceTaskRecordContent();
    public void addATplDeviceDataNote2();
    public void addATplDeviceDataUniqueIdx();

    public void addDDeviceDataHistory();
    public void addEDeviceDataHistory();
    public void addEDeviceDataHistoryDaily();
    public void addAPointTableTag();

    public void deleteItemDataUpdateTrigger();
    public void addItemDataUpdateTrigger();
}
